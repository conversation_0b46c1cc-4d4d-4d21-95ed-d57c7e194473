"""
知识库查询增强器

在向量化过程中查询知识库，结合MD文档内容加强语义理解。
"""

import os
import logging
import re
from typing import List, Dict, Optional, Tuple, Any
from pathlib import Path

from error_prone_json_reporter.common.models import MethodInfo


class KnowledgeBaseQueryResult:
    """知识库查询结果"""
    def __init__(self, found: bool = False, description: str = "", 
                 replacement_rules: List[str] = None, confidence: float = 0.0,
                 enhanced_description: str = "", semantic_tags: List[str] = None):
        self.found = found
        self.description = description
        self.replacement_rules = replacement_rules or []
        self.confidence = confidence
        self.enhanced_description = enhanced_description
        self.semantic_tags = semantic_tags or []


class KnowledgeBaseQueryEnhancer:
    """知识库查询增强器"""
    
    def __init__(self, knowledge_base_path: str, use_ai_enhancement: bool = True):
        """
        初始化知识库查询增强器
        
        Args:
            knowledge_base_path: 知识库目录路径
            use_ai_enhancement: 是否使用AI增强（默认True）
        """
        self.logger = logging.getLogger(__name__)
        self.knowledge_base_path = knowledge_base_path
        self.use_ai_enhancement = use_ai_enhancement
        
        # 缓存
        self.query_cache: Dict[str, KnowledgeBaseQueryResult] = {}
        self.md_files_cache: Optional[List[Path]] = None
        
        # 统计信息
        self.stats = {
            'total_queries': 0,
            'cache_hits': 0,
            'found_matches': 0,
            'enhanced_descriptions': 0
        }
        
        self.logger.info(f"知识库查询增强器已初始化，知识库路径: {knowledge_base_path}")
    
    def query_and_enhance(self, method_info: MethodInfo) -> KnowledgeBaseQueryResult:
        """
        查询知识库并增强方法描述
        
        Args:
            method_info: 方法信息
            
        Returns:
            知识库查询结果
        """
        self.stats['total_queries'] += 1
        
        # 生成缓存键
        cache_key = self._generate_cache_key(method_info)
        
        # 检查缓存
        if cache_key in self.query_cache:
            self.stats['cache_hits'] += 1
            self.logger.debug(f"使用缓存的查询结果: {method_info.method_name}")
            return self.query_cache[cache_key]
        
        # 执行查询
        result = self._perform_query(method_info)
        
        # 如果找到匹配，进行增强
        if result.found:
            self.stats['found_matches'] += 1
            result = self._enhance_with_llm(method_info, result)
        
        # 缓存结果
        self.query_cache[cache_key] = result
        
        return result
    
    def get_enhanced_description_for_vector(self, method_info: MethodInfo) -> str:
        """
        获取用于向量编码的增强描述
        
        Args:
            method_info: 方法信息
            
        Returns:
            增强的描述文本
        """
        # 查询知识库
        kb_result = self.query_and_enhance(method_info)
        
        # 构建基础描述
        base_description = self._build_base_description(method_info)
        
        if kb_result.found and kb_result.enhanced_description:
            # 使用增强描述
            enhanced_text = f"{base_description} {kb_result.enhanced_description}"
            self.logger.debug(f"使用增强描述: {method_info.method_name}")
        else:
            # 使用基础描述
            enhanced_text = base_description
        
        return enhanced_text
    
    def _perform_query(self, method_info: MethodInfo) -> KnowledgeBaseQueryResult:
        """执行知识库查询"""
        if not os.path.exists(self.knowledge_base_path):
            self.logger.warning(f"知识库路径不存在: {self.knowledge_base_path} (配置的路径)")
            return KnowledgeBaseQueryResult()
        
        try:
            # 获取所有MD文件
            md_files = self._get_md_files()
            
            # 搜索相关文档
            best_match = None
            best_confidence = 0.0
            
            for md_file in md_files:
                content = self._read_md_file(md_file)
                confidence = self._calculate_relevance_score(content, method_info)
                
                if confidence > best_confidence and confidence > 0.3:  # 阈值
                    best_confidence = confidence
                    best_match = (md_file, content)
            
            if best_match:
                md_file, content = best_match
                return self._extract_knowledge_info(content, method_info, best_confidence)
            
        except Exception as e:
            self.logger.error(f"知识库查询失败: {e}")
        
        return KnowledgeBaseQueryResult()
    
    def _enhance_with_llm(self, method_info: MethodInfo, 
                         kb_result: KnowledgeBaseQueryResult) -> KnowledgeBaseQueryResult:
        """使用当前AI助手增强描述"""
        if not self.use_ai_enhancement:
            return kb_result
        
        try:
            # 使用当前AI助手进行增强
            enhanced_info = self._enhance_with_current_ai(method_info, kb_result)
            
            # 更新结果
            kb_result.enhanced_description = enhanced_info.get('enhanced_description', kb_result.description)
            kb_result.semantic_tags = enhanced_info.get('semantic_tags', [])
            
            self.stats['enhanced_descriptions'] += 1
            self.logger.debug(f"AI增强完成: {method_info.method_name}")
            
        except Exception as e:
            self.logger.error(f"AI增强失败: {e}")
        
        return kb_result
    
    def _enhance_with_current_ai(self, method_info: MethodInfo, 
                                kb_result: KnowledgeBaseQueryResult) -> Dict[str, Any]:
        """使用当前AI助手增强知识库描述"""
        # 分析方法信息和知识库内容
        method_name = method_info.method_name
        method_context = f"{method_info.package}.{method_info.class_name}.{method_name}"
        kb_description = kb_result.description
        
        # 生成增强的描述
        enhanced_description = self._generate_enhanced_description(
            method_name, method_context, kb_description
        )
        
        # 生成语义标签
        semantic_tags = self._generate_semantic_tags(method_info, kb_result)
        
        return {
            'enhanced_description': enhanced_description,
            'semantic_tags': semantic_tags
        }
    
    def _generate_enhanced_description(self, method_name: str, method_context: str, 
                                     kb_description: str) -> str:
        """生成增强的描述"""
        if not kb_description:
            return f"方法 {method_name} 的功能描述"
        
        # 基于知识库内容和方法上下文生成增强描述
        enhanced_parts = []
        
        # 添加方法的业务含义
        if any(keyword in method_name.lower() for keyword in ['get', 'find', 'query']):
            enhanced_parts.append(f"数据查询方法：{kb_description}")
        elif any(keyword in method_name.lower() for keyword in ['create', 'add', 'insert']):
            enhanced_parts.append(f"数据创建方法：{kb_description}")
        elif any(keyword in method_name.lower() for keyword in ['update', 'modify', 'edit']):
            enhanced_parts.append(f"数据更新方法：{kb_description}")
        elif any(keyword in method_name.lower() for keyword in ['delete', 'remove']):
            enhanced_parts.append(f"数据删除方法：{kb_description}")
        else:
            enhanced_parts.append(f"业务处理方法：{kb_description}")
        
        # 添加上下文信息
        if 'service' in method_context.lower():
            enhanced_parts.append("属于业务服务层")
        elif 'controller' in method_context.lower():
            enhanced_parts.append("属于控制器层")
        elif 'repository' in method_context.lower():
            enhanced_parts.append("属于数据访问层")
        
        return "，".join(enhanced_parts)
    
    def _generate_semantic_tags(self, method_info: MethodInfo, 
                              kb_result: KnowledgeBaseQueryResult) -> List[str]:
        """生成语义标签"""
        tags = set()
        
        # 从方法名提取标签
        method_name = method_info.method_name.lower()
        
        # 操作类型标签
        if any(keyword in method_name for keyword in ['get', 'find', 'query', 'search']):
            tags.add('查询操作')
        if any(keyword in method_name for keyword in ['create', 'add', 'insert']):
            tags.add('创建操作')
        if any(keyword in method_name for keyword in ['update', 'modify', 'edit']):
            tags.add('更新操作')
        if any(keyword in method_name for keyword in ['delete', 'remove']):
            tags.add('删除操作')
        
        # 业务领域标签
        if any(keyword in method_name for keyword in ['energy', 'power']):
            tags.add('能源管理')
        if any(keyword in method_name for keyword in ['project', 'tree']):
            tags.add('项目管理')
        if any(keyword in method_name for keyword in ['user', 'account']):
            tags.add('用户管理')
        if any(keyword in method_name for keyword in ['data', 'info']):
            tags.add('数据管理')
        
        # 从知识库描述中提取标签
        if kb_result.description:
            desc_lower = kb_result.description.lower()
            if '迁移' in desc_lower:
                tags.add('迁移相关')
            if '替换' in desc_lower:
                tags.add('方法替换')
            if '升级' in desc_lower:
                tags.add('版本升级')
        
        return list(tags)[:5]  # 限制返回5个标签
    
    def _get_md_files(self) -> List[Path]:
        """获取所有MD文件（带缓存）"""
        if self.md_files_cache is None:
            self.md_files_cache = list(Path(self.knowledge_base_path).glob("**/*.md"))
            self.logger.info(f"找到 {len(self.md_files_cache)} 个MD文件")
        
        return self.md_files_cache
    
    def _read_md_file(self, md_file: Path) -> str:
        """读取MD文件内容"""
        try:
            return md_file.read_text(encoding='utf-8')
        except UnicodeDecodeError:
            try:
                return md_file.read_text(encoding='gbk')
            except UnicodeDecodeError:
                return md_file.read_text(encoding='latin-1')
    
    def _calculate_relevance_score(self, content: str, method_info: MethodInfo) -> float:
        """计算内容与方法的相关性分数"""
        content_lower = content.lower()
        score = 0.0
        
        # 方法名匹配
        if method_info.method_name.lower() in content_lower:
            score += 0.4
        
        # 类名匹配
        if method_info.class_name and method_info.class_name.lower() in content_lower:
            score += 0.3
        
        # 包名匹配
        if method_info.package:
            package_parts = method_info.package.split('.')
            for part in package_parts:
                if len(part) > 2 and part.lower() in content_lower:
                    score += 0.1
                    break
        
        # 参数类型匹配
        if method_info.parameters:
            for param in method_info.parameters:
                param_type = param.split(':')[-1].strip() if ':' in param else param
                if param_type.lower() in content_lower:
                    score += 0.05
        
        # 返回类型匹配
        if method_info.return_type and method_info.return_type.lower() in content_lower:
            score += 0.1
        
        return min(1.0, score)
    
    def _extract_knowledge_info(self, content: str, method_info: MethodInfo, 
                               confidence: float) -> KnowledgeBaseQueryResult:
        """从内容中提取知识信息"""
        # 提取方法描述
        description = self._extract_method_description(content, method_info.method_name)
        
        # 提取替换规则
        replacement_rules = self._extract_replacement_rules(content, method_info.method_name)
        
        # 提取语义标签
        semantic_tags = self._extract_semantic_tags(content, method_info.method_name)
        
        return KnowledgeBaseQueryResult(
            found=True,
            description=description,
            replacement_rules=replacement_rules,
            confidence=confidence,
            semantic_tags=semantic_tags
        )
    
    def _extract_method_description(self, content: str, method_name: str) -> str:
        """从内容中提取方法描述"""
        lines = content.split('\n')
        description_lines = []
        
        # 查找方法相关的描述
        in_method_section = False
        for i, line in enumerate(lines):
            line_lower = line.lower()
            
            # 检查是否进入方法描述部分
            if method_name.lower() in line_lower and ('方法' in line or 'method' in line_lower):
                in_method_section = True
                description_lines.append(line.strip())
                continue
            
            # 如果在方法描述部分
            if in_method_section:
                if line.strip() == '':
                    continue
                if line.startswith('#') and method_name.lower() not in line_lower:
                    # 新的章节开始，退出方法描述部分
                    break
                if not line.startswith('###'):  # 不是子标题
                    description_lines.append(line.strip())
                if len(description_lines) >= 5:  # 限制描述长度
                    break
        
        return ' '.join(description_lines) if description_lines else ""
    
    def _extract_replacement_rules(self, content: str, method_name: str) -> List[str]:
        """从内容中提取替换规则"""
        rules = []
        lines = content.split('\n')
        
        # 查找替换规则部分
        in_replacement_section = False
        for line in lines:
            line_lower = line.lower()
            
            # 检查是否进入替换规则部分
            if '替换规则' in line or 'replacement' in line_lower:
                in_replacement_section = True
                continue
            
            # 如果在替换规则部分
            if in_replacement_section:
                if line.strip() == '':
                    continue
                if line.startswith('#'):  # 新的章节开始
                    break
                if method_name.lower() in line_lower or '旧版本' in line or '新版本' in line:
                    rules.append(line.strip())
        
        return rules
    
    def _extract_semantic_tags(self, content: str, method_name: str) -> List[str]:
        """从内容中提取语义标签"""
        tags = set()
        
        # 从内容中提取关键词
        words = re.findall(r'\b[a-zA-Z\u4e00-\u9fff]+\b', content)
        
        # 过滤和分类关键词
        business_keywords = {'用户', '订单', '支付', '商品', '库存', '账户', '权限', '数据', '查询', '创建', '更新', '删除'}
        technical_keywords = {'service', 'dao', 'controller', 'manager', 'util', 'helper'}
        
        for word in words:
            word_lower = word.lower()
            if word in business_keywords or word_lower in technical_keywords:
                tags.add(word_lower)
            elif len(word) > 3 and word.isalpha():
                # 其他可能的业务词汇
                if any(keyword in word_lower for keyword in ['get', 'find', 'create', 'update', 'delete', 'save']):
                    tags.add(word_lower)
        
        return list(tags)[:10]  # 限制标签数量
    
    def _build_base_description(self, method_info: MethodInfo) -> str:
        """构建基础方法描述"""
        parts = []
        
        # 添加包和类信息
        if method_info.package:
            parts.append(f"package {method_info.package}")
        
        if method_info.class_name:
            parts.append(f"class {method_info.class_name}")
        
        # 添加方法签名
        method_signature = f"method {method_info.method_name}"
        
        if method_info.parameters:
            params_str = ", ".join(method_info.parameters)
            method_signature += f"({params_str})"
        else:
            method_signature += "()"
        
        if method_info.return_type:
            method_signature += f" returns {method_info.return_type}"
        
        parts.append(method_signature)
        
        # 添加上下文信息
        if method_info.context:
            parts.append(f"context {method_info.context}")
        
        return " ".join(parts)
    
    def _build_enhancement_prompt(self, method_info: MethodInfo, 
                                 kb_result: KnowledgeBaseQueryResult) -> str:
        """构建增强提示词"""
        prompt = f"""基于以下信息，生成增强的方法语义描述：

方法信息：
- 方法名: {method_info.method_name}
- 类名: {method_info.class_name}
- 包名: {method_info.package}
- 参数: {', '.join(method_info.parameters) if method_info.parameters else '无'}
- 返回类型: {method_info.return_type}

知识库描述：
{kb_result.description}

替换规则：
{chr(10).join(kb_result.replacement_rules)}

请生成：
1. 增强的功能描述（重点突出业务逻辑和用途）
2. 语义标签（3-5个关键词）

输出格式（JSON）：
{{
  "enhanced_description": "增强的功能描述",
  "semantic_tags": ["标签1", "标签2", "标签3"]
}}"""
        
        return prompt
    
    def _parse_enhancement_response(self, response: str) -> Dict[str, Any]:
        """解析增强响应"""
        try:
            import json
            return json.loads(response)
        except Exception as e:
            self.logger.error(f"解析增强响应失败: {e}")
            return {}
    
    def _generate_cache_key(self, method_info: MethodInfo) -> str:
        """生成缓存键"""
        import hashlib
        
        key_parts = [
            method_info.package or '',
            method_info.class_name or '',
            method_info.method_name or '',
            str(hash(tuple(method_info.parameters))) if method_info.parameters else '',
            method_info.return_type or ''
        ]
        
        key_string = '|'.join(key_parts)
        return hashlib.md5(key_string.encode('utf-8')).hexdigest()
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = self.stats.copy()
        stats['cache_size'] = len(self.query_cache)
        stats['cache_hit_rate'] = (self.stats['cache_hits'] / max(1, self.stats['total_queries'])) * 100
        stats['match_rate'] = (self.stats['found_matches'] / max(1, self.stats['total_queries'])) * 100
        stats['enhancement_rate'] = (self.stats['enhanced_descriptions'] / max(1, self.stats['found_matches'])) * 100
        return stats
    
    def clear_cache(self):
        """清空缓存"""
        self.query_cache.clear()
        self.md_files_cache = None
        self.logger.info("知识库查询增强器缓存已清空")
    
    def reload_knowledge_base(self):
        """重新加载知识库"""
        self.md_files_cache = None
        self.query_cache.clear()
        self.logger.info("知识库已重新加载")