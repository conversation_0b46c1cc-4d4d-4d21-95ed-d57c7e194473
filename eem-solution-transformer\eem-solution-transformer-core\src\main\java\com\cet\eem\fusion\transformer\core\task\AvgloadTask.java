package com.cet.eem.fusion.transformer.core.task;

import com.cet.eem.fusion.transformer.core.service.TransformerindexDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * @ClassName : AvgloadTask
 * <AUTHOR> yangy
 * @Date: 2022-03-28 09:54
 */
@Component
@Configuration
@EnableScheduling
public class AvgloadTask  {

    @Autowired
    TransformerindexDataService transformerindexDataService;

    @Scheduled(cron = "${cet.eem.task.transformerindex.interval:-}")
    public void execute() {
        transformerindexDataService.transferData();
    }
}
