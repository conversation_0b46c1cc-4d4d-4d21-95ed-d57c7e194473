"""
Java错误分析器包
"""
from .error_parser import <PERSON><PERSON>r<PERSON>ars<PERSON>
from .error_grouper import <PERSON>rrorGrouper
from .class_name_finder import ClassNameFinder
from .fuzzy_matcher import FuzzyMatcher
from .models import (
    CompilationError, 
    SimilarityResult, 
    ProcessingConfig, 
    ClassAnalysisResult, 
    ClassFileInfo,
    ErrorStatistics
)
from .logger_utils import setup_logger, get_logger
from .config_utils import SimpleConfigManager

__version__ = "1.1.0"
__all__ = [
    'ErrorParser',
    'ErrorGrouper', 
    'ClassNameFinder',
    'FuzzyMatcher',
    'CompilationError',
    'SimilarityResult',
    'ProcessingConfig',
    'ClassAnalysisResult',
    'ClassFileInfo',
    'ErrorStatistics',
    'setup_logger',
    'get_logger',
    'SimpleConfigManager'
]