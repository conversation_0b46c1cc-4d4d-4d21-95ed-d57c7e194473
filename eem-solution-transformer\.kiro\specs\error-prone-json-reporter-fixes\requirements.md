# 需求文档

## 介绍

本规格文档旨在解决 error_prone_json_reporter 项目中阻碍正常功能的关键问题。重点是修复配置错误、集成现有组件、实现完整的代码上下文分析，并确保 JDK8 兼容性。

## 需求

### 需求 1

**用户故事：** 作为开发者，我希望向量库路径配置能够正确工作，以便向量库能够正确生成和加载。

#### 验收标准

1. 当系统初始化向量数据库时，系统应当使用配置中的`vector_cache_dir`而不是`output_dir`
2. 当配置中指定了`vector_cache_dir`时，系统应当在该目录中创建向量库文件（如 vector_library.pkl）
3. 当系统构建向量库时，系统应当能够成功保存向量库到正确的目录
4. 当向量库目录不存在时，系统应当自动创建该目录

### 需求 2

**用户故事：** 作为开发者，我希望知识库路径配置能够被正确读取，以便知识库功能正常工作。

#### 验收标准

1. 当配置指定`knowledge_base_path: "知识库"`时，系统应当使用"知识库"作为知识库目录
2. 当系统加载知识库时，如果配置指定了不同的路径，系统不应当默认使用"knowledge_base"
3. 当知识库目录存在时，系统应当能够成功从中加载 MD 文档
4. 当知识库路径无效时，系统应当提供包含实际配置路径的清晰错误信息

### 需求 3

**用户故事：** 作为开发者，我希望现有组件能够集成到主工作流程中，以便利用遗留代码搜索和上下文分析功能。

#### 验收标准

1. 当 APIMatchingEngine 处理错误时，系统应当使用 PreciseSourceSearcher 进行遗留代码搜索
2. 当处理缺失方法时，系统应当使用 SourceContextAnalyzer 进行上下文分析
3. 当配置了`legacy_src_path`时，系统应当在该路径中搜索原始方法实现
4. 当找到原始方法代码时，系统应当将其包含在语义分析中

### 需求 4

**用户故事：** 作为开发者，我希望有完整的代码上下文分析工作流程，以便语义分析基于实际源代码而不仅仅是 JSON 元数据。

#### 验收标准

1. 当处理错误 JSON 时，系统应当使用`location.file`和`location.line`在当前项目文件中定位错误行
2. 当找到错误位置时，系统应当提取周围的代码上下文
3. 当`legacy_src_path`可用时，系统应当搜索原始方法实现
4. 当找到原始方法时，系统应当提取完整的方法代码和上下文
5. 当执行语义分析时，系统应当结合当前上下文、原始代码和 JSON 元数据

### 需求 5

**用户故事：** 作为开发者，我希望 javalang 能够正确支持 JDK8 语法，以便源代码解析正常工作。

#### 验收标准

1. 当解析包含 Lambda 表达式的 Java 文件时，javalang 应当能够无错误地解析它们
2. 当遇到方法引用（::）时，解析器应当正确处理它们
3. 当解析 Stream API 链式调用时，系统应当能够正确提取方法信息
4. 当 javalang 无法解析 JDK8 语法时，系统应当实现回退解析策略
5. 当解析失败时，系统应当记录具体的语法问题并继续处理其他文件

### 需求 6

**用户故事：** 作为开发者，我希望充分利用 target_method_test.json 信息，以便错误描述全面且准确。

#### 验收标准

1. 当构建错误描述时，系统应当使用`location.file`、`location.line`和`location.column`字段
2. 当可用时，系统应当在分析中包含`method_signature`信息
3. 当处理错误时，系统应当从指定的文件和行号读取实际源代码
4. 当构建上下文时，系统应当将 JSON 元数据与实际源代码内容结合

### 需求 7

**用户故事：** 作为开发者，我希望增强错误描述构建，以便语义分析具有丰富的上下文信息。

#### 验收标准

1. 当构建错误描述时，系统应当从错误位置读取实际源代码
2. 当源代码可用时，系统应当提取方法调用上下文和周围代码
3. 当遗留源代码可用时，系统应当包含原始方法实现
4. 当执行 AI 分析时，系统应当使用全面的代码上下文而不是简单的 JSON 字段拼接

### 需求 8

**用户故事：** 作为开发者，我希望 AI 语义分析基于完整信息，以便向量生成准确且有意义。

#### 验收标准

1. 当执行 AI 语义分析时，系统应当使用实际的源代码上下文
2. 当原始方法实现可用时，系统应当将其包含在 AI 分析中
3. 当生成方法语义时，系统应当分析业务逻辑、参数含义和使用模式
4. 当创建向量时，系统应当优先考虑 AI 生成的语义理解而不是简单的文本匹配
