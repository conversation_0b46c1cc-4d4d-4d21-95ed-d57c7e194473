# Java代码迁移API匹配工具配置文件

# 第一阶段配置：错误清单生成
stage1:
  # 错误检测工具选择: "error_prone" | "existing_tool"
  error_detector: "existing_tool"

  # 现有工具输出文件路径（当使用existing_tool时）
  existing_tool_output: "JavaAnnotator.xml"

  # Java项目路径
  project_path: "/path/to/java/project"

  # Error Prone配置（当使用error_prone时）
  error_prone:
    enabled_checks:
      - "MissingMethod"
      - "UnresolvedSymbol"
    output_format: "json"

# 第二阶段配置：API匹配
stage2:
  # GraphCodeBERT模型路径（本地模型）
  model_name: "E:/study/ai-model/graphcodebert-base"

  # 新框架源码路径
  src_path: "E:/work/project/fusion/new-code"

  # 迁移前源码路径（用于精确搜索缺失方法的原始实现）
  legacy_src_path: "E:/work/project/fusion/old-code"

  # 知识库目录路径（包含方法替换规则的MD文档）
  knowledge_base_path: "知识库"

  # 是否启用AI增强的向量编码（使用当前AI助手进行语义分析）
  use_ai_enhancement: true

  # 向量库是否已经初始化完成
  base_init: true
  # 返回的TOP-K候选方法数量
  top_k: 5

  # 批处理大小
  batch_size: 32

  # 相似度阈值
  similarity_threshold: 0.5

# 输出配置
output:
  # 第一阶段输出的错误清单JSON文件
  errors_json: "output/errors.json"

  # 第二阶段输出的迁移建议JSON文件
  migration_json: "output/migration_suggestions.json"

  # 日志文件路径
  log_file: "logs/migration.log"

  # 输出目录
  output_dir: "output"

# 性能配置
performance:
  # 是否使用GPU加速
  use_gpu: false

  # 是否缓存向量编码结果
  cache_vectors: true

  # 向量缓存目录（为空时使用默认位置：~/.cache/error_prone_json_reporter/vectors）
  vector_cache_dir: "E:/study/ai-model/cache/vectors"

  # 最大工作线程数
  max_workers: 4

  # 内存限制（MB）
  memory_limit: 8192

# 日志配置
logging:
  # 日志级别: DEBUG, INFO, WARNING, ERROR
  level: "INFO"

  # 日志格式
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

  # 是否输出到控制台
  console: true

  # 是否输出到文件
  file: true
