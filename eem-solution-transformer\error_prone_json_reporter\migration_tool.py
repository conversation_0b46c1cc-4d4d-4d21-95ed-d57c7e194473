#!/usr/bin/env python3
"""
Java代码迁移API匹配工具 - 主入口脚本

这是一个两阶段的Java代码迁移工具：
1. 第一阶段：生成项目错误清单JSON
2. 第二阶段：使用机器学习进行API相似度匹配

使用示例：
    # 执行完整流程
    python migration_tool.py --config config.yaml --stage both
    
    # 只执行第一阶段
    python migration_tool.py --config config.yaml --stage 1 --project-path /path/to/project
    
    # 只执行第二阶段
    python migration_tool.py --config config.yaml --stage 2 --src-path /path/to/new/framework
    
    # 使用命令行参数覆盖配置
    python migration_tool.py --stage both --project-path /path/to/project --src-path /path/to/framework --top-k 5
"""

import argparse
import sys
import os
import logging
import time
from pathlib import Path
from typing import Optional, Dict, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from error_prone_json_reporter.config.config_manager import ConfigManager
from error_prone_json_reporter.common.models import Configuration, ProcessingStats
from error_prone_json_reporter.common.logging_config import setup_logging, get_logger, logging_manager
from error_prone_json_reporter.common.error_handling import (
    ErrorManager, MigrationToolError, ConfigurationError, 
    FILE_RETRY_STRATEGY, SKIP_INVALID_FILE_STRATEGY, MODEL_FALLBACK_STRATEGY
)
from error_prone_json_reporter.common.workflow_controller import WorkflowController
from error_prone_json_reporter.stage1.error_list_generator import ErrorListGenerator
from error_prone_json_reporter.stage2.api_matching_engine import APIMatchingEngine


class MigrationTool:
    """Java代码迁移工具主控制器"""
    
    def __init__(self):
        self.config_manager = ConfigManager()
        self.logger = None
        self.config = None
        self.stats = ProcessingStats()
        self.error_manager = None
        self.workflow_controller = None
    
    def setup_logging_and_error_handling(self, config: Configuration) -> None:
        """设置日志系统和错误处理"""
        # 设置日志系统
        self.logger = setup_logging(config)
        
        # 设置错误管理器
        self.error_manager = ErrorManager(self.logger)
        
        # 注册预定义的错误恢复策略
        self.error_manager.register_recovery_strategy(FILE_RETRY_STRATEGY)
        self.error_manager.register_recovery_strategy(SKIP_INVALID_FILE_STRATEGY)
        self.error_manager.register_recovery_strategy(MODEL_FALLBACK_STRATEGY)
        
        # 记录系统信息
        logging_manager.log_system_info(self.logger)
        logging_manager.log_config_summary(self.logger, config)
        
        # 创建工作流程控制器
        self.workflow_controller = WorkflowController(config)
        self.workflow_controller.set_error_manager(self.error_manager)
    
    def run_stage1(self, config: Configuration) -> str:
        """
        执行第一阶段：错误清单生成
        
        Args:
            config: 配置对象
            
        Returns:
            生成的错误清单JSON文件路径
        """
        self.logger.info("=" * 60)
        self.logger.info("开始执行第一阶段：错误清单生成")
        self.logger.info("=" * 60)
        
        if not config.project_path:
            raise ValueError("第一阶段需要指定项目路径 (project_path)")
        
        if not os.path.exists(config.project_path):
            raise ValueError(f"项目路径不存在: {config.project_path}")
        
        start_time = time.time()
        
        try:
            with logging_manager.log_execution_time(self.logger, "第一阶段错误清单生成"):
                # 创建错误清单生成器
                generator = ErrorListGenerator(config)
                
                # 生成错误清单
                self.logger.info(f"分析项目: {config.project_path}")
                self.logger.info(f"使用检测器: {config.error_detector}")
                
                errors_json_path = generator.generate_error_list(config.project_path)
                
                # 更新统计信息
                processing_time = time.time() - start_time
                self.stats.processing_time += processing_time
                
                self.logger.info(f"错误清单已保存到: {errors_json_path}")
                
                return errors_json_path
            
        except Exception as e:
            # 使用错误管理器处理错误
            if self.error_manager and not self.error_manager.handle_error(
                e, {"stage": "1", "project_path": config.project_path}, "第一阶段执行"
            ):
                raise MigrationToolError(f"第一阶段执行失败: {e}", cause=e)
            raise
    
    def run_stage2(self, config: Configuration, errors_json_path: Optional[str] = None) -> str:
        """
        执行第二阶段：API相似度匹配
        
        Args:
            config: 配置对象
            errors_json_path: 错误清单JSON文件路径，如果为None则使用配置中的路径
            
        Returns:
            生成的迁移建议JSON文件路径
        """
        self.logger.info("=" * 60)
        self.logger.info("开始执行第二阶段：API相似度匹配")
        self.logger.info("=" * 60)
        
        if not config.src_path:
            raise ValueError("第二阶段需要指定新框架源码路径 (src_path)")
        
        if not os.path.exists(config.src_path):
            raise ValueError(f"新框架源码路径不存在: {config.src_path}")
        
        # 确定错误清单文件路径
        if errors_json_path is None:
            errors_json_path = config.errors_json
        
        if not os.path.exists(errors_json_path):
            raise ValueError(f"错误清单文件不存在: {errors_json_path}")
        
        start_time = time.time()
        
        try:
            with logging_manager.log_execution_time(self.logger, "第二阶段API匹配"):
                # 创建API匹配引擎
                engine = APIMatchingEngine(config)
                
                # 执行API匹配
                self.logger.info(f"加载错误清单: {errors_json_path}")
                self.logger.info(f"分析新框架源码: {config.src_path}")
                self.logger.info(f"使用模型: {config.model_name}")
                self.logger.info(f"TOP-K设置: {config.top_k}")
                
                output_path = engine.match_apis(errors_json_path, config.src_path)
                
                # 更新统计信息
                processing_time = time.time() - start_time
                self.stats.processing_time += processing_time
                
                self.logger.info(f"迁移建议已保存到: {output_path}")
                
                return output_path
            
        except Exception as e:
            # 使用错误管理器处理错误
            if self.error_manager and not self.error_manager.handle_error(
                e, {"stage": "2", "src_path": config.src_path, "errors_json": errors_json_path}, 
                "第二阶段执行"
            ):
                raise MigrationToolError(f"第二阶段执行失败: {e}", cause=e)
            raise
    
    def run_both_stages(self, config: Configuration) -> Dict[str, str]:
        """
        执行完整的两阶段流程
        
        Args:
            config: 配置对象
            
        Returns:
            包含两个阶段输出文件路径的字典
        """
        self.logger.info("=" * 60)
        self.logger.info("开始执行完整的两阶段流程")
        self.logger.info("=" * 60)
        
        total_start_time = time.time()
        
        try:
            # 执行第一阶段
            errors_json_path = self.run_stage1(config)
            
            # 执行第二阶段
            migration_json_path = self.run_stage2(config, errors_json_path)
            
            # 总结
            total_time = time.time() - total_start_time
            self.logger.info("=" * 60)
            self.logger.info("完整流程执行完成")
            self.logger.info(f"总耗时: {total_time:.2f}秒")
            self.logger.info(f"错误清单: {errors_json_path}")
            self.logger.info(f"迁移建议: {migration_json_path}")
            self.logger.info("=" * 60)
            
            return {
                "errors_json": errors_json_path,
                "migration_json": migration_json_path
            }
            
        except Exception as e:
            self.logger.error(f"完整流程执行失败: {e}")
            raise
    
    def run_workflow_controlled(self, stage: str, resume_from_checkpoint: bool = True) -> Dict[str, Any]:
        """
        使用工作流程控制器执行
        
        Args:
            stage: 执行阶段 ("1", "2", "both")
            resume_from_checkpoint: 是否从检查点恢复
            
        Returns:
            执行结果字典
        """
        self.logger.info("=" * 60)
        self.logger.info("使用工作流程控制器执行")
        self.logger.info("=" * 60)
        
        try:
            # 检查是否有现有的工作流程状态
            if resume_from_checkpoint:
                status = self.workflow_controller.get_workflow_status()
                if status.get("status") != "not_initialized":
                    self.logger.info(f"发现现有工作流程: {status.get('workflow_id')}")
                    self.logger.info(f"当前阶段: {status.get('current_stage')}")
                    
                    # 询问是否继续
                    if stage == "both":
                        self.logger.info("将从上次中断的地方继续执行")
            
            # 执行工作流程
            results = self.workflow_controller.execute_workflow(stage, resume_from_checkpoint)
            
            # 提取输出文件路径
            output_files = {}
            if self.workflow_controller.workflow_state.stage1_result:
                output_files["errors_json"] = self.workflow_controller.workflow_state.stage1_result.output_file
            if self.workflow_controller.workflow_state.stage2_result:
                output_files["migration_json"] = self.workflow_controller.workflow_state.stage2_result.output_file
            
            results.update(output_files)
            return results
            
        except Exception as e:
            self.logger.error(f"工作流程控制器执行失败: {e}")
            raise
    
    def show_workflow_status(self) -> int:
        """显示工作流程状态"""
        try:
            status = self.workflow_controller.get_workflow_status()
            
            if status.get("status") == "not_initialized":
                self.logger.info("没有找到工作流程状态")
                return 0
            
            self.logger.info("=" * 50)
            self.logger.info("工作流程状态")
            self.logger.info("=" * 50)
            self.logger.info(f"工作流程ID: {status.get('workflow_id')}")
            self.logger.info(f"当前阶段: {status.get('current_stage')}")
            self.logger.info(f"创建时间: {status.get('created_at')}")
            self.logger.info(f"更新时间: {status.get('updated_at')}")
            
            if "stage1" in status:
                stage1 = status["stage1"]
                self.logger.info(f"第一阶段: {stage1['status']}, 耗时: {stage1['duration']:.2f}秒")
                if stage1.get("output_file"):
                    self.logger.info(f"  输出文件: {stage1['output_file']}")
            
            if "stage2" in status:
                stage2 = status["stage2"]
                self.logger.info(f"第二阶段: {stage2['status']}, 耗时: {stage2['duration']:.2f}秒")
                if stage2.get("output_file"):
                    self.logger.info(f"  输出文件: {stage2['output_file']}")
            
            self.logger.info("=" * 50)
            return 0
            
        except Exception as e:
            self.logger.error(f"显示工作流程状态失败: {e}")
            return 1
    
    def cleanup_workflow(self) -> int:
        """清理工作流程状态"""
        try:
            self.workflow_controller.cleanup_workflow_state()
            self.logger.info("工作流程状态已清理")
            return 0
            
        except Exception as e:
            self.logger.error(f"清理工作流程状态失败: {e}")
            return 1
    
    def validate_stage_requirements(self, stage: str, config: Configuration) -> None:
        """
        验证阶段执行的必要条件
        
        Args:
            stage: 执行阶段 ("1", "2", "both")
            config: 配置对象
        """
        if stage in ["1", "both"]:
            if not config.project_path:
                raise ConfigurationError("执行第一阶段需要指定 --project-path 参数")
            if not os.path.exists(config.project_path):
                raise ConfigurationError(f"项目路径不存在: {config.project_path}")
        
        if stage in ["2", "both"]:
            if not config.src_path:
                raise ConfigurationError("执行第二阶段需要指定 --src-path 参数")
            if not os.path.exists(config.src_path):
                raise ConfigurationError(f"新框架源码路径不存在: {config.src_path}")
            
            # 如果只执行第二阶段，需要检查错误清单文件是否存在
            if stage == "2" and not os.path.exists(config.errors_json):
                raise ConfigurationError(f"错误清单文件不存在: {config.errors_json}")
    
    def run(self, args: argparse.Namespace) -> int:
        """
        运行迁移工具
        
        Args:
            args: 命令行参数
            
        Returns:
            退出代码 (0表示成功)
        """
        try:
            # 加载配置
            if args.config:
                self.config = self.config_manager.load_config(args.config)
            else:
                self.config = self.config_manager.load_config()
            
            # 合并命令行参数（只有非None的值才覆盖配置文件）
            cli_args = {}
            
            # 基本参数
            if args.project_path is not None:
                cli_args['project_path'] = args.project_path
            if args.src_path is not None:
                cli_args['src_path'] = args.src_path
            if args.error_detector is not None:
                cli_args['error_detector'] = args.error_detector
            if args.existing_tool_output is not None:
                cli_args['existing_tool_output'] = args.existing_tool_output
            
            # 第二阶段参数
            if args.model_name is not None:
                cli_args['model_name'] = args.model_name
            if args.top_k is not None:
                cli_args['top_k'] = args.top_k
            if args.batch_size is not None:
                cli_args['batch_size'] = args.batch_size
            if args.similarity_threshold is not None:
                cli_args['similarity_threshold'] = args.similarity_threshold
            
            # 输出参数
            if args.errors_json is not None:
                cli_args['errors_json'] = args.errors_json
            if args.output_json is not None:
                cli_args['output_json'] = args.output_json
            if args.log_level is not None:
                cli_args['log_level'] = args.log_level
            
            # 性能参数（只有在命令行明确指定时才覆盖）
            if hasattr(args, 'use_gpu') and args.use_gpu:
                cli_args['use_gpu'] = True
            if hasattr(args, 'cache_vectors') and args.cache_vectors:
                cli_args['cache_vectors'] = True
            if getattr(args, 'vector_cache_dir', None) is not None:
                cli_args['vector_cache_dir'] = args.vector_cache_dir
            if args.max_workers is not None:
                cli_args['max_workers'] = args.max_workers
            
            self.config = self.config_manager.merge_cli_args(self.config, cli_args)
            
            # 设置日志和错误处理
            self.setup_logging_and_error_handling(self.config)
            
            # 处理工作流程控制选项
            if args.status:
                return self.show_workflow_status()
            
            if args.cleanup:
                return self.cleanup_workflow()
            
            # 验证执行条件
            self.validate_stage_requirements(args.stage, self.config)
            
            # 执行相应阶段 - 使用工作流程控制器
            if args.stage in ["1", "2", "both"]:
                results = self.run_workflow_controlled(
                    args.stage, 
                    resume_from_checkpoint=not getattr(args, 'no_resume', False)
                )
                
                # 记录执行结果
                if results.get("success"):
                    self.logger.info("执行成功完成")
                    if "errors_json" in results:
                        self.logger.info(f"错误清单: {results['errors_json']}")
                    if "migration_json" in results:
                        self.logger.info(f"迁移建议: {results['migration_json']}")
                else:
                    self.logger.error("执行失败")
            else:
                raise ConfigurationError(f"无效的阶段参数: {args.stage}")
            
            # 记录错误统计摘要
            if self.error_manager:
                self.error_manager.log_error_summary()
            
            return 0
            
        except KeyboardInterrupt:
            if self.logger:
                self.logger.info("用户中断执行")
            else:
                print("用户中断执行")
            return 1
        except Exception as e:
            if self.logger:
                self.logger.error(f"执行失败: {e}")
            else:
                print(f"执行失败: {e}")
            return 1


def create_argument_parser() -> argparse.ArgumentParser:
    """创建命令行参数解析器"""
    parser = argparse.ArgumentParser(
        description="Java代码迁移API匹配工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 执行完整流程
  %(prog)s --stage both --project-path /path/to/project --src-path /path/to/framework
  
  # 只执行第一阶段（错误清单生成）
  %(prog)s --stage 1 --project-path /path/to/project
  
  # 只执行第二阶段（API匹配）
  %(prog)s --stage 2 --src-path /path/to/framework --errors-json output/errors.json
  
  # 使用配置文件
  %(prog)s --config config.yaml --stage both
  
  # 覆盖配置参数
  %(prog)s --config config.yaml --stage both --top-k 5 --use-gpu
        """
    )
    
    # 基本参数
    parser.add_argument(
        "--stage", 
        choices=["1", "2", "both"], 
        default="both",
        help="执行阶段: 1=错误清单生成, 2=API匹配, both=完整流程 (默认: both)"
    )
    
    parser.add_argument(
        "--config", 
        type=str,
        help="配置文件路径 (YAML格式)"
    )
    
    # 第一阶段参数
    stage1_group = parser.add_argument_group("第一阶段参数")
    stage1_group.add_argument(
        "--project-path", 
        type=str,
        help="Java项目路径"
    )
    
    stage1_group.add_argument(
        "--error-detector", 
        choices=["error_prone", "existing_tool", "complete_workflow"],
        help="错误检测器类型"
    )
    
    stage1_group.add_argument(
        "--existing-tool-output", 
        type=str,
        help="现有工具输出文件路径"
    )
    
    # 第二阶段参数
    stage2_group = parser.add_argument_group("第二阶段参数")
    stage2_group.add_argument(
        "--src-path", 
        type=str,
        help="新框架源码路径"
    )
    
    stage2_group.add_argument(
        "--model-name", 
        type=str,
        help="GraphCodeBERT模型名称 (默认: microsoft/graphcodebert-base)"
    )
    
    stage2_group.add_argument(
        "--top-k", 
        type=int,
        help="返回的候选方法数量 (默认: 3)"
    )
    
    stage2_group.add_argument(
        "--batch-size", 
        type=int,
        help="批处理大小 (默认: 32)"
    )
    
    stage2_group.add_argument(
        "--similarity-threshold", 
        type=float,
        help="相似度阈值 (0-1, 默认: 0.5)"
    )
    
    # 输出参数
    output_group = parser.add_argument_group("输出参数")
    output_group.add_argument(
        "--errors-json", 
        type=str,
        help="错误清单JSON文件路径 (默认: output/errors.json)"
    )
    
    output_group.add_argument(
        "--output-json", 
        type=str,
        help="迁移建议JSON文件路径 (默认: output/migration_suggestions.json)"
    )
    
    output_group.add_argument(
        "--log-level", 
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        help="日志级别 (默认: INFO)"
    )
    
    # 性能参数
    perf_group = parser.add_argument_group("性能参数")
    perf_group.add_argument(
        "--use-gpu", 
        action="store_true",
        help="使用GPU加速 (如果可用)"
    )
    
    perf_group.add_argument(
        "--cache-vectors", 
        action="store_true",
        help="缓存向量编码结果"
    )
    
    perf_group.add_argument(
        "--vector-cache-dir", 
        type=str,
        help="向量缓存目录路径"
    )
    
    perf_group.add_argument(
        "--max-workers", 
        type=int,
        help="最大工作线程数 (默认: 4)"
    )
    
    # 工作流程控制参数
    workflow_group = parser.add_argument_group("工作流程控制参数")
    workflow_group.add_argument(
        "--no-resume", 
        action="store_true",
        help="不从检查点恢复，重新开始执行"
    )
    
    workflow_group.add_argument(
        "--status", 
        action="store_true",
        help="显示当前工作流程状态"
    )
    
    workflow_group.add_argument(
        "--cleanup", 
        action="store_true",
        help="清理工作流程状态文件"
    )
    
    # 帮助和版本
    parser.add_argument(
        "--version", 
        action="version", 
        version="Java代码迁移API匹配工具 v1.0.0"
    )
    
    return parser


def main():
    """主函数"""
    parser = create_argument_parser()
    args = parser.parse_args()
    
    # 创建并运行迁移工具
    tool = MigrationTool()
    exit_code = tool.run(args)
    
    sys.exit(exit_code)


if __name__ == "__main__":
    main()