"""
参数兼容性分析器

专门处理新旧方法参数差异，智能处理框架升级带来的参数差异。
"""

import re
import logging
from typing import List, Dict, Optional, Set, Tuple, Any
from dataclasses import dataclass
from enum import Enum

from error_prone_json_reporter.common.models import MethodInfo


class ParameterChangeType(Enum):
    """参数变化类型"""
    IDENTICAL = "identical"           # 完全相同
    FRAMEWORK_ADDED = "framework_added"  # 新增框架参数
    TYPE_CHANGED = "type_changed"     # 类型变化
    ENCAPSULATED = "encapsulated"     # 参数封装（多参数->DTO）
    BUSINESS_CHANGED = "business_changed"  # 业务参数变化
    REMOVED = "removed"               # 参数移除


@dataclass
class ParameterMapping:
    """参数映射关系"""
    old_param: str                    # 旧参数
    new_param: Optional[str]          # 新参数（可能为None）
    change_type: ParameterChangeType  # 变化类型
    confidence: float                 # 映射置信度
    migration_suggestion: str         # 迁移建议


@dataclass
class CompatibilityAnalysisResult:
    """兼容性分析结果"""
    overall_compatibility: float      # 总体兼容性评分
    business_compatibility: float     # 业务参数兼容性
    framework_compatibility: float    # 框架参数兼容性
    parameter_mappings: List[ParameterMapping]  # 参数映射关系
    migration_complexity: str         # 迁移复杂度（简单/中等/复杂）
    migration_suggestions: List[str]  # 迁移建议
    framework_params_added: List[str] # 新增的框架参数
    business_params_changed: List[str] # 变化的业务参数


class ParameterCompatibilityAnalyzer:
    """参数兼容性分析器"""
    
    # 常见框架参数白名单
    FRAMEWORK_PARAM_WHITELIST = {
        'tenantid', 'projectid', 'sessionid', 'requestid', 'contextid',
        'traceid', 'spanid', 'correlationid', 'transactionid', 'clientid',
        'appid', 'version', 'timestamp', 'locale', 'timezone', 'token',
        'authorization', 'apikey', 'signature', 'nonce', 'context',
        'session', 'principal', 'authentication'
    }
    
    # 常见类型映射
    TYPE_MAPPINGS = {
        ('Long', 'String'): 0.8,      # ID类型转换
        ('Integer', 'Long'): 0.9,     # 数值类型扩展
        ('String', 'Long'): 0.7,      # 字符串转数值
        ('Date', 'LocalDateTime'): 0.9,  # 时间类型升级
        ('BigDecimal', 'Double'): 0.8,   # 金额类型转换
    }
    
    # DTO封装模式
    DTO_PATTERNS = {
        'Request', 'Req', 'DTO', 'Param', 'Input', 'Command', 'Query'
    }
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def analyze_compatibility(self, old_method: MethodInfo, 
                            new_method: MethodInfo) -> CompatibilityAnalysisResult:
        """
        分析新旧方法的参数兼容性
        
        Args:
            old_method: 旧方法信息
            new_method: 新方法信息
            
        Returns:
            兼容性分析结果
        """
        self.logger.debug(f"分析参数兼容性: {old_method.method_name} -> {new_method.method_name}")
        
        # 解析参数
        old_params = self._parse_parameters(old_method.parameters or [])
        new_params = self._parse_parameters(new_method.parameters or [])
        
        # 分离框架参数和业务参数
        old_business, old_framework = self._separate_parameters(old_params)
        new_business, new_framework = self._separate_parameters(new_params)
        
        # 分析参数映射
        parameter_mappings = self._analyze_parameter_mappings(old_params, new_params)
        
        # 计算兼容性评分
        business_compatibility = self._calculate_business_compatibility(old_business, new_business)
        framework_compatibility = self._calculate_framework_compatibility(old_framework, new_framework)
        overall_compatibility = self._calculate_overall_compatibility(
            business_compatibility, framework_compatibility, len(old_params), len(new_params)
        )
        
        # 分析迁移复杂度
        migration_complexity = self._analyze_migration_complexity(parameter_mappings)
        
        # 生成迁移建议
        migration_suggestions = self._generate_migration_suggestions(parameter_mappings, old_method, new_method)
        
        # 识别新增的框架参数
        framework_params_added = self._identify_added_framework_params(old_framework, new_framework)
        
        # 识别变化的业务参数
        business_params_changed = self._identify_changed_business_params(parameter_mappings)
        
        return CompatibilityAnalysisResult(
            overall_compatibility=overall_compatibility,
            business_compatibility=business_compatibility,
            framework_compatibility=framework_compatibility,
            parameter_mappings=parameter_mappings,
            migration_complexity=migration_complexity,
            migration_suggestions=migration_suggestions,
            framework_params_added=framework_params_added,
            business_params_changed=business_params_changed
        )
    
    def _parse_parameters(self, parameters: List[str]) -> List[Tuple[str, str]]:
        """解析参数列表为(名称, 类型)元组列表"""
        parsed = []
        
        for param in parameters:
            if ':' in param:
                name, type_str = param.split(':', 1)
                name = name.strip()
                type_str = type_str.strip()
                # 简化类型名（去掉包名）
                simple_type = type_str.split('.')[-1]
                parsed.append((name, simple_type))
            else:
                # 只有类型，没有参数名
                simple_type = param.strip().split('.')[-1]
                parsed.append(("", simple_type))
        
        return parsed
    
    def _separate_parameters(self, params: List[Tuple[str, str]]) -> Tuple[List[Tuple[str, str]], List[Tuple[str, str]]]:
        """分离业务参数和框架参数"""
        business_params = []
        framework_params = []
        
        for name, type_str in params:
            is_framework = self._is_framework_parameter(name, type_str)
            
            if is_framework:
                framework_params.append((name, type_str))
            else:
                business_params.append((name, type_str))
        
        return business_params, framework_params
    
    def _is_framework_parameter(self, name: str, type_str: str) -> bool:
        """判断是否为框架参数"""
        if not name:
            # 基于类型判断
            type_lower = type_str.lower()
            return any(pattern in type_lower for pattern in ['context', 'httpservletrequest', 'httpservletresponse', 'session'])
        
        name_lower = name.lower()
        
        # 检查参数名白名单
        if name_lower in self.FRAMEWORK_PARAM_WHITELIST:
            return True
        
        # 特殊处理：userId通常是业务参数
        if name_lower == 'userid':
            return False
        
        # 特殊处理：request/response参数需要结合类型判断
        if name_lower in ['request', 'response']:
            type_lower = type_str.lower()
            # 只有标准的Servlet类型才是框架参数
            framework_request_types = [
                'httpservletrequest', 'httpservletresponse', 'requestcontext',
                'servletrequest', 'servletresponse'
            ]
            return any(pattern in type_lower for pattern in framework_request_types)
        
        # 检查类型模式 - 更精确的框架类型检测
        type_lower = type_str.lower()
        framework_type_patterns = [
            'httpservletrequest', 'httpservletresponse', 'requestcontext', 
            'securitycontext', 'principal', 'authentication', 'session'
        ]
        
        return any(pattern in type_lower for pattern in framework_type_patterns)
    
    def _analyze_parameter_mappings(self, old_params: List[Tuple[str, str]], 
                                  new_params: List[Tuple[str, str]]) -> List[ParameterMapping]:
        """分析参数映射关系"""
        mappings = []
        used_new_params = set()
        
        # 首先检查是否有DTO封装模式
        dto_mappings = self._detect_dto_encapsulation(old_params, new_params)
        if dto_mappings:
            mappings.extend(dto_mappings)
            # 标记已使用的新参数
            for mapping in dto_mappings:
                if mapping.new_param:
                    for i, (new_name, new_type) in enumerate(new_params):
                        if f"{new_name}: {new_type}" == mapping.new_param:
                            used_new_params.add(i)
                            break
        else:
            # 常规的一对一参数匹配
            for old_name, old_type in old_params:
                best_match = None
                best_confidence = 0.0
                
                for i, (new_name, new_type) in enumerate(new_params):
                    if i in used_new_params:
                        continue
                    
                    confidence = self._calculate_parameter_similarity(
                        (old_name, old_type), (new_name, new_type)
                    )
                    
                    if confidence > best_confidence and confidence > 0.5:  # 提高阈值
                        best_confidence = confidence
                        best_match = i
                
                if best_match is not None:
                    new_name, new_type = new_params[best_match]
                    used_new_params.add(best_match)
                    
                    # 确定变化类型
                    change_type = self._determine_change_type(
                        (old_name, old_type), (new_name, new_type)
                    )
                    
                    # 生成迁移建议
                    migration_suggestion = self._generate_parameter_migration_suggestion(
                        (old_name, old_type), (new_name, new_type), change_type
                    )
                    
                    mappings.append(ParameterMapping(
                        old_param=f"{old_name}: {old_type}",
                        new_param=f"{new_name}: {new_type}",
                        change_type=change_type,
                        confidence=best_confidence,
                        migration_suggestion=migration_suggestion
                    ))
                else:
                    # 没有找到匹配的参数
                    mappings.append(ParameterMapping(
                        old_param=f"{old_name}: {old_type}",
                        new_param=None,
                        change_type=ParameterChangeType.REMOVED,
                        confidence=0.0,
                        migration_suggestion=f"参数 {old_name} 已被移除，需要检查是否有替代方案"
                    ))
        
        # 处理新增的参数
        for i, (new_name, new_type) in enumerate(new_params):
            if i not in used_new_params:
                change_type = (ParameterChangeType.FRAMEWORK_ADDED 
                             if self._is_framework_parameter(new_name, new_type) 
                             else ParameterChangeType.BUSINESS_CHANGED)
                
                migration_suggestion = self._generate_new_parameter_suggestion(new_name, new_type, change_type)
                
                mappings.append(ParameterMapping(
                    old_param="",
                    new_param=f"{new_name}: {new_type}",
                    change_type=change_type,
                    confidence=1.0,
                    migration_suggestion=migration_suggestion
                ))
        
        return mappings
    
    def _detect_dto_encapsulation(self, old_params: List[Tuple[str, str]], 
                                new_params: List[Tuple[str, str]]) -> List[ParameterMapping]:
        """检测DTO封装模式：多个旧参数封装到一个新DTO中"""
        mappings = []
        
        # 分离新参数中的业务参数和框架参数
        new_business_params = []
        new_framework_params = []
        
        for new_name, new_type in new_params:
            if self._is_framework_parameter(new_name, new_type):
                new_framework_params.append((new_name, new_type))
            else:
                new_business_params.append((new_name, new_type))
        
        # 检查是否有DTO封装模式：
        # 1. 有多个旧的业务参数
        # 2. 只有一个新的业务参数，且类型看起来像DTO
        old_business_params = [
            (name, type_) for name, type_ in old_params 
            if not self._is_framework_parameter(name, type_)
        ]
        
        if (len(old_business_params) >= 2 and 
            len(new_business_params) == 1 and 
            self._is_dto_encapsulation("", new_business_params[0][1])):
            
            # 这是DTO封装模式
            dto_name, dto_type = new_business_params[0]
            
            # 为每个旧业务参数创建到DTO的映射
            for old_name, old_type in old_business_params:
                mappings.append(ParameterMapping(
                    old_param=f"{old_name}: {old_type}",
                    new_param=f"{dto_name}: {dto_type}",
                    change_type=ParameterChangeType.ENCAPSULATED,
                    confidence=0.8,
                    migration_suggestion=f"将参数 {old_name} 封装到 {dto_type} 对象的相应字段中"
                ))
            
            return mappings
        
        return []  # 没有检测到DTO封装模式
    
    def _calculate_parameter_similarity(self, old_param: Tuple[str, str], 
                                      new_param: Tuple[str, str]) -> float:
        """计算参数相似度"""
        old_name, old_type = old_param
        new_name, new_type = new_param
        
        # 如果一个是框架参数，另一个不是，相似度很低
        old_is_framework = self._is_framework_parameter(old_name, old_type)
        new_is_framework = self._is_framework_parameter(new_name, new_type)
        
        if old_is_framework != new_is_framework:
            return 0.1  # 框架参数和业务参数之间相似度很低
        
        similarity = 0.0
        
        # 名称相似度（权重60%）
        if old_name and new_name:
            name_similarity = self._calculate_name_similarity(old_name, new_name)
            similarity += name_similarity * 0.6
        
        # 类型相似度（权重40%）
        type_similarity = self._calculate_type_similarity(old_type, new_type)
        similarity += type_similarity * 0.4
        
        return similarity
    
    def _calculate_name_similarity(self, name1: str, name2: str) -> float:
        """计算参数名相似度"""
        if name1 == name2:
            return 1.0
        
        name1_lower = name1.lower()
        name2_lower = name2.lower()
        
        # 完全匹配
        if name1_lower == name2_lower:
            return 1.0
        
        # 包含关系 - 更严格的匹配
        if name1_lower in name2_lower or name2_lower in name1_lower:
            # 计算长度比例，避免"energyType"和"deviceType"这种情况被认为高度相似
            shorter = min(name1_lower, name2_lower, key=len)
            longer = max(name1_lower, name2_lower, key=len)
            length_ratio = len(shorter) / len(longer)
            
            # 只有当长度比例较高时才认为是真正的包含关系
            if length_ratio >= 0.7:
                return 0.8
            else:
                # 只是部分包含（如"Type"在两个不同的参数名中），给较低的分数
                return 0.3
        
        # 编辑距离相似度
        edit_distance = self._calculate_edit_distance(name1_lower, name2_lower)
        max_len = max(len(name1_lower), len(name2_lower))
        
        if max_len == 0:
            return 0.0
        
        return max(0.0, 1.0 - edit_distance / max_len)
    
    def _calculate_type_similarity(self, type1: str, type2: str) -> float:
        """计算类型相似度"""
        if type1 == type2:
            return 1.0
        
        # 检查预定义的类型映射
        type_pair = (type1, type2)
        if type_pair in self.TYPE_MAPPINGS:
            return self.TYPE_MAPPINGS[type_pair]
        
        # 反向检查
        reverse_pair = (type2, type1)
        if reverse_pair in self.TYPE_MAPPINGS:
            return self.TYPE_MAPPINGS[reverse_pair]
        
        # 基于类型名的相似度
        type1_lower = type1.lower()
        type2_lower = type2.lower()
        
        if type1_lower == type2_lower:
            return 1.0
        
        # 检查是否都是数值类型
        numeric_types = {'int', 'integer', 'long', 'double', 'float', 'bigdecimal', 'number'}
        if type1_lower in numeric_types and type2_lower in numeric_types:
            return 0.7
        
        # 检查是否都是字符串类型
        string_types = {'string', 'str', 'text', 'varchar', 'char'}
        if type1_lower in string_types and type2_lower in string_types:
            return 0.9
        
        # 检查是否都是时间类型
        time_types = {'date', 'datetime', 'localdatetime', 'timestamp', 'time'}
        if type1_lower in time_types and type2_lower in time_types:
            return 0.8
        
        return 0.0
    
    def _calculate_edit_distance(self, s1: str, s2: str) -> int:
        """计算编辑距离"""
        if len(s1) < len(s2):
            return self._calculate_edit_distance(s2, s1)
        
        if len(s2) == 0:
            return len(s1)
        
        previous_row = list(range(len(s2) + 1))
        for i, c1 in enumerate(s1):
            current_row = [i + 1]
            for j, c2 in enumerate(s2):
                insertions = previous_row[j + 1] + 1
                deletions = current_row[j] + 1
                substitutions = previous_row[j] + (c1 != c2)
                current_row.append(min(insertions, deletions, substitutions))
            previous_row = current_row
        
        return previous_row[-1]
    
    def _determine_change_type(self, old_param: Tuple[str, str], 
                             new_param: Tuple[str, str]) -> ParameterChangeType:
        """确定参数变化类型"""
        old_name, old_type = old_param
        new_name, new_type = new_param
        
        # 完全相同
        if old_name == new_name and old_type == new_type:
            return ParameterChangeType.IDENTICAL
        
        # 类型变化（相同名称，不同类型）
        if old_name == new_name and old_type != new_type:
            return ParameterChangeType.TYPE_CHANGED
        
        # 检查是否为DTO封装（不同名称，且新类型是DTO）
        if old_name != new_name and self._is_dto_encapsulation(old_type, new_type):
            return ParameterChangeType.ENCAPSULATED
        
        # 检查是否为框架参数新增（新参数是框架参数，旧参数不是）
        if self._is_framework_parameter(new_name, new_type) and not self._is_framework_parameter(old_name, old_type):
            return ParameterChangeType.FRAMEWORK_ADDED
        
        # 业务参数变化
        return ParameterChangeType.BUSINESS_CHANGED
    
    def _is_dto_encapsulation(self, old_type: str, new_type: str) -> bool:
        """检查是否为DTO封装模式"""
        return any(pattern in new_type for pattern in self.DTO_PATTERNS)
    
    def _calculate_business_compatibility(self, old_business: List[Tuple[str, str]], 
                                        new_business: List[Tuple[str, str]]) -> float:
        """计算业务参数兼容性"""
        if not old_business:
            return 1.0 if not new_business else 0.8
        
        if not new_business:
            return 0.3  # 业务参数全部移除，兼容性较低
        
        # 检查是否有DTO封装模式
        if (len(old_business) >= 2 and 
            len(new_business) == 1 and 
            self._is_dto_encapsulation("", new_business[0][1])):
            # DTO封装模式：多个旧参数封装到一个新DTO中
            # 这种情况下兼容性应该是中等的，因为功能保留但结构改变
            return 0.6
        
        # 计算业务参数的加权匹配度
        total_similarity = 0.0
        for old_name, old_type in old_business:
            best_similarity = 0.0
            for new_name, new_type in new_business:
                similarity = self._calculate_parameter_similarity((old_name, old_type), (new_name, new_type))
                best_similarity = max(best_similarity, similarity)
            
            # 只有相似度超过阈值才计入匹配
            if best_similarity > 0.5:
                total_similarity += best_similarity
            # 如果没有找到匹配，这个参数的贡献为0
        
        return total_similarity / len(old_business)
    
    def _calculate_framework_compatibility(self, old_framework: List[Tuple[str, str]], 
                                         new_framework: List[Tuple[str, str]]) -> float:
        """计算框架参数兼容性"""
        # 框架参数的新增不影响兼容性评分
        return 1.0
    
    def _calculate_overall_compatibility(self, business_compatibility: float, 
                                       framework_compatibility: float,
                                       old_param_count: int, new_param_count: int) -> float:
        """计算总体兼容性"""
        # 业务参数兼容性权重更高
        base_compatibility = business_compatibility * 0.8 + framework_compatibility * 0.2
        
        # 参数数量变化的影响
        param_count_factor = 1.0
        if old_param_count > 0:
            count_ratio = new_param_count / old_param_count
            if count_ratio > 2.0:  # 参数数量大幅增加
                param_count_factor = 0.9
            elif count_ratio < 0.5:  # 参数数量大幅减少
                param_count_factor = 0.8
        
        return base_compatibility * param_count_factor
    
    def _analyze_migration_complexity(self, mappings: List[ParameterMapping]) -> str:
        """分析迁移复杂度"""
        complex_changes = 0
        total_changes = len(mappings)
        
        for mapping in mappings:
            if mapping.change_type in [ParameterChangeType.ENCAPSULATED, 
                                     ParameterChangeType.BUSINESS_CHANGED,
                                     ParameterChangeType.REMOVED]:
                complex_changes += 1
        
        if total_changes == 0:
            return "简单"
        
        complexity_ratio = complex_changes / total_changes
        
        if complexity_ratio < 0.3:
            return "简单"
        elif complexity_ratio < 0.7:
            return "中等"
        else:
            return "复杂"
    
    def _generate_migration_suggestions(self, mappings: List[ParameterMapping], 
                                      old_method: MethodInfo, new_method: MethodInfo) -> List[str]:
        """生成迁移建议"""
        suggestions = []
        
        # 方法级别的建议
        suggestions.append(f"将方法调用从 {old_method.method_name} 更改为 {new_method.method_name}")
        
        # 参数级别的建议
        framework_params_added = []
        business_params_changed = []
        
        for mapping in mappings:
            if mapping.change_type == ParameterChangeType.FRAMEWORK_ADDED:
                param_name = mapping.new_param.split(':')[0].strip() if mapping.new_param else ""
                framework_params_added.append(param_name)
            elif mapping.change_type in [ParameterChangeType.BUSINESS_CHANGED, 
                                       ParameterChangeType.TYPE_CHANGED,
                                       ParameterChangeType.ENCAPSULATED]:
                business_params_changed.append(mapping.migration_suggestion)
        
        # 框架参数建议
        if framework_params_added:
            suggestions.append(f"添加框架参数: {', '.join(framework_params_added)}")
            suggestions.append("框架参数通常可以从上下文中获取，如 SecurityContextHolder.getContext()")
        
        # 业务参数建议
        if business_params_changed:
            suggestions.extend(business_params_changed)
        
        return suggestions
    
    def _generate_parameter_migration_suggestion(self, old_param: Tuple[str, str], 
                                               new_param: Tuple[str, str],
                                               change_type: ParameterChangeType) -> str:
        """生成参数迁移建议"""
        old_name, old_type = old_param
        new_name, new_type = new_param
        
        if change_type == ParameterChangeType.IDENTICAL:
            return f"参数 {old_name} 保持不变"
        elif change_type == ParameterChangeType.TYPE_CHANGED:
            return f"将参数 {old_name} 的类型从 {old_type} 转换为 {new_type}"
        elif change_type == ParameterChangeType.ENCAPSULATED:
            return f"将参数 {old_name} 封装到 {new_type} 对象中"
        elif change_type == ParameterChangeType.BUSINESS_CHANGED:
            return f"将参数 {old_name} 映射到 {new_name}"
        else:
            return f"处理参数变化: {old_name} -> {new_name}"
    
    def _generate_new_parameter_suggestion(self, name: str, type_str: str, 
                                         change_type: ParameterChangeType) -> str:
        """生成新参数的建议"""
        if change_type == ParameterChangeType.FRAMEWORK_ADDED:
            return f"添加框架参数 {name}，通常可以从上下文获取"
        else:
            return f"添加新的业务参数 {name}: {type_str}"
    
    def _identify_added_framework_params(self, old_framework: List[Tuple[str, str]], 
                                       new_framework: List[Tuple[str, str]]) -> List[str]:
        """识别新增的框架参数"""
        old_names = {name for name, _ in old_framework}
        new_names = {name for name, _ in new_framework}
        
        added_names = new_names - old_names
        return list(added_names)
    
    def _identify_changed_business_params(self, mappings: List[ParameterMapping]) -> List[str]:
        """识别变化的业务参数"""
        changed_params = []
        
        for mapping in mappings:
            if mapping.change_type in [ParameterChangeType.BUSINESS_CHANGED,
                                     ParameterChangeType.TYPE_CHANGED,
                                     ParameterChangeType.ENCAPSULATED,
                                     ParameterChangeType.REMOVED]:
                if mapping.old_param:
                    param_name = mapping.old_param.split(':')[0].strip()
                    changed_params.append(param_name)
        
        return changed_params