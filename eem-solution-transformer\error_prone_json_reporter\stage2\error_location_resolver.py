"""
错误位置解析器

根据JSON信息定位错误在源码中的具体位置，并提取相关上下文。
"""

import os
import logging
import re
from typing import Optional, Dict, Any, List, Tuple
from dataclasses import dataclass
from pathlib import Path

from error_prone_json_reporter.common.models import Configuration


@dataclass
class ErrorLocation:
    """错误位置信息"""
    file_path: str
    line_number: int
    column_number: Optional[int]
    method_name: Optional[str]
    class_name: Optional[str]
    surrounding_lines: List[str]
    absolute_file_path: Optional[str] = None


@dataclass
class MethodContext:
    """方法上下文信息"""
    method_definition: str
    method_body: str
    calling_context: List[str]
    local_variables: List[str]
    method_calls: List[str]
    imports: List[str]
    class_context: Optional[str] = None


class ErrorLocationResolver:
    """
    错误位置解析器
    
    职责：根据JSON信息定位错误在源码中的具体位置
    输入：错误JSON信息
    输出：源码位置和上下文
    """
    
    def __init__(self, config: Configuration):
        """
        初始化错误位置解析器
        
        Args:
            config: 配置对象
        """
        self.logger = logging.getLogger(__name__)
        self.config = config
        
        # 项目根路径
        self.project_root = getattr(config, 'project_path', os.getcwd())
        
        # 源码搜索路径
        self.source_paths = self._get_source_paths()
        
        # 缓存已解析的文件
        self.file_cache = {}
        
        self.logger.info(f"错误位置解析器初始化完成，项目根路径: {self.project_root}")
        self.logger.debug(f"源码搜索路径: {self.source_paths}")
    
    def _get_source_paths(self) -> List[str]:
        """获取源码搜索路径"""
        paths = []
        
        # 添加项目根路径
        if os.path.exists(self.project_root):
            paths.append(self.project_root)
        
        # 添加常见的Java源码路径
        common_java_paths = [
            'src/main/java',
            'src/test/java',
            'src/java',
            'java'
        ]
        
        for common_path in common_java_paths:
            full_path = os.path.join(self.project_root, common_path)
            if os.path.exists(full_path):
                paths.append(full_path)
        
        return paths
    
    def resolve_error_location(self, error_json: Dict[str, Any]) -> Optional[ErrorLocation]:
        """
        解析错误位置
        
        Args:
            error_json: 错误JSON信息
            
        Returns:
            错误位置信息，如果无法解析则返回None
        """
        try:
            self.logger.debug(f"解析错误位置: {error_json.get('class', '')}.{error_json.get('missing_method', '')}")
            
            # 从JSON中提取位置信息
            location_info = error_json.get('location', {})
            if not location_info:
                self.logger.warning("错误JSON中缺少location信息")
                return self._fallback_location_resolution(error_json)
            
            file_path = location_info.get('file', '')
            line_number = location_info.get('line', 0)
            column_number = location_info.get('column')
            
            if not file_path or line_number <= 0:
                self.logger.warning(f"无效的位置信息: file={file_path}, line={line_number}")
                return self._fallback_location_resolution(error_json)
            
            # 查找实际文件路径
            absolute_file_path = self._find_actual_file_path(file_path)
            if not absolute_file_path:
                self.logger.warning(f"无法找到文件: {file_path}")
                return self._fallback_location_resolution(error_json)
            
            # 读取文件内容并提取周围行
            surrounding_lines = self._extract_surrounding_lines(absolute_file_path, line_number)
            
            # 提取方法和类名
            method_name = error_json.get('missing_method', '')
            class_name = error_json.get('class', '')
            
            error_location = ErrorLocation(
                file_path=file_path,
                line_number=line_number,
                column_number=column_number,
                method_name=method_name,
                class_name=class_name,
                surrounding_lines=surrounding_lines,
                absolute_file_path=absolute_file_path
            )
            
            self.logger.debug(f"成功解析错误位置: {absolute_file_path}:{line_number}")
            return error_location
            
        except Exception as e:
            self.logger.error(f"解析错误位置失败: {str(e)}")
            return self._fallback_location_resolution(error_json)
    
    def _find_actual_file_path(self, relative_path: str) -> Optional[str]:
        """
        查找实际文件路径
        
        Args:
            relative_path: 相对路径
            
        Returns:
            绝对文件路径，如果找不到则返回None
        """
        # 清理路径
        clean_path = relative_path.lstrip('/')
        
        # 在各个源码路径中搜索
        for source_path in self.source_paths:
            # 直接拼接路径
            candidate_path = os.path.join(source_path, clean_path)
            if os.path.exists(candidate_path):
                return os.path.abspath(candidate_path)
            
            # 尝试去掉src/main/java前缀
            if clean_path.startswith('src/main/java/'):
                short_path = clean_path[14:]  # 去掉'src/main/java/'
                candidate_path = os.path.join(source_path, short_path)
                if os.path.exists(candidate_path):
                    return os.path.abspath(candidate_path)
            
            # 尝试添加src/main/java前缀
            if not clean_path.startswith('src/'):
                prefixed_path = os.path.join('src/main/java', clean_path)
                candidate_path = os.path.join(source_path, prefixed_path)
                if os.path.exists(candidate_path):
                    return os.path.abspath(candidate_path)
        
        # 使用包名和类名进行模糊搜索
        return self._fuzzy_search_file(relative_path)
    
    def _fuzzy_search_file(self, relative_path: str) -> Optional[str]:
        """
        模糊搜索文件
        
        Args:
            relative_path: 相对路径
            
        Returns:
            找到的文件路径
        """
        try:
            # 提取文件名
            file_name = os.path.basename(relative_path)
            
            # 在所有源码路径中递归搜索
            for source_path in self.source_paths:
                for root, dirs, files in os.walk(source_path):
                    if file_name in files:
                        candidate_path = os.path.join(root, file_name)
                        self.logger.debug(f"模糊搜索找到文件: {candidate_path}")
                        return os.path.abspath(candidate_path)
            
            return None
            
        except Exception as e:
            self.logger.warning(f"模糊搜索文件失败: {str(e)}")
            return None
    
    def _extract_surrounding_lines(self, file_path: str, line_number: int, context_lines: int = 5) -> List[str]:
        """
        提取指定行周围的代码行
        
        Args:
            file_path: 文件路径
            line_number: 行号（从1开始）
            context_lines: 上下文行数
            
        Returns:
            周围的代码行列表
        """
        try:
            # 检查缓存
            cache_key = f"{file_path}:{line_number}:{context_lines}"
            if cache_key in self.file_cache:
                return self.file_cache[cache_key]
            
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
            
            # 计算提取范围
            start_line = max(0, line_number - context_lines - 1)
            end_line = min(len(lines), line_number + context_lines)
            
            # 提取周围行
            surrounding_lines = []
            for i in range(start_line, end_line):
                line_content = lines[i].rstrip('\n\r')
                line_info = f"{i + 1:4d}: {line_content}"
                
                # 标记目标行
                if i + 1 == line_number:
                    line_info = f">>> {line_info}"
                
                surrounding_lines.append(line_info)
            
            # 缓存结果
            self.file_cache[cache_key] = surrounding_lines
            
            return surrounding_lines
            
        except Exception as e:
            self.logger.warning(f"提取周围行失败 {file_path}:{line_number}: {str(e)}")
            return [f"无法读取文件 {file_path} 第 {line_number} 行"]
    
    def extract_method_context(self, file_path: str, line_number: int) -> Optional[MethodContext]:
        """
        提取方法上下文
        
        Args:
            file_path: 文件路径
            line_number: 行号
            
        Returns:
            方法上下文信息
        """
        try:
            self.logger.debug(f"提取方法上下文: {file_path}:{line_number}")
            
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
            
            if line_number <= 0 or line_number > len(lines):
                self.logger.warning(f"行号超出范围: {line_number}")
                return None
            
            # 查找包含该行的方法
            method_start, method_end = self._find_method_boundaries(lines, line_number - 1)
            
            if method_start is None or method_end is None:
                self.logger.warning(f"无法找到包含第{line_number}行的方法")
                return None
            
            # 提取方法定义
            method_definition = self._extract_method_definition(lines, method_start)
            
            # 提取方法体
            method_body = ''.join(lines[method_start:method_end + 1])
            
            # 提取调用上下文（方法调用前后的代码）
            calling_context = self._extract_calling_context(lines, line_number - 1)
            
            # 提取局部变量
            local_variables = self._extract_local_variables(lines[method_start:method_end + 1])
            
            # 提取方法调用
            method_calls = self._extract_method_calls(lines[method_start:method_end + 1])
            
            # 提取导入语句
            imports = self._extract_imports(lines)
            
            # 提取类上下文
            class_context = self._extract_class_context(lines, method_start)
            
            method_context = MethodContext(
                method_definition=method_definition,
                method_body=method_body,
                calling_context=calling_context,
                local_variables=local_variables,
                method_calls=method_calls,
                imports=imports,
                class_context=class_context
            )
            
            self.logger.debug(f"成功提取方法上下文，方法定义: {method_definition[:100]}...")
            return method_context
            
        except Exception as e:
            self.logger.error(f"提取方法上下文失败: {str(e)}")
            return None
    
    def _find_method_boundaries(self, lines: List[str], target_line: int) -> Tuple[Optional[int], Optional[int]]:
        """
        查找包含目标行的方法边界
        
        Args:
            lines: 文件行列表
            target_line: 目标行索引（从0开始）
            
        Returns:
            (方法开始行索引, 方法结束行索引)
        """
        try:
            # 向上查找方法开始
            method_start = None
            for i in range(target_line, -1, -1):
                line = lines[i].strip()
                
                # 查找方法定义模式
                if self._is_method_definition(line):
                    method_start = i
                    break
                
                # 如果遇到类定义或其他方法，停止搜索
                if self._is_class_definition(line) or (self._is_method_definition(line) and i < target_line):
                    break
            
            if method_start is None:
                return None, None
            
            # 向下查找方法结束
            method_end = None
            brace_count = 0
            in_method = False
            
            for i in range(method_start, len(lines)):
                line = lines[i].strip()
                
                # 计算大括号
                open_braces = line.count('{')
                close_braces = line.count('}')
                
                if open_braces > 0:
                    in_method = True
                
                brace_count += open_braces - close_braces
                
                # 如果大括号平衡且已经进入方法，则找到方法结束
                if in_method and brace_count == 0:
                    method_end = i
                    break
            
            return method_start, method_end
            
        except Exception as e:
            self.logger.warning(f"查找方法边界失败: {str(e)}")
            return None, None
    
    def _is_method_definition(self, line: str) -> bool:
        """判断是否为方法定义行"""
        # 简单的方法定义模式匹配
        method_patterns = [
            r'\b(public|private|protected|static|final|abstract|synchronized)\s+.*\s+\w+\s*\(',
            r'\b\w+\s+\w+\s*\(',  # 简单方法定义
            r'@\w+.*\n.*\s+\w+\s*\('  # 带注解的方法
        ]
        
        for pattern in method_patterns:
            if re.search(pattern, line):
                return True
        
        return False
    
    def _is_class_definition(self, line: str) -> bool:
        """判断是否为类定义行"""
        class_patterns = [
            r'\b(public|private|protected)?\s*(class|interface|enum)\s+\w+',
            r'@\w+.*\n.*(class|interface|enum)\s+\w+'
        ]
        
        for pattern in class_patterns:
            if re.search(pattern, line):
                return True
        
        return False
    
    def _extract_method_definition(self, lines: List[str], method_start: int) -> str:
        """提取方法定义"""
        try:
            # 查找方法定义的完整行（可能跨多行）
            definition_lines = []
            
            for i in range(method_start, len(lines)):
                line = lines[i].strip()
                definition_lines.append(line)
                
                # 如果找到开括号，方法定义结束
                if '{' in line:
                    break
                
                # 防止无限循环
                if i - method_start > 10:
                    break
            
            return ' '.join(definition_lines)
            
        except Exception as e:
            self.logger.warning(f"提取方法定义失败: {str(e)}")
            return ""
    
    def _extract_calling_context(self, lines: List[str], target_line: int, context_size: int = 3) -> List[str]:
        """提取调用上下文"""
        try:
            start = max(0, target_line - context_size)
            end = min(len(lines), target_line + context_size + 1)
            
            context = []
            for i in range(start, end):
                line_content = lines[i].rstrip('\n\r')
                if i == target_line:
                    context.append(f">>> {i + 1}: {line_content}")
                else:
                    context.append(f"    {i + 1}: {line_content}")
            
            return context
            
        except Exception as e:
            self.logger.warning(f"提取调用上下文失败: {str(e)}")
            return []
    
    def _extract_local_variables(self, method_lines: List[str]) -> List[str]:
        """提取局部变量"""
        try:
            variables = []
            
            for line in method_lines:
                line = line.strip()
                
                # 简单的变量声明模式
                var_patterns = [
                    r'\b(int|long|double|float|boolean|String|List|Map|Set)\s+(\w+)',
                    r'\b(\w+)\s+(\w+)\s*=',
                    r'\bfinal\s+(\w+)\s+(\w+)'
                ]
                
                for pattern in var_patterns:
                    matches = re.findall(pattern, line)
                    for match in matches:
                        if isinstance(match, tuple) and len(match) >= 2:
                            var_type, var_name = match[0], match[1]
                            variables.append(f"{var_type} {var_name}")
            
            return list(set(variables))  # 去重
            
        except Exception as e:
            self.logger.warning(f"提取局部变量失败: {str(e)}")
            return []
    
    def _extract_method_calls(self, method_lines: List[str]) -> List[str]:
        """提取方法调用"""
        try:
            method_calls = []
            
            for line in method_lines:
                line = line.strip()
                
                # 方法调用模式
                call_patterns = [
                    r'(\w+)\.(\w+)\s*\(',
                    r'(\w+)\s*\(',
                    r'this\.(\w+)\s*\(',
                    r'super\.(\w+)\s*\('
                ]
                
                for pattern in call_patterns:
                    matches = re.findall(pattern, line)
                    for match in matches:
                        if isinstance(match, tuple):
                            if len(match) == 2:
                                method_calls.append(f"{match[0]}.{match[1]}()")
                            else:
                                method_calls.append(f"{match[0]}()")
                        else:
                            method_calls.append(f"{match}()")
            
            return list(set(method_calls))  # 去重
            
        except Exception as e:
            self.logger.warning(f"提取方法调用失败: {str(e)}")
            return []
    
    def _extract_imports(self, lines: List[str]) -> List[str]:
        """提取导入语句"""
        try:
            imports = []
            
            for line in lines:
                line = line.strip()
                if line.startswith('import ') and line.endswith(';'):
                    imports.append(line)
            
            return imports
            
        except Exception as e:
            self.logger.warning(f"提取导入语句失败: {str(e)}")
            return []
    
    def _extract_class_context(self, lines: List[str], method_start: int) -> Optional[str]:
        """提取类上下文"""
        try:
            # 向上查找类定义
            for i in range(method_start, -1, -1):
                line = lines[i].strip()
                if self._is_class_definition(line):
                    # 提取类定义及其前几行（可能包含注解）
                    context_start = max(0, i - 3)
                    context_lines = lines[context_start:i + 1]
                    return ''.join(context_lines)
            
            return None
            
        except Exception as e:
            self.logger.warning(f"提取类上下文失败: {str(e)}")
            return None
    
    def _fallback_location_resolution(self, error_json: Dict[str, Any]) -> Optional[ErrorLocation]:
        """
        回退位置解析策略
        
        Args:
            error_json: 错误JSON信息
            
        Returns:
            基于其他信息构建的错误位置
        """
        try:
            self.logger.info("使用回退策略解析错误位置")
            
            # 尝试从包名和类名构建文件路径
            package = error_json.get('package', '')
            class_name = error_json.get('class', '')
            method_name = error_json.get('missing_method', '')
            
            if package and class_name:
                # 构建可能的文件路径
                package_path = package.replace('.', '/')
                file_name = f"{class_name}.java"
                relative_path = f"{package_path}/{file_name}"
                
                # 查找文件
                absolute_path = self._find_actual_file_path(relative_path)
                
                if absolute_path:
                    # 尝试在文件中查找方法
                    line_number = self._find_method_in_file(absolute_path, method_name)
                    
                    surrounding_lines = self._extract_surrounding_lines(absolute_path, line_number or 1)
                    
                    return ErrorLocation(
                        file_path=relative_path,
                        line_number=line_number or 1,
                        column_number=None,
                        method_name=method_name,
                        class_name=class_name,
                        surrounding_lines=surrounding_lines,
                        absolute_file_path=absolute_path
                    )
            
            return None
            
        except Exception as e:
            self.logger.warning(f"回退位置解析失败: {str(e)}")
            return None
    
    def _find_method_in_file(self, file_path: str, method_name: str) -> Optional[int]:
        """在文件中查找方法定义行号"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
            
            for i, line in enumerate(lines):
                if method_name in line and self._is_method_definition(line):
                    return i + 1
            
            return None
            
        except Exception as e:
            self.logger.warning(f"在文件中查找方法失败: {str(e)}")
            return None
    
    def clear_cache(self):
        """清理缓存"""
        self.file_cache.clear()
        self.logger.debug("错误位置解析器缓存已清理")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        return {
            'cache_size': len(self.file_cache),
            'source_paths_count': len(self.source_paths),
            'project_root': self.project_root
        }