"""
向量编码器

使用 GraphCodeBERT 模型将方法描述转换为向量表示。
"""

import os
import logging
import pickle
import hashlib
import re
from typing import List, Dict, Optional, Tuple, Any
from pathlib import Path
import numpy as np

try:
    import torch
    from transformers import AutoTokenizer, AutoModel
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    raise ImportError(
        "transformers和torch库是必需的依赖，请安装后再使用：\n"
        "pip install torch transformers\n"
        "或者使用conda: conda install pytorch transformers -c pytorch -c huggingface"
    )

from error_prone_json_reporter.common.interfaces import VectorEncoderInterface
from error_prone_json_reporter.common.models import MethodInfo
from error_prone_json_reporter.stage2.enhanced_method_features import EnhancedMethodFeatures, EnhancedMethodFeaturesExtractor


class VectorEncoder(VectorEncoderInterface):
    """
    向量编码器
    
    使用GraphCodeBERT模型将方法描述转换为向量表示。
    支持批量编码、缓存机制和降级策略。
    """
    
    def __init__(self, model_name: str = "microsoft/graphcodebert-base", 
                 cache_dir: Optional[str] = None, batch_size: int = 32,
                 max_length: int = 512, use_cache: bool = True):
        """
        初始化向量编码器
        
        Args:
            model_name: 预训练模型名称
            cache_dir: 缓存目录
            batch_size: 批处理大小
            max_length: 最大序列长度
            use_cache: 是否使用缓存
        """
        self.logger = logging.getLogger(__name__)
        self.model_name = model_name
        self.batch_size = batch_size
        self.max_length = max_length
        self.use_cache = use_cache
        
        # 设置缓存目录
        if cache_dir:
            self.cache_dir = Path(cache_dir)
        else:
            self.cache_dir = Path.home() / ".cache" / "error_prone_json_reporter" / "vectors"
        
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # 初始化特征提取器
        self.features_extractor = EnhancedMethodFeaturesExtractor()
        
        # 初始化模型和tokenizer
        self.tokenizer = None
        self.model = None
        self.device = None
        self.model_loaded = False
        
        # 统计信息
        self.stats = {
            'total_encoded': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'batch_count': 0,
            'encoding_time': 0.0,
            'enhanced_encodings': 0,
            'framework_params_filtered': 0
        }
        
        # 尝试加载模型
        self._load_model()
    
    def _load_model(self) -> bool:
        """
        加载GraphCodeBERT模型和tokenizer
        
        Returns:
            是否成功加载模型
        """
        # transformers现在是必需的依赖，在导入时已经检查
        
        try:
            self.logger.info(f"正在加载模型: {self.model_name}")
            
            # 检查是否为本地路径
            if os.path.exists(self.model_name):
                self.logger.info(f"检测到本地模型路径: {self.model_name}")
                # 验证本地模型文件是否存在
                config_file = os.path.join(self.model_name, "config.json")
                if not os.path.exists(config_file):
                    raise FileNotFoundError(f"本地模型目录缺少config.json文件: {config_file}")
                self.logger.info("本地模型文件验证通过")
            else:
                self.logger.info(f"使用Hugging Face Hub模型: {self.model_name}")
            
            # 检测设备
            if torch.cuda.is_available():
                self.device = torch.device("cuda")
                self.logger.info("使用GPU进行向量编码")
            else:
                self.device = torch.device("cpu")
                self.logger.info("使用CPU进行向量编码")
            
            # 加载tokenizer
            self.logger.info("正在加载tokenizer...")
            self.tokenizer = AutoTokenizer.from_pretrained(
                self.model_name,
                trust_remote_code=True,
                local_files_only=os.path.exists(self.model_name)
            )
            self.logger.info("Tokenizer加载成功")
            
            # 加载模型
            self.logger.info("正在加载模型...")
            self.model = AutoModel.from_pretrained(
                self.model_name,
                trust_remote_code=True,
                local_files_only=os.path.exists(self.model_name)
            )
            self.model.to(self.device)
            self.model.eval()
            
            self.model_loaded = True
            self.logger.info("模型加载成功")
            return True
            
        except Exception as e:
            self.logger.error(f"模型加载失败: {str(e)}")
            
            if os.path.exists(self.model_name):
                self.logger.error("本地模型加载失败，可能的解决方案:")
                self.logger.error(f"1. 检查模型目录是否完整: {self.model_name}")
                self.logger.error("2. 确保包含必要文件: config.json, pytorch_model.bin 或 model.safetensors")
                self.logger.error("3. 检查tokenizer相关文件: tokenizer.json, vocab.txt")
                self.logger.error("4. 验证模型文件是否损坏")
            else:
                self.logger.error("在线模型加载失败，可能的解决方案:")
                self.logger.error("1. 检查网络连接")
                self.logger.error("2. 使用代理或VPN")
                self.logger.error("3. 手动下载模型到本地")
                self.logger.error("4. 使用其他可用的预训练模型")
            
            return False
    
    def encode(self, texts: List[str]) -> np.ndarray:
        """
        将文本列表编码为向量矩阵
        
        Args:
            texts: 文本列表
            
        Returns:
            向量矩阵 (n_texts, embedding_dim)
        """
        if not texts:
            return np.array([])
        
        self.logger.info(f"开始编码 {len(texts)} 个文本")
        
        if not self.model_loaded:
            raise RuntimeError(
                "GraphCodeBERT模型加载失败，无法进行向量编码。\n"
                "请检查:\n"
                "1. 网络连接是否正常\n"
                "2. 是否需要使用代理\n"
                "3. 考虑使用本地模型或其他可用模型"
            )
        
        # 检查缓存
        vectors = []
        uncached_texts = []
        uncached_indices = []
        
        for i, text in enumerate(texts):
            if self.use_cache:
                cached_vector = self._get_cached_vector(text)
                if cached_vector is not None:
                    vectors.append((i, cached_vector))
                    self.stats['cache_hits'] += 1
                    continue
            
            uncached_texts.append(text)
            uncached_indices.append(i)
            self.stats['cache_misses'] += 1
        
        # 编码未缓存的文本
        if uncached_texts:
            uncached_vectors = self._encode_batch(uncached_texts)
            
            # 缓存新编码的向量
            if self.use_cache:
                for text, vector in zip(uncached_texts, uncached_vectors):
                    self._cache_vector(text, vector)
            
            # 添加到结果中
            for i, vector in zip(uncached_indices, uncached_vectors):
                vectors.append((i, vector))
        
        # 按原始顺序排序
        vectors.sort(key=lambda x: x[0])
        result = np.array([v[1] for v in vectors])
        
        self.stats['total_encoded'] += len(texts)
        self.logger.info(f"编码完成，缓存命中率: {self._get_cache_hit_rate():.1f}%")
        
        return result
    
    def encode_methods(self, methods: List[MethodInfo]) -> np.ndarray:
        """
        编码方法信息列表
        
        Args:
            methods: 方法信息列表
            
        Returns:
            向量矩阵
        """
        texts = [self._build_method_description(method) for method in methods]
        return self.encode(texts)
    
    def encode_enhanced_methods(self, methods: List[MethodInfo], 
                              semantic_analyses: Optional[List[Dict[str, Any]]] = None,
                              knowledge_base_infos: Optional[List[Dict[str, Any]]] = None) -> np.ndarray:
        """
        使用增强特征编码方法信息列表
        
        Args:
            methods: 方法信息列表
            semantic_analyses: 语义分析结果列表（可选）
            knowledge_base_infos: 知识库信息列表（可选）
            
        Returns:
            向量矩阵
        """
        texts = []
        
        for i, method in enumerate(methods):
            semantic_analysis = semantic_analyses[i] if semantic_analyses and i < len(semantic_analyses) else None
            kb_info = knowledge_base_infos[i] if knowledge_base_infos and i < len(knowledge_base_infos) else None
            
            # 提取增强特征
            features = self.features_extractor.extract_features(method, semantic_analysis, kb_info)
            
            # 构建加权描述
            weighted_description = self._get_weighted_description_for_vector(features)
            texts.append(weighted_description)
            
            self.stats['enhanced_encodings'] += 1
            if features.framework_parameters:
                self.stats['framework_params_filtered'] += 1
        
        return self.encode(texts)
    
    def _encode_batch(self, texts: List[str]) -> np.ndarray:
        """
        批量编码文本
        
        Args:
            texts: 文本列表
            
        Returns:
            向量矩阵
        """
        import time
        start_time = time.time()
        
        all_vectors = []
        
        # 分批处理
        for i in range(0, len(texts), self.batch_size):
            batch_texts = texts[i:i + self.batch_size]
            batch_vectors = self._encode_single_batch(batch_texts)
            all_vectors.extend(batch_vectors)
            self.stats['batch_count'] += 1
            
            if i % (self.batch_size * 10) == 0:
                self.logger.debug(f"已处理 {i + len(batch_texts)}/{len(texts)} 个文本")
        
        encoding_time = time.time() - start_time
        self.stats['encoding_time'] += encoding_time
        
        return np.array(all_vectors)
    
    def _encode_single_batch(self, texts: List[str]) -> List[np.ndarray]:
        """
        编码单个批次
        
        Args:
            texts: 文本列表
            
        Returns:
            向量列表
        """
        try:
            # 验证输入
            if not texts:
                return []
            
            # 确保所有输入都是字符串
            validated_texts = []
            for text in texts:
                if isinstance(text, str):
                    validated_texts.append(text)
                else:
                    validated_texts.append(str(text))
            
            # Tokenize
            inputs = self.tokenizer(
                validated_texts,
                padding=True,
                truncation=True,
                max_length=self.max_length,
                return_tensors="pt"
            )
            
            # 移动到设备
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # 前向传播
            with torch.no_grad():
                outputs = self.model(**inputs)
                
                # 使用[CLS]标记的向量作为句子表示
                vectors = outputs.last_hidden_state[:, 0, :].cpu().numpy()
            
            return [vector for vector in vectors]
            
        except Exception as e:
            self.logger.error(f"批次编码失败: {str(e)}")
            # 模型编码失败，抛出异常
            raise RuntimeError(f"向量编码失败: {str(e)}")
    
    def _build_method_description(self, method: MethodInfo) -> str:
        """
        构建方法描述文本
        
        Args:
            method: 方法信息
            
        Returns:
            方法描述文本
        """
        parts = []
        
        # 添加类和包信息
        if method.package:
            parts.append(f"package {method.package}")
        
        if method.class_name:
            parts.append(f"class {method.class_name}")
        
        # 添加方法签名
        method_signature = f"method {method.method_name}"
        
        if method.parameters:
            params_str = ", ".join(method.parameters)
            method_signature += f"({params_str})"
        else:
            method_signature += "()"
        
        if method.return_type:
            method_signature += f" returns {method.return_type}"
        
        parts.append(method_signature)
        
        # 添加修饰符
        if hasattr(method, 'modifiers') and method.modifiers:
            modifiers_str = " ".join(method.modifiers)
            parts.append(f"modifiers {modifiers_str}")
        
        # 添加注解
        if hasattr(method, 'annotations') and method.annotations:
            annotations_str = " ".join(method.annotations)
            parts.append(f"annotations {annotations_str}")
        
        # 添加上下文信息
        if method.context:
            parts.append(f"context {method.context}")
        
        return " ".join(parts)
    
    def _get_weighted_description_for_vector(self, features: EnhancedMethodFeatures) -> str:
        """
        构建用于向量编码的加权描述（优化AI语义分析基础信息）
        
        Args:
            features: 增强的方法特征
            
        Returns:
            加权描述文本
        """
        parts = []
        
        # 1. AI语义理解内容（权重80%，优先使用AI生成的语义理解）
        if features.semantic_description:
            # AI生成的语义描述是最重要的，重复4次以增强权重
            semantic_part = features.semantic_description
            parts.extend([semantic_part] * 4)
        
        if features.business_purpose:
            # 业务目的重复3次，这是AI分析的核心输出
            parts.extend([features.business_purpose] * 3)
        
        if features.functional_summary:
            # 功能摘要重复2次
            parts.extend([features.functional_summary] * 2)
        
        # 2. 实际源代码上下文集成（新增，权重15%）
        # 如果有实际源代码上下文，优先使用
        if hasattr(features, 'actual_source_context') and features.actual_source_context:
            parts.append(f"实际调用上下文: {features.actual_source_context}")
        
        # 如果有原始方法实现，集成到描述中
        if hasattr(features, 'original_method_logic') and features.original_method_logic:
            parts.append(f"原始业务逻辑: {features.original_method_logic}")
        
        # 3. 知识库内容（权重0-15%，基于匹配置信度）
        if features.knowledge_base_description and features.kb_confidence > 0.3:
            kb_weight = max(1, int(features.kb_confidence * 3))  # 1-3次重复
            parts.extend([features.knowledge_base_description] * kb_weight)
        
        # 添加替换规则信息
        if features.replacement_rules:
            rules_text = " ".join(features.replacement_rules[:3])  # 增加数量限制
            parts.append(f"迁移规则: {rules_text}")
        
        # 4. 业务参数特征（重点编码，排除框架参数）
        if features.business_parameters:
            business_params_text = self._build_enhanced_business_parameters_description(features.business_parameters)
            parts.append(business_params_text)
        
        # 5. 代码结构特征（权重5%，降低权重）
        if features.method_signature:
            # 过滤框架参数的方法签名
            filtered_signature = self._filter_framework_params_from_signature(
                features.method_signature, features.framework_parameters
            )
            parts.append(filtered_signature)
        
        if features.return_type:
            parts.append(f"返回: {features.return_type}")
        
        # 6. 上下文特征（权重5%）
        if features.package_info:
            # 简化包信息，只保留关键部分
            simplified_package = self._simplify_package_info(features.package_info)
            parts.append(simplified_package)
        
        # 7. 业务标签（增强业务功能描述）
        if features.business_tags:
            tags_text = " ".join(features.business_tags[:6])  # 增加标签数量
            parts.append(f"业务特征: {tags_text}")
        
        return " ".join(parts)
    
    def _build_business_parameters_description(self, business_params: List[str]) -> str:
        """构建业务参数描述"""
        if not business_params:
            return ""
        
        param_descriptions = []
        for param in business_params:
            if ':' in param:
                param_name, param_type = param.split(':', 1)
                param_name = param_name.strip()
                param_type = param_type.strip().split('.')[-1]  # 简化类型名
                
                # 生成参数语义描述
                semantic_desc = self._infer_parameter_semantic(param_name, param_type)
                param_descriptions.append(f"{param_name}({semantic_desc})")
            else:
                param_descriptions.append(param)
        
        return f"业务参数: {' '.join(param_descriptions)}"
    
    def _build_enhanced_business_parameters_description(self, business_params: List[str]) -> str:
        """构建增强的业务参数描述（优化AI语义分析）"""
        if not business_params:
            return ""
        
        param_descriptions = []
        for param in business_params:
            if ':' in param:
                param_name, param_type = param.split(':', 1)
                param_name = param_name.strip()
                param_type = param_type.strip().split('.')[-1]  # 简化类型名
                
                # 使用增强的参数语义推断
                semantic_desc = self._infer_enhanced_parameter_semantic(param_name, param_type)
                business_meaning = self._infer_business_meaning(param_name)
                
                param_descriptions.append(f"{param_name}({semantic_desc}|{business_meaning})")
            else:
                param_descriptions.append(param)
        
        return f"核心业务参数: {' '.join(param_descriptions)}"
    
    def _simplify_package_info(self, package_info: str) -> str:
        """简化包信息，只保留业务相关部分"""
        if not package_info:
            return ""
        
        # 移除常见的技术包前缀
        simplified = package_info
        prefixes_to_remove = ['com.', 'org.', 'net.', 'java.', 'javax.']
        
        for prefix in prefixes_to_remove:
            if simplified.startswith(prefix):
                simplified = simplified[len(prefix):]
                break
        
        # 只保留最后2-3个包层级
        parts = simplified.split('.')
        if len(parts) > 3:
            simplified = '.'.join(parts[-3:])
        
        return f"模块: {simplified}"
    
    def _infer_parameter_semantic(self, param_name: str, param_type: str) -> str:
        """推断参数语义"""
        param_lower = param_name.lower()
        
        # 基于参数名推断
        if 'id' in param_lower:
            return "标识符"
        elif 'name' in param_lower:
            return "名称"
        elif 'code' in param_lower:
            return "编码"
        elif 'type' in param_lower:
            return "类型"
        elif 'status' in param_lower:
            return "状态"
        elif 'amount' in param_lower or 'price' in param_lower:
            return "金额"
        elif 'count' in param_lower or 'num' in param_lower:
            return "数量"
        elif 'date' in param_lower or 'time' in param_lower:
            return "时间"
        elif 'url' in param_lower or 'path' in param_lower:
            return "路径"
        elif 'email' in param_lower:
            return "邮箱"
        elif 'phone' in param_lower:
            return "电话"
        
        # 基于参数类型推断
        type_lower = param_type.lower()
        if 'string' in type_lower:
            return "文本"
        elif 'long' in type_lower or 'integer' in type_lower:
            return "数值"
        elif 'boolean' in type_lower:
            return "布尔值"
        elif 'date' in type_lower:
            return "日期"
        elif 'list' in type_lower:
            return "列表"
        elif 'map' in type_lower:
            return "映射"
        
        return "参数"
    
    def _infer_enhanced_parameter_semantic(self, param_name: str, param_type: str) -> str:
        """推断增强的参数语义（基于AI分析优化）"""
        param_lower = param_name.lower()
        
        # 更详细的业务语义推断
        business_semantics = {
            # 标识符类
            'id': "唯一标识", 'uid': "用户标识", 'key': "主键", 'code': "业务编码",
            # 名称类
            'name': "名称", 'title': "标题", 'label': "标签", 'desc': "描述",
            # 数值类
            'amount': "金额", 'price': "价格", 'cost': "成本", 'fee': "费用",
            'count': "计数", 'num': "数量", 'size': "大小", 'length': "长度",
            # 状态类
            'status': "状态", 'state': "状态", 'flag': "标志", 'enabled': "启用状态",
            # 时间类
            'time': "时间", 'date': "日期", 'created': "创建时间", 'updated': "更新时间",
            # 联系方式
            'email': "邮箱", 'phone': "电话", 'mobile': "手机", 'address': "地址",
            # 业务对象
            'user': "用户", 'order': "订单", 'product': "产品", 'service': "服务"
        }
        
        # 查找最匹配的语义
        for keyword, semantic in business_semantics.items():
            if keyword in param_lower:
                return semantic
        
        # 基于类型的增强推断
        type_lower = param_type.lower()
        if 'string' in type_lower:
            return "文本数据"
        elif 'long' in type_lower or 'integer' in type_lower or 'int' in type_lower:
            return "数值数据"
        elif 'boolean' in type_lower or 'bool' in type_lower:
            return "布尔标志"
        elif 'date' in type_lower or 'time' in type_lower:
            return "时间数据"
        elif 'list' in type_lower or 'array' in type_lower:
            return "集合数据"
        elif 'map' in type_lower or 'dict' in type_lower:
            return "映射数据"
        elif 'object' in type_lower:
            return "业务对象"
        
        return "业务参数"
    
    def _infer_business_meaning(self, param_name: str) -> str:
        """推断参数的业务含义"""
        param_lower = param_name.lower()
        
        # 业务领域映射
        business_domains = {
            # 用户相关
            'user': "用户管理", 'customer': "客户管理", 'member': "会员管理",
            # 订单相关
            'order': "订单处理", 'payment': "支付处理", 'invoice': "发票管理",
            # 产品相关
            'product': "产品管理", 'item': "商品管理", 'goods': "货物管理",
            # 系统相关
            'system': "系统配置", 'config': "配置管理", 'setting': "设置管理",
            # 数据相关
            'data': "数据处理", 'info': "信息管理", 'record': "记录管理"
        }
        
        for keyword, domain in business_domains.items():
            if keyword in param_lower:
                return domain
        
        # 基于动作推断
        if any(action in param_lower for action in ['create', 'add', 'insert']):
            return "创建操作"
        elif any(action in param_lower for action in ['update', 'modify', 'edit']):
            return "更新操作"
        elif any(action in param_lower for action in ['delete', 'remove', 'drop']):
            return "删除操作"
        elif any(action in param_lower for action in ['query', 'search', 'find']):
            return "查询操作"
        
        return "业务处理"
    
    def _filter_framework_params_from_signature(self, signature: str, 
                                              framework_params: List[str]) -> str:
        """从方法签名中过滤框架参数"""
        if not framework_params:
            return signature
        
        # 简单的过滤逻辑：移除框架参数
        filtered_signature = signature
        
        for framework_param in framework_params:
            if framework_param in filtered_signature:
                # 移除该参数（简单实现）
                filtered_signature = filtered_signature.replace(framework_param, "")
        
        # 清理多余的逗号和空格
        filtered_signature = re.sub(r',\s*,', ',', filtered_signature)
        filtered_signature = re.sub(r'\(\s*,', '(', filtered_signature)
        filtered_signature = re.sub(r',\s*\)', ')', filtered_signature)
        
        return filtered_signature
    
    def _get_cached_vector(self, text: str) -> Optional[np.ndarray]:
        """
        从缓存中获取向量
        
        Args:
            text: 文本
            
        Returns:
            缓存的向量，如果不存在则返回None
        """
        try:
            cache_key = self._get_cache_key(text)
            cache_file = self.cache_dir / f"{cache_key}.pkl"
            
            if cache_file.exists():
                with open(cache_file, 'rb') as f:
                    return pickle.load(f)
        except Exception as e:
            self.logger.debug(f"读取缓存失败: {str(e)}")
        
        return None
    
    def _cache_vector(self, text: str, vector: np.ndarray):
        """
        缓存向量
        
        Args:
            text: 文本
            vector: 向量
        """
        try:
            cache_key = self._get_cache_key(text)
            cache_file = self.cache_dir / f"{cache_key}.pkl"
            
            with open(cache_file, 'wb') as f:
                pickle.dump(vector, f)
        except Exception as e:
            self.logger.debug(f"缓存向量失败: {str(e)}")
    
    def _get_cache_key(self, text: str) -> str:
        """
        生成缓存键
        
        Args:
            text: 文本
            
        Returns:
            缓存键
        """
        # 使用模型名和文本内容生成哈希
        content = f"{self.model_name}:{text}"
        return hashlib.md5(content.encode('utf-8')).hexdigest()
    

    
    def _get_cache_hit_rate(self) -> float:
        """获取缓存命中率"""
        total = self.stats['cache_hits'] + self.stats['cache_misses']
        if total == 0:
            return 0.0
        return (self.stats['cache_hits'] / total) * 100
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = self.stats.copy()
        stats['cache_hit_rate'] = self._get_cache_hit_rate()
        stats['model_loaded'] = self.model_loaded
        stats['device'] = str(self.device) if self.device else "none"
        return stats
    
    def clear_cache(self):
        """清空缓存"""
        try:
            import shutil
            if self.cache_dir.exists():
                shutil.rmtree(self.cache_dir)
                self.cache_dir.mkdir(parents=True, exist_ok=True)
                self.logger.info("缓存已清空")
        except Exception as e:
            self.logger.error(f"清空缓存失败: {str(e)}")
    
    def get_cache_size(self) -> int:
        """获取缓存文件数量"""
        try:
            return len(list(self.cache_dir.glob("*.pkl")))
        except Exception:
            return 0