#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re
import os

def verify_class_issues_coverage():
    """独立验证类问题解决方案的完整性"""
    
    print("=" * 60)
    print("1.1.1 类问题解决方案独立验证和完整性检查")
    print("=" * 60)
    
    # 源文件路径
    source_file = 'eem-solution-transformer/out/问题列表.md'
    task_file = 'eem-solution-transformer/out/task-import.md'
    
    print("\n第一步：源文件独立统计")
    print("-" * 40)
    
    # 读取源文件
    if not os.path.exists(source_file):
        print(f"❌ 源文件不存在: {source_file}")
        return False
        
    with open(source_file, 'r', encoding='utf-8') as f:
        source_content = f.read()
    
    # 统计源文件中的类问题
    source_class_issues = re.findall(r'error_type: "类问题"', source_content)
    source_total = len(source_class_issues)
    
    print(f"📊 源文件类问题总数: {source_total}")
    
    # 提取所有文件名和对应的类问题
    source_files = {}
    file_sections = re.split(r'^## ([A-Za-z][A-Za-z0-9_]*)', source_content, flags=re.MULTILINE)[1:]
    
    for i in range(0, len(file_sections), 2):
        if i + 1 < len(file_sections):
            file_name = file_sections[i]
            file_content = file_sections[i + 1]
            
            # 统计该文件的类问题
            file_class_issues = re.findall(r'error_type: "类问题"', file_content)
            if len(file_class_issues) > 0:
                source_files[file_name] = len(file_class_issues)
                print(f"  📁 {file_name}: {len(file_class_issues)} 个类问题")
    
    print(f"\n📈 源文件统计汇总:")
    print(f"  - 包含类问题的文件数: {len(source_files)}")
    print(f"  - 类问题总数: {sum(source_files.values())}")
    
    # 验证统计一致性
    if sum(source_files.values()) != source_total:
        print(f"⚠️  统计不一致: 分文件统计({sum(source_files.values())}) != 总体统计({source_total})")
        return False
    else:
        print("✅ 源文件统计一致性验证通过")
    
    print("\n第二步：task-import.md 文件验证")
    print("-" * 40)
    
    # 检查task-import.md是否存在
    if not os.path.exists(task_file):
        print(f"❌ task-import.md文件不存在: {task_file}")
        return False
    
    with open(task_file, 'r', encoding='utf-8') as f:
        task_content = f.read()
    
    # 提取task-import.md中处理的文件和问题
    task_files = {}
    
    # 查找所有文件标题
    file_matches = re.finditer(r'^## ([A-Za-z][A-Za-z0-9_]*)', task_content, re.MULTILINE)
    
    for file_match in file_matches:
        file_name = file_match.group(1)
        if file_name not in ['处理说明']:  # 排除非文件名的标题
            # 查找该文件下的类问题数量
            file_start = file_match.end()
            
            # 找到下一个文件标题或文档结束
            next_file_match = None
            for next_match in re.finditer(r'^## ([A-Za-z][A-Za-z0-9_]*)', task_content[file_start:], re.MULTILINE):
                if next_match.group(1) not in ['处理说明']:
                    next_file_match = next_match
                    break
            
            if next_file_match:
                file_section = task_content[file_start:file_start + next_file_match.start()]
            else:
                # 查找"处理说明"标题
                processing_match = re.search(r'^## 处理说明', task_content[file_start:], re.MULTILINE)
                if processing_match:
                    file_section = task_content[file_start:file_start + processing_match.start()]
                else:
                    file_section = task_content[file_start:]
            
            # 统计该文件的类问题数量
            class_problem_count = len(re.findall(r'### 类问题 \d+:', file_section))
            if class_problem_count > 0:
                task_files[file_name] = class_problem_count
                print(f"  📁 {file_name}: {class_problem_count} 个类问题")
    
    task_total = sum(task_files.values())
    print(f"\n📈 task-import.md统计汇总:")
    print(f"  - 处理的文件数: {len(task_files)}")
    print(f"  - 处理的类问题总数: {task_total}")
    
    print("\n第三步：数量精确匹配验证")
    print("-" * 40)
    
    # 验证总数匹配
    if task_total == source_total:
        print(f"✅ 总数匹配: task-import.md({task_total}) = 源文件({source_total})")
    else:
        print(f"❌ 总数不匹配: task-import.md({task_total}) ≠ 源文件({source_total})")
        print(f"   差异: {abs(task_total - source_total)} 个问题")
    
    # 验证文件级匹配
    print("\n📋 文件级验证:")
    all_files_match = True
    
    for file_name in source_files:
        source_count = source_files[file_name]
        task_count = task_files.get(file_name, 0)
        
        if source_count == task_count:
            print(f"  ✅ {file_name}: {source_count} = {task_count}")
        else:
            print(f"  ❌ {file_name}: 源文件({source_count}) ≠ task-import({task_count})")
            all_files_match = False
    
    # 检查是否有task-import.md中多出的文件
    for file_name in task_files:
        if file_name not in source_files:
            print(f"  ⚠️  {file_name}: task-import.md中存在但源文件中不存在")
            all_files_match = False
    
    print("\n第四步：验证结论")
    print("-" * 40)
    
    if task_total == source_total and all_files_match:
        print("🎉 验证通过!")
        print("   ✅ 问题总数完全匹配")
        print("   ✅ 所有文件的问题数量完全匹配")
        print("   ✅ 实现了100%的问题覆盖")
        return True
    else:
        print("❌ 验证不通过!")
        if task_total != source_total:
            print(f"   ❌ 总数不匹配: 差异{abs(task_total - source_total)}个问题")
        if not all_files_match:
            print("   ❌ 存在文件级别的数量不匹配")
        return False

if __name__ == '__main__':
    verify_class_issues_coverage()
