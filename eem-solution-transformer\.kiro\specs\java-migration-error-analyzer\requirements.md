# 需求文档

## 介绍

Java代码迁移错误分析器是一个自动化工具，用于分析已迁移的Java项目中的错误，并生成详细的修复任务列表。该工具能够识别、分类和处理迁移过程中产生的各种问题，包括导入错误、类/方法废弃等问题，并提供基于知识库的解决方案。

## 需求

### 需求 1

**用户故事：** 作为Java开发工程师，我希望能够由大模型结合提供的工具自动分析迁移后代码中的错误，以便快速识别需要修复的问题。

#### 验收标准

1. 当系统接收到JavaAnnotator.xml文件时，系统应当解析该文件并提取所有错误条目
2. 当错误解析完成时，系统应当生成包含所有问题的结构化列表
3. 当解析过程中遇到无法识别的错误格式时，系统应当记录并继续处理其他错误

### 需求 2

**用户故事：** 作为Java开发工程师，我希望错误能够被自动分类，以便我能够按优先级和类型处理问题。

#### 验收标准

1. 当错误条目被提取后，系统应当根据知识库对错误进行分类
2. 如果知识库中存在匹配的问题类型，系统应当将错误归类到相应类别
3. 如果知识库中不存在匹配的问题类型，系统应当将错误归类到"未识别"类别，未识别也需要更进一步分类
4. 当分类完成时，系统应当生成"问题列表.md"文件，包含按类别组织的所有问题

### 需求 3

**用户故事：** 作为Java开发工程师，我希望系统能够生成详细的修复任务列表，以便我能够按照合理的顺序修复问题。

#### 验收标准

1. 当问题分类完成后，系统应当按文件粒度生成详细的处理任务
2. 系统应当按照常量类、实体类、工具类、DAO、Service、Controller的顺序组织任务
3. 当知识库中存在解决方案时，系统应当使用知识库的解决方案
4. 当生成任务完成时，系统应当输出task.md文件，包含所有详细的修改步骤

### 需求 4

**用户故事：** 作为Java开发工程师，我希望系统能够智能处理导入问题，以便自动找到正确的替换类。

#### 验收标准

1. 当遇到导入问题时，系统应当使用类名查找工具搜索可用的替换类
2. 如果找到唯一匹配的类，系统应当使用绿色标注并直接使用该类进行替换
3. 如果找到多个匹配的类，系统应当使用模糊匹配工具进行进一步分析，首先使用模糊匹配工具查找到多个备选类，然后需要使用大模型的能力去分析这些备选文件，确认哪一个是最符合原始类的，模糊匹配工具只是提供可选的类
4. 当存在多个候选类时，系统应当分析类的内容并确定最符合的替换选项，使用黄色标注

### 需求 5

**用户故事：** 作为Java开发工程师，我希望系统能够识别并标记废弃的类和方法，以便我能够优先处理其他问题。

#### 验收标准

1. 当遇到类或方法被废弃的问题时，系统应当使用红色进行标注
2. 系统应当将废弃问题放在任务列表的最后
3. 废弃问题应当被标记为暂不处理，等待后续专门处理

### 需求 6

**用户故事：** 作为Java开发工程师，我希望能够逐步执行修复任务并跟踪进度，以便确保修复过程的可控性。

#### 验收标准

1. 当开始执行任务时，系统应当按照task.md中的顺序逐个处理子任务
2. 当每个子任务完成时，系统应当进行代码确认
3. 当代码确认无问题后，系统应当调用git提交助手进行代码提交
4. 当代码提交成功后，系统应当将该子任务标记为已完成

### 需求 7

**用户故事：** 作为Java开发工程师，我希望系统能够与现有的知识库集成，以便利用已有的解决方案经验。

#### 验收标准

1. 当处理问题时，系统应当首先查询能管代码迁移知识库
2. 如果知识库中存在相关问题的解决方案，系统应当优先使用知识库的解决方案
3. 当知识库中不存在解决方案时，系统应当使用内置的处理逻辑
4. 系统应当能够更新和扩展知识库内容