"""
核心接口定义

定义系统中各组件的抽象接口。
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
from .models import ErrorReport, MethodInfo, MatchResult, Configuration, ProcessingStats


class ErrorDetectorInterface(ABC):
    """错误检测器接口"""
    
    @abstractmethod
    def detect_errors(self, project_path: str, config: Configuration) -> List[ErrorReport]:
        """
        检测项目中的错误
        
        Args:
            project_path: 项目路径
            config: 配置对象
            
        Returns:
            错误报告列表
        """
        pass
    
    @abstractmethod
    def is_available(self) -> bool:
        """
        检查错误检测器是否可用
        
        Returns:
            是否可用
        """
        pass
    
    @abstractmethod
    def get_name(self) -> str:
        """
        获取检测器名称
        
        Returns:
            检测器名称
        """
        pass


class SourceCodeParserInterface(ABC):
    """源码解析器接口"""
    
    @abstractmethod
    def parse_java_files(self, src_dir: str) -> List[MethodInfo]:
        """
        解析Java源码文件
        
        Args:
            src_dir: 源码目录路径
            
        Returns:
            方法信息列表
        """
        pass
    
    @abstractmethod
    def parse_single_file(self, file_path: str) -> List[MethodInfo]:
        """
        解析单个Java文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            方法信息列表
        """
        pass


class VectorEncoderInterface(ABC):
    """向量编码器接口"""
    
    @abstractmethod
    def encode(self, texts: List[str]) -> Any:
        """
        将文本编码为向量
        
        Args:
            texts: 文本列表
            
        Returns:
            向量矩阵
        """
        pass
    
    @abstractmethod
    def encode_methods(self, methods: List[MethodInfo]) -> Any:
        """
        编码方法信息列表
        
        Args:
            methods: 方法信息列表
            
        Returns:
            向量矩阵
        """
        pass


class SimilarityMatcherInterface(ABC):
    """相似度匹配器接口"""
    
    @abstractmethod
    def build_index(self, vectors: Any, methods: List[MethodInfo]) -> None:
        """
        构建向量索引
        
        Args:
            vectors: 向量矩阵
            methods: 方法信息列表
        """
        pass
    
    @abstractmethod
    def search_similar(self, query_vectors: Any) -> List[List[tuple]]:
        """
        搜索相似向量
        
        Args:
            query_vectors: 查询向量矩阵
            
        Returns:
            每个查询的相似结果列表
        """
        pass
    
    @abstractmethod
    def search_single(self, query_vector: Any) -> List[tuple]:
        """
        搜索单个查询向量的相似结果
        
        Args:
            query_vector: 查询向量
            
        Returns:
            相似结果列表
        """
        pass


class ResultGeneratorInterface(ABC):
    """结果生成器接口"""
    
    @abstractmethod
    def generate_suggestions(self, errors: List[ErrorReport], 
                           matches: List[MatchResult]) -> str:
        """
        生成迁移建议JSON
        
        Args:
            errors: 原始错误列表
            matches: 匹配结果列表
            
        Returns:
            JSON字符串
        """
        pass
    
    @abstractmethod
    def generate_statistics(self, stats: ProcessingStats) -> Dict[str, Any]:
        """
        生成统计信息
        
        Args:
            stats: 处理统计信息
            
        Returns:
            统计信息字典
        """
        pass


class ConfigManagerInterface(ABC):
    """配置管理器接口"""
    
    @abstractmethod
    def load_config(self, config_path: str) -> Configuration:
        """
        加载配置文件
        
        Args:
            config_path: 配置文件路径
            
        Returns:
            配置对象
        """
        pass
    
    @abstractmethod
    def save_config(self, config: Configuration, config_path: str) -> None:
        """
        保存配置文件
        
        Args:
            config: 配置对象
            config_path: 配置文件路径
        """
        pass
    
    @abstractmethod
    def merge_cli_args(self, config: Configuration, cli_args: Dict[str, Any]) -> Configuration:
        """
        合并命令行参数到配置
        
        Args:
            config: 基础配置
            cli_args: 命令行参数
            
        Returns:
            合并后的配置
        """
        pass


class LoggerInterface(ABC):
    """日志记录器接口"""
    
    @abstractmethod
    def setup_logging(self, config: Configuration) -> None:
        """
        设置日志配置
        
        Args:
            config: 配置对象
        """
        pass
    
    @abstractmethod
    def log_progress(self, current: int, total: int, message: str) -> None:
        """
        记录进度信息
        
        Args:
            current: 当前进度
            total: 总数
            message: 消息
        """
        pass
    
    @abstractmethod
    def log_statistics(self, stats: ProcessingStats) -> None:
        """
        记录统计信息
        
        Args:
            stats: 统计信息
        """
        pass


class CacheInterface(ABC):
    """缓存接口"""
    
    @abstractmethod
    def get(self, key: str) -> Optional[Any]:
        """
        获取缓存值
        
        Args:
            key: 缓存键
            
        Returns:
            缓存值或None
        """
        pass
    
    @abstractmethod
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """
        设置缓存值
        
        Args:
            key: 缓存键
            value: 缓存值
            ttl: 生存时间（秒）
        """
        pass
    
    @abstractmethod
    def clear(self) -> None:
        """清空缓存"""
        pass
    
    @abstractmethod
    def size(self) -> int:
        """
        获取缓存大小
        
        Returns:
            缓存项数量
        """
        pass