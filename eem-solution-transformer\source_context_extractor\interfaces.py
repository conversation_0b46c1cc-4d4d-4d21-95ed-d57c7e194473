"""
源码上下文提取器接口定义

定义系统中各组件的抽象接口。
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
try:
    from .models import ErrorItem, MethodAnalysisResult, ExtractorConfig
except ImportError:
    from models import ErrorItem, MethodAnalysisResult, ExtractorConfig


class InputParserInterface(ABC):
    """输入解析器接口"""
    
    @abstractmethod
    def parse_json_file(self, file_path: str) -> List[ErrorItem]:
        """
        解析JSON错误报告文件
        
        Args:
            file_path: JSON文件路径
            
        Returns:
            错误项列表
        """
        pass
    
    @abstractmethod
    def validate_error_item(self, error_item: ErrorItem) -> bool:
        """
        验证错误项的有效性
        
        Args:
            error_item: 错误项
            
        Returns:
            是否有效
        """
        pass


class FileLocatorInterface(ABC):
    """文件定位器接口"""
    
    @abstractmethod
    def find_source_file(self, file_path: str, search_paths: List[str]) -> Optional[str]:
        """
        查找源文件
        
        Args:
            file_path: 文件路径
            search_paths: 搜索路径列表
            
        Returns:
            找到的文件路径或None
        """
        pass
    
    @abstractmethod
    def read_file_content(self, file_path: str) -> str:
        """
        读取文件内容
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件内容
        """
        pass
    
    @abstractmethod
    def get_line_content(self, file_path: str, line_number: int, context_lines: int = 5) -> Dict[str, Any]:
        """
        获取指定行的内容和上下文
        
        Args:
            file_path: 文件路径
            line_number: 行号
            context_lines: 上下文行数
            
        Returns:
            包含行内容和上下文的字典
        """
        pass


class SourceAnalyzerInterface(ABC):
    """源码分析器接口"""
    
    @abstractmethod
    def analyze_method(self, file_content: str, method_name: str, line_number: int) -> Optional[MethodAnalysisResult]:
        """
        分析方法信息
        
        Args:
            file_content: 文件内容
            method_name: 方法名
            line_number: 行号
            
        Returns:
            方法分析结果或None
        """
        pass
    
    @abstractmethod
    def extract_method_context(self, file_content: str, method_name: str) -> Dict[str, Any]:
        """
        提取方法上下文信息
        
        Args:
            file_content: 文件内容
            method_name: 方法名
            
        Returns:
            方法上下文信息
        """
        pass


class ASTParserInterface(ABC):
    """AST解析器接口"""
    
    @abstractmethod
    def parse_java_file(self, file_content: str) -> Any:
        """
        解析Java文件的AST
        
        Args:
            file_content: Java文件内容
            
        Returns:
            AST对象
        """
        pass
    
    @abstractmethod
    def find_method_in_ast(self, ast: Any, method_name: str) -> Optional[Any]:
        """
        在AST中查找方法
        
        Args:
            ast: AST对象
            method_name: 方法名
            
        Returns:
            方法节点或None
        """
        pass
    
    @abstractmethod
    def extract_method_info(self, method_node: Any, file_content: str) -> Dict[str, Any]:
        """
        从方法节点提取信息
        
        Args:
            method_node: 方法节点
            file_content: 文件内容
            
        Returns:
            方法信息字典
        """
        pass


class ReportGeneratorInterface(ABC):
    """报告生成器接口"""
    
    @abstractmethod
    def generate_report(self, method_results: List[MethodAnalysisResult], output_path: str) -> str:
        """
        生成分析报告
        
        Args:
            method_results: 方法分析结果列表
            output_path: 输出文件路径
            
        Returns:
            生成的报告内容
        """
        pass
    
    @abstractmethod
    def format_method_result(self, result: MethodAnalysisResult) -> str:
        """
        格式化单个方法结果
        
        Args:
            result: 方法分析结果
            
        Returns:
            格式化后的字符串
        """
        pass


class ConfigManagerInterface(ABC):
    """配置管理器接口"""
    
    @abstractmethod
    def load_config(self, config_path: str) -> ExtractorConfig:
        """
        加载配置文件
        
        Args:
            config_path: 配置文件路径
            
        Returns:
            配置对象
        """
        pass
    
    @abstractmethod
    def save_config(self, config: ExtractorConfig, config_path: str) -> None:
        """
        保存配置文件
        
        Args:
            config: 配置对象
            config_path: 配置文件路径
        """
        pass
    
    @abstractmethod
    def merge_cli_args(self, config: ExtractorConfig, cli_args: Dict[str, Any]) -> ExtractorConfig:
        """
        合并命令行参数到配置
        
        Args:
            config: 基础配置
            cli_args: 命令行参数
            
        Returns:
            合并后的配置
        """
        pass