"""
源码解析器

使用 javalang 解析 Java 源码并提取方法信息，支持JDK8语法特性。
"""

import os
import logging
from typing import List, Dict, Optional, Set, Tuple
from pathlib import Path
import javalang
from javalang.tree import (
    MethodDeclaration, ClassDeclaration, InterfaceDeclaration, 
    CompilationUnit, EnumDeclaration, AnnotationDeclaration,
    FormalParameter, Type, ReferenceType, BasicType
)

from error_prone_json_reporter.common.interfaces import SourceCodeParserInterface
from error_prone_json_reporter.common.models import MethodInfo
from error_prone_json_reporter.stage2.jdk8_compatibility_checker import JDK8<PERSON>ompatibilityChe<PERSON>, EnhancedJavaParser


class SourceCodeParser(SourceCodeParserInterface):
    """
    Java源码解析器
    
    使用javalang库解析Java源码，提取方法签名、参数类型、返回类型等信息。
    支持处理泛型、内部类、注解等复杂语法结构。
    """
    
    def __init__(self):
        """初始化解析器"""
        self.logger = logging.getLogger(__name__)
        self.parsed_files: Set[str] = set()
        self.failed_files: Set[str] = set()
        self.stats = {
            'total_files': 0,
            'parsed_files': 0,
            'failed_files': 0,
            'total_methods': 0,
            'total_classes': 0,
            'jdk8_features_detected': 0,
            'fallback_used': 0
        }
        
        # 初始化JDK8兼容性检查器
        self.jdk8_checker = JDK8CompatibilityChecker()
        self.enhanced_parser = self.jdk8_checker.create_enhanced_parser()
    
    def parse_java_files(self, src_dir: str) -> List[MethodInfo]:
        """
        递归解析目录中的所有Java文件，支持JDK8语法特性
        
        Args:
            src_dir: 源码目录路径
            
        Returns:
            方法信息列表
        """
        self.logger.info(f"开始解析源码目录（支持JDK8）: {src_dir}")
        
        if not os.path.exists(src_dir):
            self.logger.error(f"源码目录不存在: {src_dir}")
            return []
        
        # 使用增强解析器解析整个目录
        all_methods, check_results = self.enhanced_parser.parse_directory(src_dir)
        
        # 更新统计信息
        self.stats['total_files'] = len(check_results)
        self.stats['parsed_files'] = sum(1 for r in check_results if r.javalang_success)
        self.stats['failed_files'] = sum(1 for r in check_results if not r.javalang_success)
        self.stats['fallback_used'] = sum(1 for r in check_results if r.fallback_needed)
        self.stats['total_methods'] = len(all_methods)
        
        # 统计JDK8特性
        jdk8_features_count = 0
        for result in check_results:
            if (result.has_lambda or result.has_method_reference or 
                result.has_stream_api or result.has_default_methods or result.has_optional):
                jdk8_features_count += 1
        self.stats['jdk8_features_detected'] = jdk8_features_count
        
        # 更新文件集合
        for result in check_results:
            if result.javalang_success:
                self.parsed_files.add(result.file_path)
            else:
                self.failed_files.add(result.file_path)
        
        self._log_parsing_stats()
        self._log_jdk8_stats(check_results)
        
        return all_methods
    
    def parse_single_file(self, file_path: str) -> List[MethodInfo]:
        """
        解析单个Java文件，支持JDK8语法特性
        
        Args:
            file_path: Java文件路径
            
        Returns:
            方法信息列表
        """
        self.logger.debug(f"解析文件（支持JDK8）: {file_path}")
        
        # 使用增强解析器解析单个文件
        methods, check_result = self.enhanced_parser.parse_file(file_path)
        
        # 记录JDK8特性检测结果
        if (check_result.has_lambda or check_result.has_method_reference or 
            check_result.has_stream_api or check_result.has_default_methods or check_result.has_optional):
            self.logger.debug(f"检测到JDK8特性: {file_path}")
            features = []
            if check_result.has_lambda:
                features.append("Lambda表达式")
            if check_result.has_method_reference:
                features.append("方法引用")
            if check_result.has_stream_api:
                features.append("Stream API")
            if check_result.has_default_methods:
                features.append("默认方法")
            if check_result.has_optional:
                features.append("Optional")
            self.logger.debug(f"  特性: {', '.join(features)}")
        
        # 记录解析问题
        if check_result.parsing_issues:
            for issue in check_result.parsing_issues:
                self.logger.warning(f"解析问题 {file_path}: {issue}")
        
        # 如果使用了回退解析，记录日志
        if check_result.fallback_needed:
            self.logger.warning(f"使用回退解析策略: {file_path}")
        
        return methods
    
    def _find_java_files(self, directory: str) -> List[str]:
        """递归查找所有Java文件"""
        java_files = []
        for root, dirs, files in os.walk(directory):
            # 跳过常见的非源码目录
            dirs[:] = [d for d in dirs if d not in {
                'target', 'build', '.git', '.svn', 'node_modules', 
                '.idea', '.vscode', '__pycache__'
            }]
            
            for file in files:
                if file.endswith('.java'):
                    java_files.append(os.path.join(root, file))
        
        return java_files
    
    def _extract_methods_from_class(self, class_node: ClassDeclaration, 
                                  package_name: str, file_path: str, 
                                  path: List) -> List[MethodInfo]:
        """从类声明中提取方法信息"""
        methods = []
        class_name = self._get_full_class_name(class_node, path)
        
        for method in class_node.methods:
            if isinstance(method, MethodDeclaration):
                method_info = self._create_method_info(
                    method, package_name, class_name, file_path
                )
                methods.append(method_info)
        
        # 处理内部类
        for inner_class in class_node.body:
            if isinstance(inner_class, ClassDeclaration):
                inner_class_name = f"{class_name}.{inner_class.name}"
                inner_methods = self._extract_methods_from_inner_class(
                    inner_class, package_name, inner_class_name, file_path
                )
                methods.extend(inner_methods)
        
        return methods
    
    def _extract_methods_from_interface(self, interface_node: InterfaceDeclaration,
                                      package_name: str, file_path: str,
                                      path: List) -> List[MethodInfo]:
        """从接口声明中提取方法信息"""
        methods = []
        interface_name = self._get_full_class_name(interface_node, path)
        
        for method in interface_node.body:
            if isinstance(method, MethodDeclaration):
                method_info = self._create_method_info(
                    method, package_name, interface_name, file_path
                )
                methods.append(method_info)
        
        return methods
    
    def _extract_methods_from_enum(self, enum_node: EnumDeclaration,
                                 package_name: str, file_path: str,
                                 path: List) -> List[MethodInfo]:
        """从枚举声明中提取方法信息"""
        methods = []
        enum_name = self._get_full_class_name(enum_node, path)
        
        for method in enum_node.body.declarations:
            if isinstance(method, MethodDeclaration):
                method_info = self._create_method_info(
                    method, package_name, enum_name, file_path
                )
                methods.append(method_info)
        
        return methods
    
    def _extract_methods_from_inner_class(self, class_node: ClassDeclaration,
                                        package_name: str, class_name: str,
                                        file_path: str) -> List[MethodInfo]:
        """从内部类中提取方法信息"""
        methods = []
        
        for method in class_node.methods:
            if isinstance(method, MethodDeclaration):
                method_info = self._create_method_info(
                    method, package_name, class_name, file_path
                )
                methods.append(method_info)
        
        return methods
    
    def _create_method_info(self, method: MethodDeclaration, package_name: str,
                          class_name: str, file_path: str) -> MethodInfo:
        """创建方法信息对象"""
        # 提取方法名
        method_name = method.name
        
        # 提取参数信息
        parameters = []
        if method.parameters:
            for param in method.parameters:
                param_type = self._get_type_string(param.type)
                param_name = param.name
                parameters.append(f"{param_name}: {param_type}")
        
        # 提取返回类型
        return_type = "void"
        if method.return_type:
            return_type = self._get_type_string(method.return_type)
        
        # 提取修饰符
        modifiers = []
        if method.modifiers:
            modifiers = [str(mod) for mod in method.modifiers]
        
        # 提取注解
        annotations = []
        if method.annotations:
            annotations = [self._get_annotation_string(ann) for ann in method.annotations]
        
        # 构建上下文信息
        context = self._build_method_context(method, parameters, return_type)
        
        return MethodInfo(
            package=package_name,
            class_name=class_name,
            method_name=method_name,
            parameters=parameters,
            return_type=return_type,
            context=context,
            file_path=file_path,
            modifiers=modifiers,
            annotations=annotations
        )
    
    def _get_full_class_name(self, class_node, path: List) -> str:
        """获取完整的类名（包括外部类）"""
        class_names = []
        
        # 从路径中提取所有类名
        for node in path:
            if hasattr(node, 'name') and isinstance(node, (ClassDeclaration, InterfaceDeclaration, EnumDeclaration)):
                class_names.append(node.name)
        
        # 添加当前类名
        class_names.append(class_node.name)
        
        return '.'.join(class_names)
    
    def _get_type_string(self, type_node: Type) -> str:
        """将类型节点转换为字符串"""
        if isinstance(type_node, BasicType):
            return type_node.name
        elif isinstance(type_node, ReferenceType):
            type_str = type_node.name
            
            # 处理泛型参数
            if type_node.arguments:
                args = []
                for arg in type_node.arguments:
                    if hasattr(arg, 'type'):
                        args.append(self._get_type_string(arg.type))
                    else:
                        args.append(str(arg))
                type_str += f"<{', '.join(args)}>"
            
            # 处理数组
            if hasattr(type_node, 'dimensions') and type_node.dimensions:
                type_str += '[]' * len(type_node.dimensions)
            
            return type_str
        else:
            return str(type_node)
    
    def _get_annotation_string(self, annotation) -> str:
        """获取注解字符串"""
        annotation_str = f"@{annotation.name}"
        
        if annotation.element:
            # 处理注解参数
            if hasattr(annotation.element, 'values'):
                values = []
                for value in annotation.element.values:
                    values.append(str(value))
                annotation_str += f"({', '.join(values)})"
            else:
                annotation_str += f"({annotation.element})"
        
        return annotation_str
    
    def _build_method_context(self, method: MethodDeclaration, 
                            parameters: List[str], return_type: str) -> str:
        """构建方法上下文信息"""
        context_parts = []
        
        # 添加修饰符
        if method.modifiers:
            modifiers_str = ' '.join(str(mod) for mod in method.modifiers)
            context_parts.append(f"修饰符: {modifiers_str}")
        
        # 添加注解
        if method.annotations:
            annotations_str = ', '.join(self._get_annotation_string(ann) for ann in method.annotations)
            context_parts.append(f"注解: {annotations_str}")
        
        # 添加方法签名
        params_str = ', '.join(parameters) if parameters else "无参数"
        signature = f"方法签名: {method.name}({params_str}) -> {return_type}"
        context_parts.append(signature)
        
        # 添加泛型信息
        if method.type_parameters:
            type_params = ', '.join(str(tp) for tp in method.type_parameters)
            context_parts.append(f"泛型参数: <{type_params}>")
        
        # 添加异常信息
        if method.throws:
            throws_str = ', '.join(str(exc) for exc in method.throws)
            context_parts.append(f"抛出异常: {throws_str}")
        
        return '; '.join(context_parts)
    
    def _log_parsing_stats(self):
        """记录解析统计信息"""
        self.logger.info("=== 源码解析统计 ===")
        self.logger.info(f"总文件数: {self.stats['total_files']}")
        self.logger.info(f"成功解析: {self.stats['parsed_files']}")
        self.logger.info(f"解析失败: {self.stats['failed_files']}")
        self.logger.info(f"回退解析: {self.stats['fallback_used']}")
        self.logger.info(f"总方法数: {self.stats['total_methods']}")
        self.logger.info(f"JDK8特性文件: {self.stats['jdk8_features_detected']}")
        
        if self.stats['total_files'] > 0:
            success_rate = (self.stats['parsed_files'] / self.stats['total_files']) * 100
            self.logger.info(f"javalang成功率: {success_rate:.1f}%")
        
        if self.failed_files:
            self.logger.warning(f"解析失败的文件: {list(self.failed_files)[:10]}...")  # 只显示前10个
    
    def _log_jdk8_stats(self, check_results):
        """记录JDK8特性统计信息"""
        if not check_results:
            return
        
        # 统计各种JDK8特性
        feature_stats = {
            'lambda_expressions': sum(1 for r in check_results if r.has_lambda),
            'method_references': sum(1 for r in check_results if r.has_method_reference),
            'stream_api': sum(1 for r in check_results if r.has_stream_api),
            'default_methods': sum(1 for r in check_results if r.has_default_methods),
            'optional_usage': sum(1 for r in check_results if r.has_optional)
        }
        
        total_features = sum(feature_stats.values())
        if total_features > 0:
            self.logger.info("=== JDK8特性检测统计 ===")
            for feature, count in feature_stats.items():
                if count > 0:
                    feature_name = feature.replace('_', ' ').title()
                    self.logger.info(f"{feature_name}: {count} 个文件")
        
        # 记录解析问题
        all_issues = []
        for result in check_results:
            all_issues.extend(result.parsing_issues)
        
        if all_issues:
            issue_counts = {}
            for issue in all_issues:
                issue_counts[issue] = issue_counts.get(issue, 0) + 1
            
            self.logger.warning("=== JDK8解析问题统计 ===")
            for issue, count in sorted(issue_counts.items(), key=lambda x: x[1], reverse=True)[:3]:
                self.logger.warning(f"{issue}: {count} 次")
    
    def get_parsing_stats(self) -> Dict[str, int]:
        """获取解析统计信息"""
        return self.stats.copy()
    
    def get_failed_files(self) -> Set[str]:
        """获取解析失败的文件列表"""
        return self.failed_files.copy()
    
    def reset_stats(self):
        """重置统计信息"""
        self.parsed_files.clear()
        self.failed_files.clear()
        self.stats = {
            'total_files': 0,
            'parsed_files': 0,
            'failed_files': 0,
            'total_methods': 0,
            'total_classes': 0,
            'jdk8_features_detected': 0,
            'fallback_used': 0
        }
    
    def get_jdk8_compatibility_info(self) -> Dict[str, any]:
        """
        获取JDK8兼容性信息
        
        Returns:
            JDK8兼容性信息字典
        """
        return {
            'jdk8_checker_available': True,
            'enhanced_parser_enabled': True,
            'fallback_strategy_available': True,
            'supported_features': [
                'Lambda表达式',
                '方法引用', 
                'Stream API',
                '接口默认方法',
                'Optional类'
            ],
            'stats': {
                'jdk8_features_detected': self.stats.get('jdk8_features_detected', 0),
                'fallback_used': self.stats.get('fallback_used', 0)
            }
        }