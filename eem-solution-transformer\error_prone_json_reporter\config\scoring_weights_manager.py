#!/usr/bin/env python3
"""
评分权重配置管理器

管理向量库构建和运行时匹配的评分权重配置，
确保两个阶段使用统一的权重标准。
"""

import os
import yaml
import logging
from typing import Dict, Any, Optional
from dataclasses import dataclass
from pathlib import Path


@dataclass
class ScoringWeights:
    """评分权重配置"""
    semantic_similarity: float = 0.75
    business_domain_match: float = 0.10
    method_name_similarity: float = 0.10
    other_factors: float = 0.05
    
    def __post_init__(self):
        """验证权重配置"""
        total = (self.semantic_similarity + self.business_domain_match + 
                self.method_name_similarity + self.other_factors)
        
        if abs(total - 1.0) > 0.01:
            raise ValueError(f"权重总和必须为1.0，当前为{total:.3f}")
        
        # 验证权重范围
        weights = [self.semantic_similarity, self.business_domain_match,
                  self.method_name_similarity, self.other_factors]
        
        for weight in weights:
            if weight < 0.0 or weight > 1.0:
                raise ValueError(f"权重必须在0.0-1.0范围内，当前有权重为{weight}")


@dataclass
class ScoreComponentConfig:
    """评分组件配置"""
    # 业务领域匹配组件权重
    package_weight: float = 0.4
    class_weight: float = 0.35
    method_weight: float = 0.25
    
    # 其他因素组件权重
    parameter_compatibility_weight: float = 0.5
    return_type_match_weight: float = 0.5
    
    # 权重调整参数
    high_similarity_threshold: float = 0.8
    high_similarity_bonus: float = 0.1
    low_similarity_threshold: float = 0.3
    low_similarity_penalty: float = 0.2


@dataclass
class ScoringConfig:
    """完整的评分配置"""
    weights: ScoringWeights
    components: ScoreComponentConfig
    precompute_scores: bool = True
    store_score_components: bool = True
    use_consistent_weights: bool = True
    min_score_threshold: float = 0.3
    max_candidates: int = 5


class ScoringWeightsManager:
    """评分权重配置管理器"""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化权重配置管理器
        
        Args:
            config_path: 配置文件路径，默认使用内置配置
        """
        self.logger = logging.getLogger(__name__)
        
        # 默认配置文件路径
        if config_path is None:
            current_dir = Path(__file__).parent
            config_path = current_dir / "scoring_weights.yaml"
        
        self.config_path = Path(config_path)
        self.config = self._load_config()
        
        self.logger.info(f"评分权重配置加载完成: {self.config_path}")
        self._log_weights()
    
    def _load_config(self) -> ScoringConfig:
        """加载配置文件"""
        try:
            if not self.config_path.exists():
                self.logger.warning(f"配置文件不存在: {self.config_path}，使用默认配置")
                return self._get_default_config()
            
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)
            
            # 解析权重配置
            weights_data = config_data.get('scoring_weights', {})
            weights = ScoringWeights(
                semantic_similarity=weights_data.get('semantic_similarity', 0.75),
                business_domain_match=weights_data.get('business_domain_match', 0.10),
                method_name_similarity=weights_data.get('method_name_similarity', 0.10),
                other_factors=weights_data.get('other_factors', 0.05)
            )
            
            # 解析组件配置
            components_data = config_data.get('score_components', {})
            business_domain_data = components_data.get('business_domain_match', {})
            other_factors_data = components_data.get('other_factors', {})
            weight_adjustment_data = config_data.get('weight_adjustment', {})
            
            components = ScoreComponentConfig(
                package_weight=business_domain_data.get('package_weight', 0.4),
                class_weight=business_domain_data.get('class_weight', 0.35),
                method_weight=business_domain_data.get('method_weight', 0.25),
                parameter_compatibility_weight=other_factors_data.get('parameter_compatibility_weight', 0.5),
                return_type_match_weight=other_factors_data.get('return_type_match_weight', 0.5),
                high_similarity_threshold=weight_adjustment_data.get('high_similarity_threshold', 0.8),
                high_similarity_bonus=weight_adjustment_data.get('high_similarity_bonus', 0.1),
                low_similarity_threshold=weight_adjustment_data.get('low_similarity_threshold', 0.3),
                low_similarity_penalty=weight_adjustment_data.get('low_similarity_penalty', 0.2)
            )
            
            # 解析其他配置
            vector_library_data = config_data.get('vector_library', {})
            runtime_matching_data = config_data.get('runtime_matching', {})
            
            return ScoringConfig(
                weights=weights,
                components=components,
                precompute_scores=vector_library_data.get('precompute_scores', True),
                store_score_components=vector_library_data.get('store_score_components', True),
                use_consistent_weights=runtime_matching_data.get('use_consistent_weights', True),
                min_score_threshold=runtime_matching_data.get('min_score_threshold', 0.3),
                max_candidates=runtime_matching_data.get('max_candidates', 5)
            )
            
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            self.logger.info("使用默认配置")
            return self._get_default_config()
    
    def _get_default_config(self) -> ScoringConfig:
        """获取默认配置"""
        return ScoringConfig(
            weights=ScoringWeights(),
            components=ScoreComponentConfig()
        )
    
    def get_weights(self) -> ScoringWeights:
        """获取评分权重"""
        return self.config.weights
    
    def get_components_config(self) -> ScoreComponentConfig:
        """获取组件配置"""
        return self.config.components
    
    def get_full_config(self) -> ScoringConfig:
        """获取完整配置"""
        return self.config
    
    def update_weights(self, **kwargs) -> None:
        """
        更新权重配置
        
        Args:
            **kwargs: 权重参数，如 semantic_similarity=0.8
        """
        # 创建新的权重配置
        current_weights = self.config.weights
        
        new_weights_dict = {
            'semantic_similarity': kwargs.get('semantic_similarity', current_weights.semantic_similarity),
            'business_domain_match': kwargs.get('business_domain_match', current_weights.business_domain_match),
            'method_name_similarity': kwargs.get('method_name_similarity', current_weights.method_name_similarity),
            'other_factors': kwargs.get('other_factors', current_weights.other_factors)
        }
        
        # 验证新权重
        new_weights = ScoringWeights(**new_weights_dict)
        
        # 更新配置
        self.config.weights = new_weights
        
        self.logger.info("权重配置已更新")
        self._log_weights()
    
    def save_config(self, output_path: Optional[str] = None) -> None:
        """
        保存配置到文件
        
        Args:
            output_path: 输出文件路径，默认覆盖原文件
        """
        if output_path is None:
            output_path = self.config_path
        else:
            output_path = Path(output_path)
        
        # 构建配置数据
        config_data = {
            'scoring_weights': {
                'semantic_similarity': self.config.weights.semantic_similarity,
                'business_domain_match': self.config.weights.business_domain_match,
                'method_name_similarity': self.config.weights.method_name_similarity,
                'other_factors': self.config.weights.other_factors
            },
            'score_components': {
                'business_domain_match': {
                    'package_weight': self.config.components.package_weight,
                    'class_weight': self.config.components.class_weight,
                    'method_weight': self.config.components.method_weight
                },
                'other_factors': {
                    'parameter_compatibility_weight': self.config.components.parameter_compatibility_weight,
                    'return_type_match_weight': self.config.components.return_type_match_weight
                }
            },
            'weight_adjustment': {
                'high_similarity_threshold': self.config.components.high_similarity_threshold,
                'high_similarity_bonus': self.config.components.high_similarity_bonus,
                'low_similarity_threshold': self.config.components.low_similarity_threshold,
                'low_similarity_penalty': self.config.components.low_similarity_penalty
            },
            'vector_library': {
                'precompute_scores': self.config.precompute_scores,
                'store_score_components': self.config.store_score_components
            },
            'runtime_matching': {
                'use_consistent_weights': self.config.use_consistent_weights,
                'min_score_threshold': self.config.min_score_threshold,
                'max_candidates': self.config.max_candidates
            }
        }
        
        try:
            # 确保目录存在
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                yaml.dump(config_data, f, default_flow_style=False, 
                         allow_unicode=True, indent=2)
            
            self.logger.info(f"配置已保存到: {output_path}")
            
        except Exception as e:
            self.logger.error(f"保存配置失败: {e}")
            raise
    
    def validate_config(self) -> bool:
        """验证配置有效性"""
        try:
            # 验证权重
            weights = self.config.weights
            total = (weights.semantic_similarity + weights.business_domain_match + 
                    weights.method_name_similarity + weights.other_factors)
            
            if abs(total - 1.0) > 0.01:
                self.logger.error(f"权重总和验证失败: {total:.3f} != 1.0")
                return False
            
            # 验证组件权重
            components = self.config.components
            business_total = (components.package_weight + components.class_weight + 
                            components.method_weight)
            
            if abs(business_total - 1.0) > 0.01:
                self.logger.error(f"业务领域组件权重总和验证失败: {business_total:.3f} != 1.0")
                return False
            
            other_total = (components.parameter_compatibility_weight + 
                          components.return_type_match_weight)
            
            if abs(other_total - 1.0) > 0.01:
                self.logger.error(f"其他因素组件权重总和验证失败: {other_total:.3f} != 1.0")
                return False
            
            self.logger.info("配置验证通过")
            return True
            
        except Exception as e:
            self.logger.error(f"配置验证失败: {e}")
            return False
    
    def _log_weights(self) -> None:
        """记录权重配置"""
        weights = self.config.weights
        self.logger.info("当前评分权重配置:")
        self.logger.info(f"  语义相似度: {weights.semantic_similarity:.1%}")
        self.logger.info(f"  业务领域匹配: {weights.business_domain_match:.1%}")
        self.logger.info(f"  方法名相似度: {weights.method_name_similarity:.1%}")
        self.logger.info(f"  其他因素: {weights.other_factors:.1%}")
    
    def get_weights_for_intelligent_scorer(self):
        """获取适用于智能评分器的权重配置"""
        from error_prone_json_reporter.stage2.intelligent_matching_scorer import MatchingWeights
        
        weights = self.config.weights
        return MatchingWeights(
            semantic_weight=weights.semantic_similarity,
            business_domain_weight=weights.business_domain_match,
            method_name_weight=weights.method_name_similarity,
            other_factors_weight=weights.other_factors
        )
    
    def create_enhanced_vector_library_config(self) -> Dict[str, Any]:
        """创建增强的向量库配置"""
        return {
            'scoring_weights': {
                'semantic_similarity': self.config.weights.semantic_similarity,
                'business_domain_match': self.config.weights.business_domain_match,
                'method_name_similarity': self.config.weights.method_name_similarity,
                'other_factors': self.config.weights.other_factors
            },
            'precompute_scores': self.config.precompute_scores,
            'store_score_components': self.config.store_score_components,
            'components_config': {
                'business_domain': {
                    'package_weight': self.config.components.package_weight,
                    'class_weight': self.config.components.class_weight,
                    'method_weight': self.config.components.method_weight
                },
                'other_factors': {
                    'parameter_compatibility_weight': self.config.components.parameter_compatibility_weight,
                    'return_type_match_weight': self.config.components.return_type_match_weight
                }
            }
        }


def main():
    """测试函数"""
    import logging
    logging.basicConfig(level=logging.INFO)
    
    # 创建权重管理器
    manager = ScoringWeightsManager()
    
    # 验证配置
    if manager.validate_config():
        print("✅ 配置验证通过")
    else:
        print("❌ 配置验证失败")
    
    # 显示当前权重
    weights = manager.get_weights()
    print(f"\n📊 当前权重配置:")
    print(f"  语义相似度: {weights.semantic_similarity:.1%}")
    print(f"  业务领域匹配: {weights.business_domain_match:.1%}")
    print(f"  方法名相似度: {weights.method_name_similarity:.1%}")
    print(f"  其他因素: {weights.other_factors:.1%}")
    
    # 测试权重更新
    print(f"\n🔄 测试权重更新...")
    try:
        manager.update_weights(semantic_similarity=0.8, business_domain_match=0.1)
        print("✅ 权重更新成功")
    except Exception as e:
        print(f"❌ 权重更新失败: {e}")


if __name__ == "__main__":
    main()