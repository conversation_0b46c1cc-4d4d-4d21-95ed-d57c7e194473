# Requirements Document

## Introduction

本功能旨在创建一个脚本工具，用于在当前Java工程中查询PO（Persistent Object）类的所有引用位置。该工具将帮助开发者快速定位某个PO类在当前项目中的使用情况，便于代码重构和依赖分析。

## Requirements

### Requirement 1

**User Story:** 作为一个Java开发者，我希望能够查询指定PO类在当前工程中的所有引用位置，以便了解该类的使用范围。

#### Acceptance Criteria

1. WHEN 用户提供PO类名 THEN 系统 SHALL 在当前工程中搜索所有引用该类的位置
2. WHEN 系统找到引用 THEN 系统 SHALL 显示文件路径、行号和具体的引用代码行
3. WHEN 系统完成搜索 THEN 系统 SHALL 提供引用统计信息（总引用数、涉及文件数）

### Requirement 2

**User Story:** 作为一个开发者，我希望脚本能够使用AST语法树分析来准确识别不同类型的引用，以便更好地理解代码依赖关系。

#### Acceptance Criteria

1. WHEN 系统分析Java文件 THEN 系统 SHALL 使用AST语法树解析代码结构
2. WHEN 系统检测到import语句 THEN 系统 SHALL 标记为"导入引用"
3. WHEN 系统检测到变量声明 THEN 系统 SHALL 标记为"变量声明"
4. WHEN 系统检测到方法参数 THEN 系统 SHALL 标记为"方法参数"
5. WHEN 系统检测到返回类型 THEN 系统 SHALL 标记为"返回类型"
6. WHEN 系统分析方法调用 THEN 系统 SHALL 识别方法名和参数类型

### Requirement 3

**User Story:** 作为一个开发者，我希望脚本支持简单的搜索模式，以便快速查找引用。

#### Acceptance Criteria

1. WHEN 用户指定类名 THEN 系统 SHALL 搜索该类名的所有引用
2. WHEN 系统搜索时 THEN 系统 SHALL 在当前工程的src目录中查找
3. WHEN 系统搜索时 THEN 系统 SHALL 只处理.java文件
4. WHEN 系统搜索时 THEN 系统 SHALL 排除target、build等构建目录

### Requirement 4

**User Story:** 作为一个开发者，我希望脚本能够追踪引用链，从直接使用PO对象的方法开始，向上查找到根节点，以便了解完整的调用链路。

#### Acceptance Criteria

1. WHEN 系统找到PO对象的直接引用 THEN 系统 SHALL 识别使用该对象的方法名
2. WHEN 系统识别到使用方法 THEN 系统 SHALL 继续向上查找调用该方法的位置
3. WHEN 系统追踪引用链 THEN 系统 SHALL 持续向上查找直到根节点（无更多上级调用）
4. WHEN 系统完成引用链追踪 THEN 系统 SHALL 构建完整的调用路径

### Requirement 5

**User Story:** 作为一个开发者，我希望脚本输出markdown格式的结果，包含详细的引用信息和完整的调用链，以便快速定位和分析。

#### Acceptance Criteria

1. WHEN 系统输出结果 THEN 系统 SHALL 使用markdown格式
2. WHEN 系统显示引用 THEN 系统 SHALL 包含package信息、行号列表、使用该对象的方法名和入参信息
3. WHEN 系统输出格式 THEN 系统 SHALL 遵循格式：package:xx, line:[1,2], methods:xx, 入参:xx
4. WHEN 系统显示调用链 THEN 系统 SHALL 展示从根节点到PO对象使用的完整路径
5. WHEN 系统完成搜索 THEN 系统 SHALL 提供汇总统计信息
6. IF 未找到引用 THEN 系统 SHALL 显示明确的"未找到引用"消息