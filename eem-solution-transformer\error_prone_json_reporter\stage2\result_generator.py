"""
结果生成器

生成迁移建议的JSON格式输出。
"""

import json
import logging
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
from pathlib import Path

from error_prone_json_reporter.common.models import MethodInfo, ErrorReport


class ResultGenerator:
    """
    结果生成器
    
    将原始错误信息和相似度匹配结果组合，生成标准化的迁移建议JSON输出。
    支持结果排序、过滤和统计信息生成。
    """
    
    def __init__(self, include_metadata: bool = True, 
                 include_statistics: bool = True,
                 sort_by_similarity: bool = True):
        """
        初始化结果生成器
        
        Args:
            include_metadata: 是否包含元数据
            include_statistics: 是否包含统计信息
            sort_by_similarity: 是否按相似度排序
        """
        self.logger = logging.getLogger(__name__)
        self.include_metadata = include_metadata
        self.include_statistics = include_statistics
        self.sort_by_similarity = sort_by_similarity
        
        # 统计信息
        self.stats = {
            'total_errors': 0,
            'errors_with_matches': 0,
            'errors_without_matches': 0,
            'total_candidates': 0,
            'avg_similarity': 0.0,
            'max_similarity': 0.0,
            'min_similarity': 1.0,
            'class_matches': 0,
            'package_matches': 0
        }
    
    def generate_suggestions(self, errors: List[Dict[str, Any]], 
                           matches: List[List[Tuple[MethodInfo, float]]],
                           output_file: Optional[str] = None) -> str:
        """
        生成迁移建议JSON
        
        Args:
            errors: 原始错误信息列表
            matches: 每个错误对应的匹配结果列表
            output_file: 输出文件路径（可选）
            
        Returns:
            JSON字符串
        """
        if len(errors) != len(matches):
            raise ValueError(f"错误数量({len(errors)})与匹配结果数量({len(matches)})不匹配")
        
        self.logger.info(f"开始生成迁移建议，错误数量: {len(errors)}")
        
        # 重置统计信息
        self._reset_stats()
        self.stats['total_errors'] = len(errors)
        
        # 生成建议列表
        suggestions = []
        for error, match_results in zip(errors, matches):
            suggestion = self._create_suggestion(error, match_results)
            suggestions.append(suggestion)
        
        # 按相似度排序（如果启用）
        if self.sort_by_similarity:
            suggestions = self._sort_suggestions(suggestions)
        
        # 构建最终结果
        result = {
            'suggestions': suggestions
        }
        
        # 添加元数据
        if self.include_metadata:
            result['metadata'] = self._generate_metadata()
        
        # 添加统计信息
        if self.include_statistics:
            result['statistics'] = self._generate_statistics()
        
        # 转换为JSON
        json_str = json.dumps(result, ensure_ascii=False, indent=2)
        
        # 保存到文件
        if output_file:
            self._save_to_file(json_str, output_file)
        
        self.logger.info("迁移建议生成完成")
        return json_str
    
    def _create_suggestion(self, error: Dict[str, Any], 
                          match_results: List[Tuple[MethodInfo, float]]) -> Dict[str, Any]:
        """
        为单个错误创建迁移建议
        
        Args:
            error: 错误信息
            match_results: 匹配结果列表
            
        Returns:
            迁移建议字典
        """
        # 基本错误信息
        suggestion = {
            'missing_method': error.get('missing_method', ''),
            'in_param': error.get('in_param', {}),
            'out_return': error.get('out_return', ''),
            'context': error.get('context', ''),
            'source_location': {
                'package': error.get('package', ''),
                'class': error.get('class', ''),
                'line': error.get('line', [])
            }
        }
        
        # 处理匹配结果
        candidates = []
        if match_results:
            self.stats['errors_with_matches'] += 1
            
            for method, similarity in match_results:
                candidate = self._create_candidate(method, similarity, error)
                candidates.append(candidate)
                
                # 更新统计信息
                self.stats['total_candidates'] += 1
                self.stats['avg_similarity'] += float(similarity)
                self.stats['max_similarity'] = max(self.stats['max_similarity'], float(similarity))
                self.stats['min_similarity'] = min(self.stats['min_similarity'], float(similarity))
        else:
            self.stats['errors_without_matches'] += 1
        
        suggestion['candidates'] = candidates
        suggestion['match_count'] = len(candidates)
        
        return suggestion
    
    def _create_candidate(self, method: MethodInfo, similarity: float, 
                         original_error: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建候选方法信息
        
        Args:
            method: 方法信息
            similarity: 相似度分数
            original_error: 原始错误信息
            
        Returns:
            候选方法字典
        """
        candidate = {
            'name': method.method_name,
            'class': method.class_name,
            'package': method.package,
            'params': method.parameters,
            'return': method.return_type,
            'context': method.context,
            'similarity': round(float(similarity), 4),  # 确保转换为Python float
            'file_path': method.file_path
        }
        
        # 计算额外的匹配指标
        additional_metrics = self._calculate_additional_metrics(method, original_error)
        candidate.update(additional_metrics)
        
        return candidate
    
    def _calculate_additional_metrics(self, method: MethodInfo, 
                                    original_error: Dict[str, Any]) -> Dict[str, Any]:
        """
        计算额外的匹配指标
        
        Args:
            method: 候选方法信息
            original_error: 原始错误信息
            
        Returns:
            额外指标字典
        """
        metrics = {}
        
        # 参数数量匹配
        original_params = original_error.get('in_param', {})
        param_count_match = len(method.parameters) == len(original_params)
        metrics['param_count_match'] = param_count_match
        
        # 参数名称匹配
        param_name_match = self._calculate_param_name_match(method.parameters, original_params)
        metrics['param_name_match'] = param_name_match
        
        # 返回类型名称匹配
        original_return = original_error.get('out_return', '')
        return_name_match = self._calculate_return_name_match(method.return_type, original_return)
        metrics['return_name_match'] = return_name_match
        
        # 类全名匹配
        original_class = original_error.get('class', '')
        original_package = original_error.get('package', '')
        full_original_class = f"{original_package}.{original_class}" if original_package else original_class
        full_candidate_class = f"{method.package}.{method.class_name}" if method.package else method.class_name
        class_full_match = full_original_class == full_candidate_class
        metrics['class_full_match'] = class_full_match
        
        # 方法名相似度
        original_method = original_error.get('missing_method', '')
        name_similarity = self._calculate_name_similarity(method.method_name, original_method)
        metrics['name_similarity'] = round(float(name_similarity), 4)
        
        # 综合匹配分数
        match_score = self._calculate_match_score(metrics)
        metrics['match_score'] = round(float(match_score), 4)
        
        return metrics
    
    def _is_class_match(self, candidate_class: str, original_class: str) -> bool:
        """检查类名是否匹配"""
        if not candidate_class or not original_class:
            return False
        
        # 精确匹配
        if candidate_class == original_class:
            return True
        
        # 简单名称匹配（忽略包名）
        candidate_simple = candidate_class.split('.')[-1]
        original_simple = original_class.split('.')[-1]
        
        return candidate_simple == original_simple
    
    def _is_package_match(self, candidate_package: str, original_package: str) -> bool:
        """检查包名是否匹配"""
        if not candidate_package or not original_package:
            return False
        
        # 精确匹配
        if candidate_package == original_package:
            return True
        
        # 部分匹配（共同前缀）
        candidate_parts = candidate_package.split('.')
        original_parts = original_package.split('.')
        
        # 至少有2个共同的包层级
        common_parts = 0
        for c, o in zip(candidate_parts, original_parts):
            if c == o:
                common_parts += 1
            else:
                break
        
        return common_parts >= 2
    
    def _is_return_type_match(self, candidate_return: str, original_return: str) -> bool:
        """检查返回类型是否匹配"""
        if not candidate_return or not original_return:
            return False
        
        # 简化类型名称进行比较
        candidate_simple = self._simplify_type_name(candidate_return)
        original_simple = self._simplify_type_name(original_return)
        
        return candidate_simple == original_simple
    
    def _simplify_type_name(self, type_name: str) -> str:
        """简化类型名称"""
        if not type_name:
            return ""
        
        # 移除泛型参数
        if '<' in type_name:
            type_name = type_name.split('<')[0]
        
        # 获取简单类名
        if '.' in type_name:
            type_name = type_name.split('.')[-1]
        
        return type_name.lower()
    
    def _calculate_param_name_match(self, candidate_params: List[str], original_params: Dict[str, str]) -> float:
        """计算参数名称匹配度"""
        if not candidate_params and not original_params:
            return 1.0  # 都没有参数，完全匹配
        
        if len(candidate_params) != len(original_params):
            return 0.0  # 参数数量不同，不匹配
        
        if not candidate_params:  # 都是空参数列表
            return 1.0
        
        # 提取候选方法的参数名
        candidate_names = []
        for param in candidate_params:
            if ':' in param:
                # 格式: "param_name: Type"
                param_name = param.split(':')[0].strip()
            else:
                # 只有参数名
                param_name = param.strip()
            candidate_names.append(param_name.lower())
        
        # 提取原始方法的参数名
        original_names = [name.lower() for name in original_params.keys()]
        
        # 计算名称相似度
        total_similarity = 0.0
        for i in range(min(len(candidate_names), len(original_names))):
            similarity = self._levenshtein_similarity(candidate_names[i], original_names[i])
            total_similarity += similarity
        
        return total_similarity / len(candidate_names) if candidate_names else 0.0
    
    def _calculate_return_name_match(self, candidate_return: str, original_return: str) -> float:
        """计算返回类型名称匹配度"""
        if not candidate_return or not original_return:
            return 0.0
        
        # 提取简单类型名（去掉包名和泛型）
        candidate_simple = self._extract_simple_type_name(candidate_return)
        original_simple = self._extract_simple_type_name(original_return)
        
        # 计算名称相似度
        return self._levenshtein_similarity(candidate_simple.lower(), original_simple.lower())
    
    def _extract_simple_type_name(self, type_name: str) -> str:
        """提取简单类型名称"""
        if not type_name:
            return ""
        
        # 移除泛型参数
        if '<' in type_name:
            type_name = type_name.split('<')[0]
        
        # 移除数组标记
        if '[' in type_name:
            type_name = type_name.split('[')[0]
        
        # 获取简单类名（去掉包名）
        if '.' in type_name:
            type_name = type_name.split('.')[-1]
        
        return type_name.strip()
    

    def _calculate_name_similarity(self, candidate_name: str, original_name: str) -> float:
        """计算方法名相似度"""
        if not candidate_name or not original_name:
            return 0.0
        
        # 提取方法名（移除参数）
        if '(' in original_name:
            original_name = original_name.split('(')[0]
        
        # 简单的编辑距离相似度
        return self._levenshtein_similarity(candidate_name.lower(), original_name.lower())
    
    def _levenshtein_similarity(self, s1: str, s2: str) -> float:
        """计算Levenshtein相似度"""
        if not s1 or not s2:
            return 0.0
        
        if s1 == s2:
            return 1.0
        
        # 计算编辑距离
        m, n = len(s1), len(s2)
        dp = [[0] * (n + 1) for _ in range(m + 1)]
        
        for i in range(m + 1):
            dp[i][0] = i
        for j in range(n + 1):
            dp[0][j] = j
        
        for i in range(1, m + 1):
            for j in range(1, n + 1):
                if s1[i-1] == s2[j-1]:
                    dp[i][j] = dp[i-1][j-1]
                else:
                    dp[i][j] = 1 + min(dp[i-1][j], dp[i][j-1], dp[i-1][j-1])
        
        # 转换为相似度
        max_len = max(m, n)
        if max_len == 0:
            return 1.0
        
        return 1.0 - (dp[m][n] / max_len)
    
    def _calculate_match_score(self, metrics: Dict[str, Any]) -> float:
        """计算综合匹配分数"""
        score = 0.0
        
        # 各项指标的权重 - 基于名称匹配的简化策略
        weights = {
            'name_similarity': 0.3,      # 方法名相似度 - 最重要
            'param_name_match': 0.25,    # 参数名称匹配 - 很重要
            'param_count_match': 0.2,    # 参数数量匹配 - 重要
            'return_name_match': 0.15,   # 返回类型名称匹配 - 重要
            'class_full_match': 0.1      # 类全名匹配 - 次要
        }
        
        for metric, weight in weights.items():
            if metric in metrics:
                if isinstance(metrics[metric], bool):
                    score += weight if metrics[metric] else 0
                else:
                    score += weight * metrics[metric]
        
        return score
    
    def _sort_suggestions(self, suggestions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """按相似度排序建议"""
        def get_max_similarity(suggestion):
            candidates = suggestion.get('candidates', [])
            if not candidates:
                return 0.0
            return max(candidate.get('similarity', 0.0) for candidate in candidates)
        
        return sorted(suggestions, key=get_max_similarity, reverse=True)
    
    def _generate_metadata(self) -> Dict[str, Any]:
        """生成元数据"""
        return {
            'generated_at': datetime.now().isoformat(),
            'generator_version': '1.0.0',
            'configuration': {
                'include_metadata': self.include_metadata,
                'include_statistics': self.include_statistics,
                'sort_by_similarity': self.sort_by_similarity
            }
        }
    
    def _generate_statistics(self) -> Dict[str, Any]:
        """生成统计信息"""
        stats = self.stats.copy()
        
        # 计算平均相似度
        if stats['total_candidates'] > 0:
            stats['avg_similarity'] = stats['avg_similarity'] / stats['total_candidates']
        
        # 计算匹配率
        if stats['total_errors'] > 0:
            stats['match_rate'] = stats['errors_with_matches'] / stats['total_errors']
        else:
            stats['match_rate'] = 0.0
        
        # 计算各种匹配的比例
        if stats['total_candidates'] > 0:
            stats['class_match_rate'] = stats['class_matches'] / stats['total_candidates']
            stats['package_match_rate'] = stats['package_matches'] / stats['total_candidates']
        else:
            stats['class_match_rate'] = 0.0
            stats['package_match_rate'] = 0.0
        
        # 四舍五入数值
        for key in ['avg_similarity', 'match_rate', 'class_match_rate', 'package_match_rate']:
            if key in stats:
                stats[key] = round(float(stats[key]), 4)
        
        return stats
    
    def _save_to_file(self, json_str: str, output_file: str):
        """保存JSON到文件"""
        try:
            output_path = Path(output_file)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(json_str)
            
            self.logger.info(f"迁移建议已保存到: {output_file}")
            
        except Exception as e:
            self.logger.error(f"保存文件失败: {str(e)}")
            raise
    
    def _reset_stats(self):
        """重置统计信息"""
        self.stats = {
            'total_errors': 0,
            'errors_with_matches': 0,
            'errors_without_matches': 0,
            'total_candidates': 0,
            'avg_similarity': 0.0,
            'max_similarity': 0.0,
            'min_similarity': 1.0,
            'class_matches': 0,
            'package_matches': 0
        }
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return self.stats.copy()
    
    def export_csv(self, suggestions: List[Dict[str, Any]], output_file: str):
        """导出为CSV格式"""
        try:
            import csv
            
            with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = [
                    'missing_method', 'source_package', 'source_class',
                    'candidate_name', 'candidate_class', 'candidate_package',
                    'similarity', 'match_score', 'class_match', 'package_match'
                ]
                
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                
                for suggestion in suggestions:
                    source_info = {
                        'missing_method': suggestion.get('missing_method', ''),
                        'source_package': suggestion.get('source_location', {}).get('package', ''),
                        'source_class': suggestion.get('source_location', {}).get('class', '')
                    }
                    
                    for candidate in suggestion.get('candidates', []):
                        row = source_info.copy()
                        row.update({
                            'candidate_name': candidate.get('name', ''),
                            'candidate_class': candidate.get('class', ''),
                            'candidate_package': candidate.get('package', ''),
                            'similarity': candidate.get('similarity', 0.0),
                            'match_score': candidate.get('match_score', 0.0),
                            'class_match': candidate.get('class_match', False),
                            'package_match': candidate.get('package_match', False)
                        })
                        writer.writerow(row)
            
            self.logger.info(f"CSV文件已保存到: {output_file}")
            
        except Exception as e:
            self.logger.error(f"导出CSV失败: {str(e)}")
            raise