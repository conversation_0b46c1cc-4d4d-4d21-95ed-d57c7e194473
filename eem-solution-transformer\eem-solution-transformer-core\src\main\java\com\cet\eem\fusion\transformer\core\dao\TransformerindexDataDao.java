package com.cet.eem.fusion.transformer.core.dao;

import com.cet.eem.fusion.energy.sdk.model.EemPoiRecord;
import com.cet.eem.fusion.transformer.core.entity.po.TransformerindexData;

import java.time.LocalDateTime;
import java.util.List;

public interface TransformerindexDataDao {
    List<TransformerindexData> save(List<TransformerindexData> avgloadandpowers);

    List<TransformerindexData> queryByTimes(List<LocalDateTime> times);

    List<TransformerindexData> queryByParams(Long logTime,Integer cycle,Integer type,Boolean desc);

    List<TransformerindexData> queryByTimes(List<LocalDateTime> times,Integer type,Integer cycle);

    List<EemPoiRecord> saveEemPoiRecord(List<EemPoiRecord> pois);
}
