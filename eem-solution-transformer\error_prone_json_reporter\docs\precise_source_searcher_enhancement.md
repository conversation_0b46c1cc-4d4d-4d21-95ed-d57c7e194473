# 精确源码搜索器增强功能

## 概述

根据任务6.1.1的要求，我们对`PreciseSourceSearcher`类进行了重大增强，实现了基于错误位置信息的精确源码搜索功能。

## 新增功能

### 1. 基于错误位置的精确搜索

新增了`search_method_by_error_location`方法，支持根据具体的错误位置信息在旧源码中查找缺失方法：

```python
def search_method_by_error_location(self, error_file_path: str, error_line: int, 
                                   missing_class: str, missing_method: str) -> Optional[MethodLocation]:
```

**参数说明：**
- `error_file_path`: 报错文件路径
- `error_line`: 报错行号
- `missing_class`: 缺失的类名
- `missing_method`: 缺失的方法名

### 2. 两阶段搜索策略

实现了明确的两阶段搜索策略：

#### 第一阶段：旧源码类搜索
- 只在旧源码中搜索，定位缺失方法所在的类
- 根据类名在旧源码中定位目标类文件
- 使用AST解析验证类的存在

#### 第二阶段：方法源码提取
- 从迁移前的源码里定位到类的源码，找到缺失方法的源码
- 使用AST解析提取完整的方法定义和上下文
- 如果在旧源码中未找到类，则搜索项目依赖的jar包

### 3. 增强的方法边界识别

- 使用javalang AST解析器进行精确的方法边界识别
- 支持提取完整的方法代码块，包括注释和注解
- 准确计算方法的起始和结束行号

### 4. 简化的方法匹配

- 基于方法名进行匹配，不进行复杂的参数类型检查
- 对于重载方法，返回第一个匹配的方法
- 简化逻辑，专注于快速定位方法源码

### 5. jar包集成支持

- 集成Maven classpath构建功能
- 支持从jar包中读取源码（如果可用）
- 缓存jar包路径和内容，提升搜索效率

## 核心方法

### `_find_class_in_legacy_source`
在旧源码中查找指定类，返回包含文件路径、AST、内容等信息的字典。

### `_extract_method_from_class`
从类信息中提取指定方法，支持参数匹配和重载处理。

### `_search_method_in_jars`
在jar包中搜索方法，支持从jar包源码中提取完整方法实现。

### `_get_classpath_jars`
获取项目classpath中的所有jar包路径，使用Maven构建依赖。

## 数据结构增强

### `MethodLocation`类增强
新增了jar包相关的属性：

```python
class MethodLocation:
    def __init__(self, file_path: str, start_line: int, end_line: int, 
                 method_code: str, class_context: str, import_statements: List[str],
                 surrounding_methods: List[str], is_from_jar: bool = False, 
                 jar_entry: Optional[str] = None):
```

- `is_from_jar`: 标识方法是否来自jar包
- `jar_entry`: jar包内的条目路径

## 使用示例

```python
# 初始化搜索器
searcher = PreciseSourceSearcher(legacy_src_path, project_path)

# 根据错误位置搜索方法
result = searcher.search_method_by_error_location(
    error_file_path="/path/to/Controller.java",
    error_line=25,
    missing_class="UserService",
    missing_method="findById"
)

if result:
    print(f"找到方法: {result.file_path}:{result.start_line}")
    print(f"来自jar包: {result.is_from_jar}")
    print(f"方法代码:\n{result.method_code}")
```

## 性能优化

### 缓存机制
- 文件内容缓存：避免重复读取同一文件
- AST缓存：避免重复解析同一文件
- Java文件列表缓存：避免重复扫描目录
- jar包路径缓存：避免重复构建classpath

### 搜索策略优化
- 优先搜索旧源码，减少jar包搜索开销
- 使用文件名匹配快速定位候选文件
- 支持精确匹配和模糊匹配的组合策略

## 测试覆盖

创建了全面的测试套件 `test_precise_source_searcher_enhanced.py`，包括：

1. 基于错误位置的方法搜索测试
2. 类查找功能测试
3. 方法提取功能测试
4. 简化的方法重载处理测试
5. 缓存功能测试

## 演示脚本

提供了完整的演示脚本 `demo_precise_source_searcher.py`，展示：

1. 成功查找方法的场景
2. 简化的方法匹配处理
3. 未找到方法的处理
4. 缓存统计信息

## 兼容性

- 保持了原有`search_method_by_error`方法的向后兼容性
- 新功能通过新方法`search_method_by_error_location`提供
- 支持Windows和Linux平台的Maven可执行文件检测

## 错误处理

- 完善的异常处理和日志记录
- 优雅处理文件编码问题
- 支持跳过损坏的jar包文件
- 提供详细的错误信息和调试日志

## 总结

通过这次增强，`PreciseSourceSearcher`现在能够：

1. ✅ 根据错误位置信息精确搜索旧源码中的方法实现
2. ✅ 支持两阶段搜索策略（先搜索旧源码类，再提取方法源码）
3. ✅ 提供精确的方法边界识别和代码提取
4. ✅ 简化的方法匹配（基于方法名）
5. ✅ 集成jar包搜索功能作为备选方案
6. ✅ 实现高效的缓存机制
7. ✅ 提供全面的测试覆盖

这些功能完全满足了任务6.1.1的所有要求，为后续的智能分析和API匹配提供了坚实的基础。