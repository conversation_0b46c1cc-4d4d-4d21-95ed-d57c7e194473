# Design Document

## Overview

PO引用查找工具是一个通用的Python脚本，用于分析Java项目中PO（Persistent Object）类的引用关系和调用链。该工具使用AST语法树分析技术，能够准确识别类的使用位置，并向上追踪调用链直到根节点，最终输出markdown格式的分析报告。

## Architecture

### 核心组件架构

```
po_reference_finder.py
├── JavaASTParser          # Java AST解析器
├── ReferenceAnalyzer      # 引用分析器
├── CallChainTracker       # 调用链追踪器
├── MarkdownReporter       # Markdown报告生成器
└── CLIInterface           # 命令行接口
```

### 工作流程

1. **输入处理**: 接收PO类名和项目路径参数
2. **文件扫描**: 递归扫描Java源文件，排除构建目录
3. **AST解析**: 解析每个Java文件生成语法树
4. **引用识别**: 在AST中查找目标PO类的所有引用
5. **调用链追踪**: 从引用点向上追踪到根节点
6. **报告生成**: 输出markdown格式的分析结果

## Components and Interfaces

### 1. JavaASTParser

**职责**: 解析Java源文件生成抽象语法树

```python
class JavaASTParser:
    def parse_file(self, file_path: str) -> AST
    def extract_imports(self, ast: AST) -> List[ImportInfo]
    def extract_class_info(self, ast: AST) -> ClassInfo
    def extract_methods(self, ast: AST) -> List[MethodInfo]
```

### 2. ReferenceAnalyzer

**职责**: 在AST中识别PO类的各种引用类型

```python
class ReferenceAnalyzer:
    def find_references(self, ast: AST, target_class: str) -> List[Reference]
    def analyze_import_references(self, ast: AST, target_class: str) -> List[Reference]
    def analyze_variable_declarations(self, ast: AST, target_class: str) -> List[Reference]
    def analyze_method_parameters(self, ast: AST, target_class: str) -> List[Reference]
    def analyze_return_types(self, ast: AST, target_class: str) -> List[Reference]
```

### 3. CallChainTracker

**职责**: 追踪方法调用链，从使用点向上查找到根节点

```python
class CallChainTracker:
    def build_call_graph(self, project_files: List[str]) -> CallGraph
    def trace_to_root(self, method_name: str, class_name: str) -> List[CallChain]
    def find_method_callers(self, method_name: str) -> List[CallerInfo]
```

### 4. MarkdownReporter

**职责**: 生成markdown格式的分析报告

```python
class MarkdownReporter:
    def generate_report(self, analysis_result: AnalysisResult) -> str
    def format_reference_info(self, reference: Reference) -> str
    def format_call_chain(self, call_chain: CallChain) -> str
```

## Data Models

### Reference

```python
@dataclass
class Reference:
    file_path: str
    package_name: str
    class_name: str
    method_name: str
    line_numbers: List[int]
    reference_type: ReferenceType  # IMPORT, VARIABLE, PARAMETER, RETURN_TYPE
    parameters: List[str]
    context_code: str
```

### CallChain

```python
@dataclass
class CallChain:
    root_method: MethodInfo
    chain_path: List[MethodCall]
    target_reference: Reference
    depth: int
```

### MethodInfo

```python
@dataclass
class MethodInfo:
    name: str
    class_name: str
    package_name: str
    parameters: List[Parameter]
    return_type: str
    line_number: int
    file_path: str
```

### AnalysisResult

```python
@dataclass
class AnalysisResult:
    target_class: str
    total_references: int
    affected_files: int
    references: List[Reference]
    call_chains: List[CallChain]
    statistics: Dict[str, Any]
```

## Technical Implementation

### AST解析技术选择

使用`javalang`库进行Java AST解析：
- 纯Python实现，无需Java环境
- 支持完整的Java语法解析
- 提供详细的AST节点信息

### 引用识别策略

1. **Import语句识别**: 解析import声明，匹配目标类
2. **类型声明识别**: 在变量声明、方法参数、返回类型中查找
3. **泛型支持**: 处理`List<PO>`等泛型类型引用
4. **内部类支持**: 识别内部类和静态内部类引用

### 调用链追踪算法

1. **构建调用图**: 扫描所有方法调用关系
2. **深度优先搜索**: 从引用点开始向上追踪
3. **循环检测**: 避免无限递归调用
4. **根节点识别**: 识别入口点（Controller、Service等）

## Error Handling

### 文件处理错误

- **文件不存在**: 跳过并记录警告
- **编码错误**: 尝试多种编码格式
- **解析失败**: 记录错误文件，继续处理其他文件

### AST解析错误

- **语法错误**: 跳过有语法错误的文件
- **不完整代码**: 尽力解析可用部分
- **版本兼容**: 处理不同Java版本的语法差异

### 调用链分析错误

- **循环依赖**: 检测并标记循环调用
- **外部依赖**: 标记无法追踪的外部调用
- **动态调用**: 识别反射等动态调用模式

## Testing Strategy

### 单元测试

1. **AST解析测试**: 测试各种Java语法结构的解析
2. **引用识别测试**: 验证不同引用类型的识别准确性
3. **调用链测试**: 测试调用链追踪的正确性

### 集成测试

1. **端到端测试**: 使用示例Java项目进行完整流程测试
2. **性能测试**: 测试大型项目的处理性能
3. **输出格式测试**: 验证markdown输出格式的正确性

### 测试数据

创建包含各种引用模式的测试Java文件：
- 简单PO类引用
- 泛型PO类引用
- 继承关系的PO类
- 复杂调用链场景

## Performance Considerations

### 内存优化

- 流式处理大文件
- 及时释放AST对象
- 使用生成器减少内存占用

### 处理速度优化

- 并行处理多个文件
- 缓存AST解析结果
- 智能跳过无关文件

### 可扩展性

- 支持插件式引用类型扩展
- 可配置的输出格式
- 支持自定义过滤规则