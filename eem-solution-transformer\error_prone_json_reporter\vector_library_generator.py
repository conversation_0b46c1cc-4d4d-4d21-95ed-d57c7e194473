#!/usr/bin/env python3
"""
向量库生成工具

用于预生成Java源码项目的向量库，提高后续API匹配的性能。
"""

import os
import sys
import argparse
import logging
import time
from pathlib import Path
from typing import List, Dict, Any, Optional
import json

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from error_prone_json_reporter.config.config_manager import ConfigManager
from error_prone_json_reporter.config.scoring_weights_manager import ScoringWeightsManager
from error_prone_json_reporter.common.logging_config import setup_logging
from error_prone_json_reporter.stage2.source_code_parser import SourceCodeParser
from error_prone_json_reporter.stage2.vector_encoder import VectorEncoder
from error_prone_json_reporter.common.models import MethodInfo


class VectorLibraryGenerator:
    """
    向量库生成器
    
    扫描Java源码项目，解析所有方法，并生成向量库。
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化向量库生成器
        
        Args:
            config_path: 配置文件路径
        """
        self.logger = logging.getLogger(__name__)
        
        # 加载配置
        self.config_manager = ConfigManager()
        if config_path:
            self.config = self.config_manager.load_config(config_path)
        else:
            self.config = self.config_manager.load_config()
        
        # 加载评分权重配置
        self.scoring_weights_manager = ScoringWeightsManager()
        self.scoring_config = self.scoring_weights_manager.get_full_config()
        
        # 初始化组件
        self.parser = None
        self.encoder = None
        
        # 统计信息
        self.stats = {
            'total_files': 0,
            'parsed_files': 0,
            'failed_files': 0,
            'total_methods': 0,
            'encoded_methods': 0,
            'cache_hits': 0,
            'processing_time': 0.0,
            'encoding_time': 0.0
        }
        
        self._initialize_components()
    
    def _initialize_components(self):
        """初始化源码解析器和向量编码器"""
        try:
            # 初始化源码解析器
            self.parser = SourceCodeParser()
            self.logger.info("源码解析器初始化成功")
            
            # 初始化向量编码器
            model_name = self.config.model_name
            cache_dir = self.config.vector_cache_dir if self.config.vector_cache_dir else None
            batch_size = self.config.batch_size
            use_cache = self.config.cache_vectors
            
            self.encoder = VectorEncoder(
                model_name=model_name,
                cache_dir=cache_dir,
                batch_size=batch_size,
                use_cache=use_cache
            )
            self.logger.info("向量编码器初始化成功")
            
        except Exception as e:
            self.logger.error(f"组件初始化失败: {str(e)}")
            raise
    
    def generate_vector_library(self, src_path: str, output_path: Optional[str] = None) -> Dict[str, Any]:
        """
        生成向量库
        
        Args:
            src_path: Java源码路径
            output_path: 输出统计信息的路径（可选）
            
        Returns:
            生成统计信息
        """
        self.logger.info(f"开始生成向量库，源码路径: {src_path}")
        start_time = time.time()
        
        try:
            # 扫描Java文件
            java_files = self._scan_java_files(src_path)
            self.stats['total_files'] = len(java_files)
            self.logger.info(f"发现 {len(java_files)} 个Java文件")
            
            if not java_files:
                self.logger.warning("未发现Java文件")
                return self.stats
            
            # 解析所有方法
            all_methods = self._parse_all_methods(java_files)
            self.stats['total_methods'] = len(all_methods)
            self.logger.info(f"解析得到 {len(all_methods)} 个方法")
            
            if not all_methods:
                self.logger.warning("未解析到任何方法")
                return self.stats
            
            # 生成向量
            self._generate_vectors(all_methods)
            
            # 计算总时间
            self.stats['processing_time'] = time.time() - start_time
            
            # 获取编码器统计信息
            encoder_stats = self.encoder.get_stats()
            self.stats.update({
                'cache_hits': encoder_stats.get('cache_hits', 0),
                'encoding_time': encoder_stats.get('encoding_time', 0.0),
                'encoded_methods': encoder_stats.get('total_encoded', 0)
            })
            
            # 输出统计信息
            self._print_statistics()
            
            # 保存统计信息
            if output_path:
                self._save_statistics(output_path)
            
            self.logger.info("向量库生成完成")
            return self.stats
            
        except Exception as e:
            self.logger.error(f"向量库生成失败: {str(e)}")
            raise
    
    def _scan_java_files(self, src_path: str) -> List[str]:
        """
        扫描Java文件
        
        Args:
            src_path: 源码路径
            
        Returns:
            Java文件路径列表
        """
        java_files = []
        src_path = Path(src_path)
        
        if not src_path.exists():
            raise FileNotFoundError(f"源码路径不存在: {src_path}")
        
        if src_path.is_file() and src_path.suffix == '.java':
            java_files.append(str(src_path))
        else:
            # 递归扫描目录
            for java_file in src_path.rglob("*.java"):
                java_files.append(str(java_file))
        
        return java_files
    
    def _parse_all_methods(self, java_files: List[str]) -> List[MethodInfo]:
        """
        解析所有Java文件中的方法
        
        Args:
            java_files: Java文件路径列表
            
        Returns:
            方法信息列表
        """
        all_methods = []
        
        for i, java_file in enumerate(java_files):
            try:
                self.logger.debug(f"解析文件 ({i+1}/{len(java_files)}): {java_file}")
                
                # 解析单个文件
                methods = self.parser.parse_single_file(java_file)
                all_methods.extend(methods)
                self.stats['parsed_files'] += 1
                
                # 进度报告
                if (i + 1) % 100 == 0:
                    self.logger.info(f"已解析 {i+1}/{len(java_files)} 个文件，获得 {len(all_methods)} 个方法")
                
            except Exception as e:
                self.logger.warning(f"解析文件失败 {java_file}: {str(e)}")
                self.stats['failed_files'] += 1
                continue
        
        return all_methods
    
    def _generate_vectors(self, methods: List[MethodInfo]):
        """
        为方法列表生成向量（包含AI语义分析）
        
        Args:
            methods: 方法信息列表
        """
        self.logger.info(f"开始为 {len(methods)} 个方法生成向量（包含AI语义分析）")
        self.logger.info(f"使用评分权重配置: 语义{self.scoring_config.weights.semantic_similarity:.1%}, "
                        f"业务领域{self.scoring_config.weights.business_domain_match:.1%}, "
                        f"方法名{self.scoring_config.weights.method_name_similarity:.1%}, "
                        f"其他因素{self.scoring_config.weights.other_factors:.1%}")
        
        try:
            # 检查是否启用AI增强编码
            use_ai_enhancement = getattr(self.config, 'use_ai_enhancement', True)
            
            if use_ai_enhancement:
                self.logger.info("使用AI增强的向量编码")
                vectors = self._generate_vectors_with_ai_analysis(methods)
            else:
                self.logger.info("使用基础向量编码")
                vectors = self.encoder.encode_methods(methods)
            
            # 如果配置了预计算评分，则在向量库中存储评分组件
            if self.scoring_config.precompute_scores:
                self.logger.info("预计算评分组件并存储到向量库")
                self._precompute_and_store_scores(methods, vectors)
            
            self.logger.info(f"成功生成 {len(vectors)} 个向量")
            
        except Exception as e:
            self.logger.error(f"向量生成失败: {str(e)}")
            raise
    
    def _generate_vectors_with_ai_analysis(self, methods: List[MethodInfo]):
        """使用AI语义分析生成增强向量"""
        from error_prone_json_reporter.stage2.source_context_analyzer import SourceContextAnalyzer
        from error_prone_json_reporter.stage2.knowledge_base_query_enhancer import KnowledgeBaseQueryEnhancer
        from error_prone_json_reporter.stage2.precise_source_searcher import MethodLocation
        
        # 初始化AI分析器 - 传递已加载的配置以避免重复加载
        analyzer = SourceContextAnalyzer(config_manager=self.config_manager, config=self.config)
        kb_enhancer = KnowledgeBaseQueryEnhancer(
            knowledge_base_path=getattr(self.config, 'knowledge_base_path', 'knowledge_base'),
            use_ai_enhancement=True
        )
        
        semantic_analyses = []
        knowledge_base_infos = []
        
        self.logger.info("开始批量AI语义分析...")
        
        # 分批处理以避免内存问题
        batch_size = 100
        total_batches = (len(methods) + batch_size - 1) // batch_size
        
        for batch_idx in range(total_batches):
            start_idx = batch_idx * batch_size
            end_idx = min(start_idx + batch_size, len(methods))
            batch_methods = methods[start_idx:end_idx]
            
            self.logger.info(f"处理批次 {batch_idx + 1}/{total_batches} ({len(batch_methods)} 个方法)")
            
            # 为当前批次的方法进行AI分析
            for method in batch_methods:
                try:
                    # 构建方法位置信息
                    method_location = MethodLocation(
                        file_path=method.file_path,
                        start_line=0,
                        end_line=0,
                        method_code=self._reconstruct_method_code(method),
                        class_context=f"class {method.class_name}",
                        import_statements=[],
                        surrounding_methods=[]
                    )
                    
                    # 执行AI语义分析
                    semantics = analyzer.analyze_method_with_llm(method_location)
                    
                    # 转换为字典格式
                    semantic_analysis = {
                        'function_description': semantics.function_description,
                        'business_purpose': semantics.business_purpose,
                        'parameter_analysis': semantics.parameter_analysis,
                        'return_value_meaning': semantics.return_value_meaning,
                        'usage_scenarios': semantics.usage_scenarios,
                        'business_tags': semantics.business_tags,
                        'complexity_score': semantics.complexity_score,
                        'importance_score': semantics.importance_score,
                        'llm_confidence': semantics.llm_confidence
                    }
                    semantic_analyses.append(semantic_analysis)
                    
                    # 查询知识库信息
                    kb_result = kb_enhancer.query_and_enhance(method)
                    kb_info = {
                        'found': kb_result.found,
                        'description': kb_result.enhanced_description or kb_result.description,
                        'confidence': kb_result.confidence,
                        'semantic_tags': kb_result.semantic_tags
                    }
                    knowledge_base_infos.append(kb_info)
                    
                except Exception as e:
                    self.logger.warning(f"方法AI分析失败 {method.method_name}: {e}")
                    # 使用空的分析结果
                    semantic_analyses.append({})
                    knowledge_base_infos.append({'found': False})
        
        self.logger.info("AI语义分析完成，开始增强向量编码...")
        
        # 使用增强特征进行向量编码
        vectors = self.encoder.encode_enhanced_methods(
            methods,
            semantic_analyses,
            knowledge_base_infos
        )
        
        return vectors
    
    def _precompute_and_store_scores(self, methods: List[MethodInfo], vectors):
        """预计算评分组件并存储到向量库"""
        self.logger.info("开始预计算评分组件...")
        
        # 这里可以预计算一些基础的评分组件
        # 比如业务领域标签、方法名特征等
        for i, method in enumerate(methods):
            try:
                # 提取业务领域特征
                business_domain_features = self._extract_business_domain_features(method)
                
                # 提取方法名特征
                method_name_features = self._extract_method_name_features(method)
                
                # 将特征存储到向量库中（这里需要根据实际的向量存储格式调整）
                # 目前只是记录日志
                if i < 5:  # 只记录前5个作为示例
                    self.logger.debug(f"方法 {method.method_name} 的预计算特征:")
                    self.logger.debug(f"  业务领域: {business_domain_features}")
                    self.logger.debug(f"  方法名特征: {method_name_features}")
                
            except Exception as e:
                self.logger.warning(f"预计算方法 {method.method_name} 的评分组件失败: {e}")
                continue
        
        self.logger.info("评分组件预计算完成")
    
    def _extract_business_domain_features(self, method: MethodInfo) -> Dict[str, Any]:
        """提取业务领域特征"""
        features = {}
        
        # 从包名提取业务领域
        if method.package:
            domain_keywords = self._extract_business_domain_keywords_from_package(method.package)
            features['package_domains'] = domain_keywords
        
        # 从类名提取业务特征
        if method.class_name:
            class_features = self._extract_business_features_from_class(method.class_name)
            features['class_features'] = class_features
        
        # 从方法名提取业务操作
        if method.method_name:
            operations = self._extract_business_operations_from_method(method.method_name)
            features['operations'] = operations
        
        return features
    
    def _extract_method_name_features(self, method: MethodInfo) -> Dict[str, Any]:
        """提取方法名特征"""
        features = {}
        
        if method.method_name:
            # 提取单词
            words = self._extract_words_from_camel_case(method.method_name)
            features['words'] = words
            
            # 提取前缀（动词）
            if words:
                features['prefix'] = words[0].lower()
            
            # 计算方法名长度特征
            features['length'] = len(method.method_name)
            features['word_count'] = len(words)
        
        return features
    
    def _extract_business_domain_keywords_from_package(self, package_name: str) -> List[str]:
        """从包名提取业务领域关键词"""
        business_domains = [
            'user', 'project', 'energy', 'data', 'report', 'config', 'system',
            'service', 'dao', 'entity', 'model', 'controller', 'manager',
            'tree', 'node', 'dim', 'fact', 'analysis', 'monitor', 'alert',
            'fusion', 'common', 'base', 'core', 'util'
        ]
        
        package_lower = package_name.lower()
        found_keywords = []
        
        for domain in business_domains:
            if domain in package_lower:
                found_keywords.append(domain)
        
        return found_keywords
    
    def _extract_business_features_from_class(self, class_name: str) -> List[str]:
        """从类名提取业务特征"""
        words = self._extract_words_from_camel_case(class_name)
        
        # 过滤掉技术词汇
        technical_words = {'service', 'dao', 'impl', 'controller', 'manager', 'util', 'helper'}
        business_words = [word.lower() for word in words if word.lower() not in technical_words]
        
        return business_words
    
    def _extract_business_operations_from_method(self, method_name: str) -> List[str]:
        """从方法名提取业务操作"""
        words = self._extract_words_from_camel_case(method_name)
        
        business_operations = [
            'get', 'find', 'query', 'search', 'list', 'select',
            'create', 'add', 'insert', 'save', 'store',
            'update', 'modify', 'change', 'edit',
            'delete', 'remove', 'drop',
            'count', 'sum', 'calculate', 'compute',
            'validate', 'check', 'verify',
            'export', 'import', 'sync', 'process'
        ]
        
        found_operations = []
        for word in words:
            if word.lower() in business_operations:
                found_operations.append(word.lower())
        
        return found_operations
    
    def _extract_words_from_camel_case(self, text: str) -> List[str]:
        """从驼峰命名中提取单词"""
        import re
        words = re.findall(r'[A-Z]?[a-z]+|[A-Z]+(?=[A-Z][a-z]|\b)', text)
        return [word for word in words if word]
    
    def _reconstruct_method_code(self, method: MethodInfo) -> str:
        """重构方法代码（用于AI分析）"""
        # 构建基本的方法代码结构
        modifiers = " ".join(method.modifiers) if method.modifiers else "public"
        params = ", ".join(method.parameters) if method.parameters else ""
        
        method_code = f"""
{modifiers} {method.return_type} {method.method_name}({params}) {{
    // {method.context}
    // 方法实现...
}}
"""
        return method_code
    
    def _print_statistics(self):
        """打印统计信息"""
        self.logger.info("=" * 60)
        self.logger.info("向量库生成统计信息")
        self.logger.info("=" * 60)
        self.logger.info(f"总文件数: {self.stats['total_files']}")
        self.logger.info(f"成功解析: {self.stats['parsed_files']}")
        self.logger.info(f"解析失败: {self.stats['failed_files']}")
        self.logger.info(f"总方法数: {self.stats['total_methods']}")
        self.logger.info(f"编码方法数: {self.stats['encoded_methods']}")
        self.logger.info(f"缓存命中: {self.stats['cache_hits']}")
        self.logger.info(f"处理时间: {self.stats['processing_time']:.2f} 秒")
        self.logger.info(f"编码时间: {self.stats['encoding_time']:.2f} 秒")
        
        if self.stats['total_methods'] > 0:
            cache_hit_rate = (self.stats['cache_hits'] / self.stats['total_methods']) * 100
            self.logger.info(f"缓存命中率: {cache_hit_rate:.1f}%")
        
        self.logger.info("=" * 60)
    
    def _save_statistics(self, output_path: str):
        """
        保存统计信息到文件
        
        Args:
            output_path: 输出文件路径
        """
        try:
            output_path = Path(output_path)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(self.stats, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"统计信息已保存到: {output_path}")
            
        except Exception as e:
            self.logger.error(f"保存统计信息失败: {str(e)}")
    
    def get_cache_info(self) -> Dict[str, Any]:
        """
        获取缓存信息
        
        Returns:
            缓存信息
        """
        if self.encoder:
            return {
                'cache_size': self.encoder.get_cache_size(),
                'cache_dir': str(self.encoder.cache_dir),
                'stats': self.encoder.get_stats()
            }
        return {}
    
    def clear_cache(self):
        """清空向量缓存"""
        if self.encoder:
            self.encoder.clear_cache()
            self.logger.info("向量缓存已清空")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="Java源码向量库生成工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 使用默认配置生成向量库
  python vector_library_generator.py --src-path /path/to/java/project
  
  # 指定配置文件
  python vector_library_generator.py --config config.yaml --src-path /path/to/java/project
  
  # 生成向量库并保存统计信息
  python vector_library_generator.py --src-path /path/to/java/project --output stats.json
  
  # 清空缓存后重新生成
  python vector_library_generator.py --src-path /path/to/java/project --clear-cache
        """
    )
    
    parser.add_argument(
        '--src-path', '-s',
        required=True,
        help='Java源码路径'
    )
    
    parser.add_argument(
        '--config', '-c',
        help='配置文件路径（默认使用 config/config.yaml）'
    )
    
    parser.add_argument(
        '--output', '-o',
        help='统计信息输出文件路径'
    )
    
    parser.add_argument(
        '--clear-cache',
        action='store_true',
        help='生成前清空向量缓存'
    )
    
    parser.add_argument(
        '--cache-info',
        action='store_true',
        help='显示缓存信息'
    )
    
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='详细输出'
    )
    
    args = parser.parse_args()
    
    try:
        # 设置日志
        log_level = logging.DEBUG if args.verbose else logging.INFO
        setup_logging(level=log_level)
        
        # 创建生成器
        generator = VectorLibraryGenerator(args.config)
        
        # 显示缓存信息
        if args.cache_info:
            cache_info = generator.get_cache_info()
            print(f"缓存目录: {cache_info.get('cache_dir', 'N/A')}")
            print(f"缓存文件数: {cache_info.get('cache_size', 0)}")
            return
        
        # 清空缓存
        if args.clear_cache:
            generator.clear_cache()
        
        # 生成向量库
        stats = generator.generate_vector_library(args.src_path, args.output)
        
        print(f"\n向量库生成完成！")
        print(f"处理了 {stats['total_methods']} 个方法")
        print(f"用时 {stats['processing_time']:.2f} 秒")
        
    except KeyboardInterrupt:
        print("\n用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"错误: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()