#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re
import os

def complete_task_import():
    """完成task-import.md文件，处理所有剩余的类问题"""
    
    print("=" * 60)
    print("完成task-import.md文件 - 处理所有剩余类问题")
    print("=" * 60)
    
    # 读取源文件
    source_file = 'eem-solution-transformer/out/问题列表.md'
    task_file = 'eem-solution-transformer/out/task-import.md'
    
    with open(source_file, 'r', encoding='utf-8') as f:
        source_content = f.read()
    
    # 分割文件内容，提取所有文件和问题
    file_sections = re.split(r'^## ([A-Za-z][A-Za-z0-9_]*)', source_content, flags=re.MULTILINE)[1:]
    
    # 已处理的文件（从task-import.md中提取）
    processed_files = {'DateUtil', 'LoadRateVo', 'OverviewDataVo', 'PowerTransformerDaoImpl', 'PowerTransformerDto', 'ProjectDto'}
    
    # 生成剩余文件的内容
    additional_content = []
    
    for i in range(0, len(file_sections), 2):
        if i + 1 < len(file_sections):
            file_name = file_sections[i]
            file_content = file_sections[i + 1]
            
            # 跳过已处理的文件
            if file_name in processed_files:
                continue
            
            # 提取该文件的所有类问题
            problems = []
            problem_matches = list(re.finditer(r'### 问题 (\d+)\s*\nerror_type: "类问题"', file_content))
            
            if not problem_matches:
                continue
                
            additional_content.append(f"\n## {file_name}\n")
            
            for j, problem_match in enumerate(problem_matches):
                problem_num = problem_match.group(1)
                problem_start = problem_match.start()
                
                # 找到下一个问题的开始位置
                if j + 1 < len(problem_matches):
                    problem_end = problem_matches[j + 1].start()
                else:
                    # 查找下一个文件的开始
                    next_file_match = re.search(r'^## [A-Za-z]', file_content[problem_start + 1:], re.MULTILINE)
                    if next_file_match:
                        problem_end = problem_start + 1 + next_file_match.start()
                    else:
                        problem_end = len(file_content)
                
                problem_text = file_content[problem_start:problem_end].strip()
                
                # 解析问题信息
                missing_class_match = re.search(r'missing_class: "([^"]+)"', problem_text)
                line_match = re.search(r'line: \["([^"]+)"\]', problem_text)
                suggest_match = re.search(r'suggest: "([^"]+)"', problem_text)
                error_code_match = re.search(r'error_code: "([^"]+)"', problem_text)
                calling_method_match = re.search(r'calling_method: "([^"]+)"', problem_text)
                
                missing_class = missing_class_match.group(1) if missing_class_match else "未知"
                line_info = line_match.group(1) if line_match else "未知"
                suggest = suggest_match.group(1) if suggest_match else None
                error_code = error_code_match.group(1) if error_code_match else "类问题"
                calling_method = calling_method_match.group(1) if calling_method_match else None
                
                # 根据问题类型生成解决方案
                if suggest:
                    # 有建议的问题（通常是方法重构）
                    additional_content.append(f"### 类问题 {j+1}: {missing_class if missing_class != '未知' else calling_method} 方法重构 (🔴 红色标记)\n")
                    additional_content.append(f"- **问题位置**: 行号 {line_info}")
                    if missing_class != "未知":
                        additional_content.append(f"- **缺失类名**: {missing_class}")
                    if calling_method:
                        additional_content.append(f"- **调用方法**: {calling_method}")
                    additional_content.append(f"- **调用类**: {file_name}")
                    additional_content.append(f"- **建议**: {suggest}")
                    additional_content.append("- **解决方案**: 方法重构，需要按照建议进行代码调整")
                    additional_content.append("- **修复操作**: 根据建议重构代码逻辑")
                    additional_content.append("- **分类依据**: 方法重构，需要代码逻辑调整\n")
                elif missing_class.isupper() or '_' in missing_class:
                    # 常量类问题
                    additional_content.append(f"### 类问题 {j+1}: {missing_class} 常量 (🔴 红色标记)\n")
                    additional_content.append(f"- **问题位置**: 行号 {line_info}")
                    additional_content.append(f"- **缺失类名**: {missing_class}")
                    additional_content.append(f"- **调用类**: {file_name}")
                    additional_content.append("- **解决方案**: 常量重构，需要查找新的常量定义位置")
                    additional_content.append("- **修复操作**: 查找新的常量定义位置")
                    additional_content.append("- **分类依据**: 常量重构问题\n")
                else:
                    # 普通类导入问题
                    additional_content.append(f"### 类问题 {j+1}: {missing_class} 类导入 (🟡 黄色标记)\n")
                    additional_content.append(f"- **问题位置**: 行号 {line_info}")
                    additional_content.append(f"- **缺失类名**: {missing_class}")
                    additional_content.append(f"- **调用类**: {file_name}")
                    additional_content.append("- **解决方案**: 需要查找类的新位置")
                    additional_content.append("- **修复操作**: ")
                    additional_content.append("  1. 使用class_name_finder.py查找类")
                    additional_content.append("  2. 确定正确的import路径")
                    additional_content.append("  3. 更新import语句")
                    additional_content.append("- **分类依据**: 需要使用工具查找正确的类路径\n")
            
            additional_content.append("---\n")
    
    # 读取现有的task-import.md文件
    with open(task_file, 'r', encoding='utf-8') as f:
        existing_content = f.read()
    
    # 找到插入位置（在"## 处理说明"之前）
    insert_pos = existing_content.find("## 处理说明")
    if insert_pos == -1:
        print("❌ 未找到插入位置")
        return False
    
    # 插入新内容
    new_content = existing_content[:insert_pos] + ''.join(additional_content) + existing_content[insert_pos:]
    
    # 写入文件
    with open(task_file, 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print(f"✅ 已完成task-import.md文件扩展")
    print(f"📊 添加了 {len([f for f in file_sections[::2] if f not in processed_files])} 个文件的问题分析")
    
    return True

if __name__ == '__main__':
    complete_task_import()
