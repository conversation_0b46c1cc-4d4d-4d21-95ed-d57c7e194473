"""
错误分组和去重功能实现
"""
import logging
from typing import List, Dict
from collections import defaultdict
try:
    from .interfaces import ErrorGrouperInterface
    from .models import CompilationError, ErrorStatistics
except ImportError:
    from interfaces import ErrorGrouperInterface
    from models import CompilationError, ErrorStatistics


class ErrorGrouper(ErrorGrouperInterface):
    """错误分组器实现"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def group_by_file(self, errors: List[CompilationError]) -> Dict[str, List[CompilationError]]:
        """按文件路径分组错误"""
        self.logger.info(f"开始按文件分组 {len(errors)} 个错误")
        
        grouped_errors = defaultdict(list)
        
        for error in errors:
            file_path = error.file_path
            grouped_errors[file_path].append(error)
        
        # 对每个文件的错误按行号排序
        for file_path in grouped_errors:
            grouped_errors[file_path].sort(key=lambda e: e.line)
        
        # 转换为普通字典并按文件路径排序
        result = dict(sorted(grouped_errors.items()))
        
        self.logger.info(f"错误已分组到 {len(result)} 个文件中")
        return result
    
    def deduplicate_errors(self, errors: List[CompilationError]) -> List[CompilationError]:
        """去除重复错误 - 保守去重，确保不遗漏问题"""
        self.logger.info(f"开始去重 {len(errors)} 个错误")
        
        # 使用更精确的去重策略，避免误删重要错误
        seen = set()
        unique_errors = []
        
        for error in errors:
            # 创建更精确的去重键，包含更多上下文信息
            dedup_key = (
                error.file_path,
                error.line,
                error.description,
                error.highlighted_element,
                error.offset  # 添加偏移量确保同一行不同位置的错误不被误删
            )
            
            if dedup_key not in seen:
                seen.add(dedup_key)
                unique_errors.append(error)
            else:
                # 记录被去重的错误，便于调试
                self.logger.debug(f"去重错误: {error.file_path}:{error.line} - {error.description}")
        
        # 按文件路径和行号排序
        unique_errors.sort(key=lambda e: (e.file_path, e.line, e.offset))
        
        removed_count = len(errors) - len(unique_errors)
        self.logger.info(f"保守去重完成，移除了 {removed_count} 个重复错误，剩余 {len(unique_errors)} 个")
        
        # 验证去重结果
        self._validate_deduplication(errors, unique_errors)
        
        return unique_errors
    
    def _validate_deduplication(self, original_errors: List[CompilationError], unique_errors: List[CompilationError]):
        """验证去重结果，确保没有遗漏重要错误"""
        original_files = set(e.file_path for e in original_errors)
        unique_files = set(e.file_path for e in unique_errors)
        
        if original_files != unique_files:
            missing_files = original_files - unique_files
            self.logger.warning(f"去重后缺失文件: {missing_files}")
        
        # 检查每个文件的错误数量变化
        original_file_counts = {}
        unique_file_counts = {}
        
        for error in original_errors:
            original_file_counts[error.file_path] = original_file_counts.get(error.file_path, 0) + 1
        
        for error in unique_errors:
            unique_file_counts[error.file_path] = unique_file_counts.get(error.file_path, 0) + 1
        
        for file_path in original_file_counts:
            original_count = original_file_counts[file_path]
            unique_count = unique_file_counts.get(file_path, 0)
            reduction_rate = (original_count - unique_count) / original_count if original_count > 0 else 0
            
            if reduction_rate > 0.8:  # 如果某个文件的错误减少超过80%，记录警告
                self.logger.warning(f"文件 {file_path} 错误数量大幅减少: {original_count} -> {unique_count} ({reduction_rate:.1%})")
        
        self.logger.info("去重验证完成")
    
    def group_and_deduplicate(self, errors: List[CompilationError]) -> Dict[str, List[CompilationError]]:
        """组合操作：先去重再分组"""
        self.logger.info("开始执行去重和分组操作")
        
        # 先去重
        unique_errors = self.deduplicate_errors(errors)
        
        # 再分组
        grouped_errors = self.group_by_file(unique_errors)
        
        return grouped_errors
    
    def get_error_statistics(self, grouped_errors: Dict[str, List[CompilationError]]) -> ErrorStatistics:
        """获取错误统计信息"""
        total_errors = sum(len(errors) for errors in grouped_errors.values())
        files_with_errors = len(grouped_errors)
        
        avg_errors_per_file = total_errors / files_with_errors if files_with_errors > 0 else 0.0
        
        # 按错误数量排序的文件列表
        most_problematic_files = sorted(
            [(file_path, len(errors)) for file_path, errors in grouped_errors.items()],
            key=lambda x: x[1],
            reverse=True
        )[:10]  # 前10个错误最多的文件
        
        # 错误类型分布统计
        error_types_distribution = {}
        for errors in grouped_errors.values():
            for error in errors:
                error_type = error.severity
                error_types_distribution[error_type] = error_types_distribution.get(error_type, 0) + 1
        
        return ErrorStatistics(
            total_errors=total_errors,
            files_with_errors=files_with_errors,
            average_errors_per_file=avg_errors_per_file,
            most_problematic_files=most_problematic_files,
            error_types_distribution=error_types_distribution
        )