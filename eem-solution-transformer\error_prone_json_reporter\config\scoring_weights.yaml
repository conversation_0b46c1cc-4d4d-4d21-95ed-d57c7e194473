# 向量库构建和方法匹配评分权重配置
# 
# 此配置文件定义了代码迁移工具中向量库构建和运行时匹配的评分权重
# 确保向量库构建阶段和查询阶段使用统一的权重标准

# 评分权重配置 (总和必须为1.0)
scoring_weights:
  # 语义相似度权重 - 基于AI语义理解的方法功能匹配度
  semantic_similarity: 0.75
  
  # 业务领域匹配权重 - 基于包名、类名、方法名的业务领域相关性
  business_domain_match: 0.10
  
  # 方法名相似度权重 - 基于方法名的字面相似性
  method_name_similarity: 0.10
  
  # 其他因素权重 - 包括参数兼容性、返回类型匹配等
  other_factors: 0.05

# 向量库构建配置
vector_library:
  # 是否在构建时预计算评分权重
  precompute_scores: true
  
  # 是否存储详细的评分组件
  store_score_components: true
  
  # 评分缓存配置
  score_caching:
    enabled: true
    cache_dir: "cache/scores"
    
# 运行时匹配配置
runtime_matching:
  # 是否使用与向量库构建相同的权重
  use_consistent_weights: true
  
  # 最低匹配分数阈值
  min_score_threshold: 0.3
  
  # 返回的候选方法数量
  max_candidates: 5

# 权重调整策略
weight_adjustment:
  # 高语义相似度奖励阈值
  high_similarity_threshold: 0.8
  high_similarity_bonus: 0.1
  
  # 低语义相似度惩罚阈值  
  low_similarity_threshold: 0.3
  low_similarity_penalty: 0.2
  
  # 业务领域匹配奖励
  business_domain_bonus: 0.05
  
  # 方法名完全匹配奖励
  exact_method_name_bonus: 0.1

# 评分组件详细配置
score_components:
  # 语义相似度组件
  semantic_similarity:
    weight: 0.75
    confidence_threshold: 0.7
    
  # 业务领域匹配组件
  business_domain_match:
    weight: 0.10
    package_weight: 0.4    # 包名业务领域权重
    class_weight: 0.35     # 类名业务领域权重  
    method_weight: 0.25    # 方法名业务领域权重
    
  # 方法名相似度组件
  method_name_similarity:
    weight: 0.10
    exact_match_bonus: 0.1
    case_insensitive_bonus: 0.05
    
  # 其他因素组件
  other_factors:
    weight: 0.05
    parameter_compatibility_weight: 0.5  # 参数兼容性权重
    return_type_match_weight: 0.5        # 返回类型匹配权重

# 验证配置
validation:
  # 是否验证权重总和
  validate_weight_sum: true
  
  # 权重总和容差
  weight_sum_tolerance: 0.01
  
  # 是否验证权重范围
  validate_weight_range: true
  
  # 权重最小值
  min_weight: 0.0
  
  # 权重最大值
  max_weight: 1.0

# 日志配置
logging:
  # 是否记录权重使用情况
  log_weight_usage: true
  
  # 是否记录评分详情
  log_score_details: false
  
  # 是否记录权重调整
  log_weight_adjustments: true