"""
相似度匹配器

使用 FAISS 进行高效的向量相似度检索。
"""

import logging
import pickle
from typing import List, Dict, Tuple, Optional, Any
from pathlib import Path
import numpy as np

try:
    import faiss
    FAISS_AVAILABLE = True
except ImportError:
    raise ImportError(
        "FAISS库是必需的依赖，请安装后再使用：\n"
        "pip install faiss-cpu  # CPU版本\n"
        "pip install faiss-gpu  # GPU版本（需要CUDA支持）"
    )

from error_prone_json_reporter.common.interfaces import SimilarityMatcherInterface
from error_prone_json_reporter.common.models import MethodInfo, MatchResult


class SimilarityMatcher(SimilarityMatcherInterface):
    """
    相似度匹配器
    
    使用FAISS建立向量索引，进行高效的TOP-K相似度检索。
    支持批量查询和结果过滤。
    """
    
    def __init__(self, top_k: int = 3, similarity_threshold: float = 0.1,
                 index_type: str = "flat", cache_dir: Optional[str] = None):
        """
        初始化相似度匹配器
        
        Args:
            top_k: 返回的最相似结果数量
            similarity_threshold: 相似度阈值
            index_type: 索引类型 ("flat", "ivf", "hnsw")
            cache_dir: 缓存目录
        """
        self.logger = logging.getLogger(__name__)
        self.top_k = top_k
        self.similarity_threshold = similarity_threshold
        self.index_type = index_type
        
        # 设置缓存目录
        if cache_dir:
            self.cache_dir = Path(cache_dir)
        else:
            self.cache_dir = Path.home() / ".cache" / "error_prone_json_reporter" / "faiss"
        
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # FAISS索引和相关数据
        self.index = None
        self.candidate_methods: List[MethodInfo] = []
        self.dimension = None
        self.index_built = False
        
        # 统计信息
        self.stats = {
            'total_candidates': 0,
            'total_queries': 0,
            'index_build_time': 0.0,
            'search_time': 0.0,
            'avg_similarity': 0.0
        }
        
        # FAISS现在是必需的依赖，在导入时已经检查
    
    def build_index(self, vectors: np.ndarray, methods: List[MethodInfo]) -> None:
        """
        构建FAISS索引
        
        Args:
            vectors: 候选方法的向量矩阵 (n_methods, embedding_dim)
            methods: 候选方法信息列表
        """
        if len(vectors) != len(methods):
            raise ValueError(f"向量数量({len(vectors)})与方法数量({len(methods)})不匹配")
        
        self.logger.info(f"开始构建索引，候选方法数量: {len(methods)}")
        
        import time
        start_time = time.time()
        
        # 保存候选方法信息和向量
        self.candidate_methods = methods
        self.dimension = vectors.shape[1] if len(vectors) > 0 else 0
        self.stats['total_candidates'] = len(methods)
        
        # FAISS现在是必需的，不需要降级处理
        
        # 确保向量是连续的float32数组
        vectors = np.ascontiguousarray(vectors.astype(np.float32))
        
        # 标准化向量（用于余弦相似度）
        vectors = self._normalize_vectors(vectors)
        
        # 根据索引类型创建索引
        self.index = self._create_index(vectors)
        
        # 添加向量到索引
        self.index.add(vectors)
        
        build_time = time.time() - start_time
        self.stats['index_build_time'] = build_time
        self.index_built = True
        
        self.logger.info(f"索引构建完成，耗时: {build_time:.2f}秒")
        
        # 缓存索引
        self._save_index()
    
    def search_similar(self, query_vectors: np.ndarray) -> List[List[Tuple[MethodInfo, float]]]:
        """
        搜索相似方法
        
        Args:
            query_vectors: 查询向量矩阵 (n_queries, embedding_dim)
            
        Returns:
            每个查询的相似方法列表，每个方法包含(MethodInfo, similarity_score)
        """
        if not self.index_built:
            self.logger.error("索引未构建，无法进行搜索")
            return []
        
        # FAISS现在是必需的，不需要降级处理
        
        self.logger.info(f"开始搜索，查询数量: {len(query_vectors)}")
        
        import time
        start_time = time.time()
        
        # 确保查询向量格式正确
        query_vectors = np.ascontiguousarray(query_vectors.astype(np.float32))
        query_vectors = self._normalize_vectors(query_vectors)
        
        # 执行搜索
        distances, indices = self.index.search(query_vectors, self.top_k)
        
        search_time = time.time() - start_time
        self.stats['search_time'] += search_time
        self.stats['total_queries'] += len(query_vectors)
        
        # 转换结果
        results = []
        total_similarity = 0.0
        valid_results = 0
        
        for i, (query_distances, query_indices) in enumerate(zip(distances, indices)):
            query_results = []
            
            for distance, idx in zip(query_distances, query_indices):
                if idx == -1:  # FAISS返回-1表示没有找到足够的结果
                    continue
                
                # 将距离转换为相似度分数
                similarity = self._distance_to_similarity(distance)
                
                # 应用相似度阈值
                if similarity >= self.similarity_threshold:
                    method = self.candidate_methods[idx]
                    query_results.append((method, similarity))
                    total_similarity += similarity
                    valid_results += 1
            
            # 按相似度排序
            query_results.sort(key=lambda x: x[1], reverse=True)
            results.append(query_results)
        
        # 更新统计信息
        if valid_results > 0:
            self.stats['avg_similarity'] = total_similarity / valid_results
        
        self.logger.info(f"搜索完成，耗时: {search_time:.2f}秒，平均相似度: {self.stats['avg_similarity']:.3f}")
        
        return results
    
    def search_single(self, query_vector: np.ndarray) -> List[Tuple[MethodInfo, float]]:
        """
        搜索单个查询的相似方法
        
        Args:
            query_vector: 查询向量 (embedding_dim,)
            
        Returns:
            相似方法列表
        """
        query_vectors = query_vector.reshape(1, -1)
        results = self.search_similar(query_vectors)
        return results[0] if results else []
    
    def _create_index(self, vectors: np.ndarray) -> Any:
        """
        根据配置创建FAISS索引
        
        Args:
            vectors: 向量矩阵
            
        Returns:
            FAISS索引
        """
        dimension = vectors.shape[1]
        
        if self.index_type == "flat":
            # 使用内积索引（适合标准化向量的余弦相似度）
            index = faiss.IndexFlatIP(dimension)
            self.logger.info("使用Flat索引（精确搜索）")
            
        elif self.index_type == "ivf":
            # 使用IVF索引（近似搜索，适合大数据集）
            nlist = min(100, max(1, len(vectors) // 100))  # 聚类数量
            quantizer = faiss.IndexFlatIP(dimension)
            index = faiss.IndexIVFFlat(quantizer, dimension, nlist)
            
            # 训练索引
            self.logger.info(f"使用IVF索引，聚类数量: {nlist}")
            index.train(vectors)
            
        elif self.index_type == "hnsw":
            # 使用HNSW索引（图索引，快速近似搜索）
            index = faiss.IndexHNSWFlat(dimension, 32)
            index.hnsw.efConstruction = 200
            index.hnsw.efSearch = 100
            self.logger.info("使用HNSW索引（图索引）")
            
        else:
            self.logger.warning(f"未知索引类型: {self.index_type}，使用默认Flat索引")
            index = faiss.IndexFlatIP(dimension)
        
        return index
    
    def _normalize_vectors(self, vectors: np.ndarray) -> np.ndarray:
        """
        标准化向量（L2标准化）
        
        Args:
            vectors: 向量矩阵
            
        Returns:
            标准化后的向量矩阵
        """
        norms = np.linalg.norm(vectors, axis=1, keepdims=True)
        # 避免除零
        norms = np.where(norms == 0, 1, norms)
        return vectors / norms
    
    def _distance_to_similarity(self, distance: float) -> float:
        """
        将距离转换为相似度分数
        
        对于内积索引，距离就是余弦相似度（因为向量已标准化）
        
        Args:
            distance: FAISS返回的距离
            
        Returns:
            相似度分数 (0-1)
        """
        # 对于标准化向量的内积，结果范围是[-1, 1]
        # 转换为[0, 1]范围
        similarity = (distance + 1) / 2
        return max(0.0, min(1.0, similarity))
    

    
    def _save_index(self):
        """保存索引到缓存"""
        try:
            if not self.index_built:
                return
            
            index_file = self.cache_dir / "faiss_index.bin"
            methods_file = self.cache_dir / "candidate_methods.pkl"
            
            # 保存FAISS索引
            faiss.write_index(self.index, str(index_file))
            
            # 保存候选方法信息
            with open(methods_file, 'wb') as f:
                pickle.dump(self.candidate_methods, f)
            
            self.logger.info("索引已缓存")
            
        except Exception as e:
            self.logger.warning(f"保存索引失败: {str(e)}")
    
    def load_index(self) -> bool:
        """
        从缓存加载索引
        
        Returns:
            是否成功加载
        """
        try:
            index_file = self.cache_dir / "faiss_index.bin"
            methods_file = self.cache_dir / "candidate_methods.pkl"
            
            if not (index_file.exists() and methods_file.exists()):
                return False
            
            # 加载FAISS索引
            self.index = faiss.read_index(str(index_file))
            
            # 加载候选方法信息
            with open(methods_file, 'rb') as f:
                self.candidate_methods = pickle.load(f)
            
            self.dimension = self.index.d
            self.index_built = True
            self.stats['total_candidates'] = len(self.candidate_methods)
            
            self.logger.info(f"从缓存加载索引成功，候选方法数量: {len(self.candidate_methods)}")
            return True
            
        except Exception as e:
            self.logger.warning(f"加载索引失败: {str(e)}")
            return False
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = self.stats.copy()
        stats['index_built'] = self.index_built
        stats['index_type'] = self.index_type
        stats['top_k'] = self.top_k
        stats['similarity_threshold'] = self.similarity_threshold
        stats['dimension'] = self.dimension
        stats['faiss_available'] = True  # FAISS现在是必需的
        return stats
    
    def clear_cache(self):
        """清空缓存"""
        try:
            import shutil
            if self.cache_dir.exists():
                shutil.rmtree(self.cache_dir)
                self.cache_dir.mkdir(parents=True, exist_ok=True)
                self.logger.info("FAISS缓存已清空")
        except Exception as e:
            self.logger.error(f"清空缓存失败: {str(e)}")
    
    def update_similarity_threshold(self, threshold: float):
        """更新相似度阈值"""
        self.similarity_threshold = threshold
        self.logger.info(f"相似度阈值已更新为: {threshold}")
    
    def update_top_k(self, top_k: int):
        """更新TOP-K数量"""
        self.top_k = top_k
        self.logger.info(f"TOP-K已更新为: {top_k}")