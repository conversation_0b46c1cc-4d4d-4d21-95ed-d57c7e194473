"""
源码上下文智能分析器

扩展现有的源码解析器，增加根据报错内容精确搜索源码方法的功能，
并使用大模型分析方法的业务逻辑、功能用途和调用意图。
"""

import os
import logging
import re
import json
from typing import List, Dict, Optional, Set, Tuple, Any
from pathlib import Path

from error_prone_json_reporter.common.models import MethodInfo, ErrorReport, Configuration
from error_prone_json_reporter.stage2.source_code_parser import SourceCodeParser
from error_prone_json_reporter.stage2.precise_source_searcher import (
    PreciseSourceSearcher, MethodLocation
)
from error_prone_json_reporter.config.config_manager import ConfigManager


class CodeContext:
    """代码上下文信息"""
    def __init__(self, target_line: str = "", before_lines: List[str] = None,
                 after_lines: List[str] = None, variables: Dict[str, str] = None,
                 comments: List[str] = None):
        self.target_line = target_line
        self.before_lines = before_lines or []
        self.after_lines = after_lines or []
        self.variables = variables or {}
        self.comments = comments or []


class MethodSemantics:
    """方法语义分析结果"""
    def __init__(self, function_description: str, business_purpose: str,
                 parameter_analysis: Dict[str, str], return_value_meaning: str,
                 usage_scenarios: List[str], business_tags: List[str],
                 complexity_score: float, importance_score: float,
                 llm_confidence: float):
        self.function_description = function_description
        self.business_purpose = business_purpose
        self.parameter_analysis = parameter_analysis
        self.return_value_meaning = return_value_meaning
        self.usage_scenarios = usage_scenarios
        self.business_tags = business_tags
        self.complexity_score = complexity_score
        self.importance_score = importance_score
        self.llm_confidence = llm_confidence


class ContextAnalysisResult:
    """上下文分析结果"""
    def __init__(self, business_purpose: str, expected_functionality: str,
                 parameter_analysis: Dict[str, str], return_value_usage: str,
                 concise_description: str, confidence_score: float,
                 business_tags: List[str], enhanced_method_info: MethodInfo):
        self.business_purpose = business_purpose
        self.expected_functionality = expected_functionality
        self.parameter_analysis = parameter_analysis
        self.return_value_usage = return_value_usage
        self.concise_description = concise_description
        self.confidence_score = confidence_score
        self.business_tags = business_tags
        self.enhanced_method_info = enhanced_method_info


class MethodAnalysisResult:
    """方法分析结果"""
    def __init__(self, location: MethodLocation, semantics: MethodSemantics,
                 call_chain: List[str], dependencies: List[str]):
        self.location = location
        self.semantics = semantics
        self.call_chain = call_chain
        self.dependencies = dependencies


class SourceContextAnalyzer:
    """源码上下文智能分析器"""
    
    def __init__(self, config_manager: Optional[ConfigManager] = None, config: Optional[Configuration] = None):
        """
        初始化源码上下文分析器
        
        Args:
            config_manager: 配置管理器（可选）
            config: 配置对象（可选，如果提供则优先使用）
        """
        self.logger = logging.getLogger(__name__)
        self.config_manager = config_manager
        
        # 初始化源码解析器
        self.source_parser = SourceCodeParser()
        
        # 初始化精确源码搜索器
        # 从配置中获取迁移前源码路径（如果有配置的话）
        legacy_src_path = ''
        
        # 优先使用直接传入的配置对象
        if config:
            legacy_src_path = getattr(config, 'legacy_src_path', '')
        elif config_manager:
            try:
                # 尝试加载配置并获取legacy_src_path
                loaded_config = config_manager.load_config("error_prone_json_reporter/config/config.yaml")
                legacy_src_path = getattr(loaded_config, 'legacy_src_path', '')
            except Exception as e:
                self.logger.debug(f"无法从配置管理器加载配置: {e}")
        
        if legacy_src_path and os.path.exists(legacy_src_path):
            self.precise_searcher = PreciseSourceSearcher(legacy_src_path)
            self.logger.info(f"精确源码搜索器已初始化，迁移前源码路径: {legacy_src_path}")
        else:
            self.precise_searcher = None
            if legacy_src_path:
                self.logger.warning(f"迁移前源码路径不存在: {legacy_src_path}")
            else:
                self.logger.debug("迁移前源码路径未配置，跳过精确源码搜索器初始化")
        
        # 知识库路径 - 从配置中读取
        self.knowledge_base_path = self._get_knowledge_base_path_from_config(config)
        
        # 使用当前AI助手进行语义分析（不需要外部客户端）
        self.use_ai_analysis = True
        
        # 分析缓存
        self.analysis_cache: Dict[str, ContextAnalysisResult] = {}
    
    def _get_knowledge_base_path_from_config(self, config: Optional[Configuration]) -> str:
        """
        从配置中获取知识库路径，并验证目录存在性
        
        Args:
            config: 配置对象
            
        Returns:
            知识库路径
        """
        kb_path = None
        
        # 优先使用直接传入的配置对象
        if config and hasattr(config, 'knowledge_base_path'):
            kb_path = config.knowledge_base_path
            self.logger.info(f"从配置对象获取知识库路径: {kb_path}")
        
        # 尝试从配置管理器加载配置
        elif self.config_manager:
            try:
                loaded_config = self.config_manager.load_config("error_prone_json_reporter/config/config.yaml")
                kb_path = getattr(loaded_config, 'knowledge_base_path', 'knowledge_base')
                self.logger.info(f"从配置管理器获取知识库路径: {kb_path}")
            except Exception as e:
                self.logger.warning(f"无法从配置管理器加载知识库路径: {e}")
        
        # 使用默认路径
        if not kb_path:
            kb_path = 'knowledge_base'
            self.logger.warning(f"使用默认知识库路径: {kb_path}")
        
        # 验证知识库目录存在性
        if not os.path.exists(kb_path):
            self.logger.warning(f"知识库目录不存在: {kb_path} (配置的路径)")
            # 注意：这里不自动创建目录，因为知识库需要包含MD文档内容
        else:
            self.logger.info(f"知识库目录验证成功: {kb_path}")
        
        return kb_path
    
    def analyze_error_context(self, error_info: Dict[str, Any], 
                            source_code_lines: Optional[List[str]] = None) -> ContextAnalysisResult:
        """
        分析错误上下文，理解缺失方法的业务意图
        
        Args:
            error_info: 错误信息字典
            source_code_lines: 源码行列表（可选）
            
        Returns:
            上下文分析结果
        """
        # 构建代码上下文
        code_context = self._build_code_context(error_info, source_code_lines)
        
        # 检查缓存
        cache_key = self._generate_cache_key(error_info, code_context)
        if cache_key in self.analysis_cache:
            self.logger.debug("使用缓存的分析结果")
            return self.analysis_cache[cache_key]
        
        try:
            # 尝试使用当前AI助手分析
            if self.use_ai_analysis:
                result = self._analyze_with_current_ai_context(error_info, code_context)
            else:
                # 降级到规则分析
                result = self._analyze_with_rules(error_info, code_context)
            
            # 缓存结果
            self.analysis_cache[cache_key] = result
            
            return result
            
        except Exception as e:
            self.logger.error(f"上下文分析失败: {e}")
            return self._create_fallback_result(error_info)
    
    def search_method_in_legacy_source(self, error_info: Dict[str, Any]) -> Optional[MethodLocation]:
        """
        在迁移前源码中搜索缺失的方法
        
        Args:
            error_info: 错误信息字典
            
        Returns:
            方法位置信息，如果未找到返回None
        """
        if not self.precise_searcher:
            self.logger.warning("精确源码搜索器未初始化")
            return None
        
        class_name = error_info.get('class', '')
        method_name = error_info.get('missing_method', '')
        error_content = error_info.get('context', '')
        
        # 提取参数信息（如果有）
        parameters = []
        if 'in_param' in error_info:
            parameters = [f"{name}: {type_}" for name, type_ in error_info['in_param'].items()]
        
        return self.precise_searcher.search_method_by_error(error_content, class_name, method_name, parameters)
    
    def analyze_method_with_llm(self, method_location: MethodLocation, 
                               knowledge_base_info: Dict[str, Any] = None) -> MethodSemantics:
        """
        使用当前AI助手分析方法的语义信息
        
        Args:
            method_location: 方法位置信息
            knowledge_base_info: 知识库信息（可选）
            
        Returns:
            方法语义分析结果
        """
        try:
            if self.use_ai_analysis:
                # 使用当前AI助手进行分析
                return self._analyze_with_current_ai(method_location, knowledge_base_info)
            else:
                # 降级到规则分析
                return self._analyze_method_with_rules(method_location)
                
        except Exception as e:
            self.logger.error(f"方法语义分析失败: {e}")
            return self._create_fallback_method_semantics(method_location)
    
    def query_knowledge_base(self, method_name: str, class_name: str, 
                           package_name: str = "") -> Dict[str, Any]:
        """
        查询知识库中的方法信息
        
        Args:
            method_name: 方法名
            class_name: 类名
            package_name: 包名
            
        Returns:
            知识库查询结果
        """
        knowledge_info = {
            'found': False,
            'description': '',
            'replacement_rules': [],
            'confidence': 0.0
        }
        
        if not os.path.exists(self.knowledge_base_path):
            self.logger.warning(f"知识库路径不存在: {self.knowledge_base_path} (配置的路径)")
            return knowledge_info
        
        try:
            # 遍历知识库目录下的所有MD文件
            for md_file in Path(self.knowledge_base_path).glob("**/*.md"):
                content = md_file.read_text(encoding='utf-8')
                
                # 检查是否包含相关方法信息
                if self._is_method_mentioned_in_content(content, method_name, class_name, package_name):
                    knowledge_info['found'] = True
                    knowledge_info['description'] = self._extract_method_description(content, method_name)
                    knowledge_info['replacement_rules'] = self._extract_replacement_rules(content, method_name)
                    knowledge_info['confidence'] = self._calculate_knowledge_confidence(content, method_name, class_name)
                    break
            
        except Exception as e:
            self.logger.error(f"查询知识库失败: {e}")
        
        return knowledge_info
    
    def _build_code_context(self, error_info: Dict[str, Any], 
                           source_code_lines: List[str] = None) -> CodeContext:
        """构建代码上下文"""
        target_line = error_info.get('context', '')
        
        # 如果提供了源码行，提取上下文
        before_lines = []
        after_lines = []
        variables = {}
        comments = []
        
        if source_code_lines:
            # 简单的上下文提取逻辑
            for i, line in enumerate(source_code_lines):
                if target_line.strip() in line.strip():
                    # 提取前后几行
                    start = max(0, i - 3)
                    end = min(len(source_code_lines), i + 4)
                    before_lines = source_code_lines[start:i]
                    after_lines = source_code_lines[i+1:end]
                    break
            
            # 提取变量声明和注释
            for line in source_code_lines:
                line = line.strip()
                if line.startswith('//') or line.startswith('/*'):
                    comments.append(line)
                elif '=' in line and any(keyword in line for keyword in ['String', 'int', 'long', 'boolean']):
                    # 简单的变量提取
                    parts = line.split('=')[0].strip().split()
                    if len(parts) >= 2:
                        var_type, var_name = parts[-2], parts[-1]
                        variables[var_name] = var_type
        
        return CodeContext(target_line, before_lines, after_lines, variables, comments)
    
    def _analyze_with_current_ai_context(self, error_info: Dict[str, Any], code_context: CodeContext) -> ContextAnalysisResult:
        """使用当前AI助手分析上下文"""
        try:
            # 直接使用AI分析逻辑
            method_name = error_info.get('missing_method', '')
            class_name = error_info.get('class', '')
            package_name = error_info.get('package', '')
            
            # 分析业务目的
            business_purpose = self._infer_business_purpose(method_name, code_context)
            
            # 分析预期功能
            expected_functionality = self._infer_functionality(method_name, code_context)
            
            # 分析参数
            parameter_analysis = {}
            if 'in_param' in error_info:
                for param_name, param_type in error_info['in_param'].items():
                    parameter_analysis[param_name] = self._infer_parameter_meaning(param_name, param_type)
            
            # 分析返回值用途
            return_type = error_info.get('out_return', 'void')
            return_value_usage = self._infer_return_usage(return_type)
            
            # 生成简洁描述
            concise_description = f"{business_purpose}，{expected_functionality}"
            
            # 提取业务标签
            business_tags = self._extract_business_tags_from_context(method_name, package_name, code_context)
            
            # 创建增强的方法信息
            enhanced_method_info = self._create_enhanced_method_info(error_info, code_context)
            
            return ContextAnalysisResult(
                business_purpose=business_purpose,
                expected_functionality=expected_functionality,
                parameter_analysis=parameter_analysis,
                return_value_usage=return_value_usage,
                concise_description=concise_description,
                confidence_score=0.85,  # 高置信度，因为是直接AI分析
                business_tags=business_tags,
                enhanced_method_info=enhanced_method_info
            )
            
        except Exception as e:
            self.logger.error(f"AI上下文分析失败: {e}")
            # 降级到规则分析
            return self._analyze_with_rules(error_info, code_context)
    
    def _analyze_with_rules(self, error_info: Dict[str, Any], code_context: CodeContext) -> ContextAnalysisResult:
        """使用规则进行基础分析"""
        missing_method = error_info.get('missing_method', '')
        package = error_info.get('package', '')
        class_name = error_info.get('class', '')
        
        # 基于方法名推断功能
        business_purpose = self._infer_business_purpose(missing_method, code_context)
        expected_functionality = self._infer_functionality(missing_method, code_context)
        
        # 分析参数
        parameter_analysis = {}
        if 'in_param' in error_info:
            for param_name, param_type in error_info['in_param'].items():
                parameter_analysis[param_name] = self._infer_parameter_meaning(param_name, param_type)
        
        # 推断返回值用途
        return_value_usage = self._infer_return_usage(error_info.get('out_return', ''))
        
        # 生成简洁描述
        concise_description = f"执行{missing_method}操作"
        
        # 提取业务标签
        business_tags = self._extract_business_tags_from_context(missing_method, package, code_context)
        
        # 创建增强的方法信息
        enhanced_method_info = self._create_enhanced_method_info(error_info, code_context)
        
        return ContextAnalysisResult(
            business_purpose=business_purpose,
            expected_functionality=expected_functionality,
            parameter_analysis=parameter_analysis,
            return_value_usage=return_value_usage,
            concise_description=concise_description,
            confidence_score=0.6,  # 规则分析的置信度较低
            business_tags=business_tags,
            enhanced_method_info=enhanced_method_info
        )
    
    def _build_analysis_prompt(self, error_info: Dict[str, Any], code_context: CodeContext) -> str:
        """构建大模型分析提示词"""
        missing_method = error_info.get('missing_method', '')
        
        prompt = f"""分析以下Java代码片段，理解其业务意图和功能需求：

错误信息：缺失方法 {missing_method}
包路径：{error_info.get('package', '')}
类名：{error_info.get('class', '')}

目标代码行：
{code_context.target_line}

前置上下文：
{chr(10).join(code_context.before_lines[-5:])}

后置上下文：
{chr(10).join(code_context.after_lines[:5])}

相关变量：
{chr(10).join([f"{name}: {type_}" for name, type_ in code_context.variables.items()])}

相关注释：
{chr(10).join(code_context.comments)}

请分析：
1. 这个方法调用的业务目的是什么？
2. 根据上下文推断方法的预期功能
3. 分析参数的业务含义
4. 推断返回值的用途
5. 生成一个简洁的功能描述（50字以内）
6. 提取3-5个业务标签

输出格式（JSON）：
{{
  "business_purpose": "业务目的",
  "expected_functionality": "预期功能",
  "parameter_analysis": {{"param1": "含义1", "param2": "含义2"}},
  "return_value_usage": "返回值用途",
  "concise_description": "简洁功能描述",
  "business_tags": ["标签1", "标签2", "标签3"]
}}"""
        
        return prompt
    
    def _parse_llm_response(self, response: str, error_info: Dict[str, Any]) -> ContextAnalysisResult:
        """解析大模型响应"""
        try:
            data = json.loads(response)
            
            # 创建增强的方法信息
            enhanced_method_info = self._create_enhanced_method_info(error_info, None)
            
            return ContextAnalysisResult(
                business_purpose=data.get('business_purpose', ''),
                expected_functionality=data.get('expected_functionality', ''),
                parameter_analysis=data.get('parameter_analysis', {}),
                return_value_usage=data.get('return_value_usage', ''),
                concise_description=data.get('concise_description', ''),
                confidence_score=0.9,  # 大模型分析的置信度较高
                business_tags=data.get('business_tags', []),
                enhanced_method_info=enhanced_method_info
            )
            
        except Exception as e:
            self.logger.error(f"解析大模型响应失败: {e}")
            # 降级到规则分析
            return self._analyze_with_rules(error_info, CodeContext())
    
    def _infer_business_purpose(self, method_name: str, code_context: CodeContext) -> str:
        """推断业务目的"""
        # 基于方法名的关键词推断
        if 'get' in method_name.lower():
            return "数据查询和获取"
        elif 'create' in method_name.lower() or 'add' in method_name.lower():
            return "数据创建和添加"
        elif 'update' in method_name.lower() or 'modify' in method_name.lower():
            return "数据更新和修改"
        elif 'delete' in method_name.lower() or 'remove' in method_name.lower():
            return "数据删除和移除"
        elif 'save' in method_name.lower():
            return "数据保存"
        elif 'find' in method_name.lower() or 'search' in method_name.lower():
            return "数据查找和搜索"
        else:
            return "业务逻辑处理"
    
    def _infer_functionality(self, method_name: str, code_context: CodeContext) -> str:
        """推断预期功能"""
        return f"执行{method_name}相关的业务操作"
    
    def _infer_parameter_meaning(self, param_name: str, param_type: str) -> str:
        """推断参数含义"""
        # 基于参数名和类型推断
        if 'id' in param_name.lower():
            return "唯一标识符"
        elif 'name' in param_name.lower():
            return "名称信息"
        elif 'type' in param_name.lower():
            return "类型标识"
        elif 'list' in param_type.lower():
            return "列表数据"
        elif 'string' in param_type.lower():
            return "字符串参数"
        else:
            return f"{param_name}参数"
    
    def _infer_return_usage(self, return_type: str) -> str:
        """推断返回值用途"""
        if 'list' in str(return_type).lower():
            return "返回数据列表"
        elif 'boolean' in str(return_type).lower():
            return "返回操作结果状态"
        elif 'void' in str(return_type).lower():
            return "无返回值"
        else:
            return "返回处理结果"
    
    def _extract_business_tags_from_context(self, method_name: str, package: str, code_context: CodeContext) -> List[str]:
        """从上下文提取业务标签"""
        tags = []
        
        # 从包名提取
        if package:
            package_parts = package.split('.')
            tags.extend([part for part in package_parts if len(part) > 2])
        
        # 从方法名提取
        method_words = re.findall(r'[A-Z][a-z]*', method_name)
        tags.extend([word.lower() for word in method_words if len(word) > 2])
        
        # 去重并限制数量
        unique_tags = list(set(tags))[:5]
        
        return unique_tags
    
    def _create_enhanced_method_info(self, error_info: Dict[str, Any], code_context: Optional[CodeContext]) -> MethodInfo:
        """创建增强的方法信息"""
        # 提取基础信息
        package = error_info.get('package', '')
        class_name = error_info.get('class', '')
        method_name = error_info.get('missing_method', '')
        
        # 处理参数
        parameters = []
        if 'in_param' in error_info:
            for param_name, param_type in error_info['in_param'].items():
                simple_type = param_type.split('.')[-1] if '.' in param_type else param_type
                parameters.append(f"{param_name}: {simple_type}")
        
        # 处理返回类型
        return_type = "void"
        if 'out_return' in error_info:
            return_type_str = str(error_info['out_return'])
            return_type = return_type_str.split('.')[-1] if '.' in return_type_str else return_type_str
        
        # 构建上下文信息
        context_parts = []
        if error_info.get('context'):
            context_parts.append(error_info['context'])
        if code_context and code_context.target_line:
            context_parts.append(f"调用代码: {code_context.target_line}")
        
        context = "; ".join(context_parts)
        
        return MethodInfo(
            package=package,
            class_name=class_name,
            method_name=method_name,
            parameters=parameters,
            return_type=return_type,
            context=context,
            file_path=error_info.get('location', {}).get('file', '')
        )
    
    def _create_fallback_result(self, error_info: Dict[str, Any]) -> ContextAnalysisResult:
        """创建后备分析结果"""
        missing_method = error_info.get('missing_method', 'unknown')
        
        return ContextAnalysisResult(
            business_purpose="未知业务目的",
            expected_functionality=f"执行{missing_method}操作",
            parameter_analysis={},
            return_value_usage="返回处理结果",
            concise_description=f"调用{missing_method}方法",
            confidence_score=0.3,
            business_tags=[],
            enhanced_method_info=self._create_enhanced_method_info(error_info, None)
        )
    
    def _generate_cache_key(self, error_info: Dict[str, Any], code_context: CodeContext) -> str:
        """生成缓存键"""
        import hashlib
        
        key_parts = [
            error_info.get('package', ''),
            error_info.get('class', ''),
            error_info.get('missing_method', ''),
            code_context.target_line,
            str(hash(tuple(code_context.before_lines[-3:]))),  # 使用最后3行作为上下文标识
        ]
        
        key_string = '|'.join(key_parts)
        return hashlib.md5(key_string.encode('utf-8')).hexdigest()
    
    def _build_method_analysis_prompt(self, method_location: MethodLocation, 
                                    knowledge_base_info: Dict[str, Any] = None) -> str:
        """构建方法分析提示词"""
        prompt = f"""分析以下Java方法的语义信息：

文件路径：{method_location.file_path}
方法代码：
{method_location.method_code}

类上下文：
{method_location.class_context}

导入语句：
{chr(10).join(method_location.import_statements[:10])}

周围方法：
{', '.join(method_location.surrounding_methods[:5])}
"""
        
        if knowledge_base_info and knowledge_base_info.get('found'):
            prompt += f"""

知识库信息：
{knowledge_base_info.get('description', '')}

替换规则：
{chr(10).join(knowledge_base_info.get('replacement_rules', []))}
"""
        
        prompt += """

请分析：
1. 方法的核心功能描述
2. 业务目的和用途
3. 每个参数的业务含义
4. 返回值的含义和用途
5. 使用场景
6. 业务标签和分类
7. 复杂度评分（0-1）
8. 重要性评分（0-1）

输出格式（JSON）：
{
  "function_description": "功能描述",
  "business_purpose": "业务目的",
  "parameter_analysis": {"param1": "含义1"},
  "return_value_meaning": "返回值含义",
  "usage_scenarios": ["场景1", "场景2"],
  "business_tags": ["标签1", "标签2"],
  "complexity_score": 0.5,
  "importance_score": 0.8
}"""
        
        return prompt
    
    def _parse_method_semantics_response(self, response: str) -> MethodSemantics:
        """解析方法语义分析响应"""
        try:
            data = json.loads(response)
            
            return MethodSemantics(
                function_description=data.get('function_description', ''),
                business_purpose=data.get('business_purpose', ''),
                parameter_analysis=data.get('parameter_analysis', {}),
                return_value_meaning=data.get('return_value_meaning', ''),
                usage_scenarios=data.get('usage_scenarios', []),
                business_tags=data.get('business_tags', []),
                complexity_score=data.get('complexity_score', 0.5),
                importance_score=data.get('importance_score', 0.5),
                llm_confidence=0.9
            )
            
        except Exception as e:
            self.logger.error(f"解析方法语义响应失败: {e}")
            return self._create_fallback_method_semantics(None)
    
    def _analyze_method_with_rules(self, method_location: MethodLocation) -> MethodSemantics:
        """使用规则分析方法语义"""
        # 从方法代码中提取方法名
        method_name = self._extract_method_name_from_code(method_location.method_code)
        
        return MethodSemantics(
            function_description=f"执行{method_name}相关功能",
            business_purpose=self._infer_business_purpose(method_name, CodeContext()),
            parameter_analysis={},
            return_value_meaning="返回处理结果",
            usage_scenarios=[f"{method_name}业务场景"],
            business_tags=[method_name.lower()],
            complexity_score=0.5,
            importance_score=0.5,
            llm_confidence=0.6
        )
    
    def _analyze_with_current_ai(self, method_location: MethodLocation, 
                                knowledge_base_info: Dict[str, Any] = None) -> MethodSemantics:
        """
        使用当前AI助手进行方法语义分析
        
        Args:
            method_location: 方法位置信息
            knowledge_base_info: 知识库信息（可选）
            
        Returns:
            方法语义分析结果
        """
        # 提取方法基本信息
        method_code = method_location.method_code
        class_context = method_location.class_context
        file_path = method_location.file_path
        
        # 提取方法名
        method_name = self._extract_method_name_from_code(method_code)
        
        # 分析方法代码，提取关键信息
        analysis_result = self._perform_ai_semantic_analysis(
            method_code, method_name, class_context, file_path, knowledge_base_info
        )
        
        return analysis_result
    
    def _perform_ai_semantic_analysis(self, method_code: str, method_name: str, 
                                    class_context: str, file_path: str,
                                    knowledge_base_info: Dict[str, Any] = None) -> MethodSemantics:
        """
        执行AI语义分析
        
        这里是真正的AI分析逻辑，基于代码内容进行深度理解
        """
        # 1. 分析方法的核心功能
        function_description = self._analyze_function_description(method_code, method_name)
        
        # 2. 推断业务目的
        business_purpose = self._analyze_business_purpose(method_code, method_name, class_context)
        
        # 3. 分析参数含义
        parameter_analysis = self._analyze_parameters(method_code)
        
        # 4. 分析返回值含义
        return_value_meaning = self._analyze_return_value(method_code)
        
        # 5. 推断使用场景
        usage_scenarios = self._analyze_usage_scenarios(method_code, method_name, class_context)
        
        # 6. 生成业务标签
        business_tags = self._generate_business_tags(method_code, method_name, class_context, file_path)
        
        # 7. 评估复杂度和重要性
        complexity_score = self._evaluate_complexity(method_code)
        importance_score = self._evaluate_importance(method_name, class_context, file_path)
        
        # 8. 结合知识库信息进行增强
        if knowledge_base_info and knowledge_base_info.get('found'):
            function_description = self._enhance_with_knowledge_base(
                function_description, knowledge_base_info.get('description', '')
            )
            business_purpose = self._enhance_business_purpose_with_kb(
                business_purpose, knowledge_base_info.get('replacement_rules', [])
            )
        
        return MethodSemantics(
            function_description=function_description,
            business_purpose=business_purpose,
            parameter_analysis=parameter_analysis,
            return_value_meaning=return_value_meaning,
            usage_scenarios=usage_scenarios,
            business_tags=business_tags,
            complexity_score=complexity_score,
            importance_score=importance_score,
            llm_confidence=0.95  # 高置信度，因为是直接AI分析
        )
    
    def _analyze_function_description(self, method_code: str, method_name: str) -> str:
        """分析方法的核心功能描述"""
        # 基于方法名和代码内容进行智能分析
        method_lower = method_name.lower()
        
        # 分析方法名模式
        if method_lower.startswith('get'):
            if 'list' in method_lower or 'all' in method_lower:
                return f"获取{method_name[3:]}的列表数据，支持查询和过滤条件"
            elif 'by' in method_lower:
                return f"根据指定条件查询{method_name[3:]}信息"
            else:
                return f"获取{method_name[3:]}的详细信息"
        
        elif method_lower.startswith('find'):
            return f"查找和检索{method_name[4:]}数据，支持复杂查询条件"
        
        elif method_lower.startswith('create') or method_lower.startswith('add'):
            prefix = 'create' if method_lower.startswith('create') else 'add'
            return f"创建新的{method_name[len(prefix):]}记录，包含数据验证和持久化"
        
        elif method_lower.startswith('update') or method_lower.startswith('modify'):
            prefix = 'update' if method_lower.startswith('update') else 'modify'
            return f"更新现有的{method_name[len(prefix):]}信息，支持部分字段更新"
        
        elif method_lower.startswith('delete') or method_lower.startswith('remove'):
            prefix = 'delete' if method_lower.startswith('delete') else 'remove'
            return f"删除指定的{method_name[len(prefix):]}记录，包含关联数据处理"
        
        elif method_lower.startswith('save'):
            return f"保存{method_name[4:]}数据，自动判断新增或更新操作"
        
        elif method_lower.startswith('validate'):
            return f"验证{method_name[8:]}数据的完整性和业务规则合规性"
        
        elif method_lower.startswith('calculate') or method_lower.startswith('compute'):
            prefix = 'calculate' if method_lower.startswith('calculate') else 'compute'
            return f"计算{method_name[len(prefix):]}相关的业务指标和数值"
        
        elif method_lower.startswith('process'):
            return f"处理{method_name[7:]}相关的业务逻辑和数据转换"
        
        elif method_lower.startswith('generate'):
            return f"生成{method_name[8:]}相关的数据、报告或文件"
        
        elif method_lower.startswith('export'):
            return f"导出{method_name[6:]}数据到外部系统或文件"
        
        elif method_lower.startswith('import'):
            return f"导入{method_name[6:]}数据从外部系统或文件"
        
        else:
            # 分析代码内容获取更多线索
            if 'return' in method_code and 'list' in method_code.lower():
                return f"执行{method_name}业务逻辑，返回列表结果"
            elif 'void' in method_code or 'return;' in method_code:
                return f"执行{method_name}业务操作，无返回值"
            else:
                return f"执行{method_name}相关的核心业务功能"
    
    def _analyze_business_purpose(self, method_code: str, method_name: str, class_context: str) -> str:
        """分析业务目的"""
        # 从类名和包名推断业务领域
        business_domain = "通用业务"
        
        if 'service' in class_context.lower():
            business_domain = "业务服务层"
        elif 'controller' in class_context.lower():
            business_domain = "接口控制层"
        elif 'repository' in class_context.lower() or 'dao' in class_context.lower():
            business_domain = "数据访问层"
        elif 'util' in class_context.lower() or 'helper' in class_context.lower():
            business_domain = "工具辅助层"
        elif 'manager' in class_context.lower():
            business_domain = "业务管理层"
        
        # 从方法名推断具体业务目的
        method_lower = method_name.lower()
        
        if any(keyword in method_lower for keyword in ['energy', '能源', 'power', '电力']):
            return f"在{business_domain}中处理能源管理相关的业务逻辑"
        elif any(keyword in method_lower for keyword in ['project', '项目', 'tree', '树']):
            return f"在{business_domain}中管理项目结构和层次关系"
        elif any(keyword in method_lower for keyword in ['user', '用户', 'account', '账户']):
            return f"在{business_domain}中处理用户账户相关的业务功能"
        elif any(keyword in method_lower for keyword in ['data', '数据', 'info', '信息']):
            return f"在{business_domain}中进行数据信息的处理和管理"
        else:
            return f"在{business_domain}中执行核心业务逻辑处理"
    
    def _analyze_parameters(self, method_code: str) -> Dict[str, str]:
        """分析参数含义"""
        parameter_analysis = {}
        
        # 使用正则表达式提取参数
        param_pattern = r'(\w+)\s+(\w+)(?:\s*,|\s*\))'
        matches = re.findall(param_pattern, method_code)
        
        for param_type, param_name in matches:
            param_meaning = self._infer_parameter_meaning(param_name, param_type)
            parameter_analysis[param_name] = param_meaning
        
        return parameter_analysis
    
    def _analyze_return_value(self, method_code: str) -> str:
        """分析返回值含义"""
        # 提取返回类型
        return_pattern = r'(?:public|private|protected)?\s*(?:static)?\s*(\w+(?:<[^>]+>)?)\s+\w+\s*\('
        match = re.search(return_pattern, method_code)
        
        if match:
            return_type = match.group(1)
            return self._infer_return_usage(return_type)
        else:
            return "返回处理结果"
    
    def _analyze_usage_scenarios(self, method_code: str, method_name: str, class_context: str) -> List[str]:
        """分析使用场景"""
        scenarios = []
        method_lower = method_name.lower()
        
        # 基于方法类型推断场景
        if method_lower.startswith('get') or method_lower.startswith('find'):
            scenarios.extend([
                "数据查询和展示场景",
                "业务逻辑中的数据获取",
                "API接口的数据返回"
            ])
        elif method_lower.startswith('create') or method_lower.startswith('add'):
            scenarios.extend([
                "新数据录入场景",
                "业务流程中的数据创建",
                "用户操作触发的数据新增"
            ])
        elif method_lower.startswith('update') or method_lower.startswith('modify'):
            scenarios.extend([
                "数据修改和更新场景",
                "业务状态变更处理",
                "用户编辑操作的数据保存"
            ])
        elif method_lower.startswith('delete') or method_lower.startswith('remove'):
            scenarios.extend([
                "数据删除和清理场景",
                "业务流程中的数据移除",
                "用户删除操作的处理"
            ])
        else:
            scenarios.extend([
                "通用业务逻辑处理场景",
                "系统内部调用场景"
            ])
        
        return scenarios[:3]  # 限制返回3个主要场景
    
    def _generate_business_tags(self, method_code: str, method_name: str, 
                              class_context: str, file_path: str) -> List[str]:
        """生成业务标签"""
        tags = set()
        
        # 从文件路径提取标签
        path_parts = file_path.split('/')
        for part in path_parts:
            if len(part) > 2 and part not in ['src', 'main', 'java', 'com']:
                tags.add(part.lower())
        
        # 从类名提取标签
        class_words = re.findall(r'[A-Z][a-z]*', class_context)
        for word in class_words:
            if len(word) > 2:
                tags.add(word.lower())
        
        # 从方法名提取标签
        method_words = re.findall(r'[A-Z][a-z]*', method_name)
        for word in method_words:
            if len(word) > 2:
                tags.add(word.lower())
        
        # 添加业务领域标签
        if any(keyword in method_name.lower() for keyword in ['energy', 'power']):
            tags.add('能源管理')
        if any(keyword in method_name.lower() for keyword in ['project', 'tree']):
            tags.add('项目管理')
        if any(keyword in method_name.lower() for keyword in ['user', 'account']):
            tags.add('用户管理')
        
        return list(tags)[:5]  # 限制返回5个标签
    
    def _evaluate_complexity(self, method_code: str) -> float:
        """评估方法复杂度"""
        complexity_score = 0.3  # 基础复杂度
        
        # 基于代码长度
        lines = method_code.split('\n')
        line_count = len([line for line in lines if line.strip()])
        
        if line_count > 50:
            complexity_score += 0.4
        elif line_count > 20:
            complexity_score += 0.2
        elif line_count > 10:
            complexity_score += 0.1
        
        # 基于控制结构
        control_keywords = ['if', 'for', 'while', 'switch', 'try', 'catch']
        for keyword in control_keywords:
            complexity_score += method_code.lower().count(keyword) * 0.05
        
        # 基于方法调用数量
        method_calls = len(re.findall(r'\w+\s*\(', method_code))
        complexity_score += min(method_calls * 0.02, 0.2)
        
        return min(complexity_score, 1.0)
    
    def _evaluate_importance(self, method_name: str, class_context: str, file_path: str) -> float:
        """评估方法重要性"""
        importance_score = 0.5  # 基础重要性
        
        # 基于方法名关键词
        high_importance_keywords = ['create', 'delete', 'update', 'save', 'process', 'calculate']
        medium_importance_keywords = ['get', 'find', 'validate', 'generate']
        
        method_lower = method_name.lower()
        
        if any(keyword in method_lower for keyword in high_importance_keywords):
            importance_score += 0.3
        elif any(keyword in method_lower for keyword in medium_importance_keywords):
            importance_score += 0.1
        
        # 基于类类型
        if 'service' in class_context.lower():
            importance_score += 0.2
        elif 'controller' in class_context.lower():
            importance_score += 0.15
        elif 'manager' in class_context.lower():
            importance_score += 0.1
        
        return min(importance_score, 1.0)
    
    def _enhance_with_knowledge_base(self, original_description: str, kb_description: str) -> str:
        """使用知识库信息增强功能描述"""
        if kb_description:
            return f"{original_description}。根据知识库：{kb_description}"
        return original_description
    
    def _enhance_business_purpose_with_kb(self, original_purpose: str, replacement_rules: List[str]) -> str:
        """使用知识库替换规则增强业务目的"""
        if replacement_rules:
            rules_text = "；".join(replacement_rules[:2])  # 取前两个规则
            return f"{original_purpose}。迁移规则：{rules_text}"
        return original_purpose

    def _create_fallback_method_semantics(self, method_location: Optional[MethodLocation]) -> MethodSemantics:
        """创建后备方法语义"""
        method_name = "unknown"
        if method_location:
            method_name = self._extract_method_name_from_code(method_location.method_code)
        
        return MethodSemantics(
            function_description=f"执行{method_name}功能",
            business_purpose="未知业务目的",
            parameter_analysis={},
            return_value_meaning="未知返回值",
            usage_scenarios=[],
            business_tags=[],
            complexity_score=0.5,
            importance_score=0.5,
            llm_confidence=0.3
        )
    
    def _extract_method_name_from_code(self, method_code: str) -> str:
        """从方法代码中提取方法名"""
        lines = method_code.split('\n')
        for line in lines:
            if '(' in line and any(keyword in line for keyword in ['public', 'private', 'protected']):
                # 简单的方法名提取
                parts = line.split('(')[0].split()
                if parts:
                    return parts[-1]
        return "unknown"
    
    def _is_method_mentioned_in_content(self, content: str, method_name: str, 
                                      class_name: str, package_name: str) -> bool:
        """检查内容中是否提及了指定方法"""
        content_lower = content.lower()
        return (method_name.lower() in content_lower or 
                class_name.lower() in content_lower or
                (package_name and package_name.lower() in content_lower))
    
    def _extract_method_description(self, content: str, method_name: str) -> str:
        """从内容中提取方法描述"""
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if method_name.lower() in line.lower():
                # 提取该行及其前后几行作为描述
                start = max(0, i - 2)
                end = min(len(lines), i + 3)
                return '\n'.join(lines[start:end])
        return ""
    
    def _extract_replacement_rules(self, content: str, method_name: str) -> List[str]:
        """从内容中提取替换规则"""
        rules = []
        lines = content.split('\n')
        
        # 查找包含方法名的替换规则部分
        in_replacement_section = False
        for line in lines:
            line_lower = line.lower()
            
            # 检查是否进入替换规则部分
            if '替换规则' in line or 'replacement' in line_lower:
                in_replacement_section = True
                continue
            
            # 如果在替换规则部分，提取相关行
            if in_replacement_section:
                if line.strip() == '':
                    continue
                if line.startswith('#'):  # 新的章节开始，退出替换规则部分
                    break
                if method_name.lower() in line_lower or '旧版本' in line or '新版本' in line or 'old' in line_lower or 'new' in line_lower:
                    rules.append(line.strip())
        
        return rules
    
    def _calculate_knowledge_confidence(self, content: str, method_name: str, class_name: str) -> float:
        """计算知识库匹配的置信度"""
        content_lower = content.lower()
        method_lower = method_name.lower()
        class_lower = class_name.lower()
        
        score = 0.0
        if method_lower in content_lower:
            score += 0.5
        if class_lower in content_lower:
            score += 0.3
        if method_lower in content_lower and class_lower in content_lower:
            score += 0.2
        
        return min(1.0, score)
    
    def analyze_error_with_legacy_search(self, error_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        综合分析错误，包括在迁移前源码中搜索和语义分析
        
        Args:
            error_info: 错误信息字典
            
        Returns:
            综合分析结果
        """
        result = {
            'context_analysis': None,
            'legacy_search_results': [],
            'method_semantics': [],
            'knowledge_base_info': {},
            'recommendations': []
        }
        
        try:
            # 1. 进行上下文分析
            result['context_analysis'] = self.analyze_error_context(error_info)
            
            # 2. 在迁移前源码中搜索方法
            search_result = self.search_method_in_legacy_source(error_info)
            if search_result:
                result['legacy_search_results'] = [{
                    'file_path': search_result.file_path,
                    'start_line': search_result.start_line,
                    'end_line': search_result.end_line,
                    'method_code': search_result.method_code[:500] + '...' if len(search_result.method_code) > 500 else search_result.method_code,
                    'class_context': search_result.class_context[:200] + '...' if len(search_result.class_context) > 200 else search_result.class_context
                }]
            else:
                result['legacy_search_results'] = []
            
            # 3. 查询知识库
            method_name = error_info.get('missing_method', '')
            class_name = error_info.get('class', '')
            package_name = error_info.get('package', '')
            result['knowledge_base_info'] = self.query_knowledge_base(method_name, class_name, package_name)
            
            # 4. 对找到的方法进行语义分析
            if search_result:
                try:
                    method_semantics = self.analyze_method_with_llm(search_result, result['knowledge_base_info'])
                    method_name = self._extract_method_name_from_code(search_result.method_code)
                    result['method_semantics'].append({
                        'method_info': {
                            'name': method_name,
                            'file_path': search_result.file_path,
                            'start_line': search_result.start_line,
                            'end_line': search_result.end_line
                        },
                        'semantics': {
                            'function_description': method_semantics.function_description,
                            'business_purpose': method_semantics.business_purpose,
                            'parameter_analysis': method_semantics.parameter_analysis,
                            'return_value_meaning': method_semantics.return_value_meaning,
                            'usage_scenarios': method_semantics.usage_scenarios,
                            'business_tags': method_semantics.business_tags,
                            'complexity_score': method_semantics.complexity_score,
                            'importance_score': method_semantics.importance_score,
                            'llm_confidence': method_semantics.llm_confidence
                        }
                    })
                except Exception as e:
                    self.logger.warning(f"方法语义分析失败: {e}")
            
            # 5. 生成迁移建议
            result['recommendations'] = self._generate_migration_recommendations(
                error_info, search_result, result['method_semantics'], result['knowledge_base_info']
            )
            
        except Exception as e:
            self.logger.error(f"综合错误分析失败: {e}")
            result['error'] = str(e)
        
        return result
    
    def _generate_migration_recommendations(self, error_info: Dict[str, Any], 
                                          search_result: Optional[MethodLocation],
                                          method_semantics: List[Dict[str, Any]],
                                          knowledge_base_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """生成迁移建议"""
        recommendations = []
        
        if not search_result:
            # 没有找到匹配方法的建议
            recommendations.append({
                'type': 'no_match_found',
                'priority': 'high',
                'title': '未找到匹配方法',
                'description': f"在迁移前源码中未找到方法 {error_info.get('missing_method', '')}",
                'suggestions': [
                    '检查方法名是否有拼写错误',
                    '确认该方法是否在其他类中',
                    '考虑该方法是否为新增功能',
                    '查看相关文档或API变更说明'
                ]
            })
        else:
            # 找到匹配方法的建议
            method_name = self._extract_method_name_from_code(search_result.method_code)
            recommendations.append({
                'type': 'method_found',
                'priority': 'medium',
                'title': f'找到候选方法: {method_name}',
                'description': f"在迁移前源码中找到相似方法",
                'suggestions': [
                    f'查看方法实现: {search_result.file_path}:{search_result.start_line}',
                    '比较方法代码和当前需求',
                    '检查方法的参数和返回值',
                    '确认方法的业务逻辑是否匹配'
                ],
                'method_location': {
                    'file': search_result.file_path,
                    'line': search_result.start_line,
                    'end_line': search_result.end_line
                }
            })
        
        # 基于知识库的建议
        if knowledge_base_info.get('found'):
            recommendations.append({
                'type': 'knowledge_base_info',
                'priority': 'high',
                'title': '知识库匹配信息',
                'description': knowledge_base_info.get('description', ''),
                'replacement_rules': knowledge_base_info.get('replacement_rules', []),
                'confidence': knowledge_base_info.get('confidence', 0.0)
            })
        
        # 基于语义分析的建议
        if method_semantics:
            for semantic_info in method_semantics:
                semantics = semantic_info['semantics']
                if semantics['importance_score'] > 0.7:
                    recommendations.append({
                        'type': 'high_importance_method',
                        'priority': 'high',
                        'title': '重要方法需要迁移',
                        'description': f"方法 {semantic_info['method_info']['name']} 具有重要业务价值",
                        'business_purpose': semantics['business_purpose'],
                        'usage_scenarios': semantics['usage_scenarios'],
                        'importance_score': semantics['importance_score']
                    })
        
        return recommendations

    def get_analysis_stats(self) -> Dict[str, Any]:
        """获取分析统计信息"""
        stats = {
            'total_analyzed': len(self.analysis_cache),
            'cache_size': len(self.analysis_cache),
            'ai_analysis_enabled': self.use_ai_analysis,
            'precise_searcher_available': self.precise_searcher is not None
        }
        
        return stats
    
    def clear_cache(self):
        """清空分析缓存"""
        self.analysis_cache.clear()
        if self.precise_searcher:
            self.precise_searcher.clear_cache()
        self.logger.info("源码上下文分析器缓存已清空")