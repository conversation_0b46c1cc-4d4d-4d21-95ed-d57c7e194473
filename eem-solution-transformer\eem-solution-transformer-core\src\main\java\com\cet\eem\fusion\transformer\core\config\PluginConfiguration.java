package com.cet.eem.fusion.transformer.core.config;

import com.cet.eem.solution.common.def.common.PluginInfoDef;
import com.cet.electric.fusion.matrix.v2.client.register.JarPluginRegister;
import com.cet.electric.fusion.matrix.v2.dto.business.PluginRuntimeInfo;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR> (2025-01-12)
 * @description: 变压器能效分析的路由
 */
@Configuration
public class PluginConfiguration extends JarPluginRegister {
    @Override
    public PluginRuntimeInfo getPluginRuntimeInfo() {
        PluginRuntimeInfo pluginRuntimeInfo = new PluginRuntimeInfo();
        //设置插件的url前缀,用于路由,url前缀需要与插件唯一标识保持一致
        pluginRuntimeInfo.setPluginUrlPrefex(PluginInfoDef.Transformer.PLUGIN_NAME_PREFIX + "/**");
        //插件唯一标识
        pluginRuntimeInfo.setPluginname(PluginInfoDef.Transformer.PLUGIN_NAME_PREFIX);
        //插件的产品线
        pluginRuntimeInfo.setProductname(PluginInfoDef.PRODUCT_NAME);
        return pluginRuntimeInfo;
    }
}
