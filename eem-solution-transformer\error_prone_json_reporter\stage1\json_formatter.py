"""
JSON格式化器

将错误信息格式化为标准JSON格式，符合第二阶段API匹配的输入要求
"""

import json
import os
from datetime import datetime
from typing import List, Dict, Any
from pathlib import Path

from ..common.models import ErrorReport, ProcessingStats


class JSONFormatter:
    """JSON格式化器"""
    
    def __init__(self, output_dir: str = "output"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
    
    def format_to_json(self, errors: List[ErrorReport], 
                      output_file: str = "errors.json",
                      include_metadata: bool = True) -> str:
        """
        将错误信息格式化为标准JSON
        
        Args:
            errors: 错误报告列表
            output_file: 输出文件名
            include_metadata: 是否包含元数据
            
        Returns:
            JSON字符串
        """
        # 构建JSON结构
        json_data = {
            "metadata": self._build_metadata(errors) if include_metadata else {},
            "errors": [self._format_single_error(error) for error in errors]
        }
        
        # 转换为JSON字符串
        json_str = json.dumps(json_data, indent=2, ensure_ascii=False)
        
        # 保存到文件
        if output_file:
            output_path = self.output_dir / output_file
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(json_str)
            print(f"JSON output saved to: {output_path}")
        
        return json_str
    
    def format_for_api_matching(self, errors: List[ErrorReport], 
                              output_file: str = "errors_for_matching.json") -> str:
        """
        格式化为第二阶段API匹配专用格式
        
        Args:
            errors: 错误报告列表
            output_file: 输出文件名
            
        Returns:
            JSON字符串
        """
        # 使用API匹配格式
        api_format_data = [error.to_api_matching_format() for error in errors]
        
        json_str = json.dumps(api_format_data, indent=2, ensure_ascii=False)
        
        # 保存到文件
        if output_file:
            output_path = self.output_dir / output_file
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(json_str)
            print(f"API matching format JSON saved to: {output_path}")
        
        return json_str
    
    def _build_metadata(self, errors: List[ErrorReport]) -> Dict[str, Any]:
        """构建元数据"""
        metadata = {
            "generated_at": datetime.now().isoformat(),
            "total_errors": len(errors),
            "error_types": self._get_error_type_stats(errors),
            "packages": self._get_package_stats(errors),
            "classes": self._get_class_stats(errors),
            "confidence_stats": self._get_confidence_stats(errors),
            "source_tools": self._get_source_tool_stats(errors),
            "format_version": "1.0"
        }
        return metadata
    
    def _format_single_error(self, error: ErrorReport) -> Dict[str, Any]:
        """格式化单个错误"""
        error_data = {
            "package": error.package,
            "class": error.class_name,
            "missing_method": error.missing_method,
            "in_param": error.parameter_types or error.in_param,
            "out_return": {
                "return": error.return_type_full or error.out_return
            },
            "line": error.line,
            "context": error.context
        }
        
        # 添加扩展信息
        if error.method_signature:
            error_data["method_signature"] = error.method_signature
        
        if error.modifiers:
            error_data["modifiers"] = error.modifiers
        
        if error.annotations:
            error_data["annotations"] = error.annotations
        
        # 位置信息
        location_info = error.get_location_info()
        if location_info:
            error_data["location"] = location_info
        
        # 上下文代码
        if error.context_lines:
            error_data["context_lines"] = error.context_lines
        
        if error.surrounding_methods:
            error_data["surrounding_methods"] = error.surrounding_methods
        
        # 元数据
        error_data["metadata"] = {
            "confidence_score": error.confidence_score,
            "source_tool": error.source_tool,
            "error_type": error.error_type
        }
        
        if error.timestamp:
            error_data["metadata"]["timestamp"] = error.timestamp
        
        return error_data
    
    def _get_error_type_stats(self, errors: List[ErrorReport]) -> Dict[str, int]:
        """获取错误类型统计"""
        stats = {}
        for error in errors:
            error_type = error.error_type
            stats[error_type] = stats.get(error_type, 0) + 1
        return stats
    
    def _get_package_stats(self, errors: List[ErrorReport]) -> Dict[str, int]:
        """获取包统计"""
        stats = {}
        for error in errors:
            package = error.package or "unknown"
            stats[package] = stats.get(package, 0) + 1
        return stats
    
    def _get_class_stats(self, errors: List[ErrorReport]) -> Dict[str, int]:
        """获取类统计"""
        stats = {}
        for error in errors:
            class_name = error.class_name
            stats[class_name] = stats.get(class_name, 0) + 1
        return stats
    
    def _get_confidence_stats(self, errors: List[ErrorReport]) -> Dict[str, float]:
        """获取置信度统计"""
        if not errors:
            return {"average": 0.0, "min": 0.0, "max": 0.0}
        
        confidences = [error.confidence_score for error in errors]
        return {
            "average": sum(confidences) / len(confidences),
            "min": min(confidences),
            "max": max(confidences)
        }
    
    def _get_source_tool_stats(self, errors: List[ErrorReport]) -> Dict[str, int]:
        """获取源工具统计"""
        stats = {}
        for error in errors:
            source_tool = error.source_tool or "unknown"
            stats[source_tool] = stats.get(source_tool, 0) + 1
        return stats
    
    def validate_json_format(self, json_str: str) -> tuple[bool, str]:
        """
        验证JSON格式
        
        Args:
            json_str: JSON字符串
            
        Returns:
            (是否有效, 错误信息)
        """
        try:
            data = json.loads(json_str)
            
            # 验证基本结构
            if not isinstance(data, (dict, list)):
                return False, "JSON root must be object or array"
            
            # 如果是包含metadata的格式
            if isinstance(data, dict) and "errors" in data:
                errors = data["errors"]
                if not isinstance(errors, list):
                    return False, "errors field must be an array"
                
                # 验证每个错误项
                for i, error in enumerate(errors):
                    valid, msg = self._validate_single_error(error, i)
                    if not valid:
                        return False, msg
            
            # 如果是直接的错误数组
            elif isinstance(data, list):
                for i, error in enumerate(data):
                    valid, msg = self._validate_single_error(error, i)
                    if not valid:
                        return False, msg
            
            return True, "Valid JSON format"
            
        except json.JSONDecodeError as e:
            return False, f"Invalid JSON: {e}"
        except Exception as e:
            return False, f"Validation error: {e}"
    
    def _validate_single_error(self, error: Dict[str, Any], index: int) -> tuple[bool, str]:
        """验证单个错误项"""
        required_fields = ["package", "class", "missing_method", "in_param", "out_return"]
        
        for field in required_fields:
            if field not in error:
                return False, f"Error {index}: missing required field '{field}'"
        
        # 验证字段类型
        if not isinstance(error["in_param"], dict):
            return False, f"Error {index}: 'in_param' must be an object"
        
        if "line" in error and not isinstance(error["line"], list):
            return False, f"Error {index}: 'line' must be an array"
        
        return True, "Valid error format"
    
    def generate_schema(self) -> Dict[str, Any]:
        """生成JSON Schema"""
        schema = {
            "$schema": "http://json-schema.org/draft-07/schema#",
            "title": "Error Report JSON Schema",
            "description": "Schema for Java method error reports",
            "type": "object",
            "properties": {
                "metadata": {
                    "type": "object",
                    "properties": {
                        "generated_at": {"type": "string", "format": "date-time"},
                        "total_errors": {"type": "integer", "minimum": 0},
                        "error_types": {"type": "object"},
                        "packages": {"type": "object"},
                        "classes": {"type": "object"},
                        "confidence_stats": {"type": "object"},
                        "source_tools": {"type": "object"},
                        "format_version": {"type": "string"}
                    }
                },
                "errors": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "required": ["package", "class", "missing_method", "in_param", "out_return"],
                        "properties": {
                            "package": {"type": "string"},
                            "class": {"type": "string"},
                            "missing_method": {"type": "string"},
                            "in_param": {"type": "object"},
                            "out_return": {"type": "object"},
                            "line": {"type": "array", "items": {"type": "integer"}},
                            "context": {"type": "string"},
                            "method_signature": {"type": "string"},
                            "modifiers": {"type": "array", "items": {"type": "string"}},
                            "annotations": {"type": "array", "items": {"type": "string"}},
                            "location": {"type": "object"},
                            "context_lines": {"type": "array", "items": {"type": "string"}},
                            "surrounding_methods": {"type": "array", "items": {"type": "string"}},
                            "metadata": {
                                "type": "object",
                                "properties": {
                                    "confidence_score": {"type": "number", "minimum": 0, "maximum": 1},
                                    "source_tool": {"type": "string"},
                                    "error_type": {"type": "string"},
                                    "timestamp": {"type": "string"}
                                }
                            }
                        }
                    }
                }
            },
            "required": ["errors"]
        }
        return schema
    
    def save_schema(self, schema_file: str = "error_report_schema.json") -> str:
        """保存JSON Schema到文件"""
        schema = self.generate_schema()
        schema_path = self.output_dir / schema_file
        
        with open(schema_path, 'w', encoding='utf-8') as f:
            json.dump(schema, f, indent=2, ensure_ascii=False)
        
        print(f"JSON Schema saved to: {schema_path}")
        return str(schema_path)


def create_sample_json(output_dir: str = "output") -> str:
    """创建示例JSON文件"""
    sample_errors = [
        ErrorReport(
            package="com.cet.piem.service.impl",
            class_name="NodeServiceImpl",
            missing_method="getProjectTree",
            in_param={"energyType": "java.lang.String"},
            out_return="java.util.List",
            line=[466],
            context="Method call in service implementation",
            method_signature="getProjectTree(energyType: java.lang.String) -> java.util.List",
            parameter_types={"energyType": "java.lang.String"},
            return_type_full="java.util.List<ProjectNode>",
            file_path="/src/main/java/com/cet/piem/service/impl/NodeServiceImpl.java",
            column=25,
            confidence_score=0.95,
            source_tool="inspect_method"
        ),
        ErrorReport(
            package="com.cet.piem.controller",
            class_name="EnergyController",
            missing_method="calculateEnergyConsumption",
            in_param={"startDate": "java.time.LocalDate", "endDate": "java.time.LocalDate"},
            out_return="java.math.BigDecimal",
            line=[123, 124],
            context="Energy calculation in controller",
            method_signature="calculateEnergyConsumption(startDate: java.time.LocalDate, endDate: java.time.LocalDate) -> java.math.BigDecimal",
            parameter_types={"startDate": "java.time.LocalDate", "endDate": "java.time.LocalDate"},
            return_type_full="java.math.BigDecimal",
            modifiers=["public"],
            annotations=["@RequestMapping"],
            file_path="/src/main/java/com/cet/piem/controller/EnergyController.java",
            column=15,
            confidence_score=0.88,
            source_tool="class_file_reader"
        )
    ]
    
    formatter = JSONFormatter(output_dir)
    json_str = formatter.format_to_json(sample_errors, "sample_errors.json")
    formatter.format_for_api_matching(sample_errors, "sample_errors_for_matching.json")
    formatter.save_schema()
    
    return json_str