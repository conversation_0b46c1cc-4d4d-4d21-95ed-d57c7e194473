"""
上下文提取器实现

提取错误行的完整代码内容，获取错误行前后的上下文代码，
识别错误所在的方法和类的基本信息。
"""

import logging
import re
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass

# Interface removed for simplicity
from .models import ErrorItem, ExtractorConfig
# Error handling simplified for now


@dataclass
class CodeContext:
    """代码上下文信息"""
    target_line: str
    target_line_number: int
    surrounding_lines: List[str]
    context_start_line: int
    context_end_line: int
    total_file_lines: int


@dataclass
class MethodInfo:
    """方法基本信息"""
    method_name: str
    method_signature: str
    method_start_line: int
    method_end_line: int
    method_body: str
    method_modifiers: List[str]
    method_parameters: List[str]
    method_return_type: str


@dataclass
class ClassInfo:
    """类基本信息"""
    class_name: str
    package_name: str
    class_start_line: int
    imports: List[str]
    class_modifiers: List[str]
    extends_class: Optional[str]
    implements_interfaces: List[str]


@dataclass
class ContextExtractionResult:
    """上下文提取结果"""
    error_item: ErrorItem
    code_context: CodeContext
    method_info: Optional[MethodInfo]
    class_info: Optional[ClassInfo]
    file_path: str
    extraction_status: str  # "success", "partial", "failed"
    error_message: Optional[str] = None


class ContextExtractor:
    """
    上下文提取器 - 提取错误行的完整代码内容和上下文信息
    
    主要功能：
    1. 提取错误行的完整代码内容
    2. 获取错误行前后的上下文代码（如前后 5 行）
    3. 识别错误所在的方法和类的基本信息
    """
    
    def __init__(self, file_locator: FileLocatorInterface, config: ExtractorConfig):
        self.logger = logging.getLogger(__name__)
        self.file_locator = file_locator
        self.config = config
        self.error_handler = ErrorHandler(self.logger)
        
        # 配置参数
        self.default_context_lines = getattr(config, 'max_context_lines', 10)
        
        self.logger.info("上下文提取器初始化完成")
    
    def extract_context(self, error_item: ErrorItem, context_lines: Optional[int] = None) -> ContextExtractionResult:
        """
        提取错误项的完整上下文信息
        
        Args:
            error_item: 错误项
            context_lines: 上下文行数，默认使用配置值
            
        Returns:
            ContextExtractionResult: 上下文提取结果
        """
        try:
            self.logger.debug(f"开始提取上下文: {error_item.get_method_display_name()}")
            
            # 定位错误文件
            file_location_result = self.file_locator.locate_error_in_file(error_item)
            file_path = file_location_result["absolute_file_path"]
            line_number = file_location_result["line_number"]
            
            # 提取代码上下文
            code_context = self._extract_code_context(
                file_path, line_number, context_lines or self.default_context_lines
            )
            
            # 提取方法信息
            method_info = self._extract_method_info(file_path, line_number)
            
            # 提取类信息
            class_info = self._extract_class_info(file_path, error_item)
            
            result = ContextExtractionResult(
                error_item=error_item,
                code_context=code_context,
                method_info=method_info,
                class_info=class_info,
                file_path=file_path,
                extraction_status="success"
            )
            
            self.logger.debug(f"上下文提取成功: {error_item.get_method_display_name()}")
            return result
            
        except Exception as e:
            self.logger.error(f"上下文提取失败: {str(e)}")
            
            # 创建失败结果
            result = ContextExtractionResult(
                error_item=error_item,
                code_context=None,
                method_info=None,
                class_info=None,
                file_path="",
                extraction_status="failed",
                error_message=str(e)
            )
            
            return result
    
    def _extract_code_context(self, file_path: str, line_number: int, context_lines: int) -> CodeContext:
        """
        提取代码上下文
        
        Args:
            file_path: 文件路径
            line_number: 目标行号
            context_lines: 上下文行数
            
        Returns:
            CodeContext: 代码上下文信息
        """
        try:
            # 读取文件内容
            file_content = self.file_locator.read_file_content(file_path)
            lines = file_content.splitlines()
            
            if line_number <= 0 or line_number > len(lines):
                raise SourceExtractorError(f"行号超出范围: {line_number}, 文件总行数: {len(lines)}")
            
            # 计算上下文范围
            start_line = max(1, line_number - context_lines)
            end_line = min(len(lines), line_number + context_lines)
            
            # 提取目标行
            target_line = lines[line_number - 1]
            
            # 提取周围行
            surrounding_lines = []
            for i in range(start_line - 1, end_line):
                line_content = lines[i]
                line_info = f"{i + 1:4d}: {line_content}"
                
                # 标记目标行
                if i + 1 == line_number:
                    line_info = f">>> {line_info}"
                else:
                    line_info = f"    {line_info}"
                
                surrounding_lines.append(line_info)
            
            return CodeContext(
                target_line=target_line,
                target_line_number=line_number,
                surrounding_lines=surrounding_lines,
                context_start_line=start_line,
                context_end_line=end_line,
                total_file_lines=len(lines)
            )
            
        except Exception as e:
            raise SourceExtractorError(f"提取代码上下文失败: {str(e)}")
    
    def _extract_method_info(self, file_path: str, line_number: int) -> Optional[MethodInfo]:
        """
        提取方法基本信息
        
        Args:
            file_path: 文件路径
            line_number: 目标行号
            
        Returns:
            MethodInfo: 方法信息或None
        """
        try:
            file_content = self.file_locator.read_file_content(file_path)
            lines = file_content.splitlines()
            
            # 查找包含目标行的方法
            method_start, method_end = self._find_method_boundaries(lines, line_number - 1)
            
            if method_start is None or method_end is None:
                self.logger.warning(f"无法找到包含第{line_number}行的方法")
                return None
            
            # 解析方法签名
            method_signature_info = self._parse_method_signature(lines, method_start)
            
            if not method_signature_info:
                self.logger.warning(f"无法解析方法签名，方法开始行: {method_start + 1}")
                return None
            
            # 提取方法体
            method_body_lines = lines[method_start:method_end + 1]
            method_body = '\n'.join(method_body_lines)
            
            return MethodInfo(
                method_name=method_signature_info['name'],
                method_signature=method_signature_info['signature'],
                method_start_line=method_start + 1,
                method_end_line=method_end + 1,
                method_body=method_body,
                method_modifiers=method_signature_info['modifiers'],
                method_parameters=method_signature_info['parameters'],
                method_return_type=method_signature_info['return_type']
            )
            
        except Exception as e:
            self.logger.warning(f"提取方法信息失败: {str(e)}")
            return None
    
    def _extract_class_info(self, file_path: str, error_item: ErrorItem) -> Optional[ClassInfo]:
        """
        提取类基本信息
        
        Args:
            file_path: 文件路径
            error_item: 错误项
            
        Returns:
            ClassInfo: 类信息或None
        """
        try:
            file_content = self.file_locator.read_file_content(file_path)
            lines = file_content.splitlines()
            
            # 查找类定义
            class_start_line = self._find_class_definition(lines)
            
            if class_start_line is None:
                self.logger.warning("无法找到类定义")
                return None
            
            # 解析类信息
            class_signature_info = self._parse_class_signature(lines, class_start_line)
            
            # 提取导入语句
            imports = self._extract_imports(lines)
            
            return ClassInfo(
                class_name=error_item.class_name,
                package_name=error_item.package,
                class_start_line=class_start_line + 1,
                imports=imports,
                class_modifiers=class_signature_info.get('modifiers', []),
                extends_class=class_signature_info.get('extends', None),
                implements_interfaces=class_signature_info.get('implements', [])
            )
            
        except Exception as e:
            self.logger.warning(f"提取类信息失败: {str(e)}")
            return None
    
    def _find_method_boundaries(self, lines: List[str], target_line: int) -> Tuple[Optional[int], Optional[int]]:
        """
        查找包含目标行的方法边界
        
        Args:
            lines: 文件行列表
            target_line: 目标行索引（从0开始）
            
        Returns:
            (方法开始行索引, 方法结束行索引)
        """
        try:
            # 向上查找方法开始
            method_start = None
            for i in range(target_line, -1, -1):
                line = lines[i].strip()
                
                # 查找方法定义模式
                if self._is_method_definition(line):
                    method_start = i
                    break
                
                # 如果遇到类定义或其他方法，停止搜索
                if self._is_class_definition(line):
                    break
            
            if method_start is None:
                return None, None
            
            # 向下查找方法结束
            method_end = None
            brace_count = 0
            in_method = False
            
            for i in range(method_start, len(lines)):
                line = lines[i].strip()
                
                # 计算大括号
                open_braces = line.count('{')
                close_braces = line.count('}')
                
                if open_braces > 0:
                    in_method = True
                
                brace_count += open_braces - close_braces
                
                # 如果大括号平衡且已经进入方法，则找到方法结束
                if in_method and brace_count == 0:
                    method_end = i
                    break
            
            return method_start, method_end
            
        except Exception as e:
            self.logger.warning(f"查找方法边界失败: {str(e)}")
            return None, None
    
    def _is_method_definition(self, line: str) -> bool:
        """判断是否为方法定义行"""
        # 跳过空行和注释
        if not line or line.startswith('//') or line.startswith('/*') or line.startswith('*'):
            return False
        
        # 方法定义模式
        method_patterns = [
            # 标准方法定义：修饰符 + 返回类型 + 方法名 + 参数
            r'\b(public|private|protected|static|final|abstract|synchronized)\s+.*\s+\w+\s*\(',
            # 简单方法定义：返回类型 + 方法名 + 参数
            r'\b\w+\s+\w+\s*\(',
            # 构造函数
            r'\b(public|private|protected)\s+\w+\s*\(',
        ]
        
        for pattern in method_patterns:
            if re.search(pattern, line):
                # 排除变量声明和方法调用
                if '=' in line and line.index('=') < line.index('('):
                    continue
                return True
        
        return False
    
    def _is_class_definition(self, line: str) -> bool:
        """判断是否为类定义行"""
        class_patterns = [
            r'\b(public|private|protected)?\s*(class|interface|enum)\s+\w+',
        ]
        
        for pattern in class_patterns:
            if re.search(pattern, line):
                return True
        
        return False
    
    def _parse_method_signature(self, lines: List[str], method_start: int) -> Optional[Dict[str, Any]]:
        """
        解析方法签名
        
        Args:
            lines: 文件行列表
            method_start: 方法开始行索引
            
        Returns:
            方法签名信息字典
        """
        try:
            # 收集方法定义行（可能跨多行）
            signature_lines = []
            for i in range(method_start, min(method_start + 5, len(lines))):
                line = lines[i].strip()
                signature_lines.append(line)
                
                # 如果找到开括号，方法签名结束
                if '{' in line:
                    break
            
            signature = ' '.join(signature_lines)
            
            # 解析修饰符
            modifiers = []
            modifier_keywords = ['public', 'private', 'protected', 'static', 'final', 'abstract', 'synchronized']
            for modifier in modifier_keywords:
                if re.search(r'\b' + modifier + r'\b', signature):
                    modifiers.append(modifier)
            
            # 解析方法名和参数
            method_match = re.search(r'\b(\w+)\s*\((.*?)\)', signature)
            if not method_match:
                return None
            
            method_name = method_match.group(1)
            params_str = method_match.group(2)
            
            # 解析参数
            parameters = []
            if params_str.strip():
                param_parts = params_str.split(',')
                for param in param_parts:
                    param = param.strip()
                    if param:
                        parameters.append(param)
            
            # 解析返回类型
            return_type = "void"
            # 查找返回类型（在方法名之前的最后一个单词）
            before_method = signature[:signature.find(method_name)].strip()
            words = before_method.split()
            if words:
                # 过滤掉修饰符，找到返回类型
                for word in reversed(words):
                    if word not in modifier_keywords:
                        return_type = word
                        break
            
            return {
                'name': method_name,
                'signature': signature.split('{')[0].strip(),
                'modifiers': modifiers,
                'parameters': parameters,
                'return_type': return_type
            }
            
        except Exception as e:
            self.logger.warning(f"解析方法签名失败: {str(e)}")
            return None
    
    def _find_class_definition(self, lines: List[str]) -> Optional[int]:
        """
        查找类定义行
        
        Args:
            lines: 文件行列表
            
        Returns:
            类定义行索引或None
        """
        for i, line in enumerate(lines):
            line = line.strip()
            if self._is_class_definition(line):
                return i
        
        return None
    
    def _parse_class_signature(self, lines: List[str], class_start: int) -> Dict[str, Any]:
        """
        解析类签名
        
        Args:
            lines: 文件行列表
            class_start: 类开始行索引
            
        Returns:
            类签名信息字典
        """
        try:
            # 收集类定义行（可能跨多行）
            signature_lines = []
            for i in range(class_start, min(class_start + 3, len(lines))):
                line = lines[i].strip()
                signature_lines.append(line)
                
                # 如果找到开括号，类签名结束
                if '{' in line:
                    break
            
            signature = ' '.join(signature_lines)
            
            # 解析修饰符
            modifiers = []
            modifier_keywords = ['public', 'private', 'protected', 'static', 'final', 'abstract']
            for modifier in modifier_keywords:
                if re.search(r'\b' + modifier + r'\b', signature):
                    modifiers.append(modifier)
            
            # 解析extends
            extends_match = re.search(r'\bextends\s+(\w+)', signature)
            extends_class = extends_match.group(1) if extends_match else None
            
            # 解析implements
            implements_interfaces = []
            implements_match = re.search(r'\bimplements\s+([\w\s,]+)', signature)
            if implements_match:
                interfaces_str = implements_match.group(1)
                interfaces = [iface.strip() for iface in interfaces_str.split(',')]
                implements_interfaces = [iface for iface in interfaces if iface]
            
            return {
                'modifiers': modifiers,
                'extends': extends_class,
                'implements': implements_interfaces
            }
            
        except Exception as e:
            self.logger.warning(f"解析类签名失败: {str(e)}")
            return {}
    
    def _extract_imports(self, lines: List[str]) -> List[str]:
        """
        提取导入语句
        
        Args:
            lines: 文件行列表
            
        Returns:
            导入语句列表
        """
        imports = []
        
        for line in lines:
            line = line.strip()
            if line.startswith('import ') and line.endswith(';'):
                imports.append(line)
        
        return imports
    
    def extract_batch_context(self, error_items: List[ErrorItem], 
                            context_lines: Optional[int] = None) -> List[ContextExtractionResult]:
        """
        批量提取上下文信息
        
        Args:
            error_items: 错误项列表
            context_lines: 上下文行数
            
        Returns:
            上下文提取结果列表
        """
        results = []
        
        self.logger.info(f"开始批量提取上下文，共 {len(error_items)} 个错误项")
        
        for i, error_item in enumerate(error_items):
            try:
                self.logger.debug(f"处理第 {i + 1}/{len(error_items)} 个错误项")
                result = self.extract_context(error_item, context_lines)
                results.append(result)
                
            except Exception as e:
                self.logger.error(f"处理第 {i + 1} 个错误项失败: {str(e)}")
                
                # 创建失败结果
                failed_result = ContextExtractionResult(
                    error_item=error_item,
                    code_context=None,
                    method_info=None,
                    class_info=None,
                    file_path="",
                    extraction_status="failed",
                    error_message=str(e)
                )
                results.append(failed_result)
        
        # 统计结果
        successful = sum(1 for r in results if r.extraction_status == "success")
        failed = len(results) - successful
        
        self.logger.info(f"批量提取完成，成功: {successful}, 失败: {failed}")
        
        return results
    
    def get_extraction_summary(self, results: List[ContextExtractionResult]) -> Dict[str, Any]:
        """
        获取提取结果摘要
        
        Args:
            results: 提取结果列表
            
        Returns:
            摘要信息字典
        """
        total = len(results)
        successful = sum(1 for r in results if r.extraction_status == "success")
        partial = sum(1 for r in results if r.extraction_status == "partial")
        failed = sum(1 for r in results if r.extraction_status == "failed")
        
        success_rate = (successful / total * 100) if total > 0 else 0
        
        return {
            "total_items": total,
            "successful_extractions": successful,
            "partial_extractions": partial,
            "failed_extractions": failed,
            "success_rate": success_rate,
            "error_summary": self.error_handler.get_error_summary()
        }