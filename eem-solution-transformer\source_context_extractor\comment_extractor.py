"""
注释和文档提取器

专门用于提取方法的Javadoc注释、行注释和块注释，
并进行清理和格式化处理。
"""

import logging
import re
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

try:
    from .models import MethodAnalysisResult
except ImportError:
    from models import MethodAnalysisResult


class CommentType(Enum):
    """注释类型枚举"""
    JAVADOC = "javadoc"
    BLOCK_COMMENT = "block_comment"
    LINE_COMMENT = "line_comment"
    INLINE_COMMENT = "inline_comment"


@dataclass
class CommentInfo:
    """注释信息"""
    type: CommentType
    content: str
    start_line: int
    end_line: int
    raw_content: str
    cleaned_content: str


class JavadocParser:
    """Javadoc解析器"""
    
    def __init__(self):
        """初始化Javadoc解析器"""
        self.logger = logging.getLogger(__name__)
    
    def parse_javadoc(self, javadoc_content: str) -> Dict[str, Any]:
        """
        解析Javadoc注释
        
        Args:
            javadoc_content: Javadoc原始内容
            
        Returns:
            解析后的Javadoc信息字典
        """
        try:
            if not javadoc_content or '/**' not in javadoc_content:
                return {}
            
            # 清理Javadoc内容
            cleaned_content = self._clean_javadoc_content(javadoc_content)
            
            # 解析Javadoc结构
            javadoc_info = {
                'description': self._extract_description(cleaned_content),
                'parameters': self._extract_param_tags(cleaned_content),
                'return_info': self._extract_return_tag(cleaned_content),
                'throws_info': self._extract_throws_tags(cleaned_content),
                'author': self._extract_author_tag(cleaned_content),
                'version': self._extract_version_tag(cleaned_content),
                'since': self._extract_since_tag(cleaned_content),
                'see_also': self._extract_see_tags(cleaned_content),
                'deprecated': self._extract_deprecated_tag(cleaned_content),
                'custom_tags': self._extract_custom_tags(cleaned_content),
                'raw_content': javadoc_content,
                'cleaned_content': cleaned_content
            }
            
            return javadoc_info
            
        except Exception as e:
            self.logger.error(f"解析Javadoc时出错: {str(e)}")
            return {'raw_content': javadoc_content, 'error': str(e)}
    
    def _clean_javadoc_content(self, javadoc_content: str) -> str:
        """清理Javadoc内容"""
        lines = javadoc_content.split('\n')
        cleaned_lines = []
        
        for line in lines:
            # 移除开始和结束标记
            line = line.strip()
            if line.startswith('/**'):
                line = line[3:].strip()
            elif line.endswith('*/'):
                line = line[:-2].strip()
            elif line.startswith('*'):
                line = line[1:].strip()
            
            cleaned_lines.append(line)
        
        return '\n'.join(cleaned_lines).strip()
    
    def _extract_description(self, content: str) -> str:
        """提取描述部分"""
        lines = content.split('\n')
        description_lines = []
        
        for line in lines:
            line = line.strip()
            if line.startswith('@'):
                break  # 遇到标签，描述结束
            if line:
                description_lines.append(line)
        
        return '\n'.join(description_lines).strip()
    
    def _extract_param_tags(self, content: str) -> List[Dict[str, str]]:
        """提取@param标签"""
        return self._extract_tags_with_name_and_description(content, '@param')
    
    def _extract_return_tag(self, content: str) -> Optional[str]:
        """提取@return标签"""
        tags = self._extract_simple_tags(content, '@return')
        return tags[0] if tags else None
    
    def _extract_throws_tags(self, content: str) -> List[Dict[str, str]]:
        """提取@throws或@exception标签"""
        throws_tags = self._extract_tags_with_name_and_description(content, '@throws')
        exception_tags = self._extract_tags_with_name_and_description(content, '@exception')
        return throws_tags + exception_tags
    
    def _extract_author_tag(self, content: str) -> Optional[str]:
        """提取@author标签"""
        tags = self._extract_simple_tags(content, '@author')
        return tags[0] if tags else None
    
    def _extract_version_tag(self, content: str) -> Optional[str]:
        """提取@version标签"""
        tags = self._extract_simple_tags(content, '@version')
        return tags[0] if tags else None
    
    def _extract_since_tag(self, content: str) -> Optional[str]:
        """提取@since标签"""
        tags = self._extract_simple_tags(content, '@since')
        return tags[0] if tags else None
    
    def _extract_see_tags(self, content: str) -> List[str]:
        """提取@see标签"""
        return self._extract_simple_tags(content, '@see')
    
    def _extract_deprecated_tag(self, content: str) -> Optional[str]:
        """提取@deprecated标签"""
        tags = self._extract_simple_tags(content, '@deprecated')
        return tags[0] if tags else None
    
    def _extract_custom_tags(self, content: str) -> Dict[str, List[str]]:
        """提取自定义标签"""
        custom_tags = {}
        lines = content.split('\n')
        
        for line in lines:
            line = line.strip()
            if line.startswith('@'):
                # 检查是否为标准标签
                standard_tags = {'@param', '@return', '@throws', '@exception', '@author', 
                               '@version', '@since', '@see', '@deprecated'}
                
                tag_match = re.match(r'@(\w+)', line)
                if tag_match:
                    tag_name = f'@{tag_match.group(1)}'
                    if tag_name not in standard_tags:
                        tag_content = line[len(tag_name):].strip()
                        if tag_name not in custom_tags:
                            custom_tags[tag_name] = []
                        custom_tags[tag_name].append(tag_content)
        
        return custom_tags
    
    def _extract_simple_tags(self, content: str, tag_name: str) -> List[str]:
        """提取简单标签（只有描述的标签）"""
        tags = []
        lines = content.split('\n')
        
        i = 0
        while i < len(lines):
            line = lines[i].strip()
            if line.startswith(tag_name):
                tag_content = line[len(tag_name):].strip()
                
                # 检查后续行是否为标签内容的延续
                i += 1
                while i < len(lines):
                    next_line = lines[i].strip()
                    if next_line.startswith('@') or not next_line:
                        break
                    tag_content += ' ' + next_line
                    i += 1
                
                tags.append(tag_content.strip())
                continue
            
            i += 1
        
        return tags
    
    def _extract_tags_with_name_and_description(self, content: str, tag_name: str) -> List[Dict[str, str]]:
        """提取带名称和描述的标签（如@param）"""
        tags = []
        lines = content.split('\n')
        
        i = 0
        while i < len(lines):
            line = lines[i].strip()
            if line.startswith(tag_name):
                tag_content = line[len(tag_name):].strip()
                
                # 解析名称和描述
                parts = tag_content.split(None, 1)
                if parts:
                    name = parts[0]
                    description = parts[1] if len(parts) > 1 else ""
                    
                    # 检查后续行是否为描述的延续
                    i += 1
                    while i < len(lines):
                        next_line = lines[i].strip()
                        if next_line.startswith('@') or not next_line:
                            break
                        description += ' ' + next_line
                        i += 1
                    
                    tags.append({
                        'name': name,
                        'description': description.strip()
                    })
                    continue
            
            i += 1
        
        return tags


class CommentExtractor:
    """注释提取器"""
    
    def __init__(self):
        """初始化注释提取器"""
        self.logger = logging.getLogger(__name__)
        self.javadoc_parser = JavadocParser()
    
    def extract_method_comments(self, file_content: str, method_start_line: int, 
                              method_end_line: int) -> Dict[str, Any]:
        """
        提取方法相关的所有注释
        
        Args:
            file_content: 文件内容
            method_start_line: 方法起始行号
            method_end_line: 方法结束行号
            
        Returns:
            包含所有注释信息的字典
        """
        try:
            lines = file_content.split('\n')
            
            # 提取方法前的注释（Javadoc和其他注释）
            preceding_comments = self._extract_preceding_comments(lines, method_start_line)
            
            # 提取方法内的注释
            inline_comments = self._extract_inline_comments(lines, method_start_line, method_end_line)
            
            # 解析Javadoc
            javadoc_info = {}
            javadoc_comment = self._find_javadoc_comment(preceding_comments)
            if javadoc_comment:
                javadoc_info = self.javadoc_parser.parse_javadoc(javadoc_comment.raw_content)
            
            # 整合所有注释信息
            comment_info = {
                'javadoc': javadoc_info,
                'preceding_comments': [self._comment_to_dict(c) for c in preceding_comments],
                'inline_comments': [self._comment_to_dict(c) for c in inline_comments],
                'summary': self._generate_comment_summary(javadoc_info, preceding_comments, inline_comments),
                'formatted_notes': self._format_notes_for_output(javadoc_info, preceding_comments, inline_comments)
            }
            
            return comment_info
            
        except Exception as e:
            self.logger.error(f"提取方法注释时出错: {str(e)}")
            return {}
    
    def _extract_preceding_comments(self, lines: List[str], method_start_line: int) -> List[CommentInfo]:
        """提取方法前的注释"""
        comments = []
        
        # 向前搜索注释，直到遇到非注释、非空行
        for i in range(method_start_line - 2, -1, -1):
            line = lines[i].strip()
            
            if not line:
                continue  # 跳过空行
            
            if line.startswith('/**'):
                # Javadoc注释开始
                javadoc_content, end_line = self._extract_javadoc_block(lines, i)
                comment = CommentInfo(
                    type=CommentType.JAVADOC,
                    content=javadoc_content,
                    start_line=i + 1,
                    end_line=end_line + 1,
                    raw_content=javadoc_content,
                    cleaned_content=self._clean_javadoc_for_display(javadoc_content)
                )
                comments.insert(0, comment)
                i = end_line  # 跳过已处理的行
                
            elif line.startswith('/*'):
                # 块注释开始
                block_content, end_line = self._extract_block_comment(lines, i)
                comment = CommentInfo(
                    type=CommentType.BLOCK_COMMENT,
                    content=block_content,
                    start_line=i + 1,
                    end_line=end_line + 1,
                    raw_content=block_content,
                    cleaned_content=self._clean_block_comment(block_content)
                )
                comments.insert(0, comment)
                i = end_line
                
            elif line.startswith('//'):
                # 行注释
                comment = CommentInfo(
                    type=CommentType.LINE_COMMENT,
                    content=line,
                    start_line=i + 1,
                    end_line=i + 1,
                    raw_content=line,
                    cleaned_content=line[2:].strip()
                )
                comments.insert(0, comment)
                
            elif line.startswith('@'):
                # 注解，继续向前搜索
                continue
                
            else:
                # 遇到其他代码，停止搜索
                break
        
        return comments
    
    def _extract_inline_comments(self, lines: List[str], start_line: int, end_line: int) -> List[CommentInfo]:
        """提取方法内的注释"""
        comments = []
        
        for i in range(start_line - 1, end_line):
            if i >= len(lines):
                break
                
            line = lines[i]
            
            # 检查行注释
            line_comment_match = re.search(r'//(.*)$', line)
            if line_comment_match:
                comment_content = line_comment_match.group(1).strip()
                if comment_content:  # 忽略空注释
                    comment = CommentInfo(
                        type=CommentType.INLINE_COMMENT,
                        content=f"//{comment_content}",
                        start_line=i + 1,
                        end_line=i + 1,
                        raw_content=f"//{comment_content}",
                        cleaned_content=comment_content
                    )
                    comments.append(comment)
            
            # 检查块注释
            if '/*' in line and '*/' in line:
                # 单行块注释
                block_match = re.search(r'/\*(.*?)\*/', line)
                if block_match:
                    comment_content = block_match.group(1).strip()
                    if comment_content:
                        comment = CommentInfo(
                            type=CommentType.INLINE_COMMENT,
                            content=f"/*{comment_content}*/",
                            start_line=i + 1,
                            end_line=i + 1,
                            raw_content=f"/*{comment_content}*/",
                            cleaned_content=comment_content
                        )
                        comments.append(comment)
            elif '/*' in line:
                # 多行块注释开始
                block_content, end_line_idx = self._extract_block_comment(lines, i)
                if block_content:
                    comment = CommentInfo(
                        type=CommentType.BLOCK_COMMENT,
                        content=block_content,
                        start_line=i + 1,
                        end_line=end_line_idx + 1,
                        raw_content=block_content,
                        cleaned_content=self._clean_block_comment(block_content)
                    )
                    comments.append(comment)
                    i = end_line_idx  # 跳过已处理的行
        
        return comments
    
    def _extract_javadoc_block(self, lines: List[str], start_index: int) -> Tuple[str, int]:
        """提取Javadoc块"""
        javadoc_lines = []
        end_index = start_index
        
        for i in range(start_index, len(lines)):
            line = lines[i]
            javadoc_lines.append(line)
            
            if '*/' in line:
                end_index = i
                break
        
        return '\n'.join(javadoc_lines), end_index
    
    def _extract_block_comment(self, lines: List[str], start_index: int) -> Tuple[str, int]:
        """提取块注释"""
        comment_lines = []
        end_index = start_index
        
        for i in range(start_index, len(lines)):
            line = lines[i]
            comment_lines.append(line)
            
            if '*/' in line:
                end_index = i
                break
        
        return '\n'.join(comment_lines), end_index
    
    def _find_javadoc_comment(self, comments: List[CommentInfo]) -> Optional[CommentInfo]:
        """查找Javadoc注释"""
        for comment in comments:
            if comment.type == CommentType.JAVADOC:
                return comment
        return None
    
    def _clean_javadoc_for_display(self, javadoc_content: str) -> str:
        """清理Javadoc用于显示"""
        lines = javadoc_content.split('\n')
        cleaned_lines = []
        
        for line in lines:
            line = line.strip()
            if line.startswith('/**'):
                line = line[3:].strip()
            elif line.endswith('*/'):
                line = line[:-2].strip()
            elif line.startswith('*'):
                line = line[1:].strip()
            
            if line:  # 只保留非空行
                cleaned_lines.append(line)
        
        return '\n'.join(cleaned_lines)
    
    def _clean_block_comment(self, block_content: str) -> str:
        """清理块注释"""
        content = block_content.strip()
        if content.startswith('/*'):
            content = content[2:]
        if content.endswith('*/'):
            content = content[:-2]
        return content.strip()
    
    def _comment_to_dict(self, comment: CommentInfo) -> Dict[str, Any]:
        """将CommentInfo转换为字典"""
        return {
            'type': comment.type.value,
            'content': comment.content,
            'start_line': comment.start_line,
            'end_line': comment.end_line,
            'raw_content': comment.raw_content,
            'cleaned_content': comment.cleaned_content
        }
    
    def _generate_comment_summary(self, javadoc_info: Dict[str, Any], 
                                preceding_comments: List[CommentInfo],
                                inline_comments: List[CommentInfo]) -> Dict[str, Any]:
        """生成注释摘要"""
        summary = {
            'has_javadoc': bool(javadoc_info),
            'javadoc_description': javadoc_info.get('description', '') if javadoc_info else '',
            'parameter_count': len(javadoc_info.get('parameters', [])) if javadoc_info else 0,
            'has_return_doc': bool(javadoc_info.get('return_info')) if javadoc_info else False,
            'preceding_comment_count': len(preceding_comments),
            'inline_comment_count': len(inline_comments),
            'total_comment_lines': sum(c.end_line - c.start_line + 1 for c in preceding_comments + inline_comments)
        }
        
        return summary
    
    def _format_notes_for_output(self, javadoc_info: Dict[str, Any],
                               preceding_comments: List[CommentInfo],
                               inline_comments: List[CommentInfo]) -> str:
        """格式化注释用于输出"""
        notes_parts = []
        
        # Javadoc描述
        if javadoc_info and javadoc_info.get('description'):
            notes_parts.append(f"描述: {javadoc_info['description']}")
        
        # 参数说明
        if javadoc_info and javadoc_info.get('parameters'):
            param_docs = []
            for param in javadoc_info['parameters']:
                param_docs.append(f"  {param['name']}: {param['description']}")
            if param_docs:
                notes_parts.append(f"参数:\n" + '\n'.join(param_docs))
        
        # 返回值说明
        if javadoc_info and javadoc_info.get('return_info'):
            notes_parts.append(f"返回: {javadoc_info['return_info']}")
        
        # 异常说明
        if javadoc_info and javadoc_info.get('throws_info'):
            throws_docs = []
            for throws in javadoc_info['throws_info']:
                throws_docs.append(f"  {throws['name']}: {throws['description']}")
            if throws_docs:
                notes_parts.append(f"异常:\n" + '\n'.join(throws_docs))
        
        # 其他重要注释
        important_comments = []
        for comment in preceding_comments + inline_comments:
            if comment.type != CommentType.JAVADOC and comment.cleaned_content:
                # 过滤掉太短或无意义的注释
                if len(comment.cleaned_content) > 5:
                    important_comments.append(comment.cleaned_content)
        
        if important_comments:
            notes_parts.append(f"其他注释:\n" + '\n'.join(f"  - {comment}" for comment in important_comments[:5]))  # 限制数量
        
        return '\n\n'.join(notes_parts) if notes_parts else ""


class CommentFormatter:
    """注释格式化器"""
    
    def __init__(self):
        """初始化注释格式化器"""
        self.logger = logging.getLogger(__name__)
    
    def format_comments_for_markdown(self, comment_info: Dict[str, Any]) -> str:
        """将注释格式化为Markdown格式"""
        try:
            markdown_parts = []
            
            # Javadoc部分
            if comment_info.get('javadoc'):
                javadoc = comment_info['javadoc']
                markdown_parts.append("### 方法文档")
                
                if javadoc.get('description'):
                    markdown_parts.append(f"**描述:** {javadoc['description']}")
                
                if javadoc.get('parameters'):
                    markdown_parts.append("**参数:**")
                    for param in javadoc['parameters']:
                        markdown_parts.append(f"- `{param['name']}`: {param['description']}")
                
                if javadoc.get('return_info'):
                    markdown_parts.append(f"**返回:** {javadoc['return_info']}")
                
                if javadoc.get('throws_info'):
                    markdown_parts.append("**异常:**")
                    for throws in javadoc['throws_info']:
                        markdown_parts.append(f"- `{throws['name']}`: {throws['description']}")
            
            # 其他注释
            other_comments = []
            for comment in comment_info.get('preceding_comments', []):
                if comment['type'] != 'javadoc' and comment['cleaned_content']:
                    other_comments.append(comment['cleaned_content'])
            
            for comment in comment_info.get('inline_comments', []):
                if comment['cleaned_content']:
                    other_comments.append(comment['cleaned_content'])
            
            if other_comments:
                markdown_parts.append("### 其他注释")
                for comment in other_comments[:10]:  # 限制数量
                    markdown_parts.append(f"- {comment}")
            
            return '\n\n'.join(markdown_parts) if markdown_parts else "无注释信息"
            
        except Exception as e:
            self.logger.error(f"格式化注释为Markdown时出错: {str(e)}")
            return "注释格式化失败"
    
    def format_comments_for_json(self, comment_info: Dict[str, Any]) -> Dict[str, Any]:
        """将注释格式化为JSON格式"""
        try:
            formatted_info = {
                'javadoc': comment_info.get('javadoc', {}),
                'summary': comment_info.get('summary', {}),
                'all_comments': []
            }
            
            # 合并所有注释
            all_comments = (comment_info.get('preceding_comments', []) + 
                          comment_info.get('inline_comments', []))
            
            for comment in all_comments:
                formatted_info['all_comments'].append({
                    'type': comment['type'],
                    'content': comment['cleaned_content'],
                    'line_range': f"{comment['start_line']}-{comment['end_line']}"
                })
            
            return formatted_info
            
        except Exception as e:
            self.logger.error(f"格式化注释为JSON时出错: {str(e)}")
            return {'error': str(e)}
    
    def clean_and_format_notes(self, raw_notes: str) -> str:
        """清理和格式化注释内容"""
        if not raw_notes:
            return ""
        
        # 移除多余的空行
        lines = raw_notes.split('\n')
        cleaned_lines = []
        
        prev_empty = False
        for line in lines:
            line = line.strip()
            if not line:
                if not prev_empty:
                    cleaned_lines.append('')
                prev_empty = True
            else:
                cleaned_lines.append(line)
                prev_empty = False
        
        # 移除开头和结尾的空行
        while cleaned_lines and not cleaned_lines[0]:
            cleaned_lines.pop(0)
        while cleaned_lines and not cleaned_lines[-1]:
            cleaned_lines.pop()
        
        return '\n'.join(cleaned_lines)