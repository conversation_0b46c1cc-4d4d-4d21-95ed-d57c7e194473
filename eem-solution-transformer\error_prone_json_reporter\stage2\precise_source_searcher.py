"""
精确源码搜索器

根据报错内容在迁移前源码中进行精确搜索，提取方法的完整代码块和上下文信息。
集成JavaLangASTParser功能，提供更精确的方法边界识别和重载处理。
"""

import os
import logging
import re
import zipfile
import subprocess
from typing import List, Dict, Optional, Set, Tuple, Any
from pathlib import Path
import javalang
from javalang.tree import (
    MethodDeclaration, ClassDeclaration, InterfaceDeclaration, 
    CompilationUnit, EnumDeclaration, AnnotationDeclaration,
    FormalParameter, Type, ReferenceType, BasicType
)

from error_prone_json_reporter.common.models import MethodInfo


class MethodLocation:
    """方法位置信息"""
    def __init__(self, file_path: str, start_line: int, end_line: int, 
                 method_code: str, class_context: str, import_statements: List[str],
                 surrounding_methods: List[str], is_from_jar: bool = False, 
                 jar_entry: Optional[str] = None):
        self.file_path = file_path
        self.start_line = start_line
        self.end_line = end_line
        self.method_code = method_code
        self.class_context = class_context
        self.import_statements = import_statements
        self.surrounding_methods = surrounding_methods
        self.is_from_jar = is_from_jar  # 标识是否来自jar包
        self.jar_entry = jar_entry  # jar包内的条目路径


class PreciseSourceSearcher:
    """精确源码搜索器 - 支持源码文件和jar包搜索"""
    
    def __init__(self, legacy_src_path: str, project_path: Optional[str] = None):
        """
        初始化精确源码搜索器
        
        Args:
            legacy_src_path: 迁移前源码路径
            project_path: 项目路径（用于jar包搜索）
        """
        self.logger = logging.getLogger(__name__)
        self.legacy_src_path = legacy_src_path
        self.project_path = Path(project_path or legacy_src_path).resolve()
        self.file_cache: Dict[str, str] = {}  # 文件内容缓存
        self.ast_cache: Dict[str, CompilationUnit] = {}  # AST缓存
        self.java_files_cache: Optional[List[str]] = None  # Java文件列表缓存
        self.jar_cache: Dict[str, List[str]] = {}  # jar包内容缓存
        self.classpath_cache: Optional[List[str]] = None  # classpath jar包缓存
    
    def search_method_by_error_location(self, error_file_path: str, error_line: int, 
                                       missing_class: str, missing_method: str, 
                                       package_name: Optional[str] = None,
                                       method_signature: Optional[str] = None,
                                       method_parameters: Optional[List[str]] = None) -> Optional[MethodLocation]:
        """
        根据错误位置信息在旧源码中精确查找缺失方法的实现
        
        两阶段搜索策略：
        1. 第一阶段：根据包名和类名在旧源码中定位缺失方法所在的类
        2. 第二阶段：从迁移前的源码里定位到类的源码，根据方法名和参数找到缺失方法的源码
        
        Args:
            error_file_path: 报错文件路径
            error_line: 报错行号
            missing_class: 缺失的类名（可以是简单类名或全限定名）
            missing_method: 缺失的方法名
            package_name: 包名（可选，用于精确定位类）
            method_signature: 方法签名信息（可选，用于重载方法匹配）
            method_parameters: 方法参数类型列表（可选，用于重载方法匹配）
            
        Returns:
            方法位置信息，如果未找到返回None
        """
        self.logger.info(f"根据错误位置搜索方法: {missing_class}.{missing_method}")
        self.logger.info(f"错误位置: {error_file_path}:{error_line}")
        
        if not os.path.exists(self.legacy_src_path):
            self.logger.warning(f"迁移前源码路径不存在: {self.legacy_src_path}")
            return None
        
        # 第一阶段：在旧源码中查找缺失方法所在的类
        target_class_info = self._find_class_in_legacy_source(missing_class, package_name)
        if not target_class_info:
            self.logger.warning(f"在旧源码中未找到类: {missing_class}")
            return None
        
        self.logger.info(f"第一阶段完成：在旧源码中找到类 {missing_class}")
        
        # 第二阶段：从迁移前的源码里定位到类的源码，根据方法名和参数找到缺失方法的源码
        method_location = self._extract_method_from_class(target_class_info, missing_method, method_signature, method_parameters)
        
        if method_location:
            self.logger.info(f"第二阶段完成：在旧源码中找到方法: {method_location.file_path}:{method_location.start_line}")
            return method_location
        
        # 如果在旧源码中没找到方法，尝试在jar包中查找该类的方法
        jar_method_location = self._search_method_in_jars(missing_class, missing_method)
        if jar_method_location:
            self.logger.info(f"在jar包中找到方法: {jar_method_location.file_path}")
            return jar_method_location
        
        self.logger.warning(f"未找到方法实现: {missing_class}.{missing_method}")
        return None
    
    def search_method_by_error(self, error_content: str, class_name: str, 
                              method_name: str, parameters: Optional[List[str]] = None) -> Optional[MethodLocation]:
        """
        根据报错内容在迁移前源码中进行精确搜索（保持向后兼容）
        
        Args:
            error_content: 报错内容
            class_name: 类名
            method_name: 方法名
            parameters: 方法参数列表（可选，用于重载方法匹配）
            
        Returns:
            方法位置信息，如果未找到返回None
        """
        self.logger.info(f"搜索方法: {class_name}.{method_name} 在路径: {self.legacy_src_path}")
        
        if not os.path.exists(self.legacy_src_path):
            self.logger.warning(f"迁移前源码路径不存在: {self.legacy_src_path}")
            return None
        
        # 1. 首先尝试从源码文件中精确搜索
        method_location = self._search_by_class_and_method(class_name, method_name, parameters)
        if method_location:
            self.logger.info(f"源码精确搜索成功: {method_location.file_path}:{method_location.start_line}")
            return method_location
        
        # 2. 尝试从jar包中搜索
        method_location = self._search_method_in_jars(class_name, method_name, parameters)
        if method_location:
            self.logger.info(f"jar包搜索成功: {method_location.file_path}")
            return method_location
        
        # 3. 如果精确搜索失败，尝试模糊搜索
        method_location = self._search_by_fuzzy_matching(error_content, class_name, method_name, parameters)
        if method_location:
            self.logger.info(f"模糊搜索成功: {method_location.file_path}:{method_location.start_line}")
            return method_location
        
        # 4. 最后尝试基于错误内容的上下文搜索
        method_location = self._search_by_error_context(error_content)
        if method_location:
            self.logger.info(f"上下文搜索成功: {method_location.file_path}:{method_location.start_line}")
        else:
            self.logger.warning(f"未找到方法: {class_name}.{method_name}")
        
        return method_location
    
    def _search_by_class_and_method(self, class_name: str, method_name: str, 
                                   parameters: Optional[List[str]] = None) -> Optional[MethodLocation]:
        """根据类名和方法名精确搜索"""
        java_files = self._get_java_files()
        
        for file_path in java_files:
            try:
                # 检查文件名是否匹配类名
                file_name = os.path.basename(file_path).replace('.java', '')
                if self._is_class_match(class_name, file_name):
                    location = self._extract_method_from_file(file_path, class_name, method_name, parameters)
                    if location:
                        return location
            except Exception as e:
                self.logger.warning(f"搜索文件 {file_path} 时出错: {str(e)}")
                continue
        
        return None
    
    def _search_by_fuzzy_matching(self, error_content: str, class_name: str, 
                                 method_name: str, parameters: Optional[List[str]] = None) -> Optional[MethodLocation]:
        """模糊匹配搜索"""
        java_files = self._get_java_files()
        
        # 生成类名和方法名的变体
        class_variants = self._generate_class_variants(class_name)
        method_variants = self._generate_method_variants(method_name)
        
        for file_path in java_files:
            try:
                content = self._get_file_content(file_path)
                
                # 检查是否包含类名变体
                for class_variant in class_variants:
                    if class_variant.lower() in content.lower():
                        # 检查是否包含方法名变体
                        for method_variant in method_variants:
                            if method_variant.lower() in content.lower():
                                location = self._extract_method_from_file(file_path, class_variant, method_variant, parameters)
                                if location:
                                    return location
            except Exception as e:
                self.logger.warning(f"模糊搜索文件 {file_path} 时出错: {str(e)}")
                continue
        
        return None
    
    def _search_by_error_context(self, error_content: str) -> Optional[MethodLocation]:
        """基于错误内容的上下文搜索"""
        java_files = self._get_java_files()
        
        # 从错误内容中提取关键词
        keywords = self._extract_keywords_from_error(error_content)
        
        best_match = None
        best_score = 0
        
        for file_path in java_files:
            try:
                content = self._get_file_content(file_path)
                
                # 计算关键词匹配分数
                score = self._calculate_keyword_match_score(content, keywords)
                if score > best_score and score > 0.1:  # 降低阈值
                    best_score = score
                    # 尝试从该文件中提取最相关的方法
                    location = self._extract_most_relevant_method(file_path, keywords)
                    if location:
                        best_match = location
            except Exception as e:
                self.logger.warning(f"上下文搜索文件 {file_path} 时出错: {str(e)}")
                continue
        
        return best_match
    
    def _extract_method_from_file(self, file_path: str, class_name: str, 
                                 method_name: str, parameters: Optional[List[str]] = None) -> Optional[MethodLocation]:
        """从文件中提取指定方法"""
        try:
            content = self._get_file_content(file_path)
            ast = self._get_ast(file_path, content)
            
            if not ast:
                return None
            
            # 收集所有匹配的方法
            matching_methods = []
            
            # 遍历AST查找方法
            for path, node in ast.filter(MethodDeclaration):
                if node.name == method_name:
                    # 检查是否在正确的类中
                    containing_class = self._find_containing_class(path)
                    if containing_class and self._is_class_match(class_name, containing_class):
                        
                        # 计算匹配分数
                        match_score = 0
                        if parameters:
                            if self._is_parameter_match(node, parameters):
                                match_score = 100  # 精确参数匹配
                            else:
                                match_score = 10   # 方法名匹配但参数不匹配
                        else:
                            match_score = 50  # 没有参数要求，只匹配方法名
                        
                        if match_score > 0:
                            matching_methods.append((match_score, node, containing_class))
            
            # 选择最佳匹配
            if matching_methods:
                # 按匹配分数排序，选择最高分的
                matching_methods.sort(key=lambda x: x[0], reverse=True)
                best_score, best_node, best_class = matching_methods[0]
                
                # 提取方法的完整信息
                method_code = self._extract_method_code(content, best_node)
                class_context = self._extract_class_context(content, best_class)
                import_statements = self._extract_imports(ast)
                surrounding_methods = self._extract_surrounding_methods(ast, best_node.name)
                
                # 估算方法的行号范围
                start_line, end_line = self._estimate_method_lines(content, method_code)
                
                return MethodLocation(
                    file_path=file_path,
                    start_line=start_line,
                    end_line=end_line,
                    method_code=method_code,
                    class_context=class_context,
                    import_statements=import_statements,
                    surrounding_methods=surrounding_methods
                )
            
            return None
            
        except Exception as e:
            self.logger.error(f"从文件 {file_path} 提取方法时出错: {str(e)}")
            return None
    
    def _get_java_files(self) -> List[str]:
        """递归查找所有Java文件（带缓存）"""
        if self.java_files_cache is None:
            java_files = []
            for root, dirs, files in os.walk(self.legacy_src_path):
                # 跳过常见的非源码目录
                dirs[:] = [d for d in dirs if d not in {
                    'target', 'build', '.git', '.svn', 'node_modules', 
                    '.idea', '.vscode', '__pycache__', 'bin', 'out'
                }]
                
                for file in files:
                    if file.endswith('.java'):
                        java_files.append(os.path.join(root, file))
            
            self.java_files_cache = java_files
            self.logger.info(f"找到 {len(java_files)} 个Java文件")
        
        return self.java_files_cache
    
    def _get_file_content(self, file_path: str) -> str:
        """获取文件内容（带缓存）"""
        if file_path not in self.file_cache:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
            except UnicodeDecodeError:
                try:
                    with open(file_path, 'r', encoding='gbk') as f:
                        content = f.read()
                except UnicodeDecodeError:
                    with open(file_path, 'r', encoding='latin-1') as f:
                        content = f.read()
            
            self.file_cache[file_path] = content
        
        return self.file_cache[file_path]
    
    def _get_ast(self, file_path: str, content: str) -> Optional[CompilationUnit]:
        """获取AST（带缓存）"""
        if file_path not in self.ast_cache:
            try:
                ast = javalang.parse.parse(content)
                self.ast_cache[file_path] = ast
            except Exception as e:
                self.logger.warning(f"解析AST失败 {file_path}: {str(e)}")
                import traceback
                self.logger.debug(f"AST解析错误详情: {traceback.format_exc()}")
                return None
        
        return self.ast_cache[file_path]
    
    def _is_class_match(self, target_class: str, candidate_class: str) -> bool:
        """检查类名是否匹配"""
        if target_class == candidate_class:
            return True
        
        # 检查简单类名匹配
        target_simple = target_class.split('.')[-1]
        candidate_simple = candidate_class.split('.')[-1]
        
        return target_simple == candidate_simple
    

    
    def _generate_class_variants(self, class_name: str) -> List[str]:
        """生成类名变体"""
        variants = [class_name]
        
        # 添加简单类名（去掉包名）
        if '.' in class_name:
            simple_name = class_name.split('.')[-1]
            variants.append(simple_name)
        
        # 添加可能的内部类形式
        if '$' in class_name:
            variants.append(class_name.replace('$', '.'))
        
        return list(set(variants))
    
    def _generate_method_variants(self, method_name: str) -> List[str]:
        """生成方法名变体"""
        variants = [method_name]
        
        # 添加驼峰命名变体
        if '_' in method_name:
            camel_case = ''.join(word.capitalize() if i > 0 else word 
                               for i, word in enumerate(method_name.split('_')))
            variants.append(camel_case)
            variants.append(camel_case[0].lower() + camel_case[1:] if camel_case else '')
        
        return list(set(filter(None, variants)))
    
    def _extract_keywords_from_error(self, error_content: str) -> List[str]:
        """从错误内容中提取关键词"""
        # 移除常见的错误信息词汇
        stop_words = {'error', 'exception', 'cannot', 'find', 'symbol', 'method', 'class', 'package'}
        
        # 使用正则表达式提取标识符
        identifiers = re.findall(r'\b[a-zA-Z_][a-zA-Z0-9_]*\b', error_content)
        
        # 过滤停用词和短词
        keywords = [word for word in identifiers 
                   if word.lower() not in stop_words and len(word) > 2]
        
        return list(set(keywords))
    
    def _calculate_keyword_match_score(self, content: str, keywords: List[str]) -> float:
        """计算关键词匹配分数"""
        if not keywords:
            return 0.0
        
        content_lower = content.lower()
        matches = sum(1 for keyword in keywords if keyword.lower() in content_lower)
        
        # 降低阈值，提高匹配成功率
        score = matches / len(keywords)
        return score
    
    def _extract_most_relevant_method(self, file_path: str, keywords: List[str]) -> Optional[MethodLocation]:
        """从文件中提取最相关的方法"""
        try:
            content = self._get_file_content(file_path)
            ast = self._get_ast(file_path, content)
            
            if not ast:
                return None
            
            best_method = None
            best_score = 0
            
            # 遍历所有方法，找到最匹配的
            for path, node in ast.filter(MethodDeclaration):
                method_code = self._extract_method_code(content, node)
                score = self._calculate_keyword_match_score(method_code, keywords)
                
                if score > best_score:
                    best_score = score
                    
                    containing_class = self._find_containing_class(path)
                    class_context = self._extract_class_context(content, containing_class or "")
                    import_statements = self._extract_imports(ast)
                    surrounding_methods = self._extract_surrounding_methods(ast, node.name)
                    start_line, end_line = self._estimate_method_lines(content, method_code)
                    
                    best_method = MethodLocation(
                        file_path=file_path,
                        start_line=start_line,
                        end_line=end_line,
                        method_code=method_code,
                        class_context=class_context,
                        import_statements=import_statements,
                        surrounding_methods=surrounding_methods
                    )
            
            return best_method if best_score > 0.1 else None  # 降低阈值
            
        except Exception as e:
            self.logger.error(f"提取最相关方法时出错: {str(e)}")
            return None
    
    def _find_containing_class(self, path) -> Optional[str]:
        """查找包含方法的类名"""
        for node in reversed(path):
            if isinstance(node, (ClassDeclaration, InterfaceDeclaration, EnumDeclaration)):
                return node.name
        return None
    
    def _extract_method_code(self, content: str, method_node: MethodDeclaration) -> str:
        """提取方法代码"""
        lines = content.split('\n')
        method_name = method_node.name
        
        # 构建方法签名模式，用于精确匹配
        param_types = []
        if method_node.parameters:
            for param in method_node.parameters:
                param_type = self._get_parameter_type_string(param)
                param_types.append(param_type)
        
        # 查找匹配的方法声明行
        method_start = -1
        for i, line in enumerate(lines):
            if method_name in line and ('public' in line or 'private' in line or 'protected' in line):
                # 检查参数是否匹配
                if self._line_matches_method_signature(line, method_name, param_types):
                    method_start = i
                    break
        
        if method_start == -1:
            # 如果精确匹配失败，降级到简单匹配
            for i, line in enumerate(lines):
                if method_name in line and ('public' in line or 'private' in line or 'protected' in line):
                    method_start = i
                    break
        
        if method_start == -1:
            return f"// 方法 {method_name} 的代码提取失败"
        
        # 向前查找方法的注释和注解
        comment_start = method_start
        for i in range(method_start - 1, -1, -1):
            line = lines[i].strip()
            if line.startswith('/**') or line.startswith('/*') or line.startswith('*') or line.startswith('//') or line.startswith('@'):
                comment_start = i
            elif line == '':
                continue  # 跳过空行
            else:
                break
        
        # 查找方法结束位置（简单的大括号匹配）
        brace_count = 0
        method_end = method_start
        in_method = False
        
        for i in range(method_start, len(lines)):
            line = lines[i]
            if '{' in line:
                brace_count += line.count('{')
                in_method = True
            if '}' in line:
                brace_count -= line.count('}')
            
            if in_method and brace_count == 0:
                method_end = i
                break
        
        return '\n'.join(lines[comment_start:method_end + 1])
    
    def _line_matches_method_signature(self, line: str, method_name: str, param_types: List[str]) -> bool:
        """检查代码行是否匹配方法签名"""
        if method_name not in line:
            return False
        
        # 简单的参数类型匹配检查
        for param_type in param_types:
            if param_type not in line:
                return False
        
        return True
    
    def _extract_class_context(self, content: str, class_name: str) -> str:
        """提取类上下文信息"""
        lines = content.split('\n')
        
        # 查找类声明
        for i, line in enumerate(lines):
            if f"class {class_name}" in line or f"interface {class_name}" in line:
                # 提取类声明及其前面的注释和注解
                start = max(0, i - 5)
                end = min(len(lines), i + 3)
                return '\n'.join(lines[start:end])
        
        return f"// 类 {class_name} 的上下文信息"
    
    def _extract_imports(self, ast: CompilationUnit) -> List[str]:
        """提取import语句"""
        imports = []
        if ast.imports:
            for imp in ast.imports:
                imports.append(f"import {imp.path};")
        return imports
    
    def _extract_surrounding_methods(self, ast: CompilationUnit, target_method: str) -> List[str]:
        """提取周围的方法名"""
        methods = []
        for path, node in ast.filter(MethodDeclaration):
            if node.name != target_method:
                methods.append(node.name)
        return methods[:5]  # 限制数量
    
    def _estimate_method_lines(self, content: str, method_code: str) -> Tuple[int, int]:
        """估算方法的行号范围"""
        lines = content.split('\n')
        method_lines = method_code.split('\n')
        
        if not method_lines:
            return 1, 1
        
        first_line = method_lines[0].strip()
        
        # 查找第一行在文件中的位置
        for i, line in enumerate(lines):
            if first_line in line.strip():
                return i + 1, i + len(method_lines)
        
        return 1, len(method_lines)
    
    def clear_cache(self):
        """清空缓存"""
        self.file_cache.clear()
        self.ast_cache.clear()
        self.java_files_cache = None
        self.logger.info("精确源码搜索器缓存已清空")
    
    def get_cache_stats(self) -> Dict[str, int]:
        """获取缓存统计信息"""
        return {
            'file_cache_size': len(self.file_cache),
            'ast_cache_size': len(self.ast_cache),
            'java_files_count': len(self.java_files_cache) if self.java_files_cache else 0
        }
    
    def _find_class_in_legacy_source(self, class_name: str, package_name: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """
        在旧源码中查找指定类
        
        Args:
            class_name: 类名（可以是简单类名或全限定名）
            package_name: 包名（可选，用于精确定位）
            
        Returns:
            类信息字典，包含文件路径、AST等信息
        """
        java_files = self._get_java_files()
        
        # 提取简单类名
        simple_class_name = class_name.split('.')[-1]
        
        # 如果提供了包名，优先按包路径查找
        if package_name:
            expected_path = package_name.replace('.', os.sep) + os.sep + simple_class_name + '.java'
            self.logger.debug(f"查找包路径: {expected_path}")
            for file_path in java_files:
                self.logger.debug(f"检查文件: {file_path}")
                if file_path.endswith(expected_path.replace('/', os.sep)):
                    self.logger.debug(f"路径匹配: {file_path}")
                    class_info = self._extract_class_info_from_file(file_path, simple_class_name)
                    if class_info:
                        # 验证包名是否匹配
                        if self._verify_package_name(class_info['ast'], package_name):
                            self.logger.debug(f"包名验证成功: {package_name}")
                            return class_info
                        else:
                            self.logger.debug(f"包名验证失败: 期望 {package_name}, 实际 {class_info['ast'].package.name if class_info['ast'].package else None}")
        
        # 如果包名匹配失败或没有提供包名，按文件名查找
        for file_path in java_files:
            try:
                # 检查文件名是否匹配
                file_name = os.path.basename(file_path).replace('.java', '')
                self.logger.debug(f"检查文件名: {file_name} vs {simple_class_name}")
                if file_name == simple_class_name:
                    self.logger.debug(f"文件名匹配: {file_path}")
                    class_info = self._extract_class_info_from_file(file_path, simple_class_name)
                    if class_info:
                        # 如果提供了包名，验证是否匹配
                        if package_name:
                            if self._verify_package_name(class_info['ast'], package_name):
                                self.logger.debug(f"找到匹配的类: {file_path}")
                                return class_info
                            else:
                                self.logger.debug(f"包名不匹配，继续查找")
                        else:
                            self.logger.debug(f"找到匹配的类（无包名验证）: {file_path}")
                            return class_info
            except Exception as e:
                self.logger.warning(f"检查文件 {file_path} 时出错: {str(e)}")
                continue
        
        return None
    
    def _extract_method_from_class(self, class_info: Dict[str, Any], method_name: str, 
                                  method_signature: Optional[str] = None,
                                  method_parameters: Optional[List[str]] = None) -> Optional[MethodLocation]:
        """
        从类信息中提取指定方法，支持方法重载处理
        
        Args:
            class_info: 类信息字典
            method_name: 方法名
            method_signature: 方法签名信息（可选，用于重载方法匹配）
            method_parameters: 方法参数类型列表（可选，用于重载方法匹配）
            
        Returns:
            方法位置信息
        """
        try:
            file_path = class_info['file_path']
            content = class_info['content']
            ast = class_info['ast']
            class_name = class_info['class_name']
            
            # 收集所有匹配的方法
            matching_methods = []
            
            # 遍历AST查找方法
            for path, node in ast.filter(MethodDeclaration):
                if node.name == method_name:
                    matching_methods.append(node)
            
            # 选择最佳匹配的方法（支持方法重载）
            if matching_methods:
                best_node = self._select_best_method_match(matching_methods, method_signature, method_parameters)
                
                # 提取方法的完整信息
                method_code = self._extract_method_code(content, best_node)
                class_context = self._extract_class_context(content, class_name)
                import_statements = self._extract_imports(ast)
                surrounding_methods = self._extract_surrounding_methods(ast, best_node.name)
                
                # 估算方法的行号范围
                start_line, end_line = self._estimate_method_lines(content, method_code)
                    
                return MethodLocation(
                    file_path=file_path,
                    start_line=start_line,
                    end_line=end_line,
                    method_code=method_code,
                    class_context=class_context,
                    import_statements=import_statements,
                    surrounding_methods=surrounding_methods,
                    is_from_jar=False
                )
            
            return None
            
        except Exception as e:
            self.logger.error(f"从类信息中提取方法时出错: {str(e)}")
            return None
    
    def _search_method_in_jars(self, class_name: str, method_name: str) -> Optional[MethodLocation]:
        """
        在jar包中搜索方法
        
        Args:
            class_name: 类名
            method_name: 方法名
            
        Returns:
            方法位置信息
        """
        jar_paths = self._get_classpath_jars()
        if not jar_paths:
            self.logger.warning("未找到jar包路径")
            return None
        
        # 准备搜索模式
        simple_name = class_name.split('.')[-1]
        suffix = f"/{simple_name}.class"
        
        for jar_path in jar_paths:
            try:
                with zipfile.ZipFile(jar_path, 'r') as zipf:
                    for entry in zipf.namelist():
                        if entry.endswith(suffix):
                            # 如果是全限定名，检查是否完全匹配
                            if '.' in class_name:
                                expected_entry = class_name.replace('.', '/') + '.class'
                                if entry != expected_entry:
                                    continue
                            
                            # 尝试读取源码（如果jar包包含源码）
                            source_entry = entry.replace('.class', '.java')
                            if source_entry in zipf.namelist():
                                try:
                                    with zipf.open(source_entry) as f:
                                        content = f.read().decode('utf-8')
                                    
                                    # 解析源码并查找方法
                                    ast = javalang.parse.parse(content)
                                    if ast:
                                        method_location = self._extract_method_from_jar_source(
                                            jar_path, source_entry, content, ast, method_name
                                        )
                                        if method_location:
                                            return method_location
                                except Exception as e:
                                    self.logger.debug(f"无法读取jar包源码 {source_entry}: {e}")
                                    continue
                            
                            # 如果没有源码，返回基本信息
                            full_qualified_name = entry.replace('/', '.').replace('.class', '')
                            return MethodLocation(
                                file_path=f"{jar_path}!{entry}",
                                start_line=0,
                                end_line=0,
                                method_code=f"// 方法 {method_name} 来自jar包: {jar_path}\n// 类: {full_qualified_name}\n// 无源码可用",
                                class_context=f"// 类 {simple_name} 来自jar包",
                                import_statements=[],
                                surrounding_methods=[],
                                is_from_jar=True,
                                jar_entry=entry
                            )
            except Exception as e:
                self.logger.debug(f"无法读取jar包 {jar_path}: {e}")
                continue
        
        return None
    
    def _extract_method_from_jar_source(self, jar_path: str, source_entry: str, content: str, 
                                       ast: CompilationUnit, method_name: str) -> Optional[MethodLocation]:
        """
        从jar包源码中提取方法
        
        Args:
            jar_path: jar包路径
            source_entry: 源码条目路径
            content: 源码内容
            ast: AST对象
            method_name: 方法名
            
        Returns:
            方法位置信息
        """
        try:
            # 查找匹配的方法
            for path, node in ast.filter(MethodDeclaration):
                if node.name == method_name:
                    # 提取方法代码
                    method_code = self._extract_method_code(content, node)
                    
                    # 获取类名
                    class_name = ""
                    for class_path, class_node in ast.filter(ClassDeclaration):
                        class_name = class_node.name
                        break
                    
                    class_context = self._extract_class_context(content, class_name)
                    import_statements = self._extract_imports(ast)
                    surrounding_methods = self._extract_surrounding_methods(ast, node.name)
                    start_line, end_line = self._estimate_method_lines(content, method_code)
                    
                    return MethodLocation(
                        file_path=f"{jar_path}!{source_entry}",
                        start_line=start_line,
                        end_line=end_line,
                        method_code=method_code,
                        class_context=class_context,
                        import_statements=import_statements,
                        surrounding_methods=surrounding_methods,
                        is_from_jar=True,
                        jar_entry=source_entry
                    )
            
            return None
            
        except Exception as e:
            self.logger.error(f"从jar包源码提取方法时出错: {str(e)}")
            return None
    
    def _get_classpath_jars(self) -> List[str]:
        """获取classpath中的jar包列表"""
        if self.classpath_cache:
            return self.classpath_cache
        
        try:
            # 使用Maven构建classpath
            classpath_file = self.project_path / "classpath.txt"
            mvn_executable = self._find_maven_executable()
            
            if not mvn_executable:
                self.logger.warning("未找到Maven可执行文件")
                return []
            
            cmd = [
                mvn_executable,
                "dependency:build-classpath",
                "-DincludeScope=compile",
                f"-Dmdep.outputFile={classpath_file}",
                "-Dmaven.main.skip=true",
                "-Dmaven.test.skip=true"
            ]
            
            self.logger.info("构建Maven classpath...")
            proc = subprocess.run(
                cmd, 
                cwd=str(self.project_path), 
                stdout=subprocess.PIPE, 
                stderr=subprocess.PIPE, 
                text=True,
                timeout=120  # 2分钟超时
            )
            
            if proc.returncode != 0:
                self.logger.error(f"Maven执行失败: {proc.stderr}")
                return []
            
            if not classpath_file.exists():
                self.logger.error("classpath.txt文件未生成")
                return []
            
            with open(classpath_file, 'r', encoding='utf-8') as f:
                classpath_content = f.read().strip()
            
            jar_paths = [jar.strip() for jar in classpath_content.split(os.pathsep) if jar.strip()]
            self.classpath_cache = jar_paths
            
            self.logger.info(f"获取到 {len(jar_paths)} 个jar包")
            return jar_paths
            
        except Exception as e:
            self.logger.error(f"获取classpath失败: {e}")
            return []
    
    def _find_maven_executable(self) -> Optional[str]:
        """查找Maven可执行文件"""
        # 常见的Maven路径
        possible_paths = [
            "mvn.cmd",  # Windows
            "mvn",      # Linux/Mac
        ]
        
        for mvn_path in possible_paths:
            try:
                # 测试Maven是否可用
                proc = subprocess.run(
                    [mvn_path, "--version"], 
                    stdout=subprocess.PIPE, 
                    stderr=subprocess.PIPE,
                    timeout=10
                )
                if proc.returncode == 0:
                    self.logger.debug(f"找到Maven: {mvn_path}")
                    return mvn_path
            except Exception:
                continue
        
        return None
    
    def _extract_class_info_from_file(self, file_path: str, class_name: str) -> Optional[Dict[str, Any]]:
        """
        从文件中提取类信息
        
        Args:
            file_path: 文件路径
            class_name: 类名
            
        Returns:
            类信息字典
        """
        try:
            self.logger.debug(f"从文件提取类信息: {file_path}, 类名: {class_name}")
            content = self._get_file_content(file_path)
            self.logger.debug(f"文件内容长度: {len(content)}")
            self.logger.debug(f"文件内容前200字符: {content[:200]}")
            ast = self._get_ast(file_path, content)
            
            if not ast:
                self.logger.debug(f"无法解析AST: {file_path}")
                return None
            
            self.logger.debug(f"AST解析成功: {file_path}")
            
            # 查找匹配的类
            found_classes = []
            self.logger.debug(f"开始遍历AST查找类...")
            
            # 尝试不同的过滤方式
            try:
                for path, node in ast.filter(ClassDeclaration):
                    found_classes.append(node.name)
                    self.logger.debug(f"找到类: {node.name}")
                    if node.name == class_name:
                        self.logger.debug(f"类名匹配成功: {node.name}")
                        return {
                            'file_path': file_path,
                            'content': content,
                            'ast': ast,
                            'class_name': class_name,
                            'class_node': node
                        }
                
                # 也尝试接口和枚举
                for path, node in ast.filter(InterfaceDeclaration):
                    found_classes.append(node.name)
                    self.logger.debug(f"找到接口: {node.name}")
                    if node.name == class_name:
                        self.logger.debug(f"接口名匹配成功: {node.name}")
                        return {
                            'file_path': file_path,
                            'content': content,
                            'ast': ast,
                            'class_name': class_name,
                            'class_node': node
                        }
                
                for path, node in ast.filter(EnumDeclaration):
                    found_classes.append(node.name)
                    self.logger.debug(f"找到枚举: {node.name}")
                    if node.name == class_name:
                        self.logger.debug(f"枚举名匹配成功: {node.name}")
                        return {
                            'file_path': file_path,
                            'content': content,
                            'ast': ast,
                            'class_name': class_name,
                            'class_node': node
                        }
                        
            except Exception as e:
                self.logger.error(f"遍历AST时出错: {str(e)}")
                import traceback
                self.logger.debug(traceback.format_exc())
            
            self.logger.debug(f"未找到匹配的类 {class_name}，文件中的类: {found_classes}")
            return None
            
        except Exception as e:
            self.logger.error(f"从文件 {file_path} 提取类信息时出错: {str(e)}")
            import traceback
            self.logger.debug(traceback.format_exc())
            return None
    
    def _verify_package_name(self, ast: CompilationUnit, expected_package: str) -> bool:
        """
        验证AST中的包名是否匹配期望的包名
        
        Args:
            ast: AST对象
            expected_package: 期望的包名
            
        Returns:
            是否匹配
        """
        try:
            if not ast.package:
                return not expected_package  # 如果都没有包名，认为匹配
            
            actual_package = ast.package.name
            return actual_package == expected_package
            
        except Exception as e:
            self.logger.debug(f"验证包名时出错: {str(e)}")
            return False
    
    def _is_parameter_match(self, method_node: MethodDeclaration, expected_params: List[str]) -> bool:
        """
        检查方法参数是否匹配
        
        Args:
            method_node: 方法节点
            expected_params: 期望的参数类型列表
            
        Returns:
            是否匹配
        """
        try:
            if not method_node.parameters and not expected_params:
                return True  # 都没有参数
            
            if not method_node.parameters or not expected_params:
                return False  # 一个有参数一个没有
            
            if len(method_node.parameters) != len(expected_params):
                return False  # 参数数量不匹配
            
            # 检查每个参数类型
            for i, param in enumerate(method_node.parameters):
                param_type = self._get_parameter_type_string(param)
                expected_type = expected_params[i]
                
                if not self._is_type_compatible(param_type, expected_type):
                    return False
            
            return True
            
        except Exception as e:
            self.logger.debug(f"检查参数匹配时出错: {str(e)}")
            return False
    
    def _get_parameter_type_string(self, param: FormalParameter) -> str:
        """
        获取参数类型的字符串表示
        
        Args:
            param: 参数节点
            
        Returns:
            参数类型字符串
        """
        try:
            if isinstance(param.type, BasicType):
                return param.type.name
            elif isinstance(param.type, ReferenceType):
                type_name = param.type.name
                # 处理泛型
                if param.type.arguments:
                    args = []
                    for arg in param.type.arguments:
                        if hasattr(arg, 'type') and hasattr(arg.type, 'name'):
                            args.append(arg.type.name)
                    if args:
                        type_name += f"<{', '.join(args)}>"
                return type_name
            else:
                return str(param.type)
                
        except Exception as e:
            self.logger.debug(f"获取参数类型字符串时出错: {str(e)}")
            return "Unknown"
    
    def _is_type_compatible(self, actual_type: str, expected_type: str) -> bool:
        """
        检查两个类型是否兼容
        
        Args:
            actual_type: 实际类型
            expected_type: 期望类型
            
        Returns:
            是否兼容
        """
        # 精确匹配
        if actual_type == expected_type:
            return True
        
        # 简单类名匹配（去掉包名）
        actual_simple = actual_type.split('.')[-1]
        expected_simple = expected_type.split('.')[-1]
        
        if actual_simple == expected_simple:
            return True
        
        # 去掉泛型后匹配
        actual_base = actual_simple.split('<')[0]
        expected_base = expected_simple.split('<')[0]
        
        if actual_base == expected_base:
            return True
        
        # 使用类型相似性检查
        return self._is_similar_type(actual_type, expected_type)
    
    def extract_method_parameters_from_error(self, error_content: str) -> Optional[List[str]]:
        """
        从错误信息中提取方法参数类型
        
        Args:
            error_content: 错误内容
            
        Returns:
            参数类型列表，如果无法提取返回None
        """
        try:
            # 查找方法调用模式，如: methodName(String, int, List<String>)
            method_call_pattern = r'(\w+)\s*\(\s*([^)]*)\s*\)'
            matches = re.findall(method_call_pattern, error_content)
            
            if not matches:
                return None
            
            # 取最后一个匹配（通常是最相关的）
            method_name, params_str = matches[-1]
            
            if not params_str.strip():
                return []  # 无参数方法
            
            # 分割参数
            param_types = []
            for param in params_str.split(','):
                param = param.strip()
                if param:
                    # 提取类型名（去掉变量名）
                    type_name = self._extract_type_from_param_string(param)
                    if type_name:
                        param_types.append(type_name)
            
            return param_types if param_types else None
            
        except Exception as e:
            self.logger.debug(f"从错误信息提取参数时出错: {str(e)}")
            return None
    
    def _extract_type_from_param_string(self, param_str: str) -> Optional[str]:
        """
        从参数字符串中提取类型名
        
        Args:
            param_str: 参数字符串，如 "String name" 或 "List<String>"
            
        Returns:
            类型名
        """
        try:
            # 去掉多余空格
            param_str = param_str.strip()
            
            # 如果包含空格，第一部分通常是类型
            parts = param_str.split()
            if len(parts) >= 2:
                return parts[0]  # 第一部分是类型
            elif len(parts) == 1:
                # 可能是只有类型没有变量名，或者是简单类型
                if param_str in ['int', 'long', 'double', 'float', 'boolean', 'char', 'byte', 'short']:
                    return param_str
                elif '<' in param_str:  # 泛型类型
                    return param_str
                else:
                    return param_str  # 假设是类型名
            
            return None
            
        except Exception as e:
            self.logger.debug(f"提取参数类型时出错: {str(e)}")
            return None
    
    def _select_best_method_match(self, matching_methods: List[MethodDeclaration], 
                                 method_signature: Optional[str] = None,
                                 method_parameters: Optional[List[str]] = None) -> MethodDeclaration:
        """
        从匹配的方法中选择最佳匹配，支持方法重载处理
        
        Args:
            matching_methods: 匹配的方法列表
            method_signature: 方法签名信息（可选）
            method_parameters: 方法参数类型列表（可选）
            
        Returns:
            最佳匹配的方法节点
        """
        if not matching_methods:
            return None
        
        # 如果只有一个方法，直接返回
        if len(matching_methods) == 1:
            return matching_methods[0]
        
        # 优先使用method_parameters进行精确匹配
        if method_parameters:
            for method in matching_methods:
                if self._is_parameter_match(method, method_parameters):
                    self.logger.info(f"找到精确参数匹配的方法: {method.name}")
                    return method
        
        # 如果有方法签名信息，尝试解析并匹配
        if method_signature:
            param_info = self._parse_method_signature(method_signature)
            if param_info:
                best_method = None
                best_score = -1
                
                for method in matching_methods:
                    score = self._calculate_method_match_score(method, param_info)
                    if score > best_score:
                        best_score = score
                        best_method = method
                
                if best_method and best_score > 0:
                    self.logger.info(f"找到签名匹配的方法: {best_method.name}, 分数: {best_score}")
                    return best_method
        
        # 如果都没有匹配，选择参数最少的方法（通常是最简单的重载）
        best_method = min(matching_methods, key=lambda m: len(m.parameters) if m.parameters else 0)
        self.logger.info(f"选择参数最少的方法: {best_method.name}")
        return best_method
    
    def _parse_method_signature(self, method_signature: str) -> Optional[Dict[str, Any]]:
        """
        解析方法签名信息
        
        Args:
            method_signature: 方法签名字符串
            
        Returns:
            解析后的参数信息
        """
        try:
            # 简单解析方法签名，提取参数类型
            # 支持格式如: "methodName(String, int, List<String>)"
            if '(' not in method_signature or ')' not in method_signature:
                return None
            
            # 提取参数部分
            start = method_signature.find('(')
            end = method_signature.rfind(')')
            params_str = method_signature[start + 1:end].strip()
            
            if not params_str:
                return {'param_count': 0, 'param_types': []}
            
            # 分割参数
            param_types = []
            for param in params_str.split(','):
                param = param.strip()
                if param:
                    # 提取简单类型名（去掉泛型和包名）
                    simple_type = self._extract_simple_type_name(param)
                    param_types.append(simple_type)
            
            return {
                'param_count': len(param_types),
                'param_types': param_types
            }
            
        except Exception as e:
            self.logger.debug(f"解析方法签名失败: {method_signature}, {e}")
            return None
    
    def _extract_simple_type_name(self, type_str: str) -> str:
        """
        提取简单类型名
        
        Args:
            type_str: 类型字符串，可能包含变量名，如 "String name" 或 "List<String>"
            
        Returns:
            简单类型名
        """
        # 去掉多余空格
        type_str = type_str.strip()
        
        # 如果包含空格，第一部分通常是类型，后面是变量名
        if ' ' in type_str:
            type_str = type_str.split()[0]
        
        # 去掉泛型部分
        if '<' in type_str:
            type_str = type_str[:type_str.find('<')]
        
        # 去掉包名，只保留简单类名
        if '.' in type_str:
            type_str = type_str.split('.')[-1]
        
        return type_str.strip()
    
    def _calculate_method_match_score(self, method: MethodDeclaration, 
                                    param_info: Dict[str, Any]) -> int:
        """
        计算方法匹配分数
        
        Args:
            method: 方法节点
            param_info: 参数信息
            
        Returns:
            匹配分数
        """
        score = 0
        
        # 参数数量匹配
        method_param_count = len(method.parameters) if method.parameters else 0
        expected_param_count = param_info['param_count']
        
        if method_param_count == expected_param_count:
            score += 100  # 参数数量完全匹配
        else:
            return 0  # 参数数量不匹配，直接返回0分
        
        # 参数类型简单匹配
        if method.parameters and param_info['param_types']:
            for i, param in enumerate(method.parameters):
                if i < len(param_info['param_types']):
                    method_type = self._get_simple_parameter_type(param)
                    expected_type = param_info['param_types'][i]
                    
                    if method_type.lower() == expected_type.lower():
                        score += 10  # 类型完全匹配
                    elif self._is_similar_type(method_type, expected_type):
                        score += 5   # 类型相似匹配
        
        return score
    
    def _get_simple_parameter_type(self, param: FormalParameter) -> str:
        """
        获取参数的简单类型名
        
        Args:
            param: 参数节点
            
        Returns:
            简单类型名
        """
        if isinstance(param.type, BasicType):
            return param.type.name
        elif isinstance(param.type, ReferenceType):
            return param.type.name
        else:
            return str(param.type)
    
    def _is_similar_type(self, type1: str, type2: str) -> bool:
        """
        检查两个类型是否相似
        
        Args:
            type1: 类型1
            type2: 类型2
            
        Returns:
            是否相似
        """
        # 简单的相似性检查
        type1_lower = type1.lower()
        type2_lower = type2.lower()
        
        # 常见的类型别名映射
        type_aliases = {
            'string': ['str', 'string'],
            'integer': ['int', 'integer'],
            'long': ['long', 'bigint'],
            'double': ['double', 'float'],
            'boolean': ['bool', 'boolean'],
            'list': ['list', 'arraylist', 'collection'],
            'map': ['map', 'hashmap', 'dictionary']
        }
        
        for canonical, aliases in type_aliases.items():
            if type1_lower in aliases and type2_lower in aliases:
                return True
        
        return False