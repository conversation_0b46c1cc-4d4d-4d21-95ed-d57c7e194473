# 实现计划

## 项目结构设置和核心接口

- [x] 1. 建立项目结构和核心接口

  - 创建主要目录结构（stage1、stage2、common、tests）
  - 定义核心接口和抽象类
  - 设置配置管理系统
  - _需求: 6.1, 6.2_

- [x] 1.1 创建项目目录结构和配置文件

  - 创建 `error_prone_json_reporter/` 主目录
  - 创建子目录：`stage1/`、`stage2/`、`common/`、`tests/`、`config/`
  - 创建配置文件模板 `config.yaml`
  - 创建 `requirements.txt` 依赖文件
  - _需求: 6.2, 6.4_

- [x] 1.2 定义核心数据模型和接口

  - 实现 `ErrorReport`、`MethodInfo`、`MatchResult` 等数据类
  - 定义 `ErrorDetectorInterface` 抽象接口
  - 创建 `Configuration` 配置类
  - 编写数据模型的单元测试
  - _需求: 5.2, 5.3_

## 第一阶段：错误清单生成器实现

- [x] 2. 实现错误清单生成核心功能

  - 创建 `ErrorListGenerator` 主控制器
  - 实现工具选择和执行逻辑
  - 建立标准化的错误信息处理流程
  - _需求: 1.1, 1.2, 1.3_

- [x] 2.1 集成现有方法问题识别工具

  - 集成 `inspect_method.py` 用于识别不能解析的方法问题
  - 集成 `class_file_reader.py` 用于读取源码获取详细信息
  - 创建统一的方法问题处理流程
  - _需求: 1.1, 1.3_

- [x] 2.2 使用源码解析器获取方法详细信息

  - 使用 `inspect_method.py` 识别不能解析的方法问题
  - 使用 `class_file_reader.py` 读取源码获取详细的入参、出参和上下文信息
  - 提取缺失方法的调用上下文和周围代码
  - 解析方法调用的参数类型和返回类型

  - 添加源码位置和包信息的精确定位
  - _需求: 1.1, 1.3_

- [x] 2.3 完善方法问题的 JSON 格式

  - 扩展 `ErrorReport` 模型以包含详细的方法签名信息
  - 添加调用上下文、参数类型映射和返回类型信息
  - 实现方法调用位置的精确定位（文件、行号、列号）
  - 生成符合第二阶段 API 匹配需求的标准化 JSON 格式
  - 编写完整的数据验证和测试用例
  - _需求: 1.2, 4.2_

- [x] 2.4 完成第一阶段完整流程

  - 整合 `inspect_method.py` + `class_file_reader.py` 的完整工作流程
  - 实现：识别方法问题 → 读取源码信息 → 生成完整 JSON 清单
  - 确保生成的 JSON 格式符合第二阶段 API 匹配的输入要求
  - 添加数据质量验证和错误处理机制
  - 编写完整的端到端测试，验证从问题识别到 JSON 输出的全流程
  - _需求: 1.1, 1.2, 1.3, 4.2_

## 第二阶段：API 匹配器实现

- [x] 3. 实现源码解析和向量编码功能

  - 创建 Java 源码解析器
  - 实现 GraphCodeBERT 向量编码
  - 建立高效的批量处理机制
  - _需求: 2.1, 2.2, 2.3, 2.4_

- [x] 3.1 实现源码解析器

  - 创建 `SourceCodeParser` 类
  - 使用 javalang 实现 Java 语法树解析
  - 提取方法签名、参数类型、返回类型信息
  - 处理泛型、内部类、注解等复杂语法
  - 实现错误恢复机制（跳过有问题的文件）
  - 编写源码解析测试用例
  - _需求: 2.1, 2.2, 6.3_

- [x] 3.2 实现向量编码器

  - 创建 `VectorEncoder` 类
  - 集成 GraphCodeBERT 模型和 tokenizer
  - 实现方法描述文本构建逻辑
  - 添加批量编码和缓存机制
  - 处理模型加载失败的降级策略
  - 编写向量编码一致性测试
  - _需求: 3.1, 3.2, 6.3_

- [x] 3.3 实现相似度匹配器

  - 创建 `SimilarityMatcher` 类
  - 集成 FAISS 向量索引和检索
  - 实现 TOP-K 相似度搜索
  - 添加相似度分数计算逻辑
  - 优化批量查询性能
  - 编写相似度匹配精度测试
  - _需求: 3.3, 3.4_

- [x] 3.4 实现结果生成器

  - 创建 `ResultGenerator` 类
  - 实现迁移建议 JSON 格式生成
  - 添加类名匹配和额外指标计算
  - 实现结果排序和过滤逻辑
  - 添加统计信息和元数据
  - 编写结果生成测试用例
  - _需求: 4.1, 4.2, 4.3_

## API 匹配引擎集成和优化

- [x] 4. 集成第二阶段组件并优化性能

  - 创建 `APIMatchingEngine` 主控制器
  - 实现完整的匹配流程
  - 添加性能优化和内存管理
  - _需求: 3.1, 3.2, 3.3, 3.4_

- [x] 4.1 实现 API 匹配引擎主控制器

  - 创建 `APIMatchingEngine` 类
  - 集成所有第二阶段组件
  - 实现完整的匹配流程控制
  - 添加进度跟踪和状态报告
  - 实现检查点和恢复机制
  - _需求: 3.1, 5.3_

- [x] 4.2 实现性能优化和内存管理

  - 添加批量处理优化
  - 实现向量缓存机制
  - 优化内存使用和垃圾回收
  - 实现多线程处理优化
  - 编写性能基准测试
  - _需求: 3.2, 3.4, 6.4_

## 命令行接口和工具集成

- [x] 5. 实现命令行接口和完整工具集成

  - 创建统一的命令行接口
  - 实现两阶段流程控制
  - 添加配置管理和参数验证
  - _需求: 5.1, 5.2, 5.3, 5.4_

- [x] 5.1 实现命令行接口

  - 创建主入口脚本 `migration_tool.py`
  - 使用 argparse 实现命令行参数解析
  - 支持分阶段执行（--stage 1, 2, both）
  - 添加配置文件和命令行参数支持
  - 实现详细的帮助信息和使用示例
  - _需求: 5.2, 6.2_

- [x] 5.2 实现配置管理系统

  - 创建 `ConfigManager` 类
  - 支持 YAML 配置文件解析
  - 实现配置验证和默认值处理
  - 支持环境变量覆盖
  - 添加配置模板生成功能
  - 编写配置管理测试

  - _需求: 6.1, 6.2, 6.4_

- [x] 5.3 实现日志和错误处理系统

  - 创建统一的日志配置
  - 实现分级日志记录（DEBUG, INFO, WARNING, ERROR）
  - 添加文件和控制台双重输出
  - 实现异常捕获和友好错误信息
  - 添加执行统计和进度报告
  - _需求: 6.3, 6.4_

- [x] 5.4 集成两阶段流程控制

  - 实现完整的两阶段执行流程
  - 添加阶段间数据传递和验证
  - 支持从任意阶段重新开始
  - 实现执行状态检查和恢复
  - 编写端到端集成测试
  - _需求: 5.1, 5.3, 5.4_

## 智能化增强功能

- [-] 6. 实现基于大模型的智能分析和增强知识库

  - 实现源码上下文智能分析
  - 构建大模型增强的方法理解系统
  - 优化向量库质量和匹配精度
  - _需求: 提升 API 匹配的准确性和智能化水平_

- [x] 6.1 实现源码上下文智能分析器

  - 扩展现有的 `SourceCodeParser` 类，增加根据报错内容精确搜索源码方法的功能
  - 利用现有的 AST 语法树分析能力（基于 javalang），提取完整的方法定义和调用上下文

  - 集成迁移前源码路径配置，支持从旧源码中查找缺失方法的原始实现
  - 使用当前大模型分析方法的业务逻辑、功能用途和调用意图
  - 结合知识库目录下所有 MD 文件的方法替换规则进行智能分析
  - 生成结构化的方法功能描述和语义标签
  - 实现方法调用链分析和依赖关系理解
  - 编写精确源码搜索和 AST 分析的测试用例
  - _需求: 提升错误方法理解的准确性，支持从旧源码精确提取方法实现_

- [x] 6.1.1 实现精确源码搜索器









  - 创建 `PreciseSourceSearcher` 类，根据报错内容在迁移前源码中进行精确搜索
  - 实现基于方法名、类名、包名的多维度搜索策略
  - 扩展现有的 `JavaLangASTParser` 功能，支持内容匹配和上下文提取
  - 使用现有 AST 解析器提取方法的完整代码块和上下文信息
  - 实现方法边界识别，确保提取完整的方法定义
  - 添加方法重载处理，根据参数类型精确匹配
  - 实现源码文件缓存和索引机制提升搜索效率
  - _需求: 根据报错内容精确搜索和提取旧源码中的方法实现_

- [x] 6.2 实现大模型增强的方法理解系统

  - 创建 `MethodSemanticAnalyzer` 类，作为知识库构建的核心组件
  - 直接使用当前大模型（Kiro）进行深度代码理解
  - 实现方法代码的语义理解和功能描述生成（最高权重内容）
  - 结合知识库目录下所有 MD 文档提供的方法介绍和替换规则
  - 分析方法的业务逻辑、参数用途、返回值含义和调用场景
  - 生成结构化的方法功能标签、分类和使用说明
  - 实现批量方法分析、缓存机制和增量更新
  - 建立大模型理解内容的质量评估和验证机制
  - 实现 MD 文档知识与大模型分析结果的融合策略
  - _需求: 构建以大模型理解为核心，结合 MD 文档知识的高质量方法语义知识库_

- [x] 6.2.1 实现向量化时的知识库查询增强

  - 创建 `KnowledgeBaseQueryEnhancer` 类，在向量化过程中查询知识库
  - 实现方法名称、类名、包名在知识库 MD 文档中的实时查询
  - 当找到相关知识库条目时，结合 MD 文档内容加强语义理解
  - 使用当前大模型融合方法代码和知识库描述，生成增强的语义描述
  - 将增强后的语义描述用于向量编码，提升向量质量
  - 实现知识库匹配的置信度评估和权重分配
  - 建立知识库查询的缓存机制，提升查询效率
  - _需求: 在向量化时动态查询知识库，结合 MD 文档知识增强方法的语义理解_

- [x] 6.3 优化向量编码策略

  - 重新设计方法特征表示，创建 `EnhancedMethodFeatures` 数据模型
  - 扩展 `VectorEncoder` 支持基于增强特征的多层次加权编码
  - 将大模型生成的语义理解作为主要编码内容（权重 70%，通过重复增强）
  - 实现知识库匹配的动态权重分配（0-20%，基于匹配置信度）
  - 结合代码结构特征和上下文特征进行综合编码（各 10%权重）
  - 优化编码文本构建逻辑，使用 `get_weighted_description_for_vector()` 方法：
    - 重点编码核心业务参数，排除框架参数（tenantId 等）
    - 在参数语义描述中忽略框架级参数，专注业务逻辑参数
    - 增强业务功能描述的权重，减少参数签名差异的影响
  - 实现参数过滤机制：在向量编码时自动识别并排除框架参数
  - 建立业务参数语义增强：对核心业务参数进行语义标注和权重提升
  - 实现特征权重的自适应调整和向量质量评估
  - 编写针对框架参数差异的向量编码效果对比测试
  - _需求: 10.2, 10.3 - 基于业务逻辑的向量编码，减少框架参数差异影响_

- [x] 6.4 实现参数兼容性分析器

  - 创建 `ParameterCompatibilityAnalyzer` 类，专门处理新旧方法参数差异
  - 实现框架参数自动识别（tenantId、userId、projectId、sessionId 等常见框架参数）
  - 建立框架参数白名单，在匹配时忽略这些参数差异对相似度的影响
  - 分析核心业务参数的语义对应关系，专注于业务逻辑匹配
  - 检测参数类型变化（如 Long->String）和封装模式（多参数->DTO 对象）
  - 实现参数映射策略：识别旧方法参数如何映射到新方法的业务参数
  - 生成具体的参数迁移代码建议，包括如何获取和传递新增的框架参数
  - 计算参数兼容性评分，区分框架参数差异和业务参数差异的影响
  - 在向量匹配时应用参数兼容性权重，提升匹配准确性
  - 处理常见的框架升级模式：单一方法参数 -> 上下文对象 + 业务参数
  - 编写参数兼容性分析的测试用例，特别是 tenantId 等框架参数的处理
  - _需求: 10.1, 10.2, 10.3, 10.4 - 智能处理框架升级带来的参数差异_

- [x] 6.5 实现智能匹配评分系统

  - 创建 `IntelligentMatchingScorer` 类，集成参数兼容性分析
  - 实现多维度匹配评分算法：
    - 语义相似度（60%）：基于大模型理解的功能匹配
    - 参数兼容性（25%）：重点评估业务参数匹配，忽略框架参数差异
    - 上下文相关性（15%）：调用场景和业务逻辑匹配
  - 添加框架参数容忍机制：tenantId 等框架参数不影响匹配评分
  - 实现业务参数语义匹配：专注于核心业务逻辑参数的对应关系
  - 集成参数兼容性分析结果，优先推荐业务参数兼容性高的方法
  - 实现参数迁移复杂度评估：简单参数映射 vs 复杂 DTO 转换
  - 处理常见框架升级场景的评分优化：
    - 新增框架参数（tenantId 等）不降低匹配分数
    - 参数封装（多参数->DTO）给予合理评分
    - 参数类型变化根据语义兼容性评分
  - 生成可解释的匹配报告，说明参数差异和迁移建议
  - 编写针对框架参数差异的匹配质量评估测试
  - _需求: 10.1, 10.2, 10.3, 10.4 - 提升 API 匹配准确性，正确处理框架参数差异_

## 测试和文档完善

- [ ] 7. 完善测试覆盖和项目文档

  - 编写全面的单元测试和集成测试
  - 创建用户文档和开发者指南
  - 添加示例项目和使用案例
  - _需求: 所有需求的验证_

- [ ] 7.1 编写全面的测试套件

  - 为所有核心组件编写单元测试
  - 创建集成测试覆盖完整流程
  - 添加性能基准测试
  - 实现测试数据生成和管理
  - 设置持续集成测试流程
  - 确保测试覆盖率达到 90% 以上

- [ ] 7.2 创建项目文档

  - 编写 README.md 包含安装和快速开始指南
  - 创建详细的用户手册和 API 文档
  - 编写开发者贡献指南
  - 添加故障排除和常见问题解答
  - 创建配置参考文档

- [ ] 7.3 准备示例和演示
  - 创建示例 Java 项目用于测试
  - 准备演示用的错误清单和匹配结果
  - 编写使用案例和最佳实践文档
  - 创建性能优化指南
  - 准备部署和运维文档

## 部署和发布准备

- [ ] 8. 准备项目发布和部署

  - 完善包管理和依赖配置
  - 创建安装脚本和部署指南
  - 进行最终的质量检查和优化
  - _需求: 所有需求的最终验证_

- [ ] 8.1 完善包管理和分发

  - 创建 setup.py 和 pyproject.toml
  - 配置包依赖和版本管理
  - 准备 PyPI 发布配置
  - 创建 Docker 容器化部署选项
  - 编写自动化构建脚本

- [ ] 8.2 最终质量检查和优化
  - 进行代码质量检查和重构
  - 优化性能和内存使用
  - 验证所有需求的实现
  - 进行用户接受测试
  - 准备发布说明和变更日志

flowchart LR
  A[读取 target_method_test.json] --> B[在当前项目中查找报错的调用点]
  B --> C[从 legacy_src_path 读取原始代码]
  C --> D[定位缺失的类和方法上下文]
  D --> E[读取缺失的类的方法源码]
E-->F[大模型理解源码和上下文信息，生成查询向量]

  %% 向量库创建流程
  subgraph vec[向量库创建流程]
    v1[读取依赖代码库文件] --> v2[提取方法定义]
    v2 --> v3[读取知识库目录下的 md 文件]
    v3 --> v4[生成功能说明（代码+知识库）]
    v4 --> v5[提取方法特征：方法名/入参/出参/说明]
    v5 --> v6[生成方法向量]
    v6 --> store[(向量库)]
  end

  F --> Q[向量库相似度检索]
  store --> Q
  Q --> top5{取 Top-5 最相似方法}
  top5 --> LLM[大模型读取这 5 个方法源码]
  LLM --> plan[确认替换与重构方案]
  plan --> J[生成迁移建议输出]