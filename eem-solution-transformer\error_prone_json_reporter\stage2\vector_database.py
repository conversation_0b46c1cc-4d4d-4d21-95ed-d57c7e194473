"""
简单的向量数据库实现

用于加载和保存向量库数据。
"""

import os
import json
import logging
import pickle
from typing import List, Dict, Any, Tuple, Optional
from pathlib import Path
import numpy as np

from error_prone_json_reporter.common.models import MethodInfo


class VectorDatabase:
    """简单的向量数据库实现"""
    
    def __init__(self, db_path: str):
        """
        初始化向量数据库
        
        Args:
            db_path: 数据库文件路径
        """
        self.db_path = Path(db_path)
        self.logger = logging.getLogger(__name__)
    
    def exists(self) -> bool:
        """检查数据库文件是否存在"""
        return self.db_path.exists()
    
    def load(self) -> <PERSON>ple[List[MethodInfo], np.ndarray, Dict[str, Any]]:
        """
        加载向量库数据
        
        Returns:
            (方法列表, 向量矩阵, 元数据)
        """
        if not self.exists():
            raise FileNotFoundError(f"向量库文件不存在: {self.db_path}")
        
        try:
            with open(self.db_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 重构MethodInfo对象
            methods = []
            for method_data in data.get('methods', []):
                method = MethodInfo(
                    package=method_data.get('package', ''),
                    class_name=method_data.get('class_name', ''),
                    method_name=method_data.get('method_name', ''),
                    parameters=method_data.get('parameters', []),
                    return_type=method_data.get('return_type', ''),
                    context=method_data.get('context', ''),
                    file_path=method_data.get('file_path', ''),
                    modifiers=method_data.get('modifiers', [])
                )
                methods.append(method)
            
            # 加载向量数据
            vectors_data = data.get('vectors', [])
            if vectors_data:
                vectors = np.array(vectors_data)
            else:
                vectors = np.array([])
            
            # 加载元数据
            metadata = data.get('metadata', {})
            
            self.logger.info(f"成功加载向量库: {len(methods)} 个方法, 向量维度: {vectors.shape}")
            
            return methods, vectors, metadata
            
        except Exception as e:
            self.logger.error(f"加载向量库失败: {str(e)}")
            raise
    
    def save(self, methods: List[MethodInfo], vectors: np.ndarray, 
             metadata: Optional[Dict[str, Any]] = None) -> None:
        """
        保存向量库数据
        
        Args:
            methods: 方法列表
            vectors: 向量矩阵
            metadata: 元数据
        """
        try:
            # 确保目录存在
            self.db_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 准备数据
            data = {
                'methods': [],
                'vectors': vectors.tolist() if vectors.size > 0 else [],
                'metadata': metadata or {}
            }
            
            # 序列化方法信息
            for method in methods:
                method_data = {
                    'package': method.package,
                    'class_name': method.class_name,
                    'method_name': method.method_name,
                    'parameters': method.parameters,
                    'return_type': method.return_type,
                    'context': method.context,
                    'file_path': method.file_path,
                    'modifiers': method.modifiers
                }
                data['methods'].append(method_data)
            
            # 保存到文件
            with open(self.db_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"向量库已保存: {self.db_path}")
            
        except Exception as e:
            self.logger.error(f"保存向量库失败: {str(e)}")
            raise
    
    def get_stats(self) -> Dict[str, Any]:
        """获取数据库统计信息"""
        if not self.exists():
            return {'exists': False}
        
        try:
            with open(self.db_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            methods_count = len(data.get('methods', []))
            vectors_shape = np.array(data.get('vectors', [])).shape
            
            return {
                'exists': True,
                'methods_count': methods_count,
                'vectors_shape': vectors_shape,
                'file_size': self.db_path.stat().st_size,
                'metadata': data.get('metadata', {})
            }
            
        except Exception as e:
            self.logger.error(f"获取统计信息失败: {str(e)}")
            return {'exists': True, 'error': str(e)}