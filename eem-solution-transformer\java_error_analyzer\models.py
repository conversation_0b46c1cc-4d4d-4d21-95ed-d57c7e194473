"""
数据模型定义
"""
from dataclasses import dataclass
from typing import List, Optional, Dict


@dataclass
class CompilationError:
    """编译错误数据模型"""
    file_path: str
    line: int
    module: str
    package: str
    description: str
    highlighted_element: str
    severity: str = "ERROR"
    offset: int = 0
    length: int = 0
    
    def __str__(self):
        return f"{self.file_path}:{self.line} - {self.description} ({self.highlighted_element})"


@dataclass
class SimilarityResult:
    """相似度结果数据模型"""
    class_name: str
    similarity_score: float
    reasons: List[str]
    full_qualified_name: str
    
    def __str__(self):
        return f"{self.class_name} (相似度: {self.similarity_score:.2f})"


@dataclass
class ProcessingConfig:
    """处理配置数据模型"""
    script_path: str
    project_path: str
    timeout_seconds: int = 30
    max_results: int = 10
    knowledge_base_path: str = "./能管代码迁移知识库.md"
    log_level: str = "INFO"
    log_file: str = "error_analysis.log"


@dataclass
class ClassAnalysisResult:
    """类分析结果数据模型"""
    recommended_class: str
    confidence_score: float
    compatibility_analysis: str
    potential_issues: List[str]
    migration_notes: List[str]
    alternative_options: List[str]
    
    def __str__(self):
        return f"推荐类: {self.recommended_class} (置信度: {self.confidence_score:.2f})"


@dataclass
class ClassFileInfo:
    """类文件信息数据模型"""
    class_name: str
    file_path: str
    content: str
    methods: List[str]
    fields: List[str]
    imports: List[str]
    package_name: Optional[str] = None
    
    def __str__(self):
        return f"{self.class_name} ({self.file_path})"


@dataclass
class ErrorStatistics:
    """错误统计信息数据模型"""
    total_errors: int
    files_with_errors: int
    average_errors_per_file: float
    most_problematic_files: List[tuple]  # (file_path, error_count)
    error_types_distribution: Dict[str, int]
    
    def __str__(self):
        return f"总错误数: {self.total_errors}, 涉及文件: {self.files_with_errors}"