"""
增强的方法特征表示

重新设计方法特征表示，支持基于增强特征的多层次加权编码。
"""

import re
import logging
from typing import List, Dict, Optional, Set, Any
from dataclasses import dataclass

from error_prone_json_reporter.common.models import MethodInfo


@dataclass
class EnhancedMethodFeatures:
    """增强的方法特征"""
    
    # 核心语义特征（权重70%）
    semantic_description: str  # 大模型生成的语义理解
    business_purpose: str      # 业务目的
    functional_summary: str    # 功能摘要
    
    # 知识库特征（权重0-20%，基于匹配置信度）
    knowledge_base_description: str  # 知识库描述
    replacement_rules: List[str]     # 替换规则
    kb_confidence: float             # 知识库匹配置信度
    
    # 代码结构特征（权重10%）
    method_signature: str      # 方法签名
    parameter_types: List[str] # 参数类型
    return_type: str          # 返回类型
    class_context: str        # 类上下文
    
    # 上下文特征（权重10%）
    package_info: str         # 包信息
    usage_context: str        # 使用上下文
    business_tags: List[str]  # 业务标签
    
    # 业务参数特征（用于过滤框架参数）
    business_parameters: List[str]  # 核心业务参数
    framework_parameters: List[str] # 框架参数
    
    def __post_init__(self):
        """初始化后处理"""
        # 确保所有字符串字段不为None
        self.semantic_description = self.semantic_description or ""
        self.business_purpose = self.business_purpose or ""
        self.functional_summary = self.functional_summary or ""
        self.knowledge_base_description = self.knowledge_base_description or ""
        self.method_signature = self.method_signature or ""
        self.return_type = self.return_type or ""
        self.class_context = self.class_context or ""
        self.package_info = self.package_info or ""
        self.usage_context = self.usage_context or ""
        
        # 确保列表字段不为None
        self.replacement_rules = self.replacement_rules or []
        self.parameter_types = self.parameter_types or []
        self.business_tags = self.business_tags or []
        self.business_parameters = self.business_parameters or []
        self.framework_parameters = self.framework_parameters or []
        
        # 确保置信度在有效范围内
        self.kb_confidence = max(0.0, min(1.0, self.kb_confidence))


class EnhancedMethodFeaturesExtractor:
    """增强方法特征提取器"""
    
    # 常见框架参数名称（精确匹配）
    FRAMEWORK_PARAM_NAMES = {
        'tenantid', 'projectid', 'sessionid', 'requestid',
        'contextid', 'traceid', 'spanid', 'correlationid', 'transactionid',
        'clientid', 'appid', 'version', 'timestamp', 'locale', 'timezone',
        'token', 'authorization', 'apikey', 'signature', 'nonce'
    }
    
    # 常见框架参数类型
    FRAMEWORK_PARAM_TYPES = {
        'HttpServletRequest', 'HttpServletResponse', 'HttpSession',
        'SecurityContext', 'Authentication', 'Principal', 'UserDetails',
        'RequestContext', 'ApplicationContext', 'BeanFactory',
        'Environment', 'ConfigurableEnvironment'
    }
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def extract_features(self, method_info: MethodInfo, 
                        semantic_analysis: Optional[Dict[str, Any]] = None,
                        knowledge_base_info: Optional[Dict[str, Any]] = None) -> EnhancedMethodFeatures:
        """
        提取增强的方法特征
        
        Args:
            method_info: 基础方法信息
            semantic_analysis: 语义分析结果
            knowledge_base_info: 知识库信息
            
        Returns:
            增强的方法特征
        """
        # 提取核心语义特征
        semantic_description = self._extract_semantic_description(method_info, semantic_analysis)
        business_purpose = self._extract_business_purpose(method_info, semantic_analysis)
        functional_summary = self._extract_functional_summary(method_info, semantic_analysis)
        
        # 提取知识库特征
        kb_description = ""
        replacement_rules = []
        kb_confidence = 0.0
        
        if knowledge_base_info:
            kb_description = knowledge_base_info.get('description', '')
            replacement_rules = knowledge_base_info.get('replacement_rules', [])
            kb_confidence = knowledge_base_info.get('confidence', 0.0)
        
        # 提取代码结构特征
        method_signature = self._build_method_signature(method_info)
        parameter_types = self._extract_parameter_types(method_info)
        class_context = self._extract_class_context(method_info)
        
        # 提取上下文特征
        package_info = method_info.package or ""
        usage_context = method_info.context or ""
        business_tags = self._extract_business_tags(method_info, semantic_analysis)
        
        # 分离业务参数和框架参数
        business_params, framework_params = self._separate_parameters(method_info)
        
        return EnhancedMethodFeatures(
            semantic_description=semantic_description,
            business_purpose=business_purpose,
            functional_summary=functional_summary,
            knowledge_base_description=kb_description,
            replacement_rules=replacement_rules,
            kb_confidence=kb_confidence,
            method_signature=method_signature,
            parameter_types=parameter_types,
            return_type=method_info.return_type or "",
            class_context=class_context,
            package_info=package_info,
            usage_context=usage_context,
            business_tags=business_tags,
            business_parameters=business_params,
            framework_parameters=framework_params
        )
    
    def _extract_semantic_description(self, method_info: MethodInfo, 
                                    semantic_analysis: Optional[Dict[str, Any]]) -> str:
        """提取语义描述"""
        if semantic_analysis:
            parts = []
            
            # 功能描述
            if 'function_description' in semantic_analysis:
                parts.append(semantic_analysis['function_description'])
            
            # 业务目的
            if 'business_purpose' in semantic_analysis:
                parts.append(semantic_analysis['business_purpose'])
            
            # 使用场景
            if 'usage_scenarios' in semantic_analysis:
                scenarios = semantic_analysis['usage_scenarios']
                if scenarios:
                    parts.append("使用场景: " + ", ".join(scenarios[:3]))
            
            return " ".join(parts)
        
        # 降级到基于方法名的推断
        return self._infer_semantic_from_method_name(method_info.method_name)
    
    def _extract_business_purpose(self, method_info: MethodInfo, 
                                semantic_analysis: Optional[Dict[str, Any]]) -> str:
        """提取业务目的"""
        if semantic_analysis and 'business_purpose' in semantic_analysis:
            return semantic_analysis['business_purpose']
        
        # 基于方法名推断
        method_name = method_info.method_name.lower()
        
        if any(keyword in method_name for keyword in ['get', 'find', 'query', 'search']):
            return "数据查询和检索"
        elif any(keyword in method_name for keyword in ['create', 'add', 'insert']):
            return "数据创建和添加"
        elif any(keyword in method_name for keyword in ['update', 'modify', 'edit']):
            return "数据更新和修改"
        elif any(keyword in method_name for keyword in ['delete', 'remove', 'drop']):
            return "数据删除和移除"
        elif any(keyword in method_name for keyword in ['save', 'store', 'persist']):
            return "数据保存和持久化"
        elif any(keyword in method_name for keyword in ['validate', 'check', 'verify']):
            return "数据验证和检查"
        else:
            return "业务逻辑处理"
    
    def _extract_functional_summary(self, method_info: MethodInfo, 
                                  semantic_analysis: Optional[Dict[str, Any]]) -> str:
        """提取功能摘要"""
        if semantic_analysis and 'concise_description' in semantic_analysis:
            return semantic_analysis['concise_description']
        
        # 生成简洁的功能摘要
        class_name = method_info.class_name or "Unknown"
        method_name = method_info.method_name
        
        return f"{class_name}类的{method_name}方法"
    
    def _build_method_signature(self, method_info: MethodInfo) -> str:
        """构建方法签名"""
        parts = []
        
        if method_info.class_name:
            parts.append(method_info.class_name)
        
        method_part = method_info.method_name
        
        if method_info.parameters:
            params = ", ".join(method_info.parameters)
            method_part += f"({params})"
        else:
            method_part += "()"
        
        if method_info.return_type:
            method_part += f" -> {method_info.return_type}"
        
        parts.append(method_part)
        
        return ".".join(parts)
    
    def _extract_parameter_types(self, method_info: MethodInfo) -> List[str]:
        """提取参数类型"""
        types = []
        
        if method_info.parameters:
            for param in method_info.parameters:
                if ':' in param:
                    param_type = param.split(':')[-1].strip()
                    # 简化类型名（去掉包名）
                    simple_type = param_type.split('.')[-1]
                    types.append(simple_type)
                else:
                    types.append(param)
        
        return types
    
    def _extract_class_context(self, method_info: MethodInfo) -> str:
        """提取类上下文"""
        parts = []
        
        if method_info.package:
            parts.append(f"package: {method_info.package}")
        
        if method_info.class_name:
            parts.append(f"class: {method_info.class_name}")
        
        return " ".join(parts)
    
    def _extract_business_tags(self, method_info: MethodInfo, 
                             semantic_analysis: Optional[Dict[str, Any]]) -> List[str]:
        """提取业务标签"""
        tags = set()
        
        # 从语义分析中提取标签
        if semantic_analysis and 'business_tags' in semantic_analysis:
            tags.update(semantic_analysis['business_tags'])
        
        # 从包名提取标签
        if method_info.package:
            package_parts = method_info.package.split('.')
            for part in package_parts:
                if len(part) > 2 and part not in {'com', 'org', 'net', 'java', 'javax'}:
                    tags.add(part.lower())
        
        # 从类名提取标签
        if method_info.class_name:
            class_words = re.findall(r'[A-Z][a-z]*', method_info.class_name)
            for word in class_words:
                if len(word) > 2:
                    tags.add(word.lower())
        
        # 从方法名提取标签
        method_words = re.findall(r'[A-Z][a-z]*', method_info.method_name)
        for word in method_words:
            if len(word) > 2:
                tags.add(word.lower())
        
        return list(tags)[:10]  # 限制标签数量
    
    def _separate_parameters(self, method_info: MethodInfo) -> tuple[List[str], List[str]]:
        """分离业务参数和框架参数"""
        business_params = []
        framework_params = []
        
        if not method_info.parameters:
            return business_params, framework_params
        
        for param in method_info.parameters:
            param_name = ""
            param_type = ""
            
            if ':' in param:
                param_name, param_type = param.split(':', 1)
                param_name = param_name.strip()
                param_type = param_type.strip()
            else:
                param_type = param.strip()
            
            # 检查是否为框架参数
            is_framework = False
            
            # 基于参数名检查（精确匹配）
            if param_name and param_name.lower() in self.FRAMEWORK_PARAM_NAMES:
                is_framework = True
            
            # 特殊处理：userId 通常是业务参数，除非明确是框架上下文
            if param_name and param_name.lower() == 'userid' and 'context' not in param_type.lower():
                is_framework = False
            
            # 基于参数类型检查
            simple_type = param_type.split('.')[-1]
            if simple_type in self.FRAMEWORK_PARAM_TYPES:
                is_framework = True
            
            # 基于常见模式检查
            if any(pattern in param_type.lower() for pattern in ['context', 'request', 'response', 'session']):
                is_framework = True
            
            if is_framework:
                framework_params.append(param)
            else:
                business_params.append(param)
        
        return business_params, framework_params
    
    def _infer_semantic_from_method_name(self, method_name: str) -> str:
        """基于方法名推断语义"""
        # 分解方法名
        words = re.findall(r'[A-Z][a-z]*|[a-z]+', method_name)
        
        if not words:
            return f"执行{method_name}操作"
        
        # 识别动作词
        action_word = words[0].lower()
        object_words = words[1:] if len(words) > 1 else []
        
        action_mapping = {
            'get': '获取',
            'find': '查找',
            'search': '搜索',
            'query': '查询',
            'create': '创建',
            'add': '添加',
            'insert': '插入',
            'update': '更新',
            'modify': '修改',
            'edit': '编辑',
            'delete': '删除',
            'remove': '移除',
            'save': '保存',
            'store': '存储',
            'validate': '验证',
            'check': '检查',
            'process': '处理',
            'handle': '处理',
            'execute': '执行',
            'run': '运行'
        }
        
        action_chinese = action_mapping.get(action_word, action_word)
        
        if object_words:
            object_str = "".join(object_words)
            return f"{action_chinese}{object_str}相关数据"
        else:
            return f"执行{action_chinese}操作"
    
    def _infer_parameter_semantic(self, param_name: str, param_type: str) -> str:
        """推断参数语义"""
        param_lower = param_name.lower()
        
        # 基于参数名推断
        if 'id' in param_lower:
            return "标识符"
        elif 'name' in param_lower:
            return "名称"
        elif 'code' in param_lower:
            return "编码"
        elif 'type' in param_lower:
            return "类型"
        elif 'status' in param_lower:
            return "状态"
        elif 'amount' in param_lower or 'price' in param_lower:
            return "金额"
        elif 'email' in param_lower:
            return "邮箱"
        elif 'phone' in param_lower:
            return "电话"
        elif 'count' in param_lower or 'num' in param_lower:
            return "数量"
        elif 'date' in param_lower or 'time' in param_lower:
            return "时间"
        elif 'url' in param_lower or 'path' in param_lower:
            return "路径"
        
        # 基于参数类型推断
        type_lower = param_type.lower()
        if 'string' in type_lower:
            return "文本"
        elif 'long' in type_lower or 'integer' in type_lower:
            return "数值"
        elif 'boolean' in type_lower:
            return "布尔值"
        elif 'date' in type_lower:
            return "日期"
        elif 'list' in type_lower:
            return "列表"
        elif 'map' in type_lower:
            return "映射"
        
        return "参数"