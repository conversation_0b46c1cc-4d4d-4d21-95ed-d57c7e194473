
## 阶段 2: 第二轮问题分析（需要等第一轮完成后处理）

- [ ] 5. 执行方法问题扫描

  - [ ] 5.1 运行 inspect_method.py 生成方法问题报告

    - **执行条件**: 必须等第一轮所有问题修复完成并编译通过后才能开始
    - **工具执行**: 使用 `java_error_analyzer\inspect_method.py` 脚本
    - **数据来源**: 使用现有的 `java_error_analyzer\JavaAnnotator.xml` 文件
    - **输出目录**: `out` 目录
    - **默认参数**:
      - 项目路径: 当前项目
      - 配置文件: `java_error_analyzer\ai_method.xml`
      - 输出目录: out\method_issues_report.json
    - **执行命令**:

      ```bash
      # 使用默认参数
      python java_error_analyzer\inspect_method.py

      # 或使用自定义参数
      python java_error_analyzer\inspect_method.py --project-path "你的项目路径" --inspection-profile "配置文件路径" --output-path "输出目录"
      ```

    - **参数说明**:
      - `--project-path`: 要检查的 Java 项目根目录
      - `--inspection-profile`: java_error_analyzer\ai_method.xml
      - `--output-path`: out\method_issues_report.md
    - **输出文件**: `out\method_issues_report.json`
    - **报告格式**:
      ```json
      [
    {
    "issue_id": 107,
    "error_code": "method_issues",
    "module": "eem-solution-transformer-core",
    "package": "com.cet.eem.fusion.groupenergy.core.service.impl",
    "class": "TeamEnergyServiceImpl",
    "missing_method": "getProjectTree()",
    "description": "无法解析方法 'getProjectTree(Integer)'",
    "line": "466"
  }
]
      ```

    - **执行说明**:
      - 如果 `inspect.bat` 不可用，脚本会直接解析现有的 `JavaAnnotator.xml` 文件
      - 脚本会筛选出所有"无法解析方法"相关的问题
      - 按类名分组生成结构化的方法问题报告
    - _输出: 完整的 out\method_issues_report.json 文件，包含所有方法解析问题的详细信息_

- [-] 6. 第二轮问题分析和解决方案确定

  - [ ] 6.1 方法重构问题分析和解决方案确定

    - **数据来源**: out\method_issues_report.json 
    - **问题范围**: 包括：
      - 方法签名变更导致的兼容性问题
      - 方法名称变更问题
      - 业务逻辑适配问题
    - **完整性要求**: 必须处理所有方法重构相关的问题
    - **分段处理策略**:
    - **严禁一次性加载**: 绝对不要一次性加载整个 out\method_issues_report.md 文件
      - **分段读取**: 每次处理 1 个问题
      - **逐个验证**: 每处理一个问题立即验证是否正确分类和处理
      - **实时统计**: 处理过程中实时统计已处理问题数量，确保不遗漏
    - **知识库查找**:
    - **输出格式**: 生成详细的 out\task-refactor.md 文件，**统一按文件维度组织**
    - _输出: 完整的 out\task-refactor.md 文件，包含所有方法重构问题的详细分析和解决方案_

  - [ ] 6.2 多租户问题分析和解决方案确定（第二轮处理）

    - **执行条件**: 必须等第一轮所有问题修复完成并编译通过后才能开始
    - **数据来源**: 重新扫描编译后的代码，识别多租户相关问题
    - **问题范围**: 基于知识库第 2 类"多租户"，包括：
      - 字段和模型类变更（project_id → tenant_id）
      - 数据库字段变更（project_id → tenant_id）
      - Java 实体类字段变更（projectId → tenantId）
      - Getter/Setter 方法变更（getProjectId → getTenantId）
      - 工具类调用变更（GlobalInfoUtils.getProjectId → GlobalInfoUtils.getTenantId）
      - 注解和依赖注入变更（@Resource 需要添加插件前缀）
    - **完整性要求**: 必须处理所out\task-multitenant.md 文件，包含所有多租户问题的详细分析和解决方案\_有多租户相关的问题
    - **分段处理策略**: 同第一轮策略
    - **知识库查找**: 基于知识库第 2 类"多租户"的解决方案
    - **输出格式**: 生成详细的 out\task-multitenant.md 文件，**统一按文件维度组织**
    - _输出: 完整的  out\task-multitenant.md输出: 包含所有方法多租户问题的详细分析和解决方案_
  - [ ] 6.3 第二轮问题验证和修复任务生成

    - **验证任务**: 对 task-refactor.md 和 task-multitenant.md 进行完整性验证
    - **任务生成**: 生成 out\task-step-round2.md 第二轮修复任务文件
    - **优先级排序**: 按照依赖关系确定第二轮修复顺序（方法重构 → 多租户）
    - _输出: 完整的 out\task-step-round2.md 文件_

## 🚨 重要执行原则和防遗漏措施

### ⚠️ 关键执行要求

**基于实际经验教训，以下原则必须严格遵守：**

1. **严禁一次性加载大文件**

   - 绝对不要一次性读取整个 `out\questionlist.md` 文件
   - 该文件包含 很多 个问题，内容庞大，一次性加载会导致内容截断和遗漏
   - 必须采用分段读取策略，每次处理 10-20 个问题

2. **强制分段处理机制**

   - 按文件名、问题类型或行号范围分段读取
   - 每处理完一段立即进行数量验证
   - 建立处理进度跟踪，确保覆盖所有问题

3. **实时验证和追踪**

   - 处理前：统计源问题总数
   - 处理中：实时统计已处理问题数量
   - 处理后：进行最终数量核对
   - 建立问题唯一标识符进行精确追踪

4. **多重验证机制**

   - 数量验证：确保处理数量与源数量一致
   - 映射验证：确保每个问题都有对应解决方案
   - 文件覆盖验证：确保所有涉及文件都被处理
   - 质量验证：确保解决方案的完整性和可执行性

5. **遗漏检查和补救**

   - 如发现数量不一致，必须立即停止并排查
   - 逐一对比源文件和输出文件，找出遗漏问题
   - 补充遗漏问题后重新进行完整性验证

6. **格式一致性要求**

   - **统一按文件维度组织**: 所有输出文件（questionlist.md、task-import.md、task-abandon.md、task-other.md、task-step.md）都必须按文件维度组织
   - **保持文件顺序一致**: 各文件中的文件顺序必须与 out\问题列表.md 保持一致
   - **问题描述一致**: 同一个问题在不同文件中的描述、行号、位置信息必须完全一致
   - **便于追溯和处理**: 确保从原始问题到最终任务的完整追溯链路

7. **条件执行机制**
   - **智能跳过**: 验证通过的任务自动跳过对应的修复任务，提高执行效率
   - **按需修复**: 只有发现问题时才执行修复，避免不必要的操作
   - **重新验证**: 修复完成后必须重新验证，确保问题得到解决
   - **状态追踪**: 清晰记录每个验证和修复任务的执行状态

## 执行原则

### 🔄 迭代执行流程

1. **按序执行**: 严格按照 out\task-step.md 中的子任务顺序逐一执行
2. **条件判断**: 对于标记为"条件执行"的任务，先检查前置验证结果
   - 如果验证通过，跳过修复任务
   - 如果验证不通过，执行修复任务并重新验证
3. **应用方案**: 直接应用每个子任务中已确定的具体修复方案
4. **验证结果**: 验证修复后的编译和功能正确性
5. **提交修改**: 使用 git-commit-helper.ps1 提交每个子任务的修改
6. **更新状态**: 在 out\task-step.md 中将完成的子任务从 `- [ ]` 更新为 `- [x]`
7. **独立处理**: 每个任务可以独立执行，支持部分完成和增量处理
8. **进度跟踪**: 通过 checkbox 状态清晰跟踪整体修复进度
9. **处理未识别**: 所有已识别问题修复完成后，最后处理未识别问题

### 🎯 优先级标记说明

- **🟢 绿色**: 确定性修复，可直接执行
  - 单一匹配的 import 问题
  - 知识库有明确解决方案的废弃 API 问题
- **🟡 黄色**: 需要 AI 智能判断或测试验证
  - 多候选匹配的 import 问题（需要 class_file_reader.py 分析）
  - 方法签名变更和配置更新问题
- **⚪ 未识别**: 无法确定解决方案，需要进一步研究
  - AI 无法判断最佳匹配的 import 问题
  - 知识库没有解决方案的废弃 API 问题
  - 复杂的架构层面问题

### 📋 统一输出格式说明

**所有输出文件统一采用按文件维度组织的格式，确保一致性和处理效率：**

#### 1. **out\问题列表.md** (原始问题，按文件维度)

```markdown
## ClassesConfig.java

### 问题 1

error_code: "import_issues"
missing_class: "ModelLabel"
line: [3, 19]
```

#### 2. **out\task-import.md** (Import 问题解决方案，按文件维度)

```markdown
## ClassesConfig.java

### Import 问题 1: ModelLabel 类导入 (🟢 绿色标记)

- **问题位置**: 行号 3, 19
- **解决方案**: import com.cet.eem.fusion.common.modelutils.annotation.ModelLabel;
- **修复操作**: 在文件顶部添加导入语句
```

#### 3. **out\task-abandon.md** (废弃 API 问题解决方案，按文件维度)

```markdown
## TeamEnergyServiceImpl.java

### 废弃 API 问题 1: CommonUtils.calcDouble 废弃 (🟢 绿色标记)

- **问题位置**: 行号 94
- **解决方案**: 使用 NumberCalcUtils.calcDouble 替代
- **修复操作**: 替换方法调用
```


```markdown
- [x] 2. 修复 ClassesConfig.java 的 Import 和继承问题
  - [x] 2.1 添加 ModelLabel 导入 (行号: 3, 19)
  - [x] 2.4 修复继承关系变更 (行号: 20)
```

#### 1. **out\task-refactor.md** (第二轮方法重构问题解决方案，按文件维度)

#### 2. **out\task-multitenant.md** (第二轮多租户问题解决方案，按文件维度)

#### 3. **out\task-step-round2.md** (第二轮执行任务，按文件维度 + checkbox)

````

**格式统一的优势：**

- ✅ **一致性**: 所有文件都按相同的文件维度组织
- ✅ **可追溯性**: 从原始问题到最终任务，文件结构保持一致
- ✅ **处理效率**: 便于按文件逐个处理和验证
- ✅ **状态管理**: 支持文件级别的进度跟踪

## 工具使用说明

### 核心分析工具

- `error_parser.py`: 解析 JavaAnnotator.xml 错误文件
- `error_grouper.py`: 错误分组和去重处理
- `class_name_finder.py`: 查找替代类名
- `fuzzy_matcher.py`: 查找相似类，如果有多个需要大模型介入进行决策
- `class_file_reader.py`: 读取类文件详细信息（支持源码和 jar 包）
- `git-commit-helper.ps1`: 自动化代码提交

### 参考资料

- `能管代码迁移知识库.md`: 已知问题解决方案
- `JavaAnnotator.xml`: 编译错误源文件
- 项目源码: 需要修复的目标代码

## 📚 经验教训和改进措施

### 🔍 问题根源分析

基于任务 1.2 执行过程中发现的遗漏问题，总结以下经验教训：

#### 1. **文件处理策略问题**

- **问题**: 一次性读取 `out\questionlist.md` 导致内容截断
- **原因**: 文件包含 247 个问题，内容过于庞大
- **教训**: 大文件必须分段处理，不能依赖一次性加载

#### 2. **验证机制不足**

- **问题**: 缺少实时验证，只在最后进行检查
- **原因**: 没有建立处理过程中的检查点
- **教训**: 必须建立多层次、实时的验证机制

#### 3. **问题追踪缺失**

- **问题**: 没有建立问题的唯一标识和追踪机制
- **原因**: 依赖人工记忆和估算，缺少系统化追踪
- **教训**: 必须为每个问题建立唯一标识符

#### 4. **处理范围不完整**

- **问题**: 遗漏了 TeamEnergyController、TeamEnergyService 等文件的问题
- **原因**: 处理策略不够系统化，存在盲区
- **教训**: 必须建立完整的文件清单和覆盖验证

### 🛠️ 改进措施

#### 1. **强制分段处理**

```markdown
- 每次最多处理 10-20 个问题
- 按文件名或问题类型分段
- 每段处理完立即验证
- 建立处理进度跟踪表
````

#### 2. **多重验证机制**

```markdown
- 处理前验证：统计源问题总数
- 处理中验证：实时统计已处理数量
- 处理后验证：最终数量核对
- 质量验证：检查解决方案完整性
```

#### 3. **问题追踪系统**

```markdown
- 唯一标识符：文件名+行号+问题描述
- 处理状态跟踪：未处理/处理中/已完成
- 映射关系验证：源问题 ↔ 解决方案
- 遗漏检查机制：自动发现未处理问题
```

#### 4. **文件覆盖保证**

```markdown
- 建立涉及文件的完整清单
- 逐文件验证问题处理情况
- 交叉验证：按文件、按类型、按行号
- 盲区检查：确保没有遗漏的文件或问题类型
```

### 📈 质量保证措施

#### 1. **执行前检查**

- [ ] 确认分段处理策略
- [ ] 建立问题统计基线
- [ ] 准备验证检查清单
- [ ] 设置处理进度跟踪

#### 2. **执行中监控**

- [ ] 实时统计处理数量
- [ ] 每段处理后立即验证
- [ ] 记录处理进度和状态
- [ ] 及时发现和处理异常

#### 3. **执行后验证**

- [ ] 最终数量核对
- [ ] 问题映射关系验证
- [ ] 解决方案质量检查
- [ ] 生成完整性验证报告

### 🎯 成功标准

一个任务被认为成功完成，必须满足：

1. **数量完整性**: 处理问题数量 = 源问题数量
2. **映射完整性**: 每个源问题都有对应的解决方案
3. **文件完整性**: 所有涉及文件都被完整处理
4. **质量完整性**: 所有解决方案都可执行且准确
5. **验证完整性**: 通过多重验证机制确认

### 🔄 持续改进

基于每次任务执行的经验：

1. **记录问题**: 详细记录遇到的问题和解决方案
2. **更新规范**: 及时更新任务执行规范和要求
3. **优化流程**: 持续优化处理流程和验证机制
4. **分享经验**: 将经验教训应用到后续类似任务中

**最终目标**: 建立一套可靠、完整、高质量的任务执行体系，确保零遗漏、零错误。
