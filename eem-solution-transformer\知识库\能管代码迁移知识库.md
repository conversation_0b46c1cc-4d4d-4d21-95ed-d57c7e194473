## 能管代码迁移知识库

## 1.类问题

### 1. Controller 层变更

```
controller_changes:
  url_prefix:
    description: "Controller层接口URL前缀变更"
    detection_pattern: "@RequestMapping|@GetMapping|@PostMapping"
    rules:
      - old_pattern: "@RequestMapping(\"/api/v1/"
        new_pattern: "@RequestMapping(\"/api/v2/plugin/{plugin-name}/"
        example:
          before: '@RequestMapping("/api/v1/energy")'
          after: '@RequestMapping("/api/v2/plugin/energy-management/")'

  return_type:
    description: "统一返回类型为ApiResult"
    detection_pattern: "public.*ResponseEntity|public.*Response|public.*Result"
    mapping:
      ResponseEntity<T>: "ApiResult<T>"
      Response<T>: "ApiResult<T>"
      Map<String,Object>: "ApiResult<Map<String,Object>>"
    code_template: |
 	  // 成功返回
      return  Result.ok(int code, String msg, T t)

      // 失败返回
      return ApiResult.newResult(int code, String msg, T t)

      // 分页返回
      return ApiResult.newResult(int code, String msg, T t, Integer total);
```

### 3. 服务层部分废弃类详细替换方案

          NodeAuthCheckService:
    status: "部分废弃"
    detection: - "NodeAuthCheckService" - "nodeAuthCheckService\\." - "nodeAuthBffService.checkPartAuth"
    deprecated_methods: - method: "checkNodeAuth(Long nodeId, String operation)"
    replacement: "nodeAuthBffService.hasPermission(Long nodeId, String operation)" - method: "getUserNodeList(Long userId)"
    replacement: "方法已废弃，需要使用新的权限体系" - method: "checkPartAuth(baseVo, userId)"
    replacement: |
    // 原代码
    if (!nodeAuthBffService.checkPartAuth(baseVo, GlobalInfoUtils.getUserId())) {
    throw new ValidationException("无当前节点访问权限！");
    }
          // 新代码
          import com.cet.futureblue.i18n.LanguageUtil;
          import com.cet.eem.fusion.common.entity.ParentParam;
          import com.cet.eem.fusion.config.sdk.auth.service.NodeAuthCheckService;
          import com.cet.eem.fusion.common.def.i18n.DataMaintainLangKeyDef;

          @Autowired
          private NodeAuthCheckService nodeAuthBffService;

          ParentParam parentParam = new ParentParam();
          parentParam.setRootNode(new BaseEntity(baseVo.getId(),baseVo.getModelLabel()));
          parentParam.setTenantId(GlobalInfoUtils.getTenantId());
          parentParam.setUserId(GlobalInfoUtils.getUserId());
          if (!nodeAuthBffService.checkCompleteOrganizationNodes(parentParam)) {
              throw new ValidationException(LanguageUtil.getMessage(DataMaintainLangKeyDef.Connect.NO_COMPLETE_AUTH));
          }
    active_methods:
      - "checkBatchNodeAuth"
      - "checkNodeReadAuth"
      - "checkCompleteOrganizationNodes"
    EemCloudAuthService:
    status: "完全废弃"
    detection: - "EemCloudAuthService" - "eemCloudAuthService\\." - "cloudAuthService.queryUserBatch"
    import: com.cet.eem.fusion.config.sdk.auth.service.NodeAuthCheckService
    replacement_guide: |
    // 原权限校验
    boolean hasAuth = eemCloudAuthService.checkAuth(userId, resourceType);
     // 新权限校验 - 使用统一权限服务
      @Autowired
      private NodeAuthCheckService nodeAuthCheckService;

      boolean hasAuth = nodeAuthCheckService.checkCompleteAuth(
       userId,tenantId,resourceType,projectId
      );
    specific_methods:
      - method: "queryUserBatch(longs)"
        replacement: |
          // 原代码
          import com.cet.eem.service.EemCloudAuthService;
          import com.cet.eem.common.model.Result;

          @Resource
          private EemCloudAuthService cloudAuthService;

          Result<List<UserVo>> listResult = cloudAuthService.queryUserBatch(longs);
          List<UserVo> userInfoList = listResult.getData();

          // 新代码
          import com.cet.electric.matterhorn.cloud.authservice.api.UserRestApi;
          import com.cet.electric.matterhorn.cloud.authservice.common.util.i18n.ApiResultI18n;

          @Resource
          UserRestApi userRestApi;

          ApiResultI18n<List<UserVo>> userQueryResult = userRestApi.getUsers(userIdList);
          List<UserVo> userInfoList = userQueryResult.getData();
          AuthUtils:
    status: "完全废弃"
    detection: - "AuthUtils" - "authUtils.queryAndCheckUser"
    replacement_guide: |
    // 原代码
    import com.cet.eem.auth.service.AuthUtils;
     @Autowired
      AuthUtils authUtils;

      UserVo user = authUtils.queryAndCheckUser(userId);

      // 新代码
      import com.cet.electric.matterhorn.cloud.authservice.api.UserRestApi;
      import com.cet.electric.matterhorn.cloud.authservice.common.util.i18n.ApiResultI18n;

      @Resource
      UserRestApi userRestApi;

      ApiResultI18n<UserVo> usrRes = userRestApi.getUserByUserId(userId);
      UserVo user = usrRes.getData();

### 4. 部分工具类废弃和替换详细方案

    deprecated_utils:
    CommonUtils:
    deprecated_methods: - name: "calcDouble"
    detection: "CommonUtils.calcDouble"
    replacement:
    old: "CommonUtils.calcDouble(value1, value2, operator)"
    new: NumberCalcUtils.calcDouble()
    import：com.cet.eem.fusion.common.utils.datatype.NumberCalcUtils） 
    - name: "parseInteger"
    detection: "CommonUtils.parseInteger"
    replacement:
    old: "CommonUtils.parseInteger(str, defaultValue)"
    new: NumberUtils.parseInteger()（同步修改所属类路径：import com.cet.eem.fusion.common.utils.datatype.NumberCalcUtils;） - name: "sort"
    detection: "CommonUtils.sort"
    replacement:
    old: "CommonUtils.sort(list, field, order)"
    new: |
    // 原代码
    result.sort((v1, v2) -> CommonUtils.sort(v1.getCreateTime(), v2.getCreateTime(), false));
            // 新代码 - 使用 SortUtils
            import com.cet.eem.fusion.common.utils.SortUtils;
            result.sort((v1, v2) -> SortUtils.sort(v1.getCreateTime(), v2.getCreateTime(), false));
            JsonUtil:
    status: "完全废弃"
    detection: "JsonUtil\\."
    replacement_options: - library: "JsonTransferUtils"
    example: |
    // 原代码
    List<InspectionWorkOrderDto> workOrderList = JsonUtil.mapList2BeanList(modelEntityList.getData(), InspectionWorkOrderDto.class);

              // 新代码 - 使用 JsonTransferUtils
              import com.cet.eem.fusion.common.utils.JsonTransferUtils;
              List<InspectionWorkOrderDto> workOrderList = JsonTransferUtils.parseList(modelEntityList.getData(), InspectionWorkOrderDto.class);

          - library: "Jackson"
            example: |
              // 原代码
              String json = JsonUtil.toJson(object);
              MyClass obj = JsonUtil.fromJson(json, MyClass.class);

              // 使用 Jackson
              import com.fasterxml.jackson.databind.ObjectMapper;

              @Autowired
              private ObjectMapper objectMapper;

              String json = objectMapper.writeValueAsString(object);
              MyClass obj = objectMapper.readValue(json, MyClass.class);

          - library: "Gson"
            example: |
              import com.google.gson.Gson;

              private static final Gson gson = new Gson();

              String json = gson.toJson(object);
              MyClass obj = gson.fromJson(json, MyClass.class);

    DateUtils:
    deprecated_methods: - name: "formatDate"
    detection: "DateUtils.formatDate"
    replacement: |
    // 原代码
    String dateStr = DateUtils.formatDate(date, "yyyy-MM-dd");
         // 使用 Java 8 时间API
          import java.time.LocalDateTime;
          import java.time.format.DateTimeFormatter;

          DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
          String dateStr = LocalDateTime.now().format(formatter);

          // 或使用 SimpleDateFormat (线程不安全，需要注意)
          SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
          String dateStr = sdf.format(date);

### 5. 数据访问层变更详细方案

    dao_changes:
    QuantityObjectDao:
    status: "废弃"
    detection: - "@Autowired.\*QuantityObjectDao" - "quantityObjectDao\\."
    migration:
    old_usage: |
    @Autowired
    private QuantityObjectDao quantityObjectDao;
     public QuantityObject getById(Long id) {
            return quantityObjectDao.selectById(id);
        }

        public List<QuantityObject> getList(QueryParam param) {
            return quantityObjectDao.selectList(param);
        }

      new_usage: |
        @Resource(name = "pluginName_quantityObjectService")
        private QuantityObjectService quantityObjectService;

        public QuantityObject getById(Long id) {
            return quantityObjectService.getById(id);
        }

        public List<QuantityObject> getList(QueryParam param) {
            // 注意：查询参数可能需要调整
            QuantityQueryDTO queryDTO = QuantityQueryDTO.builder()
                .tenantId(GlobalInfoUtils.getTenantId())
                .status(StatusEnum.ACTIVE)
                .build();
            return quantityObjectService.list(queryDTO);
        }
        EnergySupplyDao:
    status: "废弃"
    detection: "EnergySupplyDao"
    replacement:
    old_class: "EnergySupplyDao"
    new_class: "EemEnergySupplyToService"
    method_mapping: - old: "findByCondition(Map<String, Object> params)"
    new: "queryList(EnergySupplyQueryDTO queryDTO)" - old: "insert(EnergySupply entity)"
    new: "save(EnergySupplyDTO dto)" - old: "updateById(EnergySupply entity)"
    new: "update(EnergySupplyDTO dto)"

### 8. 继承关系变更

```
inheritance_changes:
  BaseEntity:
    old_class: "BaseEntity"
    new_class: "EntityWithName"
    detection: "extends BaseEntity"
    package: "com.cet.eem.fusion.common.modelutils.model.model"
    migration: |
      // 原代码
      public class EnergyNode extends BaseEntity {
          private Long id;
          private String code;
      }

      // 新代码
      public class EnergyNode extends EntityWithName {
          private Long id;

          private String code;

          // EntityWithName 可能需要实现的方法
          @Override
          public String getName() {
              return this.code; // 或其他逻辑
          }
      }
    affected_methods:
      - "getId() -> 确认是否保持一致"
      - "getCreateTime() -> 可能变为 getCreatedTime()"
      - "getUpdateTime() -> 可能变为 getUpdatedTime()"
```

### 9. 返回类型统一修改

```
return_type_changes:
  ResultWithTotal:
    status: "废弃"
    detection: "ResultWithTotal"
    replacement:
      old_class: "ResultWithTotal"
      new_class: "ApiResult"
      import_change:
        old: "import com.cet.eem.common.model.ResultWithTotal;"
        new: "import com.cet.electric.commons.ApiResult;"
    migration_guide: |
      // 针对Service和Dao层的ResultWithTotal做修改统一修改为ApiResult
      // 注意修改相关的import引用

      // 原代码
      import com.cet.eem.common.model.ResultWithTotal;

      public ResultWithTotal<List<Data>> getData() {
          // 业务逻辑
      }

      // 新代码
      import com.cet.electric.commons.ApiResult;

      public ApiResult<List<Data>> getData() {
          // 业务逻辑
      }
      com.cet.eem.fusion.common.entity.Result extends ApiResult;
      方法都在Result里，方法的返回类型是ApiResult;
      <T> ApiResult<T> result=Result.ok(T data)

```

### 10. GlobalInfoUtils 方法变更

```
global_info_utils_changes:
  getHttpResponse:
    status: "方法移除"
    detection: "GlobalInfoUtils.getHttpResponse()"
    replacement_guide: |
      // com.cet.eem.fusion.config.sdk.utils.GlobalInfoUtils不提供getHttpResponse()方法
      // 需要在接口入参显示的声明并获取response

      // 原代码
      @ApiOperation(value = "根据项目下载签到点二维码")
      @GetMapping("/downloadQrCode/project")
      public ApiResult<Object> downProjectLoadQrCode(@RequestParam(name = "projectId") Long projectId) throws IOException {
          signInService.downProjectLoadQrCode(projectId, GlobalInfoUtils.getHttpResponse());
          return Result.ok();
      }

      // 新代码
      @ApiOperation(value = "根据项目下载签到点二维码")
      @GetMapping("/downloadQrCode/project")
      public ApiResult<Object> downProjectLoadQrCode(@RequestParam(name = "projectId") Long projectId, HttpServletResponse response) throws IOException {
          signInService.downProjectLoadQrCode(projectId, response);
          return Result.ok();
      }
```

### 11. 日志服务变更

```
log_service_changes:
  CommonUtilsService:
    status: "废弃"
    detection:
      - "CommonUtilsService"
      - "commonUtilsService.writeAddOperationLogs"
    replacement:
      old_class: "CommonUtilsService"
      new_class: "EemLogService"
      migration: |
        // 原代码
        import com.cet.eem.bll.common.log.service.CommonUtilsService;

        @Autowired
        CommonUtilsService commonUtilsService;

        commonUtilsService.writeAddOperationLogs(OperationLogType.INSPECTOR_WORK_ORDER, "手动创建工单", dto, GlobalInfoUtils.getUserId());

        // 新代码
        import com.cet.eem.fusion.config.sdk.service.log.EemLogService;

        @Autowired
        EemLogService eemLogService;

        ParentParam parentParam = new Parameter();
        eemLogService.writeAddOperationLogs(OperationLogType.INSPECTOR_WORK_ORDER, "手动创建工单",new Object[] {dto},parentParam);
```

### 12. 常量类替换

```
constant_changes:
  TableNameDef_to_ModelLabelDef:
    description: "废弃数据库表名常量类 TableNameDef，统一使用模型标签常量类 ModelLabelDef。"
    detection_pattern:
      - "import com.cet.piem.common.constant.TableNameDef;"
      - "TableNameDef\\."
    migration_guide: |
      // 核心变更：
      // 1. 替换 import 语句。
      // 旧: import com.cet.piem.common.constant.TableNameDef;
      // 新: import com.cet.eem.solution.common.def.common.label.ModelLabelDef;
      //
      // 2. 将原先对表名的引用，改为对模型标签(Model Label)的引用。
      // 3. 注意：常量名称可能已改变，需要根据业务逻辑找到对应的新常量。

      // 原代码
      import com.cet.piem.common.constant.TableNameDef;

      public class SomeService {
          public void doSomething() {
              // ...
              someQuery.setTableName(TableNameDef.T_ENERGY_SUPPLY);
              // ...
          }
      }

      // 新代码
      import com.cet.eem.solution.common.def.common.label.ModelLabelDef;

      public class SomeService {
          public void doSomething() {
              // ...
              someObject.setModelLabel(ModelLabelDef.ENERGY_SUPPLY); // 常量名称可能改变
              // ...
          }
      }
```

### 13.类导入问题问题

```
  import_issue:
    description: "类找不到问题",
    replacement_guide:
    - 如果这个问题在知识库不存在，则先查看建议
    - 对于单一匹配结果，直接确定替换方案（绿色标记）
    - 对于多个候选类，使用 class_file_reader.py 读取目标类和所有候选类的详细信息
    - 基于类结构、方法、字段进行 AI 智能判断选择最佳匹配
    - 为每个 import 问题/依赖配置问题确定具体的解决方案，无法确定最佳匹配的归类到"未识别"
```

## 2.多租户

### 1. 字段和模型类变更详细规则

````
model_changes:
  project_to_tenant:
    scan_patterns:
      - "private.*project_id"
      - "private.*projectId"
      - "setProjectId|getProjectId"
      - "GlobalInfoUtils.getProjectId()"

    change_rules:
      # 数据库字段
      - type: "database_field"
        old: "project_id"
        new: "tenant_id"
        files: ["*.sql", "*.xml"]

      # Java实体类
      - type: "entity_field"
        changes:
          - old: "private Long projectId;"
            new: "private Long tenantId;"
          - old: "private String project_id;"
            new: "private String tenant_id;"

      # Getter/Setter
      - type: "methods"
        changes:
          - old: "getProjectId()"
            new: "getTenantId()"
          - old: "setProjectId(Long projectId)"
            new: "setTenantId(Long tenantId)"

      # MyBatis XML
      - type: "mybatis_xml"
        changes:
          - old: "#{projectId}"
            new: "#{tenantId}"
          - old: "project_id = #{projectId}"
            new: "tenant_id = #{tenantId}"
          - old: "resultMap.*property=\"projectId\""
            new: "resultMap.*property=\"tenantId\""

      # 工具类调用
      - type: "util_class"
        changes:
          - old: "GlobalInfoUtils.getProjectId()"
            new: "GlobalInfoUtils.getTenantId()"
          - old: "GlobalInfoUtils.setProjectId"
            new: "GlobalInfoUtils.setTenantId"

    affected_files_pattern:
      - "**/dto/**/*.java"
      - "**/po/**/*.java"
      - "**/entity/**/*.java"
      - "**/mapper/**/*.xml"
      - "**/vo/**/*.java"```



````

### 7. 注解和依赖注入变更

         annotation_changes:
    resource_annotation:
    description: "@Resource 注解需要添加插件前缀"
    detection_pattern: "@Resource(?!.\*name)"
    rules: - context: "Service 注入"
    old: "@Resource"
    new: '@Resource(name = "${plugin.name}\_serviceName")'
    example: |
    // 原代码
    @Resource
    private UserService userService;
         // 新代码
          @Resource(name = "energyManagement_userService")
          private UserService userService;

      - context: "Dao注入"
        old: "@Resource"
        new: '@Resource(name = "${plugin.name}_daoName")'

      - context: "配置类注入"
        template: |
          // 在配置类中定义Bean名称
          @Configuration
          public class PluginBeanConfig {
              @Bean(name = "energyManagement_userService")
              public UserService userService() {
                  return new UserServiceImpl();
              }
          }```

## 3. 消息推送变更

```
message_push_changes:
  MessagePushUtils:
    status: "完全废弃"
    detection:
      - "MessagePushUtils\\."
      - "MessagePushUtils.pushToWeb"
      - "messagePushUtils.pushToWeb"
    migration_guide: |
      // 原代码
      import com.cet.eem.bll.common.util.MessagePushUtils;

      @Autowired
      MessagePushUtils messagePushUtils;

      messagePushUtils.pushToWeb(null, EnumSystemEventType.INSPECT_WORK_ORDER_OVER_TIME.getId(), desc, userIds, null, MessageTypeDef.SYSTEM_EVENT);

      // 新代码
      import com.cet.eem.fusion.common.utils.notice.WebNotification;

      @Resource
      private WebNotification webNotification;

      webNotification.pushToWeb(null, event.getEventType(), event.getDescription(),userIds, LossConstants.EVENT_CLASS, MessageTypeDef.SYSTEM_EVENT, tenantId, LOSS_EVENT_REDIRECT_PAGE_URL,GlobalInfoUtils.getTenantId(),null);
```

## 4. 权限 ID 调整详细方案

```
permission_id_adjustment:
  rule: "所有权限ID需要在10000-20000之间"
  detection:
    - "@OperationLog\\(operationType"
    - "public\\s+static\\s+final\\s+int\\s+(\\w+)\\s*=\\s*(\\d+)"

  adjustment_strategy:
    method: "add_offset"
    default_offset: 10000

  migration_guide: |
    // 根据@OperationLog(operationType找到对应的类型，然后修改常量类的引用值

    // 原代码
    public static final int SCHEDULING_SCHEME = 122;
    public static final int CLASSES_SCHEME = 123;
    public static final int TEAM_GROUP_INFO = 124;

    // 新代码 (原ID + 10000)
    public static final int SCHEDULING_SCHEME = 10122;
    public static final int CLASSES_SCHEME = 10123;
    public static final int TEAM_GROUP_INFO = 10124;

    // 注解中的引用无需修改，会自动使用新的常量值
    @OperationLog(operationType = GroupEnergyConstantDef.SCHEDULING_SCHEME)
```

## 5. 单位服务变更详细信息

```
unit_service_changes:
  package_change:
    old: "com.cet.piem.service.UnitService"
    new: "com.cet.eem.fusion.config.sdk.service.EnergyUnitService"

  class_name_change:
    old: "UnitService"
    new: "EnergyUnitService"

  entity_changes:
    old_entity: "UserDefineUnit"
    new_entity: "UserDefineUnitDTO"
    import: "com.cet.eem.fusion.config.sdk.entity.UserDefineUnitDTO"
    note: "UserDefineUnitDTO是UserDefineUnit的父类，很多情况下直接替换新的实体类UserDefineUnitDTO就可以了，不要再使用子类UserDefineUnit"

  method_signature_changes:
    - old_method: "getUnit(energyValueList, ProjectUnitClassify.ENERGY, energyType)"
      new_method: "queryUnitCoef(UserDefineUnitSearchDTO)"
      migration: |
        // 原代码
        import com.cet.piem.service.UnitService;

        @Resource
        private UnitService unitService;

        List<Double> energyValueList = teamGroupEnergyList.stream().map(TeamGroupEnergy::getValue).collect(Collectors.toList());
        UserDefineUnit unit = unitService.getUnit(energyValueList, ProjectUnitClassify.ENERGY, dto.getEnergyType());

        // 新代码
        import com.cet.eem.fusion.config.sdk.utils.GlobalInfoUtils;
        import com.cet.eem.fusion.config.sdk.service.EnergyUnitService;
        import com.cet.eem.fusion.config.sdk.entity.unit.UserDefineUnitSearchDTO;
        import com.cet.eem.fusion.config.sdk.entity.UserDefineUnitDTO;
        @Resource
        private EnergyUnitService energyUnitService;

        Double maxValue = teamGroupEnergyList.stream().map(TeamGroupEnergy::getValue).max(Double::compareTo).orElse(null);
        UserDefineUnitDTO userDefineUnitDTO = energyUnitService.queryUnitCoef(new UserDefineUnitSearchDTO(GlobalInfoUtils.getTenantId(), dto.getEnergyType(), ProjectUnitClassify.ENERGY, maxValue));

  business_rules:
    energy_unit: "能耗传值ProjectUnitClassify.ENERGY"
    product_unit: "产量则传值ProjectUnitClassify.PRODUCT"
    note: "需要结合具体的业务场景，将能耗、产量分开不同的方法做查询适配"

  preserved_classes:
    - class: "UnitUtils"
      note: "工具类保持不变，但可能需要适配新的服务"
    - class: "EnergyUnitService"
      note: "能源单位服务保持原有类名和包名"
```

## 6. 物理量查询服务

```
quantity_services:
  QuantityObjectService:
    description: "物理量对象获取方法"
    methods:
      - method: "queryQuantityObject(nodes)"
        usage: |
          // 获取某个节点所有节点物理量对象数据
          @Autowired
          QuantityObjectService quantityObjectService;

          List<QuantityObject> quantityObjects = quantityObjectService.queryQuantityObject(queryDTO.getNodes());

      - method: "queryQuantityObject(nodes, templates)"
        usage: |
          // 获取某个节点的指定物理量对象
          @Autowired
          QuantityObjectService quantityObjectService;

          List<QuantityObject> quantityObjects = quantityObjectService.queryQuantityObject(queryDTO.getNodes(), queryDTO.getTemplates());

  QuantityObjectMapService:
    description: "物理量映射对象数据"
    methods:
      - method: "queryQuantityObjectMapByQuantityObjectIds"
        usage: |
          // 根据物理量对象获取物理量映射对象
          @Autowired
          QuantityObjectMapService quantityObjectMapService;

          Set<Long> quantityObjectIds = quantityObjects.stream().map(BaseEntity::getId).collect(Collectors.toSet());
          List<QuantityObjectMap> quantityObjectMaps = quantityObjectMapService.queryQuantityObjectMapByQuantityObjectIds(quantityObjectIds);

  QuantityObjectDataService:
    description: "获取物理量数据"
    methods:
      - method: "queryQuantityData"
        usage: |
          // 获取指定时间区间的物理量数据
          @Autowired
          QuantityObjectDataService quantityObjectDataService;

          List<QuantityAggregationData> dataList = quantityObjectDataService.queryQuantityData(searchVo.getStartTime(), searchVo.getEndTime(), quantityObjectIds, searchVo.getAggregationCycle(), searchVo.getAggregationType());
```
