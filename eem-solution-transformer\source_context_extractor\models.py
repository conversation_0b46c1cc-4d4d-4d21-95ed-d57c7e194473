"""
源码上下文提取器数据模型

定义系统中使用的所有数据结构。
"""

import json
import os
from dataclasses import dataclass, field
from typing import Dict, List, Any, Optional, Union

from pathlib import Path


@dataclass
class ErrorLocation:
    """错误位置信息"""
    file: str
    line: int
    column: int = 0
    
    def validate(self) -> None:
        """验证错误位置数据"""
        if not self.file:
            raise ValueError("file path cannot be empty")
        
        if self.line <= 0:
            raise ValueError(f"line number must be positive, got {self.line}")
        
        if self.column < 0:
            raise ValueError(f"column number cannot be negative, got {self.column}")
    
    @classmethod
    def from_dict(cls, data: dict) -> 'ErrorLocation':
        """从字典创建ErrorLocation对象，带验证"""
        if not isinstance(data, dict):
            raise TypeError("location data must be a dictionary")
        
        location = cls(
            file=data.get('file', ''),
            line=data.get('line', 0),
            column=data.get('column', 0)
        )
        location.validate()
        return location


@dataclass
class ErrorItem:
    """错误项完整信息"""
    package: str
    class_name: str  # 对应JSON中的'class'字段
    missing_method: str
    in_param: Dict[str, str]
    out_return: str
    line: List[int]
    context: str
    method_signature: str
    location: ErrorLocation
    # 原始问题信息字段
    issue_id: Optional[int] = None
    error_code: Optional[str] = None
    module: Optional[str] = None
    description: Optional[str] = None
    
    def validate(self) -> None:
        """验证错误项数据"""
        required_fields = {
            'package': self.package,
            'class_name': self.class_name,
            'missing_method': self.missing_method,
            'out_return': self.out_return
        }
        
        for field_name, field_value in required_fields.items():
            if not field_value or not isinstance(field_value, str):
                raise ValueError(f"Required field '{field_name}' cannot be empty")
        
        if not isinstance(self.in_param, dict):
            raise TypeError("in_param must be a dictionary")
        
        if not isinstance(self.line, list) or not self.line:
            raise ValueError("line must be a non-empty list")
        
        for line_num in self.line:
            if not isinstance(line_num, int) or line_num <= 0:
                raise ValueError(f"All line numbers must be positive integers, got {line_num}")
        
        # 验证location
        if self.location:
            self.location.validate()
    
    @classmethod
    def from_dict(cls, data: dict) -> 'ErrorItem':
        """从字典创建ErrorItem对象，带验证"""
        if not isinstance(data, dict):
            raise TypeError("Error item data must be a dictionary")
        
        # 检测是否为新格式（包含 issue_id 字段）
        if 'issue_id' in data:
            return cls._from_simple_format(data)
        else:
            return cls._from_complex_format(data)
    
    @classmethod
    def _from_simple_format(cls, data: dict) -> 'ErrorItem':
        """处理新格式（target_method_test.json格式）的字段映射"""
        # 提取基本字段
        package = data.get('package', '')
        class_name = data.get('class', '')
        missing_method = data.get('missing_method', '')
        module = data.get('module', '')
        
        # 处理line字段：从字符串转换为整数列表
        line_data = data.get('line', '')
        line = []
        
        if isinstance(line_data, str):
            if line_data.strip():  # 非空字符串
                try:
                    # 尝试转换为整数
                    line_num = int(line_data.strip())
                    if line_num <= 0:
                        raise ValueError(f"Line number must be positive, got {line_num}")
                    line = [line_num]
                except ValueError as e:
                    if "invalid literal for int()" in str(e):
                        raise ValueError(f"Invalid line number format: '{line_data}', expected integer string")
                    else:
                        raise  # 重新抛出其他ValueError（如负数错误）
            else:
                # 空字符串，使用默认值
                line = [1]
        elif isinstance(line_data, int):
            # 直接是整数
            if line_data <= 0:
                raise ValueError(f"Line number must be positive, got {line_data}")
            line = [line_data]
        elif isinstance(line_data, list):
            # 已经是列表，验证每个元素
            if not line_data:
                line = [1]  # 空列表时默认为[1]
            else:
                for i, item in enumerate(line_data):
                    try:
                        if isinstance(item, str):
                            if not item.strip():
                                raise ValueError(f"Line number at index {i} cannot be empty string")
                            line_num = int(item.strip())
                        elif isinstance(item, int):
                            line_num = item
                        else:
                            raise ValueError(f"Line number at index {i} must be string or integer, got {type(item).__name__}")
                        
                        if line_num <= 0:
                            raise ValueError(f"Line number at index {i} must be positive, got {line_num}")
                        line.append(line_num)
                    except ValueError as e:
                        if "invalid literal for int()" in str(e):
                            raise ValueError(f"Invalid line number format at index {i}: '{item}', expected integer")
                        else:
                            raise  # 重新抛出其他ValueError
        elif line_data is None:
            # None值，使用默认值
            line = [1]
        else:
            # 其他类型，报错
            raise ValueError(f"Line field must be string, integer, or list, got {type(line_data).__name__}")
        
        # 构建文件路径：{module}/src/main/java/{package.replace('.', '/')}/{class}.java
        if module and package and class_name:
            package_path = package.replace('.', '/')
            file_path = f"{module}/src/main/java/{package_path}/{class_name}.java"
        else:
            file_path = ""
        
        # 创建location对象
        location = ErrorLocation(
            file=file_path,
            line=line[0] if line else 1,
            column=0
        )
        
        # 设置默认值
        in_param = {}
        out_return = "Object"
        context = data.get('description', '')
        method_signature = missing_method
        
        error_item = cls(
            package=package,
            class_name=class_name,
            missing_method=missing_method,
            in_param=in_param,
            out_return=out_return,
            line=line,
            context=context,
            method_signature=method_signature,
            location=location,
            issue_id=data.get('issue_id'),
            error_code=data.get('error_code'),
            module=module,
            description=data.get('description')
        )
        
        error_item.validate()
        return error_item
    
    @classmethod
    def _from_complex_format(cls, data: dict) -> 'ErrorItem':
        """处理现有格式的数据"""
        # 处理location数据
        location_data = data.get('location', {})
        location = ErrorLocation.from_dict(location_data) if location_data else None
        
        error_item = cls(
            package=data.get('package', ''),
            class_name=data.get('class', ''),  # JSON中的'class'字段
            missing_method=data.get('missing_method', ''),
            in_param=data.get('in_param', {}),
            out_return=data.get('out_return', ''),
            line=data.get('line', []),
            context=data.get('context', ''),
            method_signature=data.get('method_signature', ''),
            location=location
        )
        
        error_item.validate()
        return error_item
    
    @classmethod
    def from_json_file(cls, file_path: Union[str, Path]) -> List['ErrorItem']:
        """从JSON文件加载错误项列表"""
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"JSON file not found: {file_path}")
        
        if not file_path.is_file():
            raise ValueError(f"Path is not a file: {file_path}")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON format in file {file_path}: {e}")
        except Exception as e:
            raise IOError(f"Error reading file {file_path}: {e}")
        
        if not isinstance(data, list):
            raise TypeError("JSON file must contain a list of error items")
        
        error_items = []
        for i, item_data in enumerate(data):
            try:
                error_item = cls.from_dict(item_data)
                error_items.append(error_item)
            except Exception as e:
                raise ValueError(f"Error parsing item {i} in JSON file: {e}")
        
        return error_items
    
    def get_full_class_name(self) -> str:
        """获取完整的类名（包含包名）"""
        return f"{self.package}.{self.class_name}" if self.package else self.class_name
    
    def get_method_display_name(self) -> str:
        """获取方法的显示名称"""
        return f"{self.get_full_class_name()}.{self.missing_method}"


@dataclass
class MethodAnalysisResult:
    """
    方法分析结果数据模型
    
    包含指定的输出字段：missing_method, in_param, out_return, context, content, notes
    支持数据序列化和 JSON 格式化
    """
    missing_method: str
    in_param: Dict[str, str]
    out_return: str
    context: str
    content: str
    notes: str
    source_file: str = ""
    line_number: int = 0
    analysis_status: str = "success"  # "success", "partial", "failed"
    error_message: Optional[str] = None
    original_error: Optional['ErrorItem'] = None  # 保存原始错误项信息
    
    def validate(self) -> None:
        """验证方法分析结果数据"""
        required_fields = {
            'missing_method': self.missing_method,
            'out_return': self.out_return,
            'context': self.context,
            'content': self.content,
            'notes': self.notes
        }
        
        for field_name, field_value in required_fields.items():
            if not isinstance(field_value, str):
                raise TypeError(f"Field '{field_name}' must be a string, got {type(field_value)}")
        
        if not isinstance(self.in_param, dict):
            raise TypeError("in_param must be a dictionary")
        
        if self.line_number < 0:
            raise ValueError(f"line_number cannot be negative, got {self.line_number}")
        
        if self.analysis_status not in ["success", "partial", "failed"]:
            raise ValueError(f"analysis_status must be one of 'success', 'partial', 'failed', got '{self.analysis_status}'")
    
    def to_dict(self) -> dict:
        """
        转换为字典格式，只包含指定的输出字段
        
        Returns:
            dict: 包含指定字段的字典
        """
        return {
            "missing_method": self.missing_method,
            "in_param": self.in_param,
            "out_return": self.out_return,
            "context": self.context,
            "content": self.content,
            "notes": self.notes
        }
    
    def to_json(self, indent: int = 2) -> str:
        """
        转换为JSON字符串格式
        
        Args:
            indent: JSON缩进级别
            
        Returns:
            str: JSON格式的字符串
        """
        return json.dumps(self.to_dict(), ensure_ascii=False, indent=indent)
    
    def to_full_dict(self) -> dict:
        """
        转换为完整的字典格式，包含所有字段
        
        Returns:
            dict: 包含所有字段的字典
        """
        result = {
            "missing_method": self.missing_method,
            "in_param": self.in_param,
            "out_return": self.out_return,
            "context": self.context,
            "content": self.content,
            "notes": self.notes,
            "source_file": self.source_file,
            "line_number": self.line_number,
            "analysis_status": self.analysis_status
        }
        
        if self.error_message:
            result["error_message"] = self.error_message
        
        return result
    
    def to_full_json(self, indent: int = 2) -> str:
        """
        转换为完整的JSON字符串格式
        
        Args:
            indent: JSON缩进级别
            
        Returns:
            str: 完整的JSON格式字符串
        """
        return json.dumps(self.to_full_dict(), ensure_ascii=False, indent=indent)
    
    @classmethod
    def from_dict(cls, data: dict) -> 'MethodAnalysisResult':
        """
        从字典创建MethodAnalysisResult对象
        
        Args:
            data: 包含方法分析结果数据的字典
            
        Returns:
            MethodAnalysisResult: 创建的对象
            
        Raises:
            TypeError: 数据类型错误
            ValueError: 数据值错误
        """
        if not isinstance(data, dict):
            raise TypeError("Data must be a dictionary")
        
        result = cls(
            missing_method=data.get('missing_method', ''),
            in_param=data.get('in_param', {}),
            out_return=data.get('out_return', ''),
            context=data.get('context', ''),
            content=data.get('content', ''),
            notes=data.get('notes', ''),
            source_file=data.get('source_file', ''),
            line_number=data.get('line_number', 0),
            analysis_status=data.get('analysis_status', 'success'),
            error_message=data.get('error_message')
        )
        
        result.validate()
        return result
    
    @classmethod
    def from_json(cls, json_str: str) -> 'MethodAnalysisResult':
        """
        从JSON字符串创建MethodAnalysisResult对象
        
        Args:
            json_str: JSON格式的字符串
            
        Returns:
            MethodAnalysisResult: 创建的对象
            
        Raises:
            json.JSONDecodeError: JSON解析错误
            TypeError: 数据类型错误
            ValueError: 数据值错误
        """
        try:
            data = json.loads(json_str)
            return cls.from_dict(data)
        except json.JSONDecodeError as e:
            raise json.JSONDecodeError(f"Invalid JSON format: {e}", json_str, e.pos)
    
    def is_successful(self) -> bool:
        """检查分析是否成功"""
        return self.analysis_status == "success"
    
    def is_partial(self) -> bool:
        """检查分析是否部分成功"""
        return self.analysis_status == "partial"
    
    def is_failed(self) -> bool:
        """检查分析是否失败"""
        return self.analysis_status == "failed"
    
    def has_content(self) -> bool:
        """检查是否有方法内容"""
        return bool(self.content and self.content.strip())
    
    def has_notes(self) -> bool:
        """检查是否有注释信息"""
        return bool(self.notes and self.notes.strip())
    
    def get_method_display_name(self) -> str:
        """获取方法的显示名称"""
        if self.in_param:
            param_types = list(self.in_param.values())
            param_str = ", ".join(param_types)
            return f"{self.missing_method}({param_str}) -> {self.out_return}"
        else:
            return f"{self.missing_method}() -> {self.out_return}"
    
    def get_summary(self) -> str:
        """获取分析结果摘要"""
        status_desc = {
            "success": "成功",
            "partial": "部分成功", 
            "failed": "失败"
        }
        
        summary = f"方法: {self.get_method_display_name()}\n"
        summary += f"状态: {status_desc.get(self.analysis_status, self.analysis_status)}\n"
        
        if self.source_file:
            summary += f"源文件: {self.source_file}"
            if self.line_number > 0:
                summary += f" (行 {self.line_number})"
            summary += "\n"
        
        if self.error_message:
            summary += f"错误信息: {self.error_message}\n"
        
        return summary.strip()


@dataclass
class MethodNode:
    """方法AST节点信息"""
    name: str
    parameters: List[Dict[str, str]]
    return_type: str
    modifiers: List[str]
    annotations: List[str]
    body: str
    javadoc: Optional[str]
    start_line: int
    end_line: int


@dataclass
class ClassNode:
    """类AST节点信息"""
    name: str
    package: str
    imports: List[str]
    methods: List[MethodNode]
    fields: List[Dict[str, str]]
    annotations: List[str]


@dataclass
class ExtractorConfig:
    """提取器配置"""
    src_path: str = "newcode"
    legacy_src_path: str = "oldcode"
    knowledge_base_path: str = "知识库"
    output_path: str = "source_context_analysis.md"
    enable_ast_parsing: bool = True
    enable_text_fallback: bool = True
    max_context_lines: int = 10
    timeout_seconds: int = 30
    
    # 日志配置
    log_level: str = "INFO"
    log_format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    console_output: bool = True
    file_output: bool = True
    log_file: str = "logs/source_context_extractor.log"
    
    def validate(self) -> None:
        """验证配置参数"""
        if self.max_context_lines <= 0:
            raise ValueError("max_context_lines must be positive")
        
        if self.timeout_seconds <= 0:
            raise ValueError("timeout_seconds must be positive")
        
        if self.log_level not in ["DEBUG", "INFO", "WARNING", "ERROR"]:
            raise ValueError("log_level must be one of DEBUG, INFO, WARNING, ERROR")


@dataclass
class ProcessingStats:
    """处理统计信息"""
    total_errors: int = 0
    processed_errors: int = 0
    successful_extractions: int = 0
    failed_extractions: int = 0
    processing_time: float = 0.0
    
    def get_success_rate(self) -> float:
        """获取成功率"""
        if self.processed_errors == 0:
            return 0.0
        return self.successful_extractions / self.processed_errors
    
    def get_processing_rate(self) -> float:
        """获取处理速度（项目/秒）"""
        if self.processing_time == 0:
            return 0.0
        return self.processed_errors / self.processing_time


@dataclass
class ValidationResult:
    """验证结果"""
    is_valid: bool
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    
    def add_error(self, error: str) -> None:
        """添加错误"""
        self.errors.append(error)
        self.is_valid = False
    
    def add_warning(self, warning: str) -> None:
        """添加警告"""
        self.warnings.append(warning)
    
    def get_report(self) -> str:
        """获取验证报告"""
        if self.is_valid and not self.warnings:
            return "Validation passed successfully."
        
        report = []
        
        if self.errors:
            report.append("ERRORS:")
            for i, error in enumerate(self.errors, 1):
                report.append(f"  {i}. {error}")
        
        if self.warnings:
            report.append("WARNINGS:")
            for i, warning in enumerate(self.warnings, 1):
                report.append(f"  {i}. {warning}")
        
        return "\n".join(report)


class InputValidator:
    """输入验证器"""
    
    def __init__(self):
        self.result = ValidationResult(is_valid=True)
    
    def validate_error_items(self, error_items: List[ErrorItem]) -> ValidationResult:
        """
        验证错误项列表
        
        Args:
            error_items: 错误项列表
            
        Returns:
            ValidationResult: 验证结果
        """
        self.result = ValidationResult(is_valid=True)
        
        if not error_items:
            self.result.add_error("Error items list cannot be empty")
            return self.result
        
        for i, item in enumerate(error_items):
            self._validate_single_error_item(item, i)
        
        return self.result
    
    def _validate_single_error_item(self, item: ErrorItem, index: int) -> None:
        """验证单个错误项"""
        prefix = f"Item {index}"
        
        # 验证必需字段
        self._validate_required_fields(item, prefix)
        
        # 验证文件路径
        self._validate_file_path(item, prefix)
        
        # 验证行号
        self._validate_line_numbers(item, prefix)
        
        # 验证方法签名
        self._validate_method_signature(item, prefix)
        
        # 验证参数和返回类型
        self._validate_parameters_and_return(item, prefix)
    
    def _validate_required_fields(self, item: ErrorItem, prefix: str) -> None:
        """验证必需字段"""
        required_string_fields = {
            'package': item.package,
            'class_name': item.class_name,
            'missing_method': item.missing_method,
            'out_return': item.out_return
        }
        
        for field_name, field_value in required_string_fields.items():
            if not field_value or not isinstance(field_value, str):
                self.result.add_error(f"{prefix}: Required field '{field_name}' cannot be empty")
            elif field_value.strip() != field_value:
                self.result.add_warning(f"{prefix}: Field '{field_name}' has leading/trailing whitespace")
        
        # 验证in_param
        if not isinstance(item.in_param, dict):
            self.result.add_error(f"{prefix}: 'in_param' must be a dictionary")
        
        # 验证line列表
        if not isinstance(item.line, list) or not item.line:
            self.result.add_error(f"{prefix}: 'line' must be a non-empty list")
    
    def _validate_file_path(self, item: ErrorItem, prefix: str) -> None:
        """验证文件路径"""
        if not item.location:
            self.result.add_error(f"{prefix}: 'location' cannot be None")
            return
        
        file_path = item.location.file
        if not file_path:
            self.result.add_error(f"{prefix}: File path cannot be empty")
            return
        
        # 检查文件路径格式
        if not file_path.endswith('.java'):
            self.result.add_warning(f"{prefix}: File path should end with '.java', got '{file_path}'")
        
        # 检查路径分隔符
        if '\\' in file_path and '/' in file_path:
            self.result.add_warning(f"{prefix}: Mixed path separators in file path: '{file_path}'")
        
        # 验证包名与文件路径的一致性
        if item.package:
            expected_path_part = item.package.replace('.', '/')
            if expected_path_part not in file_path:
                self.result.add_warning(f"{prefix}: Package '{item.package}' doesn't match file path '{file_path}'")
    
    def _validate_line_numbers(self, item: ErrorItem, prefix: str) -> None:
        """验证行号"""
        # 验证line列表中的行号
        for i, line_num in enumerate(item.line):
            if not isinstance(line_num, int):
                self.result.add_error(f"{prefix}: Line number at index {i} must be an integer, got {type(line_num)}")
            elif line_num <= 0:
                self.result.add_error(f"{prefix}: Line number at index {i} must be positive, got {line_num}")
        
        # 验证location中的行号
        if item.location:
            if item.location.line <= 0:
                self.result.add_error(f"{prefix}: Location line number must be positive, got {item.location.line}")
            
            if item.location.column < 0:
                self.result.add_error(f"{prefix}: Location column number cannot be negative, got {item.location.column}")
            
            # 检查line列表和location.line的一致性
            if item.line and item.location.line not in item.line:
                self.result.add_warning(f"{prefix}: Location line {item.location.line} not found in line list {item.line}")
    
    def _validate_method_signature(self, item: ErrorItem, prefix: str) -> None:
        """验证方法签名"""
        if not item.method_signature:
            self.result.add_warning(f"{prefix}: Method signature is empty")
            return
        
        # 检查方法签名是否包含方法名
        if item.missing_method not in item.method_signature:
            self.result.add_warning(f"{prefix}: Method name '{item.missing_method}' not found in signature '{item.method_signature}'")
        
        # 检查方法签名格式
        if '(' not in item.method_signature or ')' not in item.method_signature:
            self.result.add_warning(f"{prefix}: Method signature should contain parentheses: '{item.method_signature}'")
    
    def _validate_parameters_and_return(self, item: ErrorItem, prefix: str) -> None:
        """验证参数和返回类型"""
        # 验证参数类型格式
        for param_name, param_type in item.in_param.items():
            if not param_name or not isinstance(param_name, str):
                self.result.add_error(f"{prefix}: Parameter name cannot be empty")
            
            if not param_type or not isinstance(param_type, str):
                self.result.add_error(f"{prefix}: Parameter type for '{param_name}' cannot be empty")
            elif not self._is_valid_java_type(param_type):
                self.result.add_warning(f"{prefix}: Parameter type '{param_type}' may not be a valid Java type")
        
        # 验证返回类型格式
        if not self._is_valid_java_type(item.out_return):
            self.result.add_warning(f"{prefix}: Return type '{item.out_return}' may not be a valid Java type")
    
    def _is_valid_java_type(self, type_str: str) -> bool:
        """检查是否为有效的Java类型"""
        if not type_str:
            return False
        
        # 基本类型
        primitive_types = {'boolean', 'byte', 'char', 'short', 'int', 'long', 'float', 'double', 'void'}
        if type_str in primitive_types:
            return True
        
        # 数组类型
        if type_str.endswith('[]'):
            return self._is_valid_java_type(type_str[:-2])
        
        # 泛型类型（简单检查）
        if '<' in type_str and '>' in type_str:
            # 简单的泛型格式检查
            return True
        
        # 类名格式检查（包含包名的完整类名）
        if '.' in type_str:
            parts = type_str.split('.')
            return all(part and part[0].isupper() or part.islower() for part in parts)
        
        # 简单类名
        return type_str and type_str[0].isupper()


class JSONInputParser:
    """JSON输入解析器"""
    
    def __init__(self):
        self.validator = InputValidator()
        self.validation_result = None
    
    def parse_json_file(self, file_path: Union[str, Path], validate: bool = True) -> List[ErrorItem]:
        """
        解析JSON错误报告文件
        
        Args:
            file_path: JSON文件路径
            validate: 是否进行详细验证
            
        Returns:
            List[ErrorItem]: 解析后的错误项列表
            
        Raises:
            FileNotFoundError: 文件不存在
            ValueError: JSON格式错误或数据验证失败
            TypeError: 数据类型错误
        """
        try:
            error_items = ErrorItem.from_json_file(file_path)
            
            if validate:
                self.validation_result = self.validator.validate_error_items(error_items)
                if not self.validation_result.is_valid:
                    raise ValueError(f"Validation failed:\n{self.validation_result.get_report()}")
            
            return error_items
        except Exception as e:
            raise
    
    def validate_json_structure(self, data: Any) -> ValidationResult:
        """
        验证JSON数据结构
        
        Args:
            data: 待验证的数据
            
        Returns:
            ValidationResult: 验证结果
        """
        result = ValidationResult(is_valid=True)
        
        if not isinstance(data, list):
            result.add_error("JSON data must be a list")
            return result
        
        if not data:
            result.add_error("JSON data cannot be empty")
            return result
        
        for i, item in enumerate(data):
            if not self._validate_error_item_structure(item, i, result):
                continue
        
        return result
    
    def _validate_error_item_structure(self, item: Any, index: int, result: ValidationResult) -> bool:
        """验证单个错误项的结构"""
        prefix = f"Item {index}"
        
        if not isinstance(item, dict):
            result.add_error(f"{prefix}: must be a dictionary")
            return False
        
        required_fields = ['package', 'class', 'missing_method', 'line', 'location']
        for field in required_fields:
            if field not in item:
                result.add_error(f"{prefix}: missing required field '{field}'")
        
        # 验证location结构
        location = item.get('location', {})
        if not isinstance(location, dict):
            result.add_error(f"{prefix}: 'location' must be a dictionary")
        else:
            location_required = ['file', 'line']
            for field in location_required:
                if field not in location:
                    result.add_error(f"{prefix}: location missing required field '{field}'")
        
        return True
    
    def get_validation_result(self) -> Optional[ValidationResult]:
        """获取最后一次验证结果"""
        return self.validation_result
    
    def get_validation_report(self) -> str:
        """获取验证报告"""
        if self.validation_result:
            return self.validation_result.get_report()
        return "No validation performed yet."