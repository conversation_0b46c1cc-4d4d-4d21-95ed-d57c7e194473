#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re

def count_class_issues():
    """统计类问题数量和文件分布"""
    
    file_path = 'eem-solution-transformer/out/问题列表.md'
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 统计类问题总数
    class_issues = re.findall(r'error_type: "类问题"', content)
    print(f'类问题总数: {len(class_issues)}')
    
    # 统计文件数量
    files = re.findall(r'^## [A-Za-z]', content, re.MULTILINE)
    print(f'涉及文件数量: {len(files)}')
    
    # 列出所有文件名
    print('\n文件清单:')
    for i, file_match in enumerate(re.finditer(r'^## ([A-Za-z][A-Za-z0-9_]*)', content, re.MULTILINE)):
        print(f'{i+1:2d}. {file_match.group(1)}')
    
    # 统计每个文件的类问题数量
    print('\n每个文件的类问题统计:')
    
    # 分割文件内容
    file_sections = re.split(r'^## ([A-Za-z][A-Za-z0-9_]*)', content, flags=re.MULTILINE)[1:]
    
    total_counted = 0
    for i in range(0, len(file_sections), 2):
        if i + 1 < len(file_sections):
            file_name = file_sections[i]
            file_content = file_sections[i + 1]
            
            # 统计该文件的类问题数量
            file_class_issues = re.findall(r'error_type: "类问题"', file_content)
            total_counted += len(file_class_issues)
            
            print(f'{file_name}: {len(file_class_issues)} 个类问题')
    
    print(f'\n验证: 总计统计 {total_counted} 个类问题')
    print(f'原始统计: {len(class_issues)} 个类问题')
    print(f'统计一致性: {"✓" if total_counted == len(class_issues) else "✗"}')

if __name__ == '__main__':
    count_class_issues()
