#!/usr/bin/env python3
"""调试导入问题"""

import sys
import traceback

try:
    print("尝试导入interfaces...")
    from interfaces import FileLocatorInterface
    print("✓ interfaces导入成功")
except Exception as e:
    print(f"✗ interfaces导入失败: {e}")
    traceback.print_exc()

try:
    print("尝试导入models...")
    from models import ErrorItem, ExtractorConfig
    print("✓ models导入成功")
except Exception as e:
    print(f"✗ models导入失败: {e}")
    traceback.print_exc()

try:
    print("尝试导入error_handling...")
    from error_handling import FileLocatorError
    print("✓ error_handling导入成功")
except Exception as e:
    print(f"✗ error_handling导入失败: {e}")
    traceback.print_exc()

try:
    print("尝试执行file_locator.py内容...")
    with open('file_locator.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    print(f"文件内容长度: {len(content)} 字符")
    print(f"文件前100字符: {content[:100]}")
    
    # 创建一个新的命名空间，包含必要的导入
    namespace = {
        '__builtins__': __builtins__,
        'FileLocatorInterface': FileLocatorInterface,
        'ErrorItem': ErrorItem,
        'ExtractorConfig': ExtractorConfig,
        'FileLocatorError': FileLocatorError,
        'os': __import__('os'),
        'logging': __import__('logging'),
        'Optional': Optional,
        'Dict': Dict,
        'Any': Any,
        'List': List,
        'Path': Path
    }
    
    exec(content, namespace)
    
    print(f"执行后的命名空间包含: {[k for k in namespace.keys() if not k.startswith('__')]}")
    
    if 'FileLocator' in namespace:
        print("✓ FileLocator类定义成功")
        FileLocator = namespace['FileLocator']
        
        # 测试实例化
        config = ExtractorConfig()
        locator = FileLocator(config)
        print("✓ FileLocator实例化成功")
        
    else:
        print("✗ FileLocator类未找到")
        
except Exception as e:
    print(f"✗ 执行file_locator.py失败: {e}")
    traceback.print_exc()