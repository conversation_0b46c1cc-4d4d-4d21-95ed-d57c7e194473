package com.cet.eem.fusion.transformer.core.dao.impl;

import com.cet.eem.fusion.transformer.core.dao.TransformerindexDataDao;
import com.cet.eem.fusion.transformer.core.entity.po.TransformerindexData;
import com.cet.eem.fusion.transformer.core.def.TransformerConstantDef;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName : TransformerindexDataDaoImpl
 * <AUTHOR> yangy
 * @Date: 2022-03-28 10:33
 */
@Component
public class TransformerindexDataDaoImpl implements TransformerindexDataDao {

    @Autowired
    ModelServiceUtils modelServiceUtilsl;

    @Override
    public List<TransformerindexData> save(List<TransformerindexData> avgloadandpowers) {
        return modelServiceUtilsl.writeData(avgloadandpowers, TransformerindexData.class);
    }

    @Override
    public List<TransformerindexData> queryByTimes(List<LocalDateTime> times) {
        QueryCondition condition = ParentQueryConditionBuilder.of(Constant.TRANSFORMERINDEXDATA)
                .in(Constant.LOGTIME, times)
                .build();
        return modelServiceUtilsl.query(condition, TransformerindexData.class);
    }

    @Override
    public List<TransformerindexData> queryByParams(Long logTime, Integer cycle,Integer type, Boolean desc) {
        QueryCondition condition = ParentQueryConditionBuilder.of(Constant.TRANSFORMERINDEXDATA)
                .eq(Constant.LOGTIME, logTime)
                .eq(Constant.AGGREGATIONCYCLE,cycle)
                .eq(Constant.TYPE,type)
                .orderBy(Constant.VALUE,desc)
                .build();
        return modelServiceUtilsl.query(condition, TransformerindexData.class);
    }

    @Override
    public List<TransformerindexData> queryByTimes(List<LocalDateTime> times, Integer type, Integer cycle) {
        QueryCondition condition = ParentQueryConditionBuilder.of(Constant.TRANSFORMERINDEXDATA)
                .in(Constant.LOGTIME, times)
                .eq(Constant.COLUMN_AGGREGATION_CYCLE, cycle)
                .eq(Constant.TYPE, type)
                .build();
        return modelServiceUtilsl.query(condition, TransformerindexData.class);
    }

    @Override
    public List<EemPoiRecord> saveEemPoiRecord(List<EemPoiRecord> pois) {
        return modelServiceUtilsl.writeData(pois,EemPoiRecord.class);
    }
}
