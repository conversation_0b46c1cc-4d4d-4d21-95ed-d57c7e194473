"""
JDK8兼容性检查器

检查javalang对JDK8语法特性的支持情况，并提供回退解析策略。
"""

import os
import re
import logging
from typing import List, Dict, Optional, Set, Tuple, Any
from dataclasses import dataclass
from pathlib import Path
import javalang
from javalang.tree import CompilationUnit, MethodDeclaration

from error_prone_json_reporter.common.models import MethodInfo


@dataclass
class JDK8Feature:
    """JDK8特性信息"""
    name: str
    pattern: str
    description: str
    example: str


@dataclass
class JDK8CheckResult:
    """JDK8检查结果"""
    file_path: str
    has_lambda: bool = False
    has_method_reference: bool = False
    has_stream_api: bool = False
    has_default_methods: bool = False
    has_optional: bool = False
    parsing_issues: List[str] = None
    fallback_needed: bool = False
    parsed_methods: List[MethodInfo] = None
    javalang_success: bool = True
    
    def __post_init__(self):
        if self.parsing_issues is None:
            self.parsing_issues = []
        if self.parsed_methods is None:
            self.parsed_methods = []


class JDK8CompatibilityChecker:
    """
    JDK8兼容性检查器
    
    检查javalang对JDK8语法特性的支持情况，提供回退解析策略。
    """
    
    def __init__(self):
        """初始化检查器"""
        self.logger = logging.getLogger(__name__)
        
        # 定义JDK8特性模式
        self.jdk8_features = {
            'lambda': JDK8Feature(
                name='Lambda表达式',
                pattern=r'(?:\w+\s*->\s*|\([^)]*\)\s*->\s*)',
                description='Lambda表达式语法',
                example='x -> x * 2, (a, b) -> a + b'
            ),
            'method_reference': JDK8Feature(
                name='方法引用',
                pattern=r'\w+::\w+',
                description='方法引用语法',
                example='String::length, System.out::println'
            ),
            'stream_api': JDK8Feature(
                name='Stream API',
                pattern=r'\.stream\(\)|\.parallelStream\(\)',
                description='Stream API调用',
                example='.stream().filter().map().collect()'
            ),
            'default_methods': JDK8Feature(
                name='接口默认方法',
                pattern=r'default\s+\w+.*\{',
                description='接口中的默认方法',
                example='default String getName() { return "default"; }'
            ),
            'optional': JDK8Feature(
                name='Optional类',
                pattern=r'Optional<[^>]+>|Optional\.',
                description='Optional类的使用',
                example='Optional<String>, Optional.of()'
            )
        }
        
        self.stats = {
            'total_files': 0,
            'javalang_success': 0,
            'javalang_failed': 0,
            'fallback_used': 0,
            'features_detected': {}
        }
    
    def check_jdk8_support(self, file_path: str) -> JDK8CheckResult:
        """
        检查单个文件的JDK8特性支持
        
        Args:
            file_path: Java文件路径
            
        Returns:
            JDK8检查结果
        """
        self.logger.debug(f"检查JDK8支持: {file_path}")
        
        result = JDK8CheckResult(file_path=file_path)
        
        try:
            # 读取文件内容
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except UnicodeDecodeError:
            try:
                with open(file_path, 'r', encoding='gbk') as f:
                    content = f.read()
            except UnicodeDecodeError:
                with open(file_path, 'r', encoding='latin-1') as f:
                    content = f.read()
        
        # 检测JDK8特性
        self._detect_jdk8_features(content, result)
        
        # 尝试使用javalang解析
        try:
            tree = javalang.parse.parse(content)
            result.javalang_success = True
            result.parsed_methods = self._extract_methods_from_ast(tree, file_path)
            self.stats['javalang_success'] += 1
            
        except Exception as e:
            # 取消回退功能，直接报错
            error_msg = f"javalang解析失败: {file_path} - {str(e)}"
            self.logger.error(error_msg)
            raise RuntimeError(error_msg)
        
        self.stats['total_files'] += 1
        return result
    
    def check_directory(self, src_dir: str) -> List[JDK8CheckResult]:
        """
        检查目录中所有Java文件的JDK8支持
        
        Args:
            src_dir: 源码目录
            
        Returns:
            检查结果列表
        """
        self.logger.info(f"开始检查目录JDK8支持: {src_dir}")
        
        java_files = self._find_java_files(src_dir)
        results = []
        
        for file_path in java_files:
            try:
                result = self.check_jdk8_support(file_path)
                results.append(result)
            except Exception as e:
                self.logger.error(f"检查文件失败 {file_path}: {str(e)}")
                continue
        
        self._log_check_stats(results)
        return results
    
    def _detect_jdk8_features(self, content: str, result: JDK8CheckResult):
        """检测JDK8特性"""
        for feature_name, feature in self.jdk8_features.items():
            if re.search(feature.pattern, content):
                setattr(result, f'has_{feature_name}', True)
                
                # 更新统计
                if feature_name not in self.stats['features_detected']:
                    self.stats['features_detected'][feature_name] = 0
                self.stats['features_detected'][feature_name] += 1
    
    def _extract_methods_from_ast(self, tree: CompilationUnit, file_path: str) -> List[MethodInfo]:
        """从AST中提取方法信息"""
        methods = []
        package_name = tree.package.name if tree.package else ""
        
        # 遍历所有类声明
        for path, node in tree.filter(javalang.tree.ClassDeclaration):
            class_name = node.name
            
            for method in node.methods:
                if isinstance(method, MethodDeclaration):
                    method_info = self._create_method_info(
                        method, package_name, class_name, file_path
                    )
                    methods.append(method_info)
        
        # 遍历接口声明
        for path, node in tree.filter(javalang.tree.InterfaceDeclaration):
            interface_name = node.name
            
            for method in node.body:
                if isinstance(method, MethodDeclaration):
                    method_info = self._create_method_info(
                        method, package_name, interface_name, file_path
                    )
                    methods.append(method_info)
        
        return methods
    
    def _create_method_info(self, method: MethodDeclaration, package_name: str,
                          class_name: str, file_path: str) -> MethodInfo:
        """创建方法信息对象"""
        # 提取方法名
        method_name = method.name
        
        # 提取参数信息
        parameters = []
        if method.parameters:
            for param in method.parameters:
                param_type = self._get_type_string(param.type)
                param_name = param.name
                parameters.append(f"{param_name}: {param_type}")
        
        # 提取返回类型
        return_type = "void"
        if method.return_type:
            return_type = self._get_type_string(method.return_type)
        
        # 提取修饰符
        modifiers = []
        if method.modifiers:
            modifiers = [str(mod) for mod in method.modifiers]
        
        # 构建上下文信息
        context = f"方法: {method_name}({', '.join(parameters)}) -> {return_type}"
        
        return MethodInfo(
            package=package_name,
            class_name=class_name,
            method_name=method_name,
            parameters=parameters,
            return_type=return_type,
            context=context,
            file_path=file_path,
            modifiers=modifiers,
            annotations=[]
        )
    
    def _get_type_string(self, type_node) -> str:
        """将类型节点转换为字符串"""
        if hasattr(type_node, 'name'):
            return type_node.name
        else:
            return str(type_node)
    
    # 回退解析功能已移除 - 系统将直接报错而不是降级
    
    def _find_java_files(self, directory: str) -> List[str]:
        """递归查找所有Java文件"""
        java_files = []
        for root, dirs, files in os.walk(directory):
            # 跳过常见的非源码目录
            dirs[:] = [d for d in dirs if d not in {
                'target', 'build', '.git', '.svn', 'node_modules', 
                '.idea', '.vscode', '__pycache__'
            }]
            
            for file in files:
                if file.endswith('.java'):
                    java_files.append(os.path.join(root, file))
        
        return java_files
    
    def _log_check_stats(self, results: List[JDK8CheckResult]):
        """记录检查统计信息"""
        self.logger.info("=== JDK8兼容性检查统计 ===")
        self.logger.info(f"总文件数: {self.stats['total_files']}")
        self.logger.info(f"javalang成功解析: {self.stats['javalang_success']}")
        self.logger.info(f"javalang解析失败: {self.stats['javalang_failed']}")
        self.logger.info(f"使用回退解析: {self.stats['fallback_used']}")
        
        if self.stats['total_files'] > 0:
            success_rate = (self.stats['javalang_success'] / self.stats['total_files']) * 100
            self.logger.info(f"javalang成功率: {success_rate:.1f}%")
        
        # 特性检测统计
        self.logger.info("=== JDK8特性检测统计 ===")
        for feature_name, count in self.stats['features_detected'].items():
            feature = self.jdk8_features[feature_name]
            self.logger.info(f"{feature.name}: {count} 个文件")
        
        # 问题汇总
        parsing_issues = []
        fallback_files = []
        
        for result in results:
            if result.parsing_issues:
                parsing_issues.extend(result.parsing_issues)
            if result.fallback_needed:
                fallback_files.append(result.file_path)
        
        if parsing_issues:
            self.logger.warning("=== 解析问题汇总 ===")
            issue_counts = {}
            for issue in parsing_issues:
                issue_counts[issue] = issue_counts.get(issue, 0) + 1
            
            for issue, count in sorted(issue_counts.items(), key=lambda x: x[1], reverse=True):
                self.logger.warning(f"{issue}: {count} 次")
        
        if fallback_files:
            self.logger.warning(f"需要回退解析的文件: {len(fallback_files)} 个")
            for file_path in fallback_files[:5]:  # 只显示前5个
                self.logger.warning(f"  - {file_path}")
    
    def get_compatibility_report(self, results: List[JDK8CheckResult]) -> Dict[str, Any]:
        """生成兼容性报告"""
        report = {
            'summary': {
                'total_files': len(results),
                'javalang_success': sum(1 for r in results if r.javalang_success),
                'javalang_failed': sum(1 for r in results if not r.javalang_success),
                'fallback_needed': sum(1 for r in results if r.fallback_needed),
                'success_rate': 0.0
            },
            'features': {
                'lambda_expressions': sum(1 for r in results if r.has_lambda),
                'method_references': sum(1 for r in results if r.has_method_reference),
                'stream_api': sum(1 for r in results if r.has_stream_api),
                'default_methods': sum(1 for r in results if r.has_default_methods),
                'optional_usage': sum(1 for r in results if r.has_optional)
            },
            'issues': {},
            'recommendations': []
        }
        
        if len(results) > 0:
            report['summary']['success_rate'] = (report['summary']['javalang_success'] / len(results)) * 100
        
        # 收集问题
        all_issues = []
        for result in results:
            all_issues.extend(result.parsing_issues)
        
        issue_counts = {}
        for issue in all_issues:
            issue_counts[issue] = issue_counts.get(issue, 0) + 1
        
        report['issues'] = dict(sorted(issue_counts.items(), key=lambda x: x[1], reverse=True))
        
        # 生成建议
        if report['summary']['success_rate'] < 90:
            report['recommendations'].append("建议升级javalang版本或使用替代解析器")
        
        if report['features']['lambda_expressions'] > 0:
            report['recommendations'].append("检测到Lambda表达式，确保解析器支持")
        
        if report['features']['stream_api'] > 0:
            report['recommendations'].append("检测到Stream API，可能需要特殊处理")
        
        return report
    
    def create_enhanced_parser(self) -> 'EnhancedJavaParser':
        """创建增强的Java解析器"""
        return EnhancedJavaParser(self)


class EnhancedJavaParser:
    """
    增强的Java解析器
    
    结合javalang和回退策略，提供更好的JDK8支持
    """
    
    def __init__(self, compatibility_checker: JDK8CompatibilityChecker):
        """初始化增强解析器"""
        self.compatibility_checker = compatibility_checker
        self.logger = logging.getLogger(__name__)
    
    def parse_file(self, file_path: str) -> Tuple[List[MethodInfo], JDK8CheckResult]:
        """
        解析Java文件
        
        Args:
            file_path: Java文件路径
            
        Returns:
            (方法信息列表, JDK8检查结果)
        """
        check_result = self.compatibility_checker.check_jdk8_support(file_path)
        
        if check_result.javalang_success:
            self.logger.debug(f"javalang成功解析: {file_path}")
        else:
            self.logger.warning(f"javalang解析失败，使用回退策略: {file_path}")
        
        return check_result.parsed_methods, check_result
    
    def parse_directory(self, src_dir: str) -> Tuple[List[MethodInfo], List[JDK8CheckResult]]:
        """
        解析目录中的所有Java文件
        
        Args:
            src_dir: 源码目录
            
        Returns:
            (所有方法信息列表, JDK8检查结果列表)
        """
        check_results = self.compatibility_checker.check_directory(src_dir)
        
        all_methods = []
        for result in check_results:
            all_methods.extend(result.parsed_methods)
        
        return all_methods, check_results