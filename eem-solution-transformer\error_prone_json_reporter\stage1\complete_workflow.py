"""
完整的第一阶段工作流程

整合 inspect_method.py + class_file_reader.py 的完整工作流程
实现：识别方法问题 → 读取源码信息 → 生成完整 JSON 清单
"""

import os
import sys
import time
import json
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime

# 添加java_error_analyzer到路径
current_dir = Path(__file__).parent
project_root = current_dir.parent.parent
java_analyzer_path = project_root / "java_error_analyzer"
if str(java_analyzer_path) not in sys.path:
    sys.path.insert(0, str(java_analyzer_path))

try:
    from inspect_method import InspectRunner
    from class_file_reader import ClassFileReader
    from models import ClassFileInfo
except ImportError as e:
    print(f"Warning: Could not import java_error_analyzer modules: {e}")
    InspectRunner = None
    ClassFileReader = None
    ClassFileInfo = None

from ..common.models import ErrorReport, Configuration, ProcessingStats
from .json_formatter import JSONFormatter


class CompleteWorkflow:
    """完整的第一阶段工作流程控制器"""
    
    def __init__(self, config: Configuration):
        self.config = config
        self.stats = ProcessingStats()
        self.json_formatter = JSONFormatter(config.output_dir)
        
        # 确保输出目录存在
        Path(config.output_dir).mkdir(parents=True, exist_ok=True)
        
        # 初始化工具
        self.inspect_runner = None
        self.class_reader = None
        
    def run_complete_workflow(self, project_path: str) -> str:
        """
        运行完整的第一阶段工作流程
        
        Args:
            project_path: Java项目路径
            
        Returns:
            生成的JSON文件路径
        """
        print("=== Starting Complete Stage 1 Workflow ===")
        start_time = time.time()
        
        try:
            # 步骤1: 初始化工具
            print("\nStep 1: Initializing tools...")
            if not self._initialize_tools(project_path):
                raise RuntimeError("Failed to initialize required tools")
            
            # 步骤2: 运行inspect_method.py识别方法问题
            print("\nStep 2: Running inspect_method.py to identify method problems...")
            method_problems = self._run_inspect_method()
            if not method_problems:
                print("No method problems found")
                return self._generate_empty_result()
            
            print(f"Found {len(method_problems)} method problems")
            self.stats.total_errors = len(method_problems)
            
            # 步骤3: 使用class_file_reader.py获取详细源码信息
            print("\nStep 3: Enhancing with detailed source code information...")
            enhanced_errors = self._enhance_with_source_info(method_problems, project_path)
            print(f"Enhanced {len(enhanced_errors)} errors with source information")
            self.stats.processed_errors = len(enhanced_errors)
            
            # 步骤4: 数据质量验证和清理
            print("\nStep 4: Validating and cleaning data...")
            validated_errors = self._validate_and_clean_data(enhanced_errors)
            print(f"Validated {len(validated_errors)} errors")
            self.stats.successful_matches = len(validated_errors)
            
            # 步骤5: 生成符合第二阶段要求的JSON格式
            print("\nStep 5: Generating JSON output for API matching...")
            output_path = self._generate_json_output(validated_errors)
            
            # 步骤6: 记录统计信息
            self.stats.processing_time = time.time() - start_time
            self._log_statistics()
            
            print(f"\n=== Workflow completed successfully! ===")
            print(f"Output saved to: {output_path}")
            return output_path
            
        except Exception as e:
            self.stats.processing_time = time.time() - start_time
            self.stats.failed_matches = self.stats.total_errors - self.stats.successful_matches
            print(f"Error in complete workflow: {e}")
            raise
    
    def _initialize_tools(self, project_path: str) -> bool:
        """初始化所需工具"""
        if not InspectRunner or not ClassFileReader:
            print("Error: Required tools (InspectRunner, ClassFileReader) are not available")
            return False
        
        try:
            # 初始化InspectRunner
            self.inspect_runner = InspectRunner(
                project_path=project_path,
                output_path=self.config.output_dir
            )
            print(f"  ✓ InspectRunner initialized for project: {project_path}")
            
            # 初始化ClassFileReader
            self.class_reader = ClassFileReader(project_path=project_path)
            print(f"  ✓ ClassFileReader initialized for project: {project_path}")
            
            return True
            
        except Exception as e:
            print(f"  ✗ Error initializing tools: {e}")
            return False
    
    def _run_inspect_method(self) -> List[Dict[str, Any]]:
        """运行inspect_method.py获取方法问题"""
        try:
            print("  Running inspect.bat...")
            if not self.inspect_runner.run_inspect():
                print("  ✗ Failed to run inspect.bat")
                return []
            
            print("  ✓ inspect.bat completed successfully")
            
            print("  Parsing problems from XML output...")
            all_problems = self.inspect_runner.parse_problems_manually()
            if not all_problems:
                print("  ✗ No problems found in XML output")
                return []
            
            print(f"  ✓ Parsed {len(all_problems)} total problems")
            
            print("  Filtering method-related problems...")
            method_problems = self.inspect_runner.filter_method_problems(all_problems)
            print(f"  ✓ Found {len(method_problems)} method-related problems")
            
            return method_problems
            
        except Exception as e:
            print(f"  ✗ Error running inspect_method: {e}")
            return []
    
    def _enhance_with_source_info(self, method_problems: List[Dict[str, Any]], 
                                project_path: str) -> List[ErrorReport]:
        """使用class_file_reader.py增强错误信息"""
        enhanced_errors = []
        
        for i, problem in enumerate(method_problems, 1):
            try:
                print(f"  Processing problem {i}/{len(method_problems)}: {problem.get('highlighted_element', 'unknown')}")
                
                # 提取基本信息
                basic_info = self._extract_basic_info(problem)
                if not basic_info:
                    print(f"    ✗ Failed to extract basic info")
                    continue
                
                # 使用class_file_reader获取详细信息
                detailed_info = self._get_detailed_source_info(basic_info)
                
                # 构建增强的ErrorReport
                enhanced_error = self._build_enhanced_error_report(basic_info, detailed_info)
                if enhanced_error:
                    enhanced_errors.append(enhanced_error)
                    print(f"    ✓ Enhanced successfully")
                else:
                    print(f"    ✗ Failed to build error report")
                
            except Exception as e:
                print(f"    ✗ Error enhancing problem {i}: {e}")
                continue
        
        return enhanced_errors
    
    def _extract_basic_info(self, problem: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """从problem中提取基本信息"""
        try:
            # 提取文件路径和类名
            file_path = problem.get('file', '')
            class_name = self._extract_class_name_from_file(file_path)
            
            # 提取包名
            package_name = problem.get('package', '')
            
            # 提取方法名
            highlighted_element = problem.get('highlighted_element', '')
            method_name = self._clean_method_name(highlighted_element)
            
            # 提取行号
            line_str = problem.get('line', '0')
            line_number = self._parse_line_number(line_str)
            
            # 提取描述
            description = problem.get('description', '')
            
            if not class_name or not method_name:
                return None
            
            return {
                'file_path': file_path,
                'class_name': class_name,
                'package_name': package_name,
                'method_name': method_name,
                'line_number': line_number,
                'description': description,
                'original_problem': problem
            }
            
        except Exception as e:
            print(f"Error extracting basic info: {e}")
            return None
    
    def _extract_class_name_from_file(self, file_path: str) -> str:
        """从文件路径提取类名"""
        if not file_path:
            return ""
        
        filename = os.path.basename(file_path)
        if filename.endswith('.java'):
            return filename[:-5]
        
        return filename
    
    def _clean_method_name(self, highlighted_element: str) -> str:
        """清理方法名"""
        if not highlighted_element:
            return ""
        
        # 移除空白字符
        method_name = highlighted_element.strip()
        
        # 如果包含括号，只取括号前的部分
        if '(' in method_name:
            method_name = method_name.split('(')[0].strip()
        
        return method_name
    
    def _parse_line_number(self, line_str: str) -> int:
        """解析行号"""
        try:
            return int(line_str) if line_str and line_str.isdigit() else 0
        except (ValueError, TypeError):
            return 0
    
    def _get_detailed_source_info(self, basic_info: Dict[str, Any]) -> Dict[str, Any]:
        """使用class_file_reader获取详细源码信息"""
        detailed_info = {
            'class_info': None,
            'method_signature': None,
            'parameters': {},
            'return_type': 'unknown',
            'context_lines': [],
            'surrounding_methods': []
        }
        
        try:
            class_name = basic_info['class_name']
            package_name = basic_info['package_name']
            method_name = basic_info['method_name']
            line_number = basic_info['line_number']
            
            # 尝试读取类文件信息
            class_info = self._read_class_file_info(class_name, package_name)
            detailed_info['class_info'] = class_info
            
            if class_info:
                print(f"      ✓ Found class info for {class_name}")
                
                # 分析方法调用上下文
                method_context = self._analyze_method_context(
                    class_info, method_name, line_number
                )
                detailed_info.update(method_context)
                
                # 获取周围的方法信息
                if hasattr(class_info, 'methods') and class_info.methods:
                    detailed_info['surrounding_methods'] = class_info.methods[:5]  # 前5个方法
            else:
                print(f"      ✗ Could not find class info for {class_name}")
            
        except Exception as e:
            print(f"      ✗ Error getting detailed source info: {e}")
        
        return detailed_info
    
    def _read_class_file_info(self, class_name: str, package_name: str) -> Optional[Any]:
        """读取类文件信息"""
        # 尝试不同的类名组合
        class_names_to_try = [
            class_name,
            f"{package_name}.{class_name}" if package_name else class_name,
        ]
        
        for full_class_name in class_names_to_try:
            try:
                class_info = self.class_reader.read_class_file(full_class_name)
                if class_info:
                    return class_info
            except Exception as e:
                print(f"        Warning: Failed to read {full_class_name}: {e}")
                continue
        
        return None
    
    def _analyze_method_context(self, class_info: Any, method_name: str, 
                              line_number: int) -> Dict[str, Any]:
        """分析方法调用上下文"""
        context = {
            'method_signature': None,
            'parameters': {},
            'return_type': 'unknown',
            'context_lines': []
        }
        
        try:
            if not hasattr(class_info, 'content') or not class_info.content:
                return context
            
            lines = class_info.content.split('\n')
            
            # 如果有行号，分析该行及周围的代码
            if line_number > 0 and line_number <= len(lines):
                target_line = lines[line_number - 1]
                
                # 获取上下文行
                start_line = max(0, line_number - 3)
                end_line = min(len(lines), line_number + 3)
                context_lines = []
                
                for i in range(start_line, end_line):
                    line_content = lines[i].strip()
                    if line_content:
                        context_lines.append(f"Line {i+1}: {line_content}")
                
                context['context_lines'] = context_lines
                
                # 分析方法调用
                method_call_info = self._parse_method_call(target_line, method_name)
                context.update(method_call_info)
            
            # 尝试从整个文件中查找方法定义或调用
            method_usage_info = self._find_method_usage_in_source(
                class_info.content, method_name
            )
            if method_usage_info:
                # 合并信息，优先使用更详细的信息
                for key, value in method_usage_info.items():
                    if value and (not context.get(key) or context[key] == 'unknown'):
                        context[key] = value
            
        except Exception as e:
            print(f"        Error analyzing method context: {e}")
        
        return context
    
    def _parse_method_call(self, line: str, method_name: str) -> Dict[str, Any]:
        """解析方法调用行"""
        call_info = {
            'parameters': {},
            'return_type': 'unknown'
        }
        
        try:
            if method_name not in line:
                return call_info
            
            # 查找方法调用模式
            import re
            
            # 尝试匹配方法调用 methodName(params)
            method_pattern = rf'{re.escape(method_name)}\s*\((.*?)\)'
            match = re.search(method_pattern, line)
            
            if match:
                params_str = match.group(1).strip()
                if params_str:
                    # 简单的参数解析
                    param_parts = [p.strip() for p in params_str.split(',')]
                    for i, param in enumerate(param_parts):
                        if param:
                            param_type = self._infer_parameter_type(param)
                            call_info['parameters'][f"param{i+1}"] = param_type
            
            # 尝试推断返回类型
            return_type = self._infer_return_type_from_line(line, method_name)
            if return_type:
                call_info['return_type'] = return_type
            
        except Exception as e:
            print(f"        Error parsing method call: {e}")
        
        return call_info
    
    def _infer_parameter_type(self, param_expr: str) -> str:
        """推断参数类型"""
        param_expr = param_expr.strip()
        
        # 字符串字面量
        if (param_expr.startswith('"') and param_expr.endswith('"')) or \
           (param_expr.startswith("'") and param_expr.endswith("'")):
            return "java.lang.String"
        
        # 数字字面量
        if param_expr.isdigit():
            return "int"
        
        if '.' in param_expr and param_expr.replace('.', '').replace('-', '').isdigit():
            return "double"
        
        # 布尔值
        if param_expr.lower() in ['true', 'false']:
            return "boolean"
        
        # null
        if param_expr.lower() == 'null':
            return "java.lang.Object"
        
        # 变量名或复杂表达式
        return "java.lang.Object"
    
    def _infer_return_type_from_line(self, line: str, method_name: str) -> str:
        """从代码行推断返回类型"""
        try:
            # 查看赋值语句
            if '=' in line and method_name in line:
                import re
                # 查找变量声明模式
                var_pattern = r'(\w+(?:<[^>]+>)?)\s+\w+\s*='
                match = re.search(var_pattern, line)
                if match:
                    return match.group(1)
            
            # 查看条件语句
            if any(keyword in line for keyword in ['if', 'while', '&&', '||']) and method_name in line:
                return "boolean"
            
            # 查看return语句
            if 'return' in line and method_name in line:
                return "java.lang.Object"  # 需要更多上下文来确定
            
        except Exception as e:
            print(f"        Error inferring return type: {e}")
        
        return "unknown"
    
    def _find_method_usage_in_source(self, source_content: str, method_name: str) -> Dict[str, Any]:
        """在源码中查找方法使用情况"""
        usage_info = {}
        
        try:
            lines = source_content.split('\n')
            
            for line_num, line in enumerate(lines, 1):
                if method_name in line:
                    # 分析这一行的方法调用
                    call_info = self._parse_method_call(line, method_name)
                    
                    # 如果找到了参数信息，合并到结果中
                    if call_info['parameters']:
                        if not usage_info.get('parameters'):
                            usage_info['parameters'] = {}
                        usage_info['parameters'].update(call_info['parameters'])
                    
                    # 如果找到了返回类型信息
                    if call_info['return_type'] != 'unknown':
                        usage_info['return_type'] = call_info['return_type']
            
        except Exception as e:
            print(f"        Error finding method usage: {e}")
        
        return usage_info
    
    def _build_enhanced_error_report(self, basic_info: Dict[str, Any], 
                                   detailed_info: Dict[str, Any]) -> Optional[ErrorReport]:
        """构建增强的错误报告"""
        try:
            # 构建上下文信息
            context_parts = []
            
            # 添加基本信息
            context_parts.append(f"Method: {basic_info['method_name']}")
            if basic_info['line_number'] > 0:
                context_parts.append(f"Line: {basic_info['line_number']}")
            
            # 添加文件信息
            if basic_info['file_path']:
                context_parts.append(f"File: {basic_info['file_path']}")
            
            # 添加源码上下文
            if detailed_info['context_lines']:
                context_lines_str = "; ".join(detailed_info['context_lines'][:3])  # 限制长度
                context_parts.append(f"Context: {context_lines_str}")
            
            # 添加周围方法信息
            if detailed_info['surrounding_methods']:
                methods_str = ", ".join(detailed_info['surrounding_methods'][:3])
                context_parts.append(f"Available methods: {methods_str}")
            
            context = "; ".join(context_parts)
            
            # 构建ErrorReport
            error_report = ErrorReport(
                package=basic_info['package_name'] or "",
                class_name=basic_info['class_name'],
                missing_method=basic_info['method_name'],
                in_param=detailed_info['parameters'],
                out_return=detailed_info['return_type'],
                line=[basic_info['line_number']] if basic_info['line_number'] > 0 else [],
                context=context,
                error_type="missing_method",
                
                # 扩展字段
                method_signature=detailed_info.get('method_signature', ''),
                parameter_types=detailed_info['parameters'],
                return_type_full=detailed_info['return_type'],
                file_path=basic_info['file_path'],
                context_lines=detailed_info['context_lines'],
                surrounding_methods=detailed_info['surrounding_methods'],
                
                # 元数据
                source_tool="complete_workflow",
                timestamp=datetime.now().isoformat()
            )
            
            return error_report
            
        except Exception as e:
            print(f"Error building enhanced error report: {e}")
            return None
    
    def _validate_and_clean_data(self, errors: List[ErrorReport]) -> List[ErrorReport]:
        """验证和清理数据"""
        validated_errors = []
        
        for i, error in enumerate(errors, 1):
            try:
                print(f"  Validating error {i}/{len(errors)}: {error.missing_method}")
                
                # 基本验证
                if not error.class_name or not error.missing_method:
                    print(f"    ✗ Missing required fields")
                    continue
                
                # 数据清理
                cleaned_error = self._clean_error_data(error)
                
                # 计算置信度
                confidence = self._calculate_confidence(cleaned_error)
                cleaned_error.confidence_score = confidence
                
                # 只保留置信度足够高的错误
                if confidence >= 0.3:  # 最低置信度阈值
                    validated_errors.append(cleaned_error)
                    print(f"    ✓ Validated (confidence: {confidence:.2f})")
                else:
                    print(f"    ✗ Low confidence: {confidence:.2f}")
                
            except Exception as e:
                print(f"    ✗ Validation error: {e}")
                continue
        
        return validated_errors
    
    def _clean_error_data(self, error: ErrorReport) -> ErrorReport:
        """清理错误数据"""
        # 清理包名
        if not error.package or error.package == "unknown":
            error.package = ""
        
        # 清理方法名
        error.missing_method = error.missing_method.strip()
        
        # 清理参数信息
        if error.in_param:
            cleaned_params = {}
            for key, value in error.in_param.items():
                if value and value != "unknown":
                    cleaned_params[key.strip()] = value.strip()
            error.in_param = cleaned_params
        
        # 清理返回类型
        if not error.out_return or error.out_return == "unknown":
            error.out_return = "java.lang.Object"
        
        return error
    
    def _calculate_confidence(self, error: ErrorReport) -> float:
        """计算置信度分数"""
        confidence = 1.0
        
        # 根据信息完整性调整
        if not error.package:
            confidence -= 0.1
        
        if not error.in_param:
            confidence -= 0.15
        
        if error.out_return == "java.lang.Object":
            confidence -= 0.05
        
        if not error.line:
            confidence -= 0.1
        
        if not error.context:
            confidence -= 0.1
        
        # 根据上下文信息质量调整
        if error.context_lines:
            confidence += 0.1
        
        if error.surrounding_methods:
            confidence += 0.05
        
        if error.file_path:
            confidence += 0.05
        
        return max(0.0, min(1.0, confidence))
    
    def _generate_json_output(self, errors: List[ErrorReport]) -> str:
        """生成JSON输出"""
        print(f"  Generating JSON output for {len(errors)} errors...")
        
        # 生成标准格式
        standard_output = self.json_formatter.format_to_json(
            errors, self.config.errors_json, include_metadata=True
        )
        
        # 生成API匹配格式
        api_matching_file = self.config.errors_json.replace('.json', '_for_matching.json')
        self.json_formatter.format_for_api_matching(errors, api_matching_file)
        
        # 生成Schema文件
        self.json_formatter.save_schema("error_report_schema.json")
        
        # 验证生成的JSON
        valid, message = self.json_formatter.validate_json_format(standard_output)
        if valid:
            print(f"  ✓ JSON validation passed")
        else:
            print(f"  ✗ JSON validation failed: {message}")
        
        output_path = str(Path(self.config.output_dir) / self.config.errors_json)
        print(f"  ✓ Standard format saved to: {output_path}")
        print(f"  ✓ API matching format saved to: {Path(self.config.output_dir) / api_matching_file}")
        
        return output_path
    
    def _generate_empty_result(self) -> str:
        """生成空结果"""
        empty_errors = []
        output_path = self.json_formatter.format_to_json(
            empty_errors, self.config.errors_json, include_metadata=True
        )
        return str(Path(self.config.output_dir) / self.config.errors_json)
    
    def _log_statistics(self):
        """记录统计信息"""
        print("\n=== Complete Workflow Statistics ===")
        print(f"Total method problems found: {self.stats.total_errors}")
        print(f"Errors processed with source info: {self.stats.processed_errors}")
        print(f"Errors validated and included: {self.stats.successful_matches}")
        print(f"Processing time: {self.stats.processing_time:.2f} seconds")
        
        if self.stats.total_errors > 0:
            success_rate = self.stats.successful_matches / self.stats.total_errors
            print(f"Success rate: {success_rate:.2%}")
        
        if self.stats.processing_time > 0:
            processing_rate = self.stats.processed_errors / self.stats.processing_time
            print(f"Processing rate: {processing_rate:.2f} errors/second")
        
        print("=" * 40)


def run_complete_workflow(project_path: str, config: Configuration) -> str:
    """
    运行完整的第一阶段工作流程
    
    Args:
        project_path: 项目路径
        config: 配置对象
        
    Returns:
        生成的JSON文件路径
    """
    workflow = CompleteWorkflow(config)
    return workflow.run_complete_workflow(project_path)


def main():
    """命令行入口"""
    import argparse
    from ..config.config_manager import ConfigManager
    
    parser = argparse.ArgumentParser(description='Run complete Stage 1 workflow')
    parser.add_argument('project_path', help='Path to Java project')
    parser.add_argument('--config', help='Configuration file path', 
                       default='config/config.yaml')
    parser.add_argument('--output-dir', help='Output directory', default='output')
    
    args = parser.parse_args()
    
    try:
        # 加载配置
        config_manager = ConfigManager()
        config = config_manager.load_config(args.config)
        
        # 覆盖命令行参数
        config.project_path = args.project_path
        config.output_dir = args.output_dir
        config.error_detector = "complete_workflow"
        
        # 运行完整工作流程
        output_path = run_complete_workflow(args.project_path, config)
        print(f"\nSuccess! Complete workflow output saved to: {output_path}")
        
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()