"""
源码上下文提取器主程序

整合所有组件，提供命令行接口和完整的处理流程。
"""

import argparse
import sys
import os
import time
import logging
from pathlib import Path
from typing import List, Optional

from .models import ExtractorConfig, ErrorItem, MethodAnalysisResult, ProcessingStats
from .config import ConfigManager
from .logging_config import setup_logging, get_logger, logging_manager


def create_argument_parser() -> argparse.ArgumentParser:
    """创建命令行参数解析器"""
    parser = argparse.ArgumentParser(
        description="源码上下文提取器 - 分析Java代码迁移错误并提取源码上下文",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python -m source_context_extractor.main -i target_method_test.json
  python -m source_context_extractor.main -i errors.json -o analysis.md
  python -m source_context_extractor.main -i errors.json --src-path newcode --legacy-src-path oldcode
        """
    )
    
    # 输入输出参数
    parser.add_argument(
        '-i', '--input',
        required=True,
        help='输入的JSON错误报告文件路径'
    )
    
    parser.add_argument(
        '-o', '--output',
        help='输出的Markdown分析报告文件路径'
    )
    
    # 路径配置
    parser.add_argument(
        '--src-path',
        help='新框架源码路径'
    )
    
    parser.add_argument(
        '--legacy-src-path',
        help='迁移前源码路径'
    )
    
    parser.add_argument(
        '--knowledge-base-path',
        help='知识库路径'
    )
    
    # 处理选项
    parser.add_argument(
        '--disable-ast',
        action='store_true',
        help='禁用AST解析，仅使用文本解析'
    )
    
    parser.add_argument(
        '--disable-text-fallback',
        action='store_true',
        help='禁用文本解析回退'
    )
    
    parser.add_argument(
        '--max-context-lines',
        type=int,
        help='最大上下文行数'
    )
    
    parser.add_argument(
        '--timeout',
        type=int,
        help='处理超时时间（秒）'
    )
    
    # 配置文件
    parser.add_argument(
        '-c', '--config',
        help='配置文件路径'
    )
    
    # 日志选项
    parser.add_argument(
        '--log-level',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        help='日志级别'
    )
    
    parser.add_argument(
        '--log-file',
        help='日志文件路径'
    )
    
    parser.add_argument(
        '--no-console',
        action='store_true',
        help='禁用控制台日志输出'
    )
    
    parser.add_argument(
        '--no-file-log',
        action='store_true',
        help='禁用文件日志输出'
    )
    
    # 其他选项
    parser.add_argument(
        '--version',
        action='version',
        version='源码上下文提取器 1.0.0'
    )
    
    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='试运行模式，仅验证配置和输入文件'
    )
    
    return parser


def parse_cli_args() -> argparse.Namespace:
    """解析命令行参数"""
    parser = create_argument_parser()
    return parser.parse_args()


def validate_input_file(input_path: str) -> bool:
    """验证输入文件"""
    if not os.path.exists(input_path):
        print(f"错误: 输入文件不存在: {input_path}")
        return False
    
    if not input_path.lower().endswith('.json'):
        print(f"警告: 输入文件不是JSON格式: {input_path}")
    
    return True


def validate_paths(config: ExtractorConfig) -> List[str]:
    """验证路径配置"""
    warnings = []
    
    if config.src_path and not os.path.exists(config.src_path):
        warnings.append(f"源码路径不存在: {config.src_path}")
    
    if config.legacy_src_path and not os.path.exists(config.legacy_src_path):
        warnings.append(f"遗留源码路径不存在: {config.legacy_src_path}")
    
    if config.knowledge_base_path and not os.path.exists(config.knowledge_base_path):
        warnings.append(f"知识库路径不存在: {config.knowledge_base_path}")
    
    return warnings


def create_output_directory(output_path: str) -> bool:
    """创建输出目录"""
    try:
        output_dir = os.path.dirname(output_path)
        if output_dir:
            Path(output_dir).mkdir(parents=True, exist_ok=True)
        return True
    except Exception as e:
        print(f"错误: 无法创建输出目录: {e}")
        return False


def load_configuration(args: argparse.Namespace) -> Optional[ExtractorConfig]:
    """加载配置"""
    try:
        config_manager = ConfigManager()
        
        # 加载基础配置
        if args.config:
            config = config_manager.load_config(args.config)
        else:
            config = config_manager.load_config()
        
        # 合并命令行参数
        cli_args = {}
        
        if args.output:
            cli_args['output_path'] = args.output
        if args.src_path:
            cli_args['src_path'] = args.src_path
        if args.legacy_src_path:
            cli_args['legacy_src_path'] = args.legacy_src_path
        if args.knowledge_base_path:
            cli_args['knowledge_base_path'] = args.knowledge_base_path
        if args.disable_ast:
            cli_args['enable_ast_parsing'] = False
        if args.disable_text_fallback:
            cli_args['enable_text_fallback'] = False
        if args.max_context_lines:
            cli_args['max_context_lines'] = args.max_context_lines
        if args.timeout:
            cli_args['timeout_seconds'] = args.timeout
        if args.log_level:
            cli_args['log_level'] = args.log_level
        if args.log_file:
            cli_args['log_file'] = args.log_file
        if args.no_console:
            cli_args['console_output'] = False
        if args.no_file_log:
            cli_args['file_output'] = False
        
        # 合并配置
        if cli_args:
            config = config_manager.merge_cli_args(config, cli_args)
        
        return config
        
    except Exception as e:
        print(f"错误: 配置加载失败: {e}")
        return None


def dry_run_mode(config: ExtractorConfig, input_path: str) -> bool:
    """试运行模式"""
    print("=" * 60)
    print("试运行模式 - 配置验证")
    print("=" * 60)
    
    # 验证输入文件
    print(f"输入文件: {input_path}")
    if not validate_input_file(input_path):
        return False
    print("✓ 输入文件验证通过")
    
    # 验证配置
    print(f"\n配置信息:")
    print(f"  源码路径: {config.src_path}")
    print(f"  遗留源码路径: {config.legacy_src_path}")
    print(f"  知识库路径: {config.knowledge_base_path}")
    print(f"  输出路径: {config.output_path}")
    print(f"  启用AST解析: {config.enable_ast_parsing}")
    print(f"  启用文本回退: {config.enable_text_fallback}")
    print(f"  最大上下文行数: {config.max_context_lines}")
    print(f"  超时时间: {config.timeout_seconds}秒")
    
    # 验证路径
    warnings = validate_paths(config)
    if warnings:
        print(f"\n路径警告:")
        for warning in warnings:
            print(f"  ⚠ {warning}")
    else:
        print("✓ 路径配置验证通过")
    
    # 验证输出目录
    if not create_output_directory(config.output_path):
        return False
    print("✓ 输出目录验证通过")
    
    print("\n✓ 试运行验证完成，配置有效")
    return True


def process_error_items(error_items: List[ErrorItem], config: ExtractorConfig, logger: logging.Logger) -> List[MethodAnalysisResult]:
    """
    处理错误项列表，提取方法上下文信息
    
    Args:
        error_items: 错误项列表
        config: 配置对象
        logger: 日志器
        
    Returns:
        方法分析结果列表
    """
    from .file_locator import FileLocator
    from .legacy_code_searcher import LegacyCodeSearcher
    from .context_analyzer import ContextIntegrator
    from .ast_parser import JavaASTParser as ASTParser
    
    # 初始化组件
    file_locator = FileLocator(config)
    legacy_searcher = LegacyCodeSearcher(config)
    context_integrator = ContextIntegrator(config.knowledge_base_path)
    ast_parser = ASTParser()
    
    results = []
    progress_reporter = logging_manager.get_progress_reporter("processing")
    progress_reporter.start(len(error_items), "错误项处理")
    
    for i, error_item in enumerate(error_items):
        try:
            logger.info(f"处理错误项 {i+1}/{len(error_items)}: {error_item.get_method_display_name()}")
            
            # 步骤1: 在当前项目中定位错误文件
            current_file_path = file_locator.find_source_file(
                error_item.location.file, 
                [config.src_path]
            )
            
            current_context = None
            if current_file_path:
                logger.debug(f"找到当前项目文件: {current_file_path}")
                file_content = file_locator.read_file_content(current_file_path)
                current_context = file_locator.get_line_content(
                    current_file_path, 
                    error_item.location.line, 
                    config.max_context_lines
                )
            else:
                logger.warning(f"未找到当前项目文件: {error_item.location.file}")
            
            # 步骤2: 在遗留代码中搜索原始方法实现
            original_method = legacy_searcher.search_original_method(error_item)
            
            if not original_method:
                logger.warning(f"未找到原始方法实现: {error_item.get_method_display_name()}")
                # 创建失败的分析结果
                result = MethodAnalysisResult(
                    missing_method=error_item.missing_method,
                    in_param=error_item.in_param,
                    out_return=error_item.out_return,
                    context=error_item.context,
                    content="",
                    notes="未找到原始方法实现",
                    analysis_status="failed",
                    error_message="Method not found in legacy code",
                    original_error=error_item
                )
                results.append(result)
                progress_reporter.update()
                continue
            
            logger.debug(f"找到原始方法: {original_method.file_path}")
            
            # 步骤3: 使用AST解析提取详细方法信息
            method_info = None
            if config.enable_ast_parsing:
                # 读取完整的文件内容进行AST解析
                try:
                    with open(original_method.file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        full_file_content = f.read()
                except Exception as e:
                    raise ValueError(f"无法读取文件: {original_method.file_path}, 错误: {e}")
                
                # 解析AST
                ast = ast_parser.parse_java_file(full_file_content)
                if not ast:
                    raise ValueError(f"AST解析失败: {original_method.file_path}")
                
                # 首先尝试直接查找缺失的方法
                method_node = ast_parser.find_method_in_ast(
                    ast, 
                    error_item.missing_method.replace("()", ""),  # 移除括号进行匹配
                    error_item.class_name
                )
                
                if not method_node:
                    # 如果直接查找失败，分析调用行来确定实际的方法位置
                    logger.warning(f"在当前类中未找到方法 {error_item.missing_method}，分析方法调用")
                    
                    # 获取调用行的内容来分析方法调用
                    with open(original_method.file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        lines = f.readlines()
                    
                    if original_method.line_number <= len(lines):
                        call_line = lines[original_method.line_number - 1].strip()
                        logger.debug(f"分析调用行 (第{original_method.line_number}行): {call_line}")
                        
                        # 检查是否是对象方法调用 (如 nodeService.getProjectTree())
                        method_name_clean = error_item.missing_method.replace("()", "")
                        if '.' in call_line and method_name_clean in call_line:
                            # 尝试提取服务对象名称
                            import re
                            pattern = rf'(\w+)\.{re.escape(method_name_clean)}\s*\('
                            match = re.search(pattern, call_line)
                            
                            if match:
                                service_object = match.group(1)
                                logger.info(f"检测到对象方法调用: {service_object}.{method_name_clean}")
                                
                                # 尝试在legacy代码中查找对应的服务类
                                service_method = legacy_searcher.search_service_method(
                                    service_object, method_name_clean, original_method.file_path
                                )
                                
                                if service_method:
                                    logger.info(f"找到服务方法: {service_method.file_path}")
                                    # 重新解析服务类的AST
                                    with open(service_method.file_path, 'r', encoding='utf-8', errors='ignore') as f:
                                        service_file_content = f.read()
                                    
                                    service_ast = ast_parser.parse_java_file(service_file_content)
                                    if service_ast:
                                        # 在服务类中查找方法，优先查找带参数的版本
                                        # 从调用行分析参数
                                        import re
                                        param_match = re.search(rf'{re.escape(method_name_clean)}\s*\(([^)]*)\)', call_line)
                                        has_params = param_match and param_match.group(1).strip()
                                        
                                        # 查找所有同名方法
                                        all_methods = ast_parser.find_methods_by_name(service_ast, method_name_clean)
                                        method_node = None
                                        
                                        if all_methods:
                                            # 优先选择带参数的方法（如果调用时有参数）
                                            if has_params:
                                                for method in all_methods:
                                                    method_params = ast_parser._extract_parameters(method)
                                                    if method_params:  # 有参数的方法
                                                        method_node = method
                                                        logger.info(f"选择带参数的重载方法: {method_name_clean}({len(method_params)} 参数)")
                                                        break
                                            
                                            # 如果没找到带参数的，或者调用时无参数，选择第一个
                                            if not method_node:
                                                method_node = all_methods[0]
                                                logger.info(f"选择默认重载方法: {method_name_clean}")
                                        
                                        if method_node:
                                            logger.info(f"在服务类中找到方法: {method_name_clean}")
                                            # 更新文件内容为服务类的内容
                                            full_file_content = service_file_content
                                            # 更新文件路径
                                            original_method.file_path = service_method.file_path
                            
                            # 如果还是没找到，回退到查找包含调用的方法
                            if not method_node:
                                logger.info(f"未找到服务方法，查找包含调用的方法以提供上下文")
                                method_node = ast_parser.find_method_by_line_number(
                                    ast, 
                                    original_method.line_number
                                )
                        else:
                            # 尝试根据行号查找包含该行的方法
                            method_node = ast_parser.find_method_by_line_number(
                                ast, 
                                original_method.line_number
                            )
                
                if not method_node:
                    raise ValueError(f"未在AST中找到方法: {error_item.missing_method}")
                
                method_info = ast_parser.extract_method_info(method_node, full_file_content)
                if not method_info:
                    raise ValueError(f"无法从AST提取方法信息: {error_item.missing_method}")
                
                # 调试打印：查看提取到的方法信息
                logger.info("=" * 60)
                logger.info("调试：提取到的方法信息")
                logger.info("=" * 60)
                logger.info(f"method_info 类型: {type(method_info)}")
                if isinstance(method_info, dict):
                    logger.info(f"method_info 键: {list(method_info.keys())}")
                    logger.info(f"方法名: {method_info.get('name', 'N/A')}")
                    logger.info(f"方法体长度: {len(method_info.get('body', ''))}")
                    logger.info(f"方法体前100字符: {method_info.get('body', '')[:100]}")
                else:
                    logger.info(f"method_info 属性: {dir(method_info)}")
                logger.info("=" * 60)
            else:
                raise ValueError("AST解析被禁用，无法提取方法信息")
            
            if not method_info:
                logger.error(f"无法提取方法信息: {error_item.get_method_display_name()}")
                result = MethodAnalysisResult(
                    missing_method=error_item.missing_method,
                    in_param=error_item.in_param,
                    out_return=error_item.out_return,
                    context=error_item.context,
                    content="",
                    notes="无法提取方法信息",
                    analysis_status="failed",
                    error_message="Failed to extract method information",
                    original_error=error_item
                )
                results.append(result)
                progress_reporter.update()
                continue
            
            # 步骤5: 整合上下文信息
            integrated_context = context_integrator.integrate_context(
                error_item=error_item,
                current_context=current_context,
                original_method=original_method
            )
            
            # 步骤6: 创建分析结果
            # 从实际找到的方法信息中提取参数和返回类型
            actual_in_param = {}
            for param in method_info.get('parameters', []):
                actual_in_param[param['name']] = param['type']
            
            actual_out_return = method_info.get('return_type', 'Object')
            
            # 构建正确的方法签名
            method_name = method_info.get('name', error_item.missing_method.replace('()', ''))
            if actual_in_param:
                param_types = list(actual_in_param.values())
                actual_missing_method = f"{method_name}({', '.join(param_types)})"
            else:
                actual_missing_method = f"{method_name}()"
            
            result = MethodAnalysisResult(
                missing_method=actual_missing_method,
                in_param=actual_in_param,
                out_return=actual_out_return,
                context=integrated_context.business_description,
                content=method_info.get('body', ''),
                notes=integrated_context.migration_notes,
                source_file=original_method.file_path,
                line_number=original_method.line_number,
                analysis_status="success",
                original_error=error_item
            )
            
            results.append(result)
            logger.info(f"成功处理错误项: {error_item.get_method_display_name()}")
            
        except Exception as e:
            logger.error(f"处理错误项失败: {error_item.get_method_display_name()}: {e}", exc_info=True)
            
            # 创建失败的分析结果
            result = MethodAnalysisResult(
                missing_method=error_item.missing_method,
                in_param=error_item.in_param,
                out_return=error_item.out_return,
                context=error_item.context,
                content="",
                notes=f"处理失败: {str(e)}",
                analysis_status="failed",
                error_message=str(e),
                original_error=error_item
            )
            results.append(result)
        
        progress_reporter.update()
    
    progress_reporter.finish()
    return results


def main():
    """主函数"""
    # 解析命令行参数
    args = parse_cli_args()
    
    # 验证输入文件
    if not validate_input_file(args.input):
        sys.exit(1)
    
    # 加载配置
    config = load_configuration(args)
    if not config:
        sys.exit(1)
    
    # 试运行模式
    if args.dry_run:
        if dry_run_mode(config, args.input):
            print("试运行成功")
            sys.exit(0)
        else:
            print("试运行失败")
            sys.exit(1)
    
    # 设置日志系统
    logger = setup_logging(config)
    
    # 记录启动信息
    logging_manager.log_system_info(logger)
    logging_manager.log_config_summary(logger, config)
    
    # 验证路径配置
    warnings = validate_paths(config)
    for warning in warnings:
        logger.warning(warning)
    
    # 创建输出目录
    if not create_output_directory(config.output_path):
        logger.error("无法创建输出目录")
        sys.exit(1)
    
    # 开始处理
    start_time = time.time()
    stats = ProcessingStats()
    from .logging_config import StatisticsReporter
    statistics_reporter = StatisticsReporter(logger)
    statistics_reporter.start_processing()
    
    try:
        logger.info(f"开始处理输入文件: {args.input}")
        
        # 步骤1: 解析JSON输入文件
        with logging_manager.log_execution_time(logger, "JSON文件解析"):
            from .models import JSONInputParser
            parser = JSONInputParser()
            error_items = parser.parse_json_file(args.input, validate=True)
            
            stats.total_errors = len(error_items)
            statistics_reporter.update_stats(total_errors=len(error_items))
            
            logger.info(f"成功解析 {len(error_items)} 个错误项")
            
            # 记录验证结果
            validation_result = parser.get_validation_result()
            if validation_result and validation_result.warnings:
                logger.warning("输入验证警告:")
                for warning in validation_result.warnings:
                    logger.warning(f"  {warning}")
        
        # 步骤2: 处理错误项
        with logging_manager.log_execution_time(logger, "错误项处理"):
            results = process_error_items(error_items, config, logger)
            
            # 更新统计信息
            stats.processed_errors = len(results)
            stats.successful_extractions = sum(1 for r in results if r.is_successful())
            stats.failed_extractions = sum(1 for r in results if r.is_failed())
            
            statistics_reporter.update_stats(
                processed_errors=len(results),
                successful_extractions=stats.successful_extractions,
                failed_extractions=stats.failed_extractions
            )
            
            logger.info(f"处理完成: 成功 {stats.successful_extractions}, 失败 {stats.failed_extractions}")
        
        # 步骤3: 生成分析报告
        with logging_manager.log_execution_time(logger, "报告生成"):
            from .report_generator import MarkdownReportGenerator
            
            report_generator = MarkdownReportGenerator(
                title="源码上下文分析报告",
                include_toc=True,
                include_stats=True
            )
            
            report_content = report_generator.generate_report(results, config.output_path)
            logger.info(f"报告生成完成，输出文件: {config.output_path}")
            logger.info(f"报告包含 {len(results)} 个方法分析结果")
        
        # 完成处理统计
        stats.processing_time = time.time() - start_time
        statistics_reporter.finish_processing()
        statistics_reporter.log_final_statistics()
        
        # 输出处理摘要
        logger.info("=" * 60)
        logger.info("处理完成摘要")
        logger.info("=" * 60)
        logger.info(f"输入文件: {args.input}")
        logger.info(f"输出文件: {config.output_path}")
        logger.info(f"总错误项: {stats.total_errors}")
        logger.info(f"处理成功: {stats.successful_extractions}")
        logger.info(f"处理失败: {stats.failed_extractions}")
        logger.info(f"成功率: {stats.get_success_rate()*100:.1f}%")
        logger.info(f"总耗时: {stats.processing_time:.2f}秒")
        logger.info("=" * 60)
        
        # 根据处理结果设置退出码
        if stats.failed_extractions > 0:
            logger.warning(f"有 {stats.failed_extractions} 个错误项处理失败")
            if stats.successful_extractions == 0:
                logger.error("所有错误项处理失败")
                sys.exit(1)
            else:
                logger.info("部分错误项处理成功，程序正常退出")
        
        logger.info("程序执行完成")
        
    except KeyboardInterrupt:
        logger.info("用户中断处理")
        sys.exit(1)
    except Exception as e:
        logger.error(f"处理过程中发生错误: {e}", exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    main()