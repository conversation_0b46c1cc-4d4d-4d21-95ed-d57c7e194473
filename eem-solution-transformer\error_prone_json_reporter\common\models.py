"""
核心数据模型

定义系统中使用的所有数据结构。
"""

from dataclasses import dataclass, field
from typing import Dict, List, Any, Optional
from dataclasses_json import dataclass_json


@dataclass_json
@dataclass
class ErrorReport:
    """错误报告数据模型"""
    package: str
    class_name: str
    missing_method: str
    in_param: Dict[str, str]
    out_return: str
    line: List[int]
    context: str = ""
    error_type: str = "missing_method"
    
    # 扩展字段用于详细的方法签名信息
    method_signature: str = ""
    parameter_types: Dict[str, str] = field(default_factory=dict)
    return_type_full: str = ""
    modifiers: List[str] = field(default_factory=list)
    annotations: List[str] = field(default_factory=list)
    
    # 位置信息
    file_path: str = ""
    column: int = 0
    
    # 调用上下文
    context_lines: List[str] = field(default_factory=list)
    surrounding_methods: List[str] = field(default_factory=list)
    
    # 元数据
    confidence_score: float = 1.0
    source_tool: str = ""
    timestamp: str = ""
    
    def __post_init__(self):
        """数据验证"""
        if not self.package and self.package != "":
            self.package = "unknown"
        if not self.class_name:
            raise ValueError("Class name cannot be empty")
        if not self.missing_method:
            raise ValueError("Missing method name cannot be empty")
        
        # 确保confidence_score在有效范围内
        if not 0 <= self.confidence_score <= 1:
            self.confidence_score = 1.0
    
    def get_method_signature(self) -> str:
        """获取完整的方法签名"""
        if self.method_signature:
            return self.method_signature
        
        # 构建方法签名
        params = []
        if self.parameter_types:
            params = [f"{name}: {type_}" for name, type_ in self.parameter_types.items()]
        elif self.in_param:
            params = [f"{name}: {type_}" for name, type_ in self.in_param.items()]
        
        params_str = ", ".join(params)
        return_type = self.return_type_full or self.out_return
        
        signature = f"{self.missing_method}({params_str})"
        if return_type and return_type != "unknown":
            signature += f" -> {return_type}"
        
        return signature
    
    def get_full_qualified_name(self) -> str:
        """获取完全限定的方法名"""
        if self.package and self.package != "unknown":
            return f"{self.package}.{self.class_name}.{self.missing_method}"
        else:
            return f"{self.class_name}.{self.missing_method}"
    
    def get_location_info(self) -> Dict[str, Any]:
        """获取位置信息"""
        location = {}
        if self.file_path:
            location["file"] = self.file_path
        if self.line:
            location["line"] = self.line[0] if len(self.line) == 1 else self.line
        if self.column > 0:
            location["column"] = self.column
        return location
    
    def to_api_matching_format(self) -> Dict[str, Any]:
        """转换为第二阶段API匹配需要的格式"""
        return {
            "package": self.package,
            "class": self.class_name,
            "missing_method": self.missing_method,
            "in_param": self.parameter_types or self.in_param,
            "out_return": self.return_type_full or self.out_return,
            "line": self.line,
            "context": self.context,
            "method_signature": self.get_method_signature(),
            "full_qualified_name": self.get_full_qualified_name(),
            "location": self.get_location_info(),
            "confidence": self.confidence_score,
            "source": self.source_tool
        }


@dataclass_json
@dataclass
class MethodInfo:
    """方法信息数据模型"""
    package: str
    class_name: str
    method_name: str
    parameters: List[str]
    return_type: str
    context: str
    file_path: str
    modifiers: List[str] = field(default_factory=list)
    annotations: List[str] = field(default_factory=list)
    
    def get_method_signature(self) -> str:
        """获取方法签名字符串"""
        params_str = ", ".join(self.parameters)
        return f"{self.method_name}({params_str}): {self.return_type}"
    
    def get_full_name(self) -> str:
        """获取完整的方法名称"""
        return f"{self.package}.{self.class_name}.{self.method_name}"


@dataclass_json
@dataclass
class CandidateMethod:
    """候选方法数据模型"""
    method_info: MethodInfo
    similarity_score: float
    class_match: bool
    additional_metrics: Dict[str, float] = field(default_factory=dict)
    
    def __post_init__(self):
        """数据验证"""
        if not 0 <= self.similarity_score <= 1:
            raise ValueError("Similarity score must be between 0 and 1")


@dataclass_json
@dataclass
class MatchResult:
    """匹配结果数据模型"""
    original_error: ErrorReport
    candidates: List[CandidateMethod]
    match_metadata: Dict[str, Any] = field(default_factory=dict)
    
    def get_best_candidate(self) -> Optional[CandidateMethod]:
        """获取最佳候选方法"""
        if not self.candidates:
            return None
        return max(self.candidates, key=lambda c: c.similarity_score)
    
    def get_candidates_above_threshold(self, threshold: float) -> List[CandidateMethod]:
        """获取超过阈值的候选方法"""
        return [c for c in self.candidates if c.similarity_score >= threshold]


@dataclass_json
@dataclass
class Configuration:
    """配置数据模型"""
    # 第一阶段配置
    project_path: str
    error_detector: str = "existing_tool"
    existing_tool_output: str = "JavaAnnotator.xml"
    
    # 第二阶段配置
    src_path: str = ""
    legacy_src_path: str = ""
    knowledge_base_path: str = "knowledge_base"
    use_ai_enhancement: bool = True
    base_init: bool = False  # 向量库是否已经初始化完成
    model_name: str = "microsoft/graphcodebert-base"
    top_k: int = 3
    batch_size: int = 32
    similarity_threshold: float = 0.5
    
    # 输出配置
    errors_json: str = "output/errors.json"
    output_json: str = "output/migration_suggestions.json"
    log_file: str = "logs/migration.log"
    output_dir: str = "output"
    
    # 性能配置
    use_gpu: bool = False
    cache_vectors: bool = True
    vector_cache_dir: str = ""  # 向量缓存目录，为空时使用默认位置
    max_workers: int = 4
    memory_limit: int = 8192
    
    # 日志配置
    log_level: str = "INFO"
    log_format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    console_output: bool = True
    file_output: bool = True
    
    def validate(self) -> None:
        """验证配置参数"""
        if self.error_detector not in ["error_prone", "existing_tool"]:
            raise ValueError("error_detector must be 'error_prone' or 'existing_tool'")
        
        if self.top_k <= 0:
            raise ValueError("top_k must be positive")
        
        if self.batch_size <= 0:
            raise ValueError("batch_size must be positive")
        
        if not 0 <= self.similarity_threshold <= 1:
            raise ValueError("similarity_threshold must be between 0 and 1")
        
        if self.log_level not in ["DEBUG", "INFO", "WARNING", "ERROR"]:
            raise ValueError("log_level must be one of DEBUG, INFO, WARNING, ERROR")


@dataclass_json
@dataclass
class ProcessingStats:
    """处理统计信息数据模型"""
    total_errors: int = 0
    processed_errors: int = 0
    total_methods: int = 0
    processed_methods: int = 0
    successful_matches: int = 0
    failed_matches: int = 0
    processing_time: float = 0.0
    memory_usage: float = 0.0
    
    def get_success_rate(self) -> float:
        """获取成功率"""
        if self.processed_errors == 0:
            return 0.0
        return self.successful_matches / self.processed_errors
    
    def get_processing_rate(self) -> float:
        """获取处理速度（项目/秒）"""
        if self.processing_time == 0:
            return 0.0
        return self.processed_errors / self.processing_time