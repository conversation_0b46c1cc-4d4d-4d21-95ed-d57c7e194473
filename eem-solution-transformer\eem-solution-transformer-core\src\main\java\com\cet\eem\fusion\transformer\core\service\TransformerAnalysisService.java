package com.cet.eem.fusion.transformer.core.service;

import java.util.List;
import java.util.Map;

/**
 * @ClassName : TransformerAnalysisService
 * @Description : 变压器分析service
 * <AUTHOR> jiangzixuan
 * @Date: 2022-03-07 10:50
 */
public interface TransformerAnalysisService {
    /**
     * 设备监测
     *
     * @param id
     * @return
     */
    EquipmentMonitorVo queryEquipmentMonitorInfo(Long id);

    /**
     * 高低压侧电压
     *
     * @param id
     * @return
     * @throws IllegalAccessException
     * @throws InstantiationException
     */
    List<VoltageSideMonitorVo> queryVoltageSideMonitor(Long id,Long projectId) throws IllegalAccessException, InstantiationException;

    /**
     * 历史负载
     *
     * @param id
     * @return
     */
    LoadInfoVo queryLoadInfo(Long id,Long projectId) throws IllegalAccessException, InstantiationException;

    /**
     * 雷达分析
     * @param id
     * @return
     * @throws IllegalAccessException
     * @throws InstantiationException
     */
    RadarChartInfo queryRadarChartInfo(Long id, Long projectId) throws IllegalAccessException, InstantiationException;

    /**
     * 负载率趋势
     * @param param
     * @return
     * @throws IllegalAccessException
     * @throws InstantiationException
     */
    LoadRateVo queryLoadRateTrend(LoadRateParam param,Long projectId) throws IllegalAccessException, InstantiationException;

    /**
     * 获得高压侧，低压侧节点
     * @param id 变压器id
     * @param nodes 拓扑配置解析出来的节点信息
     * @param links 拓扑配置解析出来的链接关系
     * @param isTop 为true是上端，false是下端
     * @return
     */
    List<BaseVo> getTopOrDownNodes(Long id, List<PointNode> nodes, List<LinkNode> links, Boolean isTop);

    /**
     * 计算平均负载率
     * @param id 变压器id
     * @param nodes  拓扑配置解析出来的节点信息，用来获得变压器的上下端进线
     * @param links  拓扑配置解析出来的链接关系，用来获得变压器的上下端进线
     * @param aggregationDataBatch  查询定时记录的物理量的入参，不用传节点
     * @param ratedCapacity  该变压器的容量
     * @return  返回值logtime是时间，value是对应的计算平均负载率
     */
    List<DataLogData> getLoadRate(Long id, List<PointNode> nodes, List<LinkNode> links, QuantityDataBatchSearchVo aggregationDataBatch, Double ratedCapacity);

    /**
     * 批量计算平均负载率
     * @param powerTransformerDtos 变压器信息
     * @param aggregationDataBatch
     * @return
     */
    Map<Long,List<DataLogData>> queryLoadRateBatch(List<PowerTransformerDto> powerTransformerDtos, QuantityDataBatchSearchVo aggregationDataBatch
    , Long projectId);

    /**
     * 计算最佳经济运行负载率
     * @param powerTransformerVo  变压器参数
     * @return 最佳经济负载率
     */
    Double calculateOptimalLoadRate(PowerTransformerDto powerTransformerVo);

    /**
     * 获得实时负载
     * @param id
     * @param nodes
     * @param links
     * @return
     */
    Double calculateLoadRealTime(Long id,List<PointNode> nodes,List<LinkNode> links);

    /**
     * 返回变压器和id和对应的实时负载
     * @param ids 变压器id列表
     * @param nodes
     * @param links
     * @return
     */
    Map<Long,Double> calculateLoadRealTimeBatch(List<Long> ids,List<PointNode> nodes,List<LinkNode> links);
}
