"""
增强上下文构建器

构建包含完整信息的上下文，合并当前上下文、原始代码和JSON元数据。
"""

import logging
from typing import Optional, Dict, Any, List
from dataclasses import dataclass

from error_prone_json_reporter.common.models import Configuration
from error_prone_json_reporter.stage2.error_location_resolver import ErrorLocation, MethodContext
from error_prone_json_reporter.stage2.legacy_code_searcher import OriginalMethod


@dataclass
class CompleteContext:
    """完整上下文信息"""
    current_context: Optional[MethodContext]
    original_method: Optional[OriginalMethod]
    json_metadata: Dict[str, Any]
    error_location: Optional[ErrorLocation]
    combined_description: str
    confidence_score: float
    context_quality: str
    available_information: Dict[str, bool]


@dataclass
class SemanticAnalysis:
    """语义分析结果"""
    business_purpose: str
    functional_description: str
    parameter_meanings: Dict[str, str]
    return_value_meaning: str
    usage_scenarios: List[str]
    business_tags: List[str]
    complexity_assessment: str
    confidence_level: float


@dataclass
class SemanticFeatures:
    """语义特征"""
    primary_function: str
    business_domain: str
    key_concepts: List[str]
    parameter_semantics: Dict[str, str]
    functional_category: str
    usage_patterns: List[str]
    technical_keywords: List[str]
    business_keywords: List[str]


class EnhancedContextBuilder:
    """
    增强上下文构建器
    
    职责：构建包含完整信息的上下文
    输入：当前上下文、原始代码、JSON元数据
    输出：增强的上下文信息
    """
    
    def __init__(self, config: Configuration):
        """
        初始化增强上下文构建器
        
        Args:
            config: 配置对象
        """
        self.logger = logging.getLogger(__name__)
        self.config = config
        
        # 上下文权重配置
        self.context_weights = {
            'original_method': 0.4,      # 原始方法代码权重
            'current_context': 0.3,      # 当前上下文权重
            'json_metadata': 0.2,        # JSON元数据权重
            'error_location': 0.1        # 错误位置权重
        }
        
        # 质量评估阈值
        self.quality_thresholds = {
            'high': 0.8,
            'medium': 0.5,
            'low': 0.2
        }
        
        self.logger.info("增强上下文构建器初始化完成")
    
    def build_complete_context(self, 
                              current_context: Optional[MethodContext],
                              original_method: Optional[OriginalMethod],
                              json_metadata: Dict[str, Any],
                              error_location: Optional[ErrorLocation] = None) -> CompleteContext:
        """
        构建完整上下文
        
        Args:
            current_context: 当前方法上下文
            original_method: 原始方法信息
            json_metadata: JSON元数据
            error_location: 错误位置信息
            
        Returns:
            完整上下文信息
        """
        try:
            self.logger.debug(f"构建完整上下文: {json_metadata.get('class', '')}.{json_metadata.get('missing_method', '')}")
            
            # 评估可用信息
            available_info = self._assess_available_information(
                current_context, original_method, json_metadata, error_location
            )
            
            # 计算置信度分数
            confidence_score = self._calculate_confidence_score(available_info)
            
            # 评估上下文质量
            context_quality = self._assess_context_quality(confidence_score)
            
            # 创建综合描述
            combined_description = self._create_combined_description(
                current_context, original_method, json_metadata, error_location
            )
            
            complete_context = CompleteContext(
                current_context=current_context,
                original_method=original_method,
                json_metadata=json_metadata,
                error_location=error_location,
                combined_description=combined_description,
                confidence_score=confidence_score,
                context_quality=context_quality,
                available_information=available_info
            )
            
            self.logger.debug(f"完整上下文构建完成，质量: {context_quality}, 置信度: {confidence_score:.2f}")
            return complete_context
            
        except Exception as e:
            self.logger.error(f"构建完整上下文失败: {str(e)}")
            
            # 返回基础上下文
            return CompleteContext(
                current_context=current_context,
                original_method=original_method,
                json_metadata=json_metadata,
                error_location=error_location,
                combined_description=self._create_fallback_description(json_metadata),
                confidence_score=0.1,
                context_quality='low',
                available_information={'json_only': True}
            )
    
    def _assess_available_information(self, 
                                    current_context: Optional[MethodContext],
                                    original_method: Optional[OriginalMethod],
                                    json_metadata: Dict[str, Any],
                                    error_location: Optional[ErrorLocation]) -> Dict[str, bool]:
        """评估可用信息"""
        return {
            'has_current_context': current_context is not None,
            'has_original_method': original_method is not None,
            'has_json_metadata': bool(json_metadata),
            'has_error_location': error_location is not None,
            'has_method_body': current_context is not None and bool(current_context.method_body),
            'has_original_code': original_method is not None and bool(original_method.method_code),
            'has_javadoc': original_method is not None and bool(original_method.javadoc),
            'has_location_info': error_location is not None and bool(error_location.surrounding_lines),
            'has_parameters': bool(json_metadata.get('in_param', {})),
            'has_return_type': bool(json_metadata.get('out_return', '')),
            'has_context_description': bool(json_metadata.get('context', ''))
        }
    
    def _calculate_confidence_score(self, available_info: Dict[str, bool]) -> float:
        """计算置信度分数"""
        try:
            score = 0.0
            
            # 基础信息权重
            if available_info.get('has_json_metadata', False):
                score += 0.1
            
            # 当前上下文权重
            if available_info.get('has_current_context', False):
                score += 0.2
                if available_info.get('has_method_body', False):
                    score += 0.1
            
            # 原始方法权重
            if available_info.get('has_original_method', False):
                score += 0.3
                if available_info.get('has_original_code', False):
                    score += 0.1
                if available_info.get('has_javadoc', False):
                    score += 0.1
            
            # 错误位置权重
            if available_info.get('has_error_location', False):
                score += 0.1
                if available_info.get('has_location_info', False):
                    score += 0.05
            
            # 详细信息权重
            if available_info.get('has_parameters', False):
                score += 0.05
            if available_info.get('has_return_type', False):
                score += 0.05
            if available_info.get('has_context_description', False):
                score += 0.05
            
            return min(1.0, score)
            
        except Exception as e:
            self.logger.warning(f"计算置信度分数失败: {str(e)}")
            return 0.1
    
    def _assess_context_quality(self, confidence_score: float) -> str:
        """评估上下文质量"""
        if confidence_score >= self.quality_thresholds['high']:
            return 'high'
        elif confidence_score >= self.quality_thresholds['medium']:
            return 'medium'
        else:
            return 'low'
    
    def _create_combined_description(self, 
                                   current_context: Optional[MethodContext],
                                   original_method: Optional[OriginalMethod],
                                   json_metadata: Dict[str, Any],
                                   error_location: Optional[ErrorLocation]) -> str:
        """创建综合描述"""
        try:
            description_parts = []
            
            # 1. 基础信息部分
            basic_info = self._build_basic_info_section(json_metadata)
            if basic_info:
                description_parts.append(basic_info)
            
            # 2. 错误位置部分
            if error_location:
                location_info = self._build_location_info_section(error_location)
                if location_info:
                    description_parts.append(location_info)
            
            # 3. 当前上下文部分
            if current_context:
                current_info = self._build_current_context_section(current_context)
                if current_info:
                    description_parts.append(current_info)
            
            # 4. 原始方法部分
            if original_method:
                original_info = self._build_original_method_section(original_method)
                if original_info:
                    description_parts.append(original_info)
            
            # 5. 技术分析部分
            technical_info = self._build_technical_analysis_section(
                current_context, original_method, json_metadata
            )
            if technical_info:
                description_parts.append(technical_info)
            
            return '\n\n'.join(description_parts)
            
        except Exception as e:
            self.logger.error(f"创建综合描述失败: {str(e)}")
            return self._create_fallback_description(json_metadata)
    
    def _build_basic_info_section(self, json_metadata: Dict[str, Any]) -> str:
        """构建基础信息部分"""
        try:
            parts = []
            
            # 方法基本信息
            package = json_metadata.get('package', '')
            class_name = json_metadata.get('class', '')
            method_name = json_metadata.get('missing_method', '')
            
            if package and class_name and method_name:
                parts.append(f"缺失方法: {package}.{class_name}.{method_name}")
            
            # 参数信息
            in_params = json_metadata.get('in_param', {})
            if in_params:
                param_strs = [f"{name}: {type_}" for name, type_ in in_params.items()]
                parts.append(f"输入参数: {', '.join(param_strs)}")
            
            # 返回类型
            out_return = json_metadata.get('out_return', '')
            if out_return:
                parts.append(f"返回类型: {out_return}")
            
            # 上下文描述
            context = json_metadata.get('context', '')
            if context:
                parts.append(f"调用上下文: {context}")
            
            return '\n'.join(parts) if parts else ''
            
        except Exception as e:
            self.logger.warning(f"构建基础信息部分失败: {str(e)}")
            return ''
    
    def _build_location_info_section(self, error_location: ErrorLocation) -> str:
        """构建位置信息部分"""
        try:
            parts = []
            
            parts.append("=== 错误位置信息 ===")
            parts.append(f"文件: {error_location.file_path}")
            parts.append(f"行号: {error_location.line_number}")
            
            if error_location.column_number:
                parts.append(f"列号: {error_location.column_number}")
            
            if error_location.surrounding_lines:
                parts.append("周围代码:")
                parts.extend(error_location.surrounding_lines[:10])  # 限制行数
            
            return '\n'.join(parts)
            
        except Exception as e:
            self.logger.warning(f"构建位置信息部分失败: {str(e)}")
            return ''
    
    def _build_current_context_section(self, current_context: MethodContext) -> str:
        """构建当前上下文部分"""
        try:
            parts = []
            
            parts.append("=== 当前项目上下文 ===")
            
            if current_context.method_definition:
                parts.append(f"方法定义: {current_context.method_definition}")
            
            if current_context.calling_context:
                parts.append("调用上下文:")
                parts.extend(current_context.calling_context[:5])  # 限制行数
            
            if current_context.local_variables:
                parts.append(f"局部变量: {', '.join(current_context.local_variables[:10])}")
            
            if current_context.method_calls:
                parts.append(f"方法调用: {', '.join(current_context.method_calls[:10])}")
            
            if current_context.imports:
                parts.append(f"相关导入: {', '.join(current_context.imports[:5])}")
            
            return '\n'.join(parts)
            
        except Exception as e:
            self.logger.warning(f"构建当前上下文部分失败: {str(e)}")
            return ''
    
    def _build_original_method_section(self, original_method: OriginalMethod) -> str:
        """构建原始方法部分"""
        try:
            parts = []
            
            parts.append("=== 原始方法实现 ===")
            parts.append(f"文件: {original_method.file_path}")
            parts.append(f"行号: {original_method.line_number}")
            parts.append(f"方法签名: {original_method.method_signature}")
            
            if original_method.javadoc:
                parts.append("Javadoc:")
                parts.append(original_method.javadoc[:500] + "..." if len(original_method.javadoc) > 500 else original_method.javadoc)
            
            if original_method.annotations:
                parts.append(f"注解: {', '.join(original_method.annotations)}")
            
            if original_method.method_code:
                parts.append("方法代码:")
                # 限制代码长度
                code = original_method.method_code
                if len(code) > 1000:
                    code = code[:1000] + "\n... (代码已截断)"
                parts.append(code)
            
            if original_method.dependencies:
                parts.append(f"依赖导入: {', '.join(original_method.dependencies[:10])}")
            
            return '\n'.join(parts)
            
        except Exception as e:
            self.logger.warning(f"构建原始方法部分失败: {str(e)}")
            return ''
    
    def _build_technical_analysis_section(self, 
                                        current_context: Optional[MethodContext],
                                        original_method: Optional[OriginalMethod],
                                        json_metadata: Dict[str, Any]) -> str:
        """构建技术分析部分"""
        try:
            parts = []
            
            parts.append("=== 技术分析 ===")
            
            # 分析方法复杂度
            complexity = self._analyze_method_complexity(current_context, original_method)
            if complexity:
                parts.append(f"复杂度评估: {complexity}")
            
            # 分析业务领域
            domain = self._analyze_business_domain(json_metadata, original_method)
            if domain:
                parts.append(f"业务领域: {domain}")
            
            # 分析技术特征
            tech_features = self._analyze_technical_features(current_context, original_method)
            if tech_features:
                parts.append(f"技术特征: {', '.join(tech_features)}")
            
            # 分析使用模式
            usage_patterns = self._analyze_usage_patterns(current_context, original_method, json_metadata)
            if usage_patterns:
                parts.append(f"使用模式: {', '.join(usage_patterns)}")
            
            return '\n'.join(parts) if len(parts) > 1 else ''
            
        except Exception as e:
            self.logger.warning(f"构建技术分析部分失败: {str(e)}")
            return ''
    
    def _analyze_method_complexity(self, 
                                 current_context: Optional[MethodContext],
                                 original_method: Optional[OriginalMethod]) -> str:
        """分析方法复杂度"""
        try:
            complexity_indicators = []
            
            # 基于原始方法代码分析
            if original_method and original_method.method_code:
                code = original_method.method_code
                
                # 计算代码行数
                lines = len(code.split('\n'))
                if lines > 50:
                    complexity_indicators.append("长方法")
                elif lines > 20:
                    complexity_indicators.append("中等长度")
                else:
                    complexity_indicators.append("短方法")
                
                # 检查控制结构
                if 'if' in code or 'switch' in code:
                    complexity_indicators.append("条件逻辑")
                if 'for' in code or 'while' in code:
                    complexity_indicators.append("循环逻辑")
                if 'try' in code or 'catch' in code:
                    complexity_indicators.append("异常处理")
            
            # 基于当前上下文分析
            if current_context:
                if current_context.method_calls and len(current_context.method_calls) > 10:
                    complexity_indicators.append("多方法调用")
                if current_context.local_variables and len(current_context.local_variables) > 5:
                    complexity_indicators.append("多局部变量")
            
            return ', '.join(complexity_indicators) if complexity_indicators else '简单'
            
        except Exception as e:
            self.logger.warning(f"分析方法复杂度失败: {str(e)}")
            return ''
    
    def _analyze_business_domain(self, json_metadata: Dict[str, Any], original_method: Optional[OriginalMethod]) -> str:
        """分析业务领域"""
        try:
            domain_keywords = {
                'energy': ['energy', 'power', 'electric', 'consumption', 'meter'],
                'user': ['user', 'account', 'login', 'auth', 'profile'],
                'data': ['data', 'query', 'search', 'filter', 'report'],
                'system': ['system', 'config', 'setting', 'admin', 'manage'],
                'service': ['service', 'api', 'rest', 'http', 'client']
            }
            
            # 从包名、类名、方法名中提取关键词
            text_to_analyze = ' '.join([
                json_metadata.get('package', ''),
                json_metadata.get('class', ''),
                json_metadata.get('missing_method', ''),
                json_metadata.get('context', '')
            ]).lower()
            
            # 从原始方法中提取关键词
            if original_method:
                if original_method.javadoc:
                    text_to_analyze += ' ' + original_method.javadoc.lower()
                text_to_analyze += ' ' + original_method.file_path.lower()
            
            # 匹配业务领域
            matched_domains = []
            for domain, keywords in domain_keywords.items():
                if any(keyword in text_to_analyze for keyword in keywords):
                    matched_domains.append(domain)
            
            return ', '.join(matched_domains) if matched_domains else '通用'
            
        except Exception as e:
            self.logger.warning(f"分析业务领域失败: {str(e)}")
            return ''
    
    def _analyze_technical_features(self, 
                                  current_context: Optional[MethodContext],
                                  original_method: Optional[OriginalMethod]) -> List[str]:
        """分析技术特征"""
        try:
            features = []
            
            # 基于原始方法分析
            if original_method:
                if original_method.annotations:
                    features.extend([f"注解:{ann}" for ann in original_method.annotations[:3]])
                
                if original_method.method_code:
                    code = original_method.method_code.lower()
                    if 'spring' in code or '@autowired' in code:
                        features.append('Spring框架')
                    if 'sql' in code or 'query' in code:
                        features.append('数据库操作')
                    if 'http' in code or 'rest' in code:
                        features.append('HTTP/REST')
                    if 'json' in code or 'xml' in code:
                        features.append('数据序列化')
            
            # 基于当前上下文分析
            if current_context:
                if current_context.imports:
                    import_text = ' '.join(current_context.imports).lower()
                    if 'springframework' in import_text:
                        features.append('Spring框架')
                    if 'jackson' in import_text or 'gson' in import_text:
                        features.append('JSON处理')
                    if 'hibernate' in import_text or 'mybatis' in import_text:
                        features.append('ORM框架')
            
            return features
            
        except Exception as e:
            self.logger.warning(f"分析技术特征失败: {str(e)}")
            return []
    
    def _analyze_usage_patterns(self, 
                              current_context: Optional[MethodContext],
                              original_method: Optional[OriginalMethod],
                              json_metadata: Dict[str, Any]) -> List[str]:
        """分析使用模式"""
        try:
            patterns = []
            
            # 基于方法名分析
            method_name = json_metadata.get('missing_method', '').lower()
            if method_name.startswith('get'):
                patterns.append('查询方法')
            elif method_name.startswith('set') or method_name.startswith('update'):
                patterns.append('更新方法')
            elif method_name.startswith('create') or method_name.startswith('add'):
                patterns.append('创建方法')
            elif method_name.startswith('delete') or method_name.startswith('remove'):
                patterns.append('删除方法')
            elif method_name.startswith('validate') or method_name.startswith('check'):
                patterns.append('验证方法')
            
            # 基于参数分析
            in_params = json_metadata.get('in_param', {})
            if len(in_params) == 0:
                patterns.append('无参方法')
            elif len(in_params) == 1:
                patterns.append('单参方法')
            else:
                patterns.append('多参方法')
            
            # 基于返回类型分析
            out_return = json_metadata.get('out_return', '')
            if 'List' in out_return:
                patterns.append('列表返回')
            elif 'Map' in out_return:
                patterns.append('映射返回')
            elif 'void' in out_return:
                patterns.append('无返回值')
            
            return patterns
            
        except Exception as e:
            self.logger.warning(f"分析使用模式失败: {str(e)}")
            return []
    
    def create_enhanced_description(self, context: CompleteContext) -> str:
        """
        创建增强描述
        
        Args:
            context: 完整上下文
            
        Returns:
            增强的描述文本
        """
        try:
            # 如果已有综合描述，直接返回
            if context.combined_description:
                return context.combined_description
            
            # 否则创建基础描述
            return self._create_fallback_description(context.json_metadata)
            
        except Exception as e:
            self.logger.error(f"创建增强描述失败: {str(e)}")
            return self._create_fallback_description(context.json_metadata)
    
    def _create_fallback_description(self, json_metadata: Dict[str, Any]) -> str:
        """创建回退描述"""
        try:
            parts = []
            
            package = json_metadata.get('package', '')
            class_name = json_metadata.get('class', '')
            method_name = json_metadata.get('missing_method', '')
            
            if package and class_name and method_name:
                parts.append(f"缺失方法: {package}.{class_name}.{method_name}")
            
            context = json_metadata.get('context', '')
            if context:
                parts.append(f"上下文: {context}")
            
            in_params = json_metadata.get('in_param', {})
            if in_params:
                param_strs = [f"{name}:{type_}" for name, type_ in in_params.items()]
                parts.append(f"参数: {', '.join(param_strs)}")
            
            out_return = json_metadata.get('out_return', '')
            if out_return:
                parts.append(f"返回: {out_return}")
            
            return '\n'.join(parts) if parts else '无可用信息'
            
        except Exception as e:
            self.logger.warning(f"创建回退描述失败: {str(e)}")
            return '无可用信息'
    
    def extract_semantic_features(self, context: CompleteContext) -> SemanticFeatures:
        """
        提取语义特征
        
        Args:
            context: 完整上下文
            
        Returns:
            语义特征
        """
        try:
            # 提取主要功能
            primary_function = self._extract_primary_function(context)
            
            # 分析业务领域
            business_domain = self._analyze_business_domain(context.json_metadata, context.original_method)
            
            # 提取关键概念
            key_concepts = self._extract_key_concepts(context)
            
            # 分析参数语义
            parameter_semantics = self._analyze_parameter_semantics(context)
            
            # 确定功能类别
            functional_category = self._determine_functional_category(context)
            
            # 分析使用模式
            usage_patterns = self._analyze_usage_patterns(
                context.current_context, context.original_method, context.json_metadata
            )
            
            # 提取技术关键词
            technical_keywords = self._extract_technical_keywords(context)
            
            # 提取业务关键词
            business_keywords = self._extract_business_keywords(context)
            
            return SemanticFeatures(
                primary_function=primary_function,
                business_domain=business_domain,
                key_concepts=key_concepts,
                parameter_semantics=parameter_semantics,
                functional_category=functional_category,
                usage_patterns=usage_patterns,
                technical_keywords=technical_keywords,
                business_keywords=business_keywords
            )
            
        except Exception as e:
            self.logger.error(f"提取语义特征失败: {str(e)}")
            return self._create_fallback_semantic_features(context.json_metadata)
    
    def _extract_primary_function(self, context: CompleteContext) -> str:
        """提取主要功能"""
        try:
            method_name = context.json_metadata.get('missing_method', '')
            
            # 基于方法名推断功能
            if method_name.lower().startswith('get'):
                return '数据查询'
            elif method_name.lower().startswith(('set', 'update')):
                return '数据更新'
            elif method_name.lower().startswith(('create', 'add')):
                return '数据创建'
            elif method_name.lower().startswith(('delete', 'remove')):
                return '数据删除'
            elif method_name.lower().startswith(('validate', 'check')):
                return '数据验证'
            elif method_name.lower().startswith('calculate'):
                return '数据计算'
            else:
                return '业务处理'
                
        except Exception as e:
            self.logger.warning(f"提取主要功能失败: {str(e)}")
            return '未知功能'
    
    def _extract_key_concepts(self, context: CompleteContext) -> List[str]:
        """提取关键概念"""
        try:
            concepts = []
            
            # 从类名和方法名提取
            class_name = context.json_metadata.get('class', '')
            method_name = context.json_metadata.get('missing_method', '')
            
            # 简单的驼峰命名分割
            for name in [class_name, method_name]:
                if name:
                    # 分割驼峰命名
                    import re
                    words = re.findall(r'[A-Z][a-z]*|[a-z]+', name)
                    concepts.extend([word.lower() for word in words if len(word) > 2])
            
            # 从Javadoc提取
            if context.original_method and context.original_method.javadoc:
                javadoc = context.original_method.javadoc.lower()
                # 提取常见的业务概念词汇
                business_words = ['user', 'data', 'service', 'system', 'config', 'energy', 'power', 'node', 'project']
                for word in business_words:
                    if word in javadoc:
                        concepts.append(word)
            
            return list(set(concepts))[:10]  # 去重并限制数量
            
        except Exception as e:
            self.logger.warning(f"提取关键概念失败: {str(e)}")
            return []
    
    def _analyze_parameter_semantics(self, context: CompleteContext) -> Dict[str, str]:
        """分析参数语义"""
        try:
            semantics = {}
            
            in_params = context.json_metadata.get('in_param', {})
            for param_name, param_type in in_params.items():
                # 基于参数名推断语义
                param_lower = param_name.lower()
                if 'id' in param_lower:
                    semantics[param_name] = '标识符'
                elif 'name' in param_lower:
                    semantics[param_name] = '名称'
                elif 'type' in param_lower:
                    semantics[param_name] = '类型'
                elif 'code' in param_lower:
                    semantics[param_name] = '编码'
                elif 'time' in param_lower or 'date' in param_lower:
                    semantics[param_name] = '时间'
                elif 'status' in param_lower:
                    semantics[param_name] = '状态'
                else:
                    semantics[param_name] = '业务参数'
            
            return semantics
            
        except Exception as e:
            self.logger.warning(f"分析参数语义失败: {str(e)}")
            return {}
    
    def _determine_functional_category(self, context: CompleteContext) -> str:
        """确定功能类别"""
        try:
            method_name = context.json_metadata.get('missing_method', '').lower()
            class_name = context.json_metadata.get('class', '').lower()
            
            # 基于类名判断
            if 'service' in class_name:
                return '业务服务'
            elif 'controller' in class_name:
                return 'Web控制器'
            elif 'dao' in class_name or 'repository' in class_name:
                return '数据访问'
            elif 'util' in class_name or 'helper' in class_name:
                return '工具类'
            elif 'config' in class_name:
                return '配置类'
            
            # 基于方法名判断
            if any(prefix in method_name for prefix in ['get', 'find', 'query', 'search']):
                return '查询操作'
            elif any(prefix in method_name for prefix in ['save', 'update', 'modify']):
                return '更新操作'
            elif any(prefix in method_name for prefix in ['create', 'add', 'insert']):
                return '创建操作'
            elif any(prefix in method_name for prefix in ['delete', 'remove']):
                return '删除操作'
            
            return '业务逻辑'
            
        except Exception as e:
            self.logger.warning(f"确定功能类别失败: {str(e)}")
            return '未知类别'
    
    def _extract_technical_keywords(self, context: CompleteContext) -> List[str]:
        """提取技术关键词"""
        try:
            keywords = []
            
            # 从导入语句提取
            if context.current_context and context.current_context.imports:
                for import_stmt in context.current_context.imports:
                    if 'springframework' in import_stmt:
                        keywords.append('Spring')
                    elif 'jackson' in import_stmt:
                        keywords.append('Jackson')
                    elif 'hibernate' in import_stmt:
                        keywords.append('Hibernate')
                    elif 'mybatis' in import_stmt:
                        keywords.append('MyBatis')
            
            # 从原始方法代码提取
            if context.original_method and context.original_method.method_code:
                code = context.original_method.method_code.lower()
                tech_patterns = ['sql', 'http', 'json', 'xml', 'rest', 'api', 'database', 'cache']
                for pattern in tech_patterns:
                    if pattern in code:
                        keywords.append(pattern.upper())
            
            return list(set(keywords))
            
        except Exception as e:
            self.logger.warning(f"提取技术关键词失败: {str(e)}")
            return []
    
    def _extract_business_keywords(self, context: CompleteContext) -> List[str]:
        """提取业务关键词"""
        try:
            keywords = []
            
            # 从包名提取业务领域
            package = context.json_metadata.get('package', '')
            if package:
                package_parts = package.split('.')
                business_parts = [part for part in package_parts if part not in ['com', 'org', 'cn', 'java', 'javax']]
                keywords.extend(business_parts)
            
            # 从上下文描述提取
            context_desc = context.json_metadata.get('context', '')
            if context_desc:
                business_words = ['energy', 'power', 'user', 'project', 'node', 'data', 'system', 'service']
                for word in business_words:
                    if word in context_desc.lower():
                        keywords.append(word)
            
            return list(set(keywords))[:10]
            
        except Exception as e:
            self.logger.warning(f"提取业务关键词失败: {str(e)}")
            return []
    
    def _create_fallback_semantic_features(self, json_metadata: Dict[str, Any]) -> SemanticFeatures:
        """创建回退语义特征"""
        return SemanticFeatures(
            primary_function='未知功能',
            business_domain='通用',
            key_concepts=[],
            parameter_semantics={},
            functional_category='业务逻辑',
            usage_patterns=[],
            technical_keywords=[],
            business_keywords=[]
        )
    
    def get_context_summary(self, context: CompleteContext) -> Dict[str, Any]:
        """获取上下文摘要"""
        return {
            'confidence_score': context.confidence_score,
            'context_quality': context.context_quality,
            'available_information': context.available_information,
            'has_original_method': context.original_method is not None,
            'has_current_context': context.current_context is not None,
            'has_error_location': context.error_location is not None,
            'description_length': len(context.combined_description)
        }