package com.cet.eem.fusion.transformer.core.controller;

import com.cet.eem.fusion.transformer.core.entity.dto.EquipmentCondition;
import com.cet.eem.fusion.transformer.core.entity.dto.EquipmentForm;
import com.cet.eem.fusion.transformer.core.entity.vo.OverviewDataVo;
import com.cet.eem.fusion.transformer.core.service.TransformerOverviewService;
import com.cet.eem.solution.common.def.common.PluginInfoDef;
import com.cet.eem.fusion.config.sdk.utils.GlobalInfoUtils;
import com.cet.electric.commons.ApiResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> yangy
 * @ClassName : TransformerOverviewController
 * @Date: 2022-03-15 15:26
 */
@Validated
@RestController
@Api(value = PluginInfoDef.Transformer.INTERFACE_PREFIX + "/v1/overview", tags = "变压器总览接口")
@RequestMapping(value = PluginInfoDef.Transformer.PLUGIN_NAME_PREFIX + "/v1/overview")
public class TransformerOverviewController {

    @Resource
    private TransformerOverviewService transformerOverviewService;

    @GetMapping("/standingBook")
    @ApiOperation("总览台账信息")
    public ApiResult<OverviewDataVo> getOverviewData() {
        return Result.ok(transformerOverviewService.aboveData(GlobalInfoUtils.getTenantId()));
    }

    @GetMapping("/standingBookForDayEvent")
    @ApiOperation("总览台账信息-日事件")
    public ApiResult<OverviewDataVo> getOverviewDataForDayEvent() {
        return Result.ok(transformerOverviewService.aboveDataForDayEvent(GlobalInfoUtils.getTenantId()));
    }

    @GetMapping("/standingBookForMonthEvent")
    @ApiOperation("总览台账信息-月时间")
    public ApiResult<OverviewDataVo> getOverviewDataForMonthEvent() {
        return Result.ok(transformerOverviewService.aboveDataForMonthEvent(GlobalInfoUtils.getTenantId()));
    }

    @GetMapping("/standingBookForStatus")
    @ApiOperation("总览台账信息-状态统计")
    public ApiResult<OverviewDataVo> getOverviewDataForStatus() {
        return Result.ok(transformerOverviewService.aboveDataForStatus(GlobalInfoUtils.getTenantId()));
    }

    @PostMapping("/equipmentStatus")
    @ApiOperation("总览设备状态监测")
    public ApiResult<List<EquipmentCondition>> equipmentStatus(@RequestBody EquipmentForm form) {
        return Result.ok(transformerOverviewService.equipmentStatus(form, GlobalInfoUtils.getTenantId()));
    }
}
