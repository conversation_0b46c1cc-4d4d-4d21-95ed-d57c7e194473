"""
智能匹配评分系统

集成参数兼容性分析，实现多维度匹配评分算法，专注于业务参数匹配，
智能处理框架参数差异，提升API匹配准确性。
"""

import logging
import math
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, field
from enum import Enum

from error_prone_json_reporter.common.models import MethodInfo, ErrorReport
from error_prone_json_reporter.stage2.parameter_compatibility_analyzer import (
    ParameterCompatibilityAnalyzer, CompatibilityAnalysisResult, ParameterChangeType
)


class MatchingDimension(Enum):
    """匹配维度"""
    SEMANTIC_SIMILARITY = "semantic_similarity"      # 语义相似度
    PARAMETER_COMPATIBILITY = "parameter_compatibility"  # 参数兼容性
    CONTEXT_RELEVANCE = "context_relevance"         # 上下文相关性


@dataclass
class MatchingWeights:
    """匹配权重配置"""
    semantic_weight: float = 0.75     # 语义相似度权重（75%）
    business_domain_weight: float = 0.10  # 业务领域匹配权重（10%）
    method_name_weight: float = 0.10  # 方法名相似度权重（10%）
    other_factors_weight: float = 0.05  # 其他因素权重（5%）
    
    def __post_init__(self):
        """验证权重总和为1.0"""
        total = self.semantic_weight + self.business_domain_weight + self.method_name_weight + self.other_factors_weight
        if abs(total - 1.0) > 0.01:
            raise ValueError(f"权重总和必须为1.0，当前为{total}")


@dataclass
class DimensionScore:
    """维度评分"""
    dimension: MatchingDimension
    score: float
    confidence: float
    details: Dict[str, Any] = field(default_factory=dict)
    explanation: str = ""


@dataclass
class MatchingReport:
    """匹配报告"""
    overall_score: float                    # 总体评分
    dimension_scores: List[DimensionScore]  # 各维度评分
    parameter_analysis: Optional[CompatibilityAnalysisResult]  # 参数兼容性分析
    migration_suggestions: List[str]        # 迁移建议
    migration_complexity: str               # 迁移复杂度
    framework_tolerance_applied: bool       # 是否应用了框架参数容忍
    business_focus_score: float            # 业务逻辑匹配评分
    explanation: str                       # 匹配解释
    confidence: float                      # 整体置信度


class IntelligentMatchingScorer:
    """智能匹配评分系统"""
    
    def __init__(self, weights: Optional[MatchingWeights] = None):
        """
        初始化智能匹配评分系统
        
        Args:
            weights: 匹配权重配置，默认使用标准权重
        """
        self.logger = logging.getLogger(__name__)
        self.weights = weights or MatchingWeights()
        self.parameter_analyzer = ParameterCompatibilityAnalyzer()
        
        # 框架参数容忍阈值
        self.framework_tolerance_threshold = 0.8
        
        # 业务参数匹配最低要求
        self.business_match_threshold = 0.3
        
        self.logger.info(f"初始化智能匹配评分系统，权重配置: "
                        f"语义{self.weights.semantic_weight}, "
                        f"业务领域{self.weights.business_domain_weight}, "
                        f"方法名{self.weights.method_name_weight}, "
                        f"其他因素{self.weights.other_factors_weight}")
    
    def score_match(self, missing_method: ErrorReport, candidate_method: MethodInfo,
                   semantic_similarity: float, context_info: Optional[Dict[str, Any]] = None) -> MatchingReport:
        """
        对候选方法进行智能评分
        
        Args:
            missing_method: 缺失的方法信息
            candidate_method: 候选方法信息
            semantic_similarity: 语义相似度（来自向量匹配）
            context_info: 上下文信息（可选）
            
        Returns:
            匹配报告
        """
        self.logger.debug(f"评分匹配: {missing_method.missing_method} -> {candidate_method.method_name}")
        
        # 1. 语义相似度评分
        semantic_score = self._score_semantic_similarity(semantic_similarity)
        
        # 2. 业务领域匹配评分
        business_domain_score = self._score_business_domain_match(
            missing_method, candidate_method
        )
        
        # 3. 方法名相似度评分
        method_name_score = self._score_method_name_similarity(
            missing_method, candidate_method
        )
        
        # 4. 其他因素评分（参数兼容性等）
        other_factors_score, parameter_analysis = self._score_other_factors(
            missing_method, candidate_method, context_info
        )
        
        # 5. 计算总体评分
        overall_score = self._calculate_overall_score(
            semantic_score, business_domain_score, method_name_score, other_factors_score
        )
        
        # 5. 应用框架参数容忍机制
        framework_tolerance_applied = self._apply_framework_tolerance(
            parameter_analysis, overall_score
        )
        
        # 6. 计算业务逻辑匹配评分
        business_focus_score = self._calculate_business_focus_score(
            semantic_score, parameter_analysis
        )
        
        # 7. 生成迁移建议
        migration_suggestions = self._generate_migration_suggestions(
            missing_method, candidate_method, parameter_analysis
        )
        
        # 8. 评估迁移复杂度
        migration_complexity = self._assess_migration_complexity(parameter_analysis)
        
        # 9. 生成解释
        explanation = self._generate_explanation(
            missing_method, candidate_method, semantic_score, business_domain_score,
            method_name_score, other_factors_score, parameter_analysis
        )
        
        # 10. 计算整体置信度
        confidence = self._calculate_confidence(
            semantic_score, business_domain_score, method_name_score, 
            other_factors_score, parameter_analysis
        )
        
        return MatchingReport(
            overall_score=overall_score.score,
            dimension_scores=[semantic_score, business_domain_score, method_name_score, other_factors_score],
            parameter_analysis=parameter_analysis,
            migration_suggestions=migration_suggestions,
            migration_complexity=migration_complexity,
            framework_tolerance_applied=framework_tolerance_applied,
            business_focus_score=business_focus_score,
            explanation=explanation,
            confidence=confidence
        )
    
    def _score_semantic_similarity(self, similarity: float) -> DimensionScore:
        """评分语义相似度"""
        # 语义相似度直接使用向量匹配的结果，但进行一些调整
        adjusted_score = similarity
        
        # 对高相似度进行奖励
        if similarity > 0.8:
            adjusted_score = min(1.0, similarity * 1.1)
        
        # 对低相似度进行惩罚
        elif similarity < 0.3:
            adjusted_score = similarity * 0.8
        
        confidence = similarity  # 置信度等于原始相似度
        
        details = {
            "original_similarity": similarity,
            "adjusted_score": adjusted_score,
            "high_similarity_bonus": similarity > 0.8,
            "low_similarity_penalty": similarity < 0.3
        }
        
        explanation = f"语义相似度: {similarity:.3f}"
        if adjusted_score != similarity:
            explanation += f" (调整后: {adjusted_score:.3f})"
        
        return DimensionScore(
            dimension=MatchingDimension.SEMANTIC_SIMILARITY,
            score=adjusted_score,
            confidence=confidence,
            details=details,
            explanation=explanation
        )
    
    def _score_business_domain_match(self, missing_method: ErrorReport, 
                                   candidate_method: MethodInfo) -> DimensionScore:
        """评分业务领域匹配"""
        # 基于包名、类名和方法名分析业务领域匹配度
        domain_score = 0.0
        confidence = 0.7
        details = {}
        
        # 1. 包名业务领域匹配（权重40%）
        package_match = self._analyze_business_domain_from_package(
            missing_method.package, candidate_method.package
        )
        domain_score += package_match * 0.4
        details["package_domain_match"] = package_match
        
        # 2. 类名业务领域匹配（权重35%）
        class_match = self._analyze_business_domain_from_class(
            missing_method.class_name, candidate_method.class_name
        )
        domain_score += class_match * 0.35
        details["class_domain_match"] = class_match
        
        # 3. 方法名业务领域匹配（权重25%）
        method_match = self._analyze_business_domain_from_method(
            missing_method.missing_method, candidate_method.method_name
        )
        domain_score += method_match * 0.25
        details["method_domain_match"] = method_match
        
        explanation = f"业务领域匹配: 包名{package_match:.2f}, 类名{class_match:.2f}, 方法名{method_match:.2f}"
        
        return DimensionScore(
            dimension=MatchingDimension.CONTEXT_RELEVANCE,
            score=domain_score,
            confidence=confidence,
            details=details,
            explanation=explanation
        )
    
    def _score_method_name_similarity(self, missing_method: ErrorReport,
                                    candidate_method: MethodInfo) -> DimensionScore:
        """评分方法名相似度"""
        similarity = self._calculate_method_name_similarity(
            missing_method.missing_method, candidate_method.method_name
        )
        
        # 对高相似度进行奖励
        adjusted_score = similarity
        if similarity > 0.8:
            adjusted_score = min(1.0, similarity * 1.1)
        elif similarity < 0.2:
            adjusted_score = similarity * 0.8
        
        confidence = 0.9  # 方法名相似度计算置信度较高
        
        details = {
            "original_similarity": similarity,
            "adjusted_score": adjusted_score,
            "method1": missing_method.missing_method,
            "method2": candidate_method.method_name
        }
        
        explanation = f"方法名相似度: {similarity:.3f}"
        if adjusted_score != similarity:
            explanation += f" (调整后: {adjusted_score:.3f})"
        
        return DimensionScore(
            dimension=MatchingDimension.CONTEXT_RELEVANCE,
            score=adjusted_score,
            confidence=confidence,
            details=details,
            explanation=explanation
        )
    
    def _score_other_factors(self, missing_method: ErrorReport, 
                           candidate_method: MethodInfo, 
                           context_info: Optional[Dict[str, Any]] = None) -> Tuple[DimensionScore, CompatibilityAnalysisResult]:
        """评分其他因素（参数兼容性、返回类型匹配等）"""
        # 转换ErrorReport为MethodInfo格式进行分析
        missing_method_info = self._convert_error_to_method_info(missing_method)
        
        # 执行参数兼容性分析
        analysis = self.parameter_analyzer.analyze_compatibility(
            missing_method_info, candidate_method
        )
        
        # 计算参数兼容性评分，重点关注业务参数
        business_compatibility = analysis.business_compatibility
        framework_compatibility = analysis.framework_compatibility
        
        # 业务参数兼容性权重更高（80%），框架参数兼容性权重较低（20%）
        base_score = business_compatibility * 0.8 + framework_compatibility * 0.2
        
        # 应用框架参数容忍机制：如果主要是框架参数差异，不大幅降低评分
        framework_params_ratio = len(analysis.framework_params_added) / max(1, len(analysis.parameter_mappings))
        if framework_params_ratio > 0.5:  # 超过50%是框架参数变化
            # 提升评分，因为框架参数差异不影响核心功能
            base_score = min(1.0, base_score * 1.2)
        
        # DTO封装模式的特殊处理
        has_dto_encapsulation = any(
            mapping.change_type == ParameterChangeType.ENCAPSULATED 
            for mapping in analysis.parameter_mappings
        )
        if has_dto_encapsulation:
            # DTO封装是常见的框架升级模式，给予合理评分
            base_score = max(base_score, 0.6)
        
        # 计算置信度
        confidence = self._calculate_parameter_confidence(analysis)
        
        details = {
            "business_compatibility": business_compatibility,
            "framework_compatibility": framework_compatibility,
            "framework_params_added": len(analysis.framework_params_added),
            "business_params_changed": len(analysis.business_params_changed),
            "has_dto_encapsulation": has_dto_encapsulation,
            "framework_tolerance_applied": framework_params_ratio > 0.5,
            "migration_complexity": analysis.migration_complexity
        }
        
        # 添加返回类型匹配评分（权重50%）
        return_type_match = self._calculate_return_type_compatibility(
            missing_method, candidate_method
        )
        final_score = base_score * 0.5 + return_type_match * 0.5
        
        details["return_type_match"] = return_type_match
        
        explanation = f"其他因素: 参数兼容性{base_score:.2f}, 返回类型匹配{return_type_match:.2f}"
        if framework_params_ratio > 0.5:
            explanation += " (应用框架参数容忍)"
        
        return DimensionScore(
            dimension=MatchingDimension.PARAMETER_COMPATIBILITY,
            score=final_score,
            confidence=confidence,
            details=details,
            explanation=explanation
        ), analysis
    

    
    def _calculate_overall_score(self, semantic_score: DimensionScore, 
                               business_domain_score: DimensionScore,
                               method_name_score: DimensionScore,
                               other_factors_score: DimensionScore) -> DimensionScore:
        """计算总体评分"""
        weighted_score = (
            semantic_score.score * self.weights.semantic_weight +
            business_domain_score.score * self.weights.business_domain_weight +
            method_name_score.score * self.weights.method_name_weight +
            other_factors_score.score * self.weights.other_factors_weight
        )
        
        # 计算加权置信度
        weighted_confidence = (
            semantic_score.confidence * self.weights.semantic_weight +
            business_domain_score.confidence * self.weights.business_domain_weight +
            method_name_score.confidence * self.weights.method_name_weight +
            other_factors_score.confidence * self.weights.other_factors_weight
        )
        
        details = {
            "semantic_contribution": semantic_score.score * self.weights.semantic_weight,
            "business_domain_contribution": business_domain_score.score * self.weights.business_domain_weight,
            "method_name_contribution": method_name_score.score * self.weights.method_name_weight,
            "other_factors_contribution": other_factors_score.score * self.weights.other_factors_weight,
            "weights_used": {
                "semantic": self.weights.semantic_weight,
                "business_domain": self.weights.business_domain_weight,
                "method_name": self.weights.method_name_weight,
                "other_factors": self.weights.other_factors_weight
            }
        }
        
        explanation = f"总体评分: {weighted_score:.3f} (语义{semantic_score.score:.2f}×{self.weights.semantic_weight}, 业务领域{business_domain_score.score:.2f}×{self.weights.business_domain_weight}, 方法名{method_name_score.score:.2f}×{self.weights.method_name_weight}, 其他{other_factors_score.score:.2f}×{self.weights.other_factors_weight})"
        
        return DimensionScore(
            dimension=MatchingDimension.SEMANTIC_SIMILARITY,  # 使用语义作为总体维度
            score=weighted_score,
            confidence=weighted_confidence,
            details=details,
            explanation=explanation
        )
    
    def _apply_framework_tolerance(self, parameter_analysis: CompatibilityAnalysisResult,
                                 overall_score: DimensionScore) -> bool:
        """应用框架参数容忍机制"""
        if not parameter_analysis:
            return False
        
        # 检查是否主要是框架参数变化
        total_mappings = len(parameter_analysis.parameter_mappings)
        framework_changes = len(parameter_analysis.framework_params_added)
        
        if total_mappings == 0:
            return False
        
        # 计算框架参数在新增参数中的比例
        new_params_count = sum(1 for mapping in parameter_analysis.parameter_mappings 
                              if not mapping.old_param)  # 新增的参数
        
        if new_params_count == 0:
            return False
        
        framework_ratio = framework_changes / new_params_count
        
        # 如果新增参数中框架参数比例超过50%，或者框架参数数量>=2，应用容忍机制
        if framework_ratio >= 0.5 or framework_changes >= 2:
            self.logger.debug(f"应用框架参数容忍机制，框架参数变化: {framework_changes}, 新增参数: {new_params_count}, 比例: {framework_ratio:.2f}")
            return True
        
        return False
    
    def _calculate_business_focus_score(self, semantic_score: DimensionScore,
                                      parameter_analysis: CompatibilityAnalysisResult) -> float:
        """计算业务逻辑匹配评分"""
        # 基础评分来自语义相似度
        base_score = semantic_score.score
        
        # 业务参数兼容性加权
        if parameter_analysis:
            business_compatibility = parameter_analysis.business_compatibility
            # 业务参数兼容性权重为40%，语义相似度权重为60%
            base_score = base_score * 0.6 + business_compatibility * 0.4
        
        return base_score
    
    def _generate_migration_suggestions(self, missing_method: ErrorReport, 
                                      candidate_method: MethodInfo,
                                      parameter_analysis: CompatibilityAnalysisResult) -> List[str]:
        """生成迁移建议"""
        suggestions = []
        
        # 基本迁移建议
        basic_suggestion = f"将方法调用从 {missing_method.missing_method} 更改为 {candidate_method.method_name}"
        suggestions.append(basic_suggestion)
        
        if parameter_analysis:
            # 添加参数分析的建议，但避免重复
            for suggestion in parameter_analysis.migration_suggestions:
                if suggestion not in suggestions:
                    suggestions.append(suggestion)
            
            # 框架参数特殊建议
            if parameter_analysis.framework_params_added:
                suggestions.append("框架参数获取建议:")
                for param in parameter_analysis.framework_params_added:
                    param_lower = param.lower()
                    if param_lower == 'tenantid':
                        suggestions.append("  - tenantId: 从 SecurityContextHolder 或请求上下文获取")
                    elif param_lower == 'userid':
                        suggestions.append("  - userId: 从当前用户会话或认证信息获取")
                    elif param_lower == 'projectid':
                        suggestions.append("  - projectId: 从业务上下文或请求参数获取")
                    elif param_lower == 'sessionid':
                        suggestions.append("  - sessionId: 从当前会话获取")
                    else:
                        suggestions.append(f"  - {param}: 从相应的上下文或配置获取")
            
            # DTO封装建议
            has_dto_encapsulation = any(
                mapping.change_type == ParameterChangeType.ENCAPSULATED 
                for mapping in parameter_analysis.parameter_mappings
            )
            if has_dto_encapsulation:
                suggestions.append("参数封装建议:")
                suggestions.append("  - 创建相应的DTO对象，将原有参数封装到DTO的字段中")
                suggestions.append("  - 注意保持参数的语义对应关系")
        
        return suggestions
    
    def _assess_migration_complexity(self, parameter_analysis: CompatibilityAnalysisResult) -> str:
        """评估迁移复杂度"""
        if not parameter_analysis:
            return "中等"
        
        # 基于参数分析结果评估复杂度
        complexity = parameter_analysis.migration_complexity
        
        # 考虑框架参数的影响
        framework_params_count = len(parameter_analysis.framework_params_added)
        business_params_changed = len(parameter_analysis.business_params_changed)
        
        # 如果主要是框架参数变化，复杂度不会太高
        if framework_params_count > business_params_changed:
            if complexity == "复杂":
                complexity = "中等"
            elif complexity == "中等" and framework_params_count >= 3:
                complexity = "中等"  # 保持中等
        
        return complexity
    
    def _generate_explanation(self, missing_method: ErrorReport, candidate_method: MethodInfo,
                            semantic_score: DimensionScore, business_domain_score: DimensionScore,
                            method_name_score: DimensionScore, other_factors_score: DimensionScore,
                            parameter_analysis: CompatibilityAnalysisResult) -> str:
        """生成匹配解释"""
        explanation_parts = []
        
        # 总体匹配情况
        explanation_parts.append(f"候选方法 {candidate_method.method_name} 与缺失方法 {missing_method.missing_method} 的匹配分析:")
        
        # 语义匹配
        explanation_parts.append(f"• 语义相似度: {semantic_score.score:.3f} - {self._get_score_description(semantic_score.score)}")
        
        # 业务领域匹配
        explanation_parts.append(f"• 业务领域匹配: {business_domain_score.score:.3f} - {self._get_score_description(business_domain_score.score)}")
        
        # 方法名匹配
        explanation_parts.append(f"• 方法名相似度: {method_name_score.score:.3f} - {self._get_score_description(method_name_score.score)}")
        
        # 其他因素匹配
        explanation_parts.append(f"• 其他因素: {other_factors_score.score:.3f} - {self._get_score_description(other_factors_score.score)}")
        if parameter_analysis:
            if parameter_analysis.framework_params_added:
                explanation_parts.append(f"  - 新增框架参数: {', '.join(parameter_analysis.framework_params_added)}")
            if parameter_analysis.business_params_changed:
                explanation_parts.append(f"  - 业务参数变化: {len(parameter_analysis.business_params_changed)}个")
        
        # 迁移复杂度
        if parameter_analysis:
            explanation_parts.append(f"• 迁移复杂度: {parameter_analysis.migration_complexity}")
        
        return "\n".join(explanation_parts)
    
    def _calculate_confidence(self, semantic_score: DimensionScore, business_domain_score: DimensionScore,
                            method_name_score: DimensionScore, other_factors_score: DimensionScore,
                            parameter_analysis: CompatibilityAnalysisResult) -> float:
        """计算整体置信度"""
        # 基础置信度来自各维度的加权平均
        base_confidence = (
            semantic_score.confidence * self.weights.semantic_weight +
            business_domain_score.confidence * self.weights.business_domain_weight +
            method_name_score.confidence * self.weights.method_name_weight +
            other_factors_score.confidence * self.weights.other_factors_weight
        )
        
        # 参数分析的置信度调整
        if parameter_analysis:
            # 如果业务参数兼容性很高，提升置信度
            if parameter_analysis.business_compatibility > 0.8:
                base_confidence = min(1.0, base_confidence * 1.1)
            
            # 如果主要是框架参数差异，置信度不应该太低
            framework_ratio = len(parameter_analysis.framework_params_added) / max(1, len(parameter_analysis.parameter_mappings))
            if framework_ratio > 0.6:
                base_confidence = max(base_confidence, 0.6)
        
        return base_confidence
    
    def _convert_error_to_method_info(self, error_report: ErrorReport) -> MethodInfo:
        """将ErrorReport转换为MethodInfo格式"""
        # 构建参数列表
        parameters = []
        if error_report.parameter_types:
            parameters = [f"{name}: {type_}" for name, type_ in error_report.parameter_types.items()]
        elif error_report.in_param:
            parameters = [f"{name}: {type_}" for name, type_ in error_report.in_param.items()]
        
        return MethodInfo(
            package=error_report.package,
            class_name=error_report.class_name,
            method_name=error_report.missing_method,
            parameters=parameters,
            return_type=error_report.return_type_full or error_report.out_return,
            context=error_report.context,
            file_path=error_report.file_path
        )
    
    def _calculate_parameter_confidence(self, analysis: CompatibilityAnalysisResult) -> float:
        """计算参数兼容性分析的置信度"""
        if not analysis.parameter_mappings:
            return 0.5
        
        # 基于映射置信度的平均值
        mapping_confidences = [mapping.confidence for mapping in analysis.parameter_mappings]
        avg_confidence = sum(mapping_confidences) / len(mapping_confidences)
        
        # 如果业务参数兼容性高，提升置信度
        if analysis.business_compatibility > 0.8:
            avg_confidence = min(1.0, avg_confidence * 1.2)
        
        return avg_confidence
    
    def _calculate_class_name_similarity(self, class1: str, class2: str) -> float:
        """计算类名相似度"""
        if not class1 or not class2:
            return 0.0
        
        if class1 == class2:
            return 1.0
        
        # 转换为小写进行比较
        class1_lower = class1.lower()
        class2_lower = class2.lower()
        
        if class1_lower == class2_lower:
            return 0.9
        
        # 检查包含关系
        if class1_lower in class2_lower or class2_lower in class1_lower:
            return 0.7
        
        # 检查共同词根
        class1_words = self._extract_words_from_camel_case(class1)
        class2_words = self._extract_words_from_camel_case(class2)
        
        common_words = set(class1_words) & set(class2_words)
        total_words = set(class1_words) | set(class2_words)
        
        if total_words:
            return len(common_words) / len(total_words)
        
        return 0.0
    
    def _calculate_package_similarity(self, package1: str, package2: str) -> float:
        """计算包名相似度"""
        if not package1 or not package2:
            return 0.0
        
        if package1 == package2:
            return 1.0
        
        # 分割包名
        parts1 = package1.split('.')
        parts2 = package2.split('.')
        
        # 计算共同前缀长度
        common_prefix = 0
        for p1, p2 in zip(parts1, parts2):
            if p1 == p2:
                common_prefix += 1
            else:
                break
        
        # 相似度基于共同前缀比例
        max_parts = max(len(parts1), len(parts2))
        return common_prefix / max_parts if max_parts > 0 else 0.0
    
    def _calculate_method_name_similarity(self, method1: str, method2: str) -> float:
        """计算方法名相似度"""
        if not method1 or not method2:
            return 0.0
        
        if method1 == method2:
            return 1.0
        
        # 转换为小写进行比较
        method1_lower = method1.lower()
        method2_lower = method2.lower()
        
        if method1_lower == method2_lower:
            return 0.9
        
        # 检查包含关系
        if method1_lower in method2_lower or method2_lower in method1_lower:
            return 0.6
        
        # 提取方法名中的单词
        words1 = self._extract_words_from_camel_case(method1)
        words2 = self._extract_words_from_camel_case(method2)
        
        # 计算单词重叠度
        common_words = set(words1) & set(words2)
        total_words = set(words1) | set(words2)
        
        if total_words:
            return len(common_words) / len(total_words)
        
        return 0.0
    
    def _extract_words_from_camel_case(self, text: str) -> List[str]:
        """从驼峰命名中提取单词"""
        import re
        # 使用正则表达式分割驼峰命名
        words = re.findall(r'[A-Z]?[a-z]+|[A-Z]+(?=[A-Z][a-z]|\b)', text)
        return [word.lower() for word in words if word]
    
    def _analyze_context_match(self, context_info: Dict[str, Any]) -> float:
        """分析上下文匹配度"""
        # 这里可以根据具体的上下文信息进行分析
        # 目前返回默认值
        return 0.5
    
    def _get_score_description(self, score: float) -> str:
        """获取评分描述"""
        if score >= 0.9:
            return "优秀"
        elif score >= 0.7:
            return "良好"
        elif score >= 0.5:
            return "中等"
        elif score >= 0.3:
            return "较低"
        else:
            return "很低"
    
    def batch_score_matches(self, missing_method: ErrorReport, 
                          candidates: List[Tuple[MethodInfo, float]],
                          context_info: Optional[Dict[str, Any]] = None) -> List[MatchingReport]:
        """
        批量评分候选方法
        
        Args:
            missing_method: 缺失的方法信息
            candidates: 候选方法列表，每个元素为(方法信息, 语义相似度)
            context_info: 上下文信息
            
        Returns:
            匹配报告列表，按评分降序排列
        """
        reports = []
        
        for candidate_method, semantic_similarity in candidates:
            try:
                report = self.score_match(
                    missing_method, candidate_method, semantic_similarity, context_info
                )
                reports.append(report)
            except Exception as e:
                self.logger.error(f"评分候选方法时出错: {candidate_method.method_name}, 错误: {e}")
                continue
        
        # 按总体评分降序排列
        reports.sort(key=lambda r: r.overall_score, reverse=True)
        
        return reports
    
    def get_top_matches(self, reports: List[MatchingReport], 
                       top_k: int = 3, min_score: float = 0.3) -> List[MatchingReport]:
        """
        获取最佳匹配结果
        
        Args:
            reports: 匹配报告列表
            top_k: 返回的最大数量
            min_score: 最低评分阈值
            
        Returns:
            过滤后的最佳匹配列表
        """
        # 过滤低分结果
        filtered_reports = [r for r in reports if r.overall_score >= min_score]
        
        # 返回前top_k个结果
        return filtered_reports[:top_k]
    
    def _analyze_business_domain_from_package(self, package1: str, package2: str) -> float:
        """从包名分析业务领域匹配度"""
        if not package1 or not package2:
            return 0.0
        
        # 提取业务领域关键词
        domain_keywords1 = self._extract_business_domain_keywords(package1)
        domain_keywords2 = self._extract_business_domain_keywords(package2)
        
        if not domain_keywords1 or not domain_keywords2:
            return 0.3  # 默认中等匹配度
        
        # 计算关键词重叠度
        common_keywords = set(domain_keywords1) & set(domain_keywords2)
        total_keywords = set(domain_keywords1) | set(domain_keywords2)
        
        if total_keywords:
            return len(common_keywords) / len(total_keywords)
        
        return 0.0
    
    def _analyze_business_domain_from_class(self, class1: str, class2: str) -> float:
        """从类名分析业务领域匹配度"""
        if not class1 or not class2:
            return 0.0
        
        # 提取业务相关词汇
        business_words1 = self._extract_business_words_from_class(class1)
        business_words2 = self._extract_business_words_from_class(class2)
        
        if not business_words1 or not business_words2:
            return 0.3
        
        common_words = set(business_words1) & set(business_words2)
        total_words = set(business_words1) | set(business_words2)
        
        if total_words:
            return len(common_words) / len(total_words)
        
        return 0.0
    
    def _analyze_business_domain_from_method(self, method1: str, method2: str) -> float:
        """从方法名分析业务领域匹配度"""
        if not method1 or not method2:
            return 0.0
        
        # 提取业务操作词汇
        business_ops1 = self._extract_business_operations(method1)
        business_ops2 = self._extract_business_operations(method2)
        
        if not business_ops1 or not business_ops2:
            return 0.3
        
        common_ops = set(business_ops1) & set(business_ops2)
        total_ops = set(business_ops1) | set(business_ops2)
        
        if total_ops:
            return len(common_ops) / len(total_ops)
        
        return 0.0
    
    def _extract_business_domain_keywords(self, package_name: str) -> List[str]:
        """从包名提取业务领域关键词"""
        # 常见业务领域关键词
        business_domains = [
            'user', 'project', 'energy', 'data', 'report', 'config', 'system',
            'service', 'dao', 'entity', 'model', 'controller', 'manager',
            'tree', 'node', 'dim', 'fact', 'analysis', 'monitor', 'alert'
        ]
        
        package_lower = package_name.lower()
        found_keywords = []
        
        for domain in business_domains:
            if domain in package_lower:
                found_keywords.append(domain)
        
        return found_keywords
    
    def _extract_business_words_from_class(self, class_name: str) -> List[str]:
        """从类名提取业务相关词汇"""
        words = self._extract_words_from_camel_case(class_name)
        
        # 过滤掉技术词汇，保留业务词汇
        technical_words = {'service', 'dao', 'impl', 'controller', 'manager', 'util', 'helper'}
        business_words = [word for word in words if word.lower() not in technical_words]
        
        return business_words
    
    def _extract_business_operations(self, method_name: str) -> List[str]:
        """从方法名提取业务操作词汇"""
        words = self._extract_words_from_camel_case(method_name)
        
        # 常见业务操作词汇
        business_operations = [
            'get', 'find', 'query', 'search', 'list', 'select',
            'create', 'add', 'insert', 'save', 'store',
            'update', 'modify', 'change', 'edit',
            'delete', 'remove', 'drop',
            'count', 'sum', 'calculate', 'compute',
            'validate', 'check', 'verify',
            'export', 'import', 'sync', 'process'
        ]
        
        found_operations = []
        for word in words:
            if word.lower() in business_operations:
                found_operations.append(word.lower())
        
        return found_operations
    
    def _calculate_return_type_compatibility(self, missing_method: ErrorReport,
                                           candidate_method: MethodInfo) -> float:
        """计算返回类型兼容性"""
        missing_return = missing_method.return_type_full or missing_method.out_return or ""
        candidate_return = candidate_method.return_type or ""
        
        if not missing_return or not candidate_return:
            return 0.5  # 默认中等兼容性
        
        # 完全匹配
        if missing_return == candidate_return:
            return 1.0
        
        # 类型名匹配（忽略包名）
        missing_type_name = missing_return.split('.')[-1]
        candidate_type_name = candidate_return.split('.')[-1]
        
        if missing_type_name == candidate_type_name:
            return 0.9
        
        # 泛型类型匹配
        if self._is_generic_type_compatible(missing_return, candidate_return):
            return 0.8
        
        # 基本类型兼容性
        if self._is_basic_type_compatible(missing_return, candidate_return):
            return 0.7
        
        return 0.3
    
    def _is_generic_type_compatible(self, type1: str, type2: str) -> bool:
        """检查泛型类型兼容性"""
        # 简单的泛型类型检查
        generic_patterns = ['List<', 'Set<', 'Map<', 'Collection<']
        
        for pattern in generic_patterns:
            if pattern in type1 and pattern in type2:
                return True
        
        return False
    
    def _is_basic_type_compatible(self, type1: str, type2: str) -> bool:
        """检查基本类型兼容性"""
        # 基本类型兼容性映射
        compatible_types = {
            'String': ['String', 'CharSequence'],
            'Integer': ['Integer', 'int', 'Long', 'long'],
            'Long': ['Long', 'long', 'Integer', 'int'],
            'Boolean': ['Boolean', 'boolean'],
            'Double': ['Double', 'double', 'Float', 'float'],
            'Float': ['Float', 'float', 'Double', 'double']
        }
        
        type1_simple = type1.split('.')[-1]
        type2_simple = type2.split('.')[-1]
        
        for base_type, compatible_list in compatible_types.items():
            if type1_simple in compatible_list and type2_simple in compatible_list:
                return True
        
        return False