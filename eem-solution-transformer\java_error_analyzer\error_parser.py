"""
XML错误解析器实现
"""
import xml.etree.ElementTree as ET
import logging
import json
from collections import defaultdict, Counter
from typing import List, Dict, Tuple
try:
    from .interfaces import ErrorParserInterface
    from .models import CompilationError, ErrorStatistics
except ImportError:
    from interfaces import ErrorParserInterface
    from models import CompilationError, ErrorStatistics


class ErrorParser(ErrorParserInterface):
    """JavaAnnotator.xml错误解析器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.statistics = None
    
    def parse_xml(self, xml_file_path: str) -> List[CompilationError]:
        """解析XML文件，返回编译错误列表"""
        try:
            self.logger.info(f"开始解析XML文件: {xml_file_path}")
            tree = ET.parse(xml_file_path)
            root = tree.getroot()
            
            errors = []
            problem_nodes = root.findall('.//problem')
            
            self.logger.info(f"找到 {len(problem_nodes)} 个错误节点")
            
            for problem_node in problem_nodes:
                try:
                    error = self.extract_error_info(problem_node)
                    if error:
                        errors.append(error)
                except Exception as e:
                    self.logger.warning(f"解析错误节点失败: {e}")
                    continue
            
            self.logger.info(f"成功解析 {len(errors)} 个错误")
            
            # 生成统计信息
            self.statistics = self.generate_statistics(errors)
            
            return errors
            
        except ET.ParseError as e:
            self.logger.error(f"XML解析失败: {e}")
            return []
        except FileNotFoundError:
            self.logger.error(f"文件不存在: {xml_file_path}")
            return []
        except Exception as e:
            self.logger.error(f"解析XML时发生未知错误: {e}")
            return []
    
    def extract_error_info(self, problem_node) -> CompilationError:
        """从XML节点提取错误信息"""
        try:
            # 提取基本信息
            file_element = problem_node.find('file')
            line_element = problem_node.find('line')
            module_element = problem_node.find('module')
            package_element = problem_node.find('package')
            description_element = problem_node.find('description')
            highlighted_element = problem_node.find('highlighted_element')
            severity_element = problem_node.find('problem_class')
            offset_element = problem_node.find('offset')
            length_element = problem_node.find('length')
            
            # 处理文件路径，移除项目路径前缀
            file_path = file_element.text if file_element is not None else ""
            if file_path.startswith("file://$PROJECT_DIR$/"):
                file_path = file_path.replace("file://$PROJECT_DIR$/", "")
            
            # 创建CompilationError对象
            error = CompilationError(
                file_path=file_path,
                line=int(line_element.text) if line_element is not None and line_element.text else 0,
                module=module_element.text if module_element is not None else "",
                package=package_element.text if package_element is not None else "",
                description=description_element.text if description_element is not None else "",
                highlighted_element=highlighted_element.text if highlighted_element is not None else "",
                severity=severity_element.get('severity', 'ERROR') if severity_element is not None else 'ERROR',
                offset=int(offset_element.text) if offset_element is not None and offset_element.text else 0,
                length=int(length_element.text) if length_element is not None and length_element.text else 0
            )
            
            return error
            
        except Exception as e:
            self.logger.warning(f"提取错误信息失败: {e}")
            return None
    
    def _clean_file_path(self, file_path: str) -> str:
        """清理文件路径，移除不必要的前缀"""
        prefixes_to_remove = [
            "file://$PROJECT_DIR$/",
            "file://",
            "$PROJECT_DIR$/"
        ]
        
        for prefix in prefixes_to_remove:
            if file_path.startswith(prefix):
                file_path = file_path[len(prefix):]
        
        return file_path
    
    def generate_statistics(self, errors: List[CompilationError]) -> ErrorStatistics:
        """生成错误统计信息"""
        if not errors:
            return ErrorStatistics(0, 0, 0.0, [], {})
        
        # 按文件分组统计
        file_error_count = defaultdict(int)
        error_types = Counter()
        
        for error in errors:
            file_error_count[error.file_path] += 1
            # 根据描述分类错误类型
            error_type = self._classify_error_type(error.description)
            error_types[error_type] += 1
        
        total_errors = len(errors)
        files_with_errors = len(file_error_count)
        average_errors_per_file = total_errors / files_with_errors if files_with_errors > 0 else 0
        
        # 找出最有问题的文件（前10个）
        most_problematic_files = sorted(
            file_error_count.items(), 
            key=lambda x: x[1], 
            reverse=True
        )[:10]
        
        return ErrorStatistics(
            total_errors=total_errors,
            files_with_errors=files_with_errors,
            average_errors_per_file=average_errors_per_file,
            most_problematic_files=most_problematic_files,
            error_types_distribution=dict(error_types)
        )
    
    def _classify_error_type(self, description: str) -> str:
        """根据错误描述分类错误类型"""
        description_lower = description.lower()
        
        if "cannot resolve symbol" in description_lower:
            return "无法解析符号"
        elif "unknown class" in description_lower:
            return "未知类"
        elif "cannot find symbol" in description_lower:
            return "找不到符号"
        elif "package does not exist" in description_lower:
            return "包不存在"
        elif "method" in description_lower and ("not found" in description_lower or "undefined" in description_lower):
            return "方法未定义"
        elif "incompatible types" in description_lower:
            return "类型不兼容"
        elif "deprecated" in description_lower:
            return "已废弃"
        else:
            return "其他错误"
    
    def organize_errors_by_file(self, errors: List[CompilationError]) -> Dict[str, List[CompilationError]]:
        """按文件路径组织错误信息"""
        organized_errors = defaultdict(list)
        
        for error in errors:
            organized_errors[error.file_path].append(error)
        
        # 按行号排序每个文件的错误
        for file_path in organized_errors:
            organized_errors[file_path].sort(key=lambda x: x.line)
        
        return dict(organized_errors)
    
    def export_structured_data(self, errors: List[CompilationError], output_file: str = "原始错误列表.json"):
        """导出结构化的错误数据"""
        try:
            # 按文件组织错误
            organized_errors = self.organize_errors_by_file(errors)
            
            # 准备导出数据
            export_data = {
                "summary": {
                    "total_errors": len(errors),
                    "files_with_errors": len(organized_errors),
                    "analysis_timestamp": self._get_timestamp(),
                    "statistics": self.statistics.__dict__ if self.statistics else None
                },
                "errors_by_file": {}
            }
            
            # 转换错误数据为可序列化格式
            for file_path, file_errors in organized_errors.items():
                export_data["errors_by_file"][file_path] = [
                    {
                        "line": error.line,
                        "module": error.module,
                        "package": error.package,
                        "description": error.description,
                        "highlighted_element": error.highlighted_element,
                        "severity": error.severity,
                        "offset": error.offset,
                        "length": error.length
                    }
                    for error in file_errors
                ]
            
            # 写入JSON文件
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"结构化错误数据已导出到: {output_file}")
            return output_file
            
        except Exception as e:
            self.logger.error(f"导出结构化数据失败: {e}")
            return None
    
    def generate_statistics_report(self, output_file: str = "错误统计报告.md"):
        """生成错误统计报告"""
        if not self.statistics:
            self.logger.warning("没有统计信息可用")
            return None
        
        try:
            report_content = self._build_statistics_report()
            
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(report_content)
            
            self.logger.info(f"错误统计报告已生成: {output_file}")
            return output_file
            
        except Exception as e:
            self.logger.error(f"生成统计报告失败: {e}")
            return None
    
    def _build_statistics_report(self) -> str:
        """构建统计报告内容"""
        stats = self.statistics
        
        report = f"""# Java 代码迁移错误统计报告

## 总体统计

- **总错误数**: {stats.total_errors}
- **涉及文件数**: {stats.files_with_errors}
- **平均每文件错误数**: {stats.average_errors_per_file:.2f}
- **分析时间**: {self._get_timestamp()}

## 错误类型分布

"""
        
        # 错误类型分布
        for error_type, count in sorted(stats.error_types_distribution.items(), key=lambda x: x[1], reverse=True):
            percentage = (count / stats.total_errors) * 100
            report += f"- **{error_type}**: {count} 个 ({percentage:.1f}%)\n"
        
        report += "\n## 最有问题的文件 (Top 10)\n\n"
        
        # 最有问题的文件
        for i, (file_path, error_count) in enumerate(stats.most_problematic_files, 1):
            percentage = (error_count / stats.total_errors) * 100
            report += f"{i}. `{file_path}` - {error_count} 个错误 ({percentage:.1f}%)\n"
        
        report += f"""

## 分析建议

### 优先处理建议

1. **高频错误类型**: 优先解决 "无法解析符号" 和 "未知类" 类型的错误
2. **问题文件**: 重点关注错误数量最多的前5个文件
3. **批量处理**: 相同类型的错误可以批量处理以提高效率

### 修复策略

- **导入问题**: 使用类名查找工具找到正确的替换类
- **废弃API**: 查阅知识库寻找替代方案
- **类型不兼容**: 检查API变更和类型转换需求

---
*报告生成时间: {self._get_timestamp()}*
"""
        
        return report
    
    def _get_timestamp(self) -> str:
        """获取当前时间戳"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")