# 代码扫描问题报告

总问题数: 226

## DateUtil

### 问题 1
error_type: "类问题"
error_code: "DateUtil_issues"
calling_class: "DateUtil"
usage_pattern: "DateUtil"
suggest: "请使用TimeUtil重构"
line: ["17"]

## LoadRateVo

### 问题 1
error_type: "类问题"
error_code: "类问题"
missing_class: "TimeValue"
calling_class: "LoadRateVo"
old_dependency: "com.cet.eem.fusion.transformer.core.entity.vo"
current_dependency: "eem-solution-transformer-core"
line: ["[18, 18]"]

## OverviewDataVo

### 问题 1
error_type: "类问题"
error_code: "类问题"
missing_class: "Event"
calling_class: "OverviewDataVo"
old_dependency: "com.cet.eem.fusion.transformer.core.entity.vo"
current_dependency: "eem-solution-transformer-core"
line: ["[18, 18]"]

### 问题 2
error_type: "类问题"
error_code: "类问题"
missing_class: "Operation"
calling_class: "OverviewDataVo"
old_dependency: "com.cet.eem.fusion.transformer.core.entity.vo"
current_dependency: "eem-solution-transformer-core"
line: ["[14, 14]"]

### 问题 3
error_type: "类问题"
error_code: "类问题"
missing_class: "Quantity"
calling_class: "OverviewDataVo"
old_dependency: "com.cet.eem.fusion.transformer.core.entity.vo"
current_dependency: "eem-solution-transformer-core"
line: ["[16, 16]"]

## PowerTransformerDaoImpl

### 问题 1
error_type: "类问题"
error_code: "类问题"
missing_class: "NodeLabelDef"
calling_class: "PowerTransformerDaoImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.dao.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[35, 35, 29, 43, 43]"]

### 问题 2
error_type: "类问题"
error_code: "类问题"
missing_class: "POWER_TRANS_FORMER"
calling_class: "PowerTransformerDaoImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.dao.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[35, 29, 43]"]

### 问题 3
error_type: "类问题"
error_code: "类问题"
missing_class: "PROJECT"
calling_class: "PowerTransformerDaoImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.dao.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[35, 43]"]

### 问题 4
error_type: "类问题"
error_code: "类问题"
missing_class: "ProjectDto"
calling_class: "PowerTransformerDaoImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.dao.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[43, 43, 43, 43]"]

### 问题 5
error_type: "类问题"
error_code: "regex_pattern"
calling_class: "PowerTransformerDaoImpl"
calling_method: "modelServiceUtils_queryWithChildren"
suggest: "请使用ParentQueryConditionBuilder.leftJoinSubBuilder替代modelServiceUtils.queryWithChildren泛型"
line: ["35"]

### 问题 6
error_type: "类问题"
error_code: "regex_pattern"
calling_class: "PowerTransformerDaoImpl"
calling_method: "modelServiceUtils_queryWithChildren"
suggest: "请使用ParentQueryConditionBuilder.leftJoinSubBuilder替代modelServiceUtils.queryWithChildren泛型"
line: ["43"]

## PowerTransformerDto

### 问题 1
error_type: "类问题"
error_code: "类问题"
missing_class: "PowerTransformerVo"
calling_class: "PowerTransformerDto"
old_dependency: "com.cet.eem.fusion.transformer.core.entity.dto"
current_dependency: "eem-solution-transformer-core"
line: ["[4, 19]"]

## ProjectDto

### 问题 1
error_type: "类问题"
error_code: "类问题"
missing_class: "Project"
calling_class: "ProjectDto"
old_dependency: "com.cet.eem.fusion.transformer.core.entity.dto"
current_dependency: "eem-solution-transformer-core"
line: ["[4, 16]"]

## TransformerAnalysisController

### 问题 1
error_type: "类问题"
error_code: "类问题"
missing_class: "VoltageSideMonitorVo"
calling_class: "TransformerAnalysisController"
old_dependency: "com.cet.eem.fusion.transformer.core.controller"
current_dependency: "eem-solution-transformer-core"
line: ["[44, 44]"]

### 问题 2
error_type: "类问题"
error_code: "类问题"
missing_class: "Result"
calling_class: "TransformerAnalysisController"
old_dependency: "com.cet.eem.fusion.transformer.core.controller"
current_dependency: "eem-solution-transformer-core"
line: ["[45, 51, 57, 63, 39]"]

### 问题 3
error_type: "类问题"
error_code: "类问题"
missing_class: "LoadInfoVo"
calling_class: "TransformerAnalysisController"
old_dependency: "com.cet.eem.fusion.transformer.core.controller"
current_dependency: "eem-solution-transformer-core"
line: ["[50, 50]"]

### 问题 4
error_type: "类问题"
error_code: "类问题"
missing_class: "LoadRateVo"
calling_class: "TransformerAnalysisController"
old_dependency: "com.cet.eem.fusion.transformer.core.controller"
current_dependency: "eem-solution-transformer-core"
line: ["[62, 62]"]

### 问题 5
error_type: "类问题"
error_code: "类问题"
missing_class: "LoadRateParam"
calling_class: "TransformerAnalysisController"
old_dependency: "com.cet.eem.fusion.transformer.core.controller"
current_dependency: "eem-solution-transformer-core"
line: ["[62, 62]"]

### 问题 6
error_type: "类问题"
error_code: "类问题"
missing_class: "EquipmentMonitorVo"
calling_class: "TransformerAnalysisController"
old_dependency: "com.cet.eem.fusion.transformer.core.controller"
current_dependency: "eem-solution-transformer-core"
line: ["[38, 38]"]

### 问题 7
error_type: "多租户"
error_code: "resource_issues"
calling_class: "TransformerAnalysisController"
usage_pattern: "@Resource"
suggest: "请使用@Autowired替代@Resource注解"
line: ["30"]

### 问题 8
error_type: "多租户"
error_code: "resource_issues"
calling_class: "TransformerAnalysisController"
usage_pattern: "@Resource"
suggest: "请使用@Autowired替代@Resource注解"
line: ["33"]

## TransformerAnalysisService

### 问题 1
error_type: "类问题"
error_code: "类问题"
missing_class: "DataLogData"
calling_class: "TransformerAnalysisService"
old_dependency: "com.cet.eem.fusion.transformer.core.service"
current_dependency: "eem-solution-transformer-core"
line: ["[84, 84, 76, 76]"]

### 问题 2
error_type: "类问题"
error_code: "类问题"
missing_class: "PowerTransformerDto"
calling_class: "TransformerAnalysisService"
old_dependency: "com.cet.eem.fusion.transformer.core.service"
current_dependency: "eem-solution-transformer-core"
line: ["[84, 84, 92, 92]"]

### 问题 3
error_type: "类问题"
error_code: "类问题"
missing_class: "QuantityDataBatchSearchVo"
calling_class: "TransformerAnalysisService"
old_dependency: "com.cet.eem.fusion.transformer.core.service"
current_dependency: "eem-solution-transformer-core"
line: ["[84, 84, 76, 76]"]

### 问题 4
error_type: "类问题"
error_code: "类问题"
missing_class: "PointNode"
calling_class: "TransformerAnalysisService"
old_dependency: "com.cet.eem.fusion.transformer.core.service"
current_dependency: "eem-solution-transformer-core"
line: ["[101, 101, 76, 76, 110, 110, 65, 65]"]

### 问题 5
error_type: "类问题"
error_code: "类问题"
missing_class: "LinkNode"
calling_class: "TransformerAnalysisService"
old_dependency: "com.cet.eem.fusion.transformer.core.service"
current_dependency: "eem-solution-transformer-core"
line: ["[101, 101, 76, 76, 110, 110, 65, 65]"]

### 问题 6
error_type: "类问题"
error_code: "类问题"
missing_class: "VoltageSideMonitorVo"
calling_class: "TransformerAnalysisService"
old_dependency: "com.cet.eem.fusion.transformer.core.service"
current_dependency: "eem-solution-transformer-core"
line: ["[29, 29]"]

### 问题 7
error_type: "类问题"
error_code: "类问题"
missing_class: "EquipmentMonitorVo"
calling_class: "TransformerAnalysisService"
old_dependency: "com.cet.eem.fusion.transformer.core.service"
current_dependency: "eem-solution-transformer-core"
line: ["[19, 19]"]

### 问题 8
error_type: "类问题"
error_code: "类问题"
missing_class: "RadarChartInfo"
calling_class: "TransformerAnalysisService"
old_dependency: "com.cet.eem.fusion.transformer.core.service"
current_dependency: "eem-solution-transformer-core"
line: ["[46, 46]"]

### 问题 9
error_type: "类问题"
error_code: "类问题"
missing_class: "LoadInfoVo"
calling_class: "TransformerAnalysisService"
old_dependency: "com.cet.eem.fusion.transformer.core.service"
current_dependency: "eem-solution-transformer-core"
line: ["[37, 37]"]

### 问题 10
error_type: "类问题"
error_code: "类问题"
missing_class: "BaseVo"
calling_class: "TransformerAnalysisService"
old_dependency: "com.cet.eem.fusion.transformer.core.service"
current_dependency: "eem-solution-transformer-core"
line: ["[65, 65]"]

### 问题 11
error_type: "类问题"
error_code: "类问题"
missing_class: "LoadRateVo"
calling_class: "TransformerAnalysisService"
old_dependency: "com.cet.eem.fusion.transformer.core.service"
current_dependency: "eem-solution-transformer-core"
line: ["[55, 55]"]

### 问题 12
error_type: "类问题"
error_code: "类问题"
missing_class: "LoadRateParam"
calling_class: "TransformerAnalysisService"
old_dependency: "com.cet.eem.fusion.transformer.core.service"
current_dependency: "eem-solution-transformer-core"
line: ["[55, 55]"]

### 问题 13
error_type: "类问题"
error_code: "QuantityDataBatchSearchVo_issues"
calling_class: "TransformerAnalysisService"
usage_pattern: "QuantityDataBatchSearchVo"
suggest: "QuantityDataBatchSearchVo废弃"
line: ["76"]

### 问题 14
error_type: "类问题"
error_code: "QuantityDataBatchSearchVo_issues"
calling_class: "TransformerAnalysisService"
usage_pattern: "QuantityDataBatchSearchVo"
suggest: "QuantityDataBatchSearchVo废弃"
line: ["84"]

## TransformerAnalysisServiceImpl

### 问题 1
error_type: "类问题"
error_code: "类问题"
missing_class: "PipeNetworkConnectionModelDao"
calling_class: "TransformerAnalysisServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[4, 81, 81]"]

### 问题 2
error_type: "类问题"
error_code: "类问题"
missing_class: "PowerTransformerVo"
calling_class: "TransformerAnalysisServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[13, 161, 161]"]

### 问题 3
error_type: "类问题"
error_code: "类问题"
missing_class: "TransformerConstantDef"
calling_class: "TransformerAnalysisServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[17, 18, 19, 20]"]

### 问题 4
error_type: "类问题"
error_code: "类问题"
missing_class: "AggregationType"
calling_class: "TransformerAnalysisServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[17, 183, 344, 306]"]

### 问题 5
error_type: "类问题"
error_code: "类问题"
missing_class: "EnergyTypeDef"
calling_class: "TransformerAnalysisServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[18, 1139, 1130, 1185, 113, 1121, 1112, 1167, 1157, 1176, 271, 1194, 165, 1102, 127, 1084, 1093, 1148]"]

### 问题 6
error_type: "类问题"
error_code: "类问题"
missing_class: "EnumDataTypeId"
calling_class: "TransformerAnalysisServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[19, 1209, 811]"]

### 问题 7
error_type: "类问题"
error_code: "类问题"
missing_class: "EnumOperationType"
calling_class: "TransformerAnalysisServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[20, 240, 241, 242, 245, 324, 735, 616, 1308, 1308, 1315, 604, 605, 606, 824, 826, 788, 1253, 1254]"]

### 问题 8
error_type: "类问题"
error_code: "类问题"
missing_class: "RealTimeValueVo"
calling_class: "TransformerAnalysisServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[27]"]

### 问题 9
error_type: "类问题"
error_code: "类问题"
missing_class: "Topology1Service"
calling_class: "TransformerAnalysisServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[37, 75, 75]"]

### 问题 10
error_type: "类问题"
error_code: "类问题"
missing_class: "QuantityObjectDao"
calling_class: "TransformerAnalysisServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[39, 77, 77]"]

### 问题 11
error_type: "类问题"
error_code: "类问题"
missing_class: "QuantityDataBatchSearchVo"
calling_class: "TransformerAnalysisServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[40, 512, 512, 412, 412, 446, 446, 1207, 1207, 1208, 1208, 1208, 335, 335, 809, 809, 810, 810, 810, 291, 291, 356, 356, 698, 698, 460, 460]"]

### 问题 12
error_type: "类问题"
error_code: "类问题"
missing_class: "QuantityManageService"
calling_class: "TransformerAnalysisServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[42, 73, 73]"]

### 问题 13
error_type: "类问题"
error_code: "类问题"
missing_class: "PowerTransformerDto"
calling_class: "TransformerAnalysisServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[510, 510, 411, 411, 413, 425, 425, 426, 426, 445, 445, 262, 262, 355, 355, 361, 361, 362, 362, 1233, 1233, 98, 98, 123, 123, 459, 459, 462, 472, 472, 1217, 1217, 1219, 1219, 1219, 1219]"]

### 问题 14
error_type: "类问题"
error_code: "类问题"
missing_class: "NumberCalcUtils"
calling_class: "TransformerAnalysisServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[240, 241, 242, 245, 324, 734, 614, 1307, 1307, 1315, 604, 605, 606, 824, 826, 788, 1252, 1254]"]

### 问题 15
error_type: "类问题"
error_code: "类问题"
missing_class: "MULTIPLICATION"
calling_class: "TransformerAnalysisServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[240, 241, 1308, 1308, 604, 605, 824, 826, 1253]"]

### 问题 16
error_type: "类问题"
error_code: "类问题"
missing_class: "ADD"
calling_class: "TransformerAnalysisServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[242, 1315, 606]"]

### 问题 17
error_type: "类问题"
error_code: "类问题"
missing_class: "DIVISION"
calling_class: "TransformerAnalysisServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[245, 324, 616, 788, 1254]"]

### 问题 18
error_type: "类问题"
error_code: "类问题"
missing_class: "ELECTRIC"
calling_class: "TransformerAnalysisServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[1139, 1130, 1185, 113, 1121, 1112, 1167, 1157, 1176, 271, 1194, 165, 1102, 127, 1084, 1093, 1148]"]

### 问题 19
error_type: "类问题"
error_code: "类问题"
missing_class: "VoltageSideMonitorVo"
calling_class: "TransformerAnalysisServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[112, 112, 116, 116, 117, 117, 1011, 1011, 971, 971, 972, 972, 972, 955, 955]"]

### 问题 20
error_type: "类问题"
error_code: "类问题"
missing_class: "LoadInfoVo"
calling_class: "TransformerAnalysisServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[937, 937, 938, 938, 938, 834, 834, 1263, 1263, 122, 122, 125, 132, 132]"]

### 问题 21
error_type: "类问题"
error_code: "类问题"
missing_class: "RealTimeValue"
calling_class: "TransformerAnalysisServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[937, 937, 939, 939, 940, 940, 941, 941, 942, 943, 943, 943, 944, 944, 945, 945, 1022, 1022, 971, 971, 838, 838, 1053, 1053, 1053, 1053, 1055, 1055, 1056, 1056, 1032, 1032, 1041, 1041, 1043, 1043, 1044, 1044, 1276, 1276, 1289, 1289, 1298, 1298, 965, 965, 1065, 1065, 1067, 1067, 1068, 1068]"]

### 问题 22
error_type: "类问题"
error_code: "类问题"
missing_class: "SUBTRACT"
calling_class: "TransformerAnalysisServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[735]"]

### 问题 23
error_type: "类问题"
error_code: "类问题"
missing_class: "LoadRateVo"
calling_class: "TransformerAnalysisServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[261, 261, 263, 263, 263]"]

### 问题 24
error_type: "类问题"
error_code: "类问题"
missing_class: "LoadRateParam"
calling_class: "TransformerAnalysisServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[261, 261]"]

### 问题 25
error_type: "类问题"
error_code: "类问题"
missing_class: "SectionVo"
calling_class: "TransformerAnalysisServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[268, 268, 820, 820, 825, 825, 825, 827, 827, 827, 828, 828, 828, 829, 829, 829, 144, 144]"]

### 问题 26
error_type: "类问题"
error_code: "类问题"
missing_class: "REALTIME"
calling_class: "TransformerAnalysisServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[1209, 811]"]

### 问题 27
error_type: "类问题"
error_code: "类问题"
missing_class: "TimeValue"
calling_class: "TransformerAnalysisServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[761, 761, 763, 763, 765, 765, 765]"]

### 问题 28
error_type: "类问题"
error_code: "类问题"
missing_class: "RadarChartInfo"
calling_class: "TransformerAnalysisServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[160, 160, 163, 199, 199, 199, 782, 782]"]

### 问题 29
error_type: "类问题"
error_code: "类问题"
missing_class: "STEP_ACCUMULATION"
calling_class: "TransformerAnalysisServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[183, 344, 306]"]

### 问题 30
error_type: "类问题"
error_code: "类问题"
missing_class: "EquipmentMonitorVo"
calling_class: "TransformerAnalysisServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[1029, 1029, 97, 97, 100, 102, 102, 102]"]

### 问题 31
error_type: "类问题"
error_code: "类问题"
missing_class: "NodeDao"
calling_class: "TransformerAnalysisServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[83, 83]"]

### 问题 32
error_type: "类问题"
error_code: "类问题"
missing_class: "QueryConditionBuilder"
calling_class: "TransformerAnalysisServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[755, 755]"]

### 问题 33
error_type: "类问题"
error_code: "类问题"
missing_class: "BaseEntity"
calling_class: "TransformerAnalysisServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[755, 755]"]

### 问题 34
error_type: "类问题"
error_code: "QuantityDataBatchSearchVo_issues"
calling_class: "TransformerAnalysisServiceImpl"
usage_pattern: "QuantityDataBatchSearchVo"
suggest: "QuantityDataBatchSearchVo废弃"
line: ["40"]

### 问题 35
error_type: "类问题"
error_code: "QuantityDataBatchSearchVo_issues"
calling_class: "TransformerAnalysisServiceImpl"
usage_pattern: "QuantityDataBatchSearchVo"
suggest: "QuantityDataBatchSearchVo废弃"
line: ["291"]

### 问题 36
error_type: "类问题"
error_code: "QuantityDataBatchSearchVo_issues"
calling_class: "TransformerAnalysisServiceImpl"
usage_pattern: "QuantityDataBatchSearchVo"
suggest: "QuantityDataBatchSearchVo废弃"
line: ["335"]

### 问题 37
error_type: "类问题"
error_code: "QuantityDataBatchSearchVo_issues"
calling_class: "TransformerAnalysisServiceImpl"
usage_pattern: "QuantityDataBatchSearchVo"
suggest: "QuantityDataBatchSearchVo废弃"
line: ["356"]

### 问题 38
error_type: "类问题"
error_code: "QuantityDataBatchSearchVo_issues"
calling_class: "TransformerAnalysisServiceImpl"
usage_pattern: "QuantityDataBatchSearchVo"
suggest: "QuantityDataBatchSearchVo废弃"
line: ["412"]

### 问题 39
error_type: "类问题"
error_code: "QuantityDataBatchSearchVo_issues"
calling_class: "TransformerAnalysisServiceImpl"
usage_pattern: "QuantityDataBatchSearchVo"
suggest: "QuantityDataBatchSearchVo废弃"
line: ["446"]

### 问题 40
error_type: "类问题"
error_code: "QuantityDataBatchSearchVo_issues"
calling_class: "TransformerAnalysisServiceImpl"
usage_pattern: "QuantityDataBatchSearchVo"
suggest: "QuantityDataBatchSearchVo废弃"
line: ["460"]

### 问题 41
error_type: "类问题"
error_code: "QuantityDataBatchSearchVo_issues"
calling_class: "TransformerAnalysisServiceImpl"
usage_pattern: "QuantityDataBatchSearchVo"
suggest: "QuantityDataBatchSearchVo废弃"
line: ["512"]

### 问题 42
error_type: "类问题"
error_code: "QuantityDataBatchSearchVo_issues"
calling_class: "TransformerAnalysisServiceImpl"
usage_pattern: "QuantityDataBatchSearchVo"
suggest: "QuantityDataBatchSearchVo废弃"
line: ["698"]

### 问题 43
error_type: "类问题"
error_code: "QuantityDataBatchSearchVo_issues"
calling_class: "TransformerAnalysisServiceImpl"
usage_pattern: "QuantityDataBatchSearchVo"
suggest: "QuantityDataBatchSearchVo废弃"
line: ["809"]

### 问题 44
error_type: "类问题"
error_code: "QuantityDataBatchSearchVo_issues"
calling_class: "TransformerAnalysisServiceImpl"
usage_pattern: "QuantityDataBatchSearchVo"
suggest: "QuantityDataBatchSearchVo废弃"
line: ["810"]

### 问题 45
error_type: "类问题"
error_code: "QuantityDataBatchSearchVo_issues"
calling_class: "TransformerAnalysisServiceImpl"
usage_pattern: "QuantityDataBatchSearchVo"
suggest: "QuantityDataBatchSearchVo废弃"
line: ["810"]

### 问题 46
error_type: "类问题"
error_code: "QuantityDataBatchSearchVo_issues"
calling_class: "TransformerAnalysisServiceImpl"
usage_pattern: "QuantityDataBatchSearchVo"
suggest: "QuantityDataBatchSearchVo废弃"
line: ["1207"]

### 问题 47
error_type: "类问题"
error_code: "QuantityDataBatchSearchVo_issues"
calling_class: "TransformerAnalysisServiceImpl"
usage_pattern: "QuantityDataBatchSearchVo"
suggest: "QuantityDataBatchSearchVo废弃"
line: ["1208"]

### 问题 48
error_type: "类问题"
error_code: "QuantityDataBatchSearchVo_issues"
calling_class: "TransformerAnalysisServiceImpl"
usage_pattern: "QuantityDataBatchSearchVo"
suggest: "QuantityDataBatchSearchVo废弃"
line: ["1208"]

### 问题 49
error_type: "类问题"
error_code: "QueryConditionBuilder_issues"
calling_class: "TransformerAnalysisServiceImpl"
usage_pattern: "QueryConditionBuilder"
suggest: "QueryConditionBuilder废弃，请使用ParentQueryConditionBuilder重构"
line: ["755"]

### 问题 50
error_type: "物理量查询服务"
error_code: "target_detection"
calling_class: "TransformerAnalysisServiceImpl"
calling_method: "TransformerAnalysisServiceImpl -> QuantityObjectDao"
suggest: "请使用新的QuantityObjectService替代QuantityObjectDao"
line: ["{'声明': 77, '使用': [173, 297, 418, 467]}"]

### 问题 51
error_type: "物理量查询服务"
error_code: "target_detection"
calling_class: "TransformerAnalysisServiceImpl"
calling_method: "TransformerAnalysisServiceImpl -> QuantityAggregationDataDao"
suggest: "请使用新的QuantityAggregationDataService替代QuantityAggregationDataDao"
line: ["{'声明': 79, '使用': [181, 305, 339]}"]

### 问题 52
error_type: "物理量查询服务"
error_code: "target_detection"
calling_class: "TransformerAnalysisServiceImpl"
calling_method: "TransformerAnalysisServiceImpl -> QuantityManageService"
suggest: "QuantityManageService已经废弃"
line: ["{'声明': 73, '使用': [699, 838, 965, 1022, 1032, 1276]}"]

### 问题 53
error_type: "类问题"
error_code: "target_detection"
calling_class: "TransformerAnalysisServiceImpl"
calling_method: "TransformerAnalysisServiceImpl -> Topology1Service"
suggest: "Topology1Service已经废弃（平台提供了PipeNetworkConnectionServiceImpl需要重构）"
line: ["{'声明': 75, '使用': [113, 127, 165, 271]}"]

### 问题 54
error_type: "类问题"
error_code: "target_detection"
calling_class: "TransformerAnalysisServiceImpl"
calling_method: "TransformerAnalysisServiceImpl -> PipeNetworkConnectionModelDao"
suggest: "PipeNetworkConnectionModelDao已经废弃（平台提供了PipeNetworkConnectionServiceImpl需要重构）"
line: ["{'声明': 81, '使用': [415, 464]}"]

### 问题 55
error_type: "类问题"
error_code: "target_detection"
calling_class: "TransformerAnalysisServiceImpl"
calling_method: "TransformerAnalysisServiceImpl -> NodeDao"
suggest: "NodeDao已经废弃，考虑通过EemNodeService重构"
line: ["{'声明': 83}"]

## TransformerOverviewController

### 问题 1
error_type: "类问题"
error_code: "类问题"
missing_class: "Result"
calling_class: "TransformerOverviewController"
old_dependency: "com.cet.eem.fusion.transformer.core.controller"
current_dependency: "eem-solution-transformer-core"
line: ["[35, 41, 53, 59, 47]"]

### 问题 2
error_type: "多租户"
error_code: "resource_issues"
calling_class: "TransformerOverviewController"
usage_pattern: "@Resource"
suggest: "请使用@Autowired替代@Resource注解"
line: ["29"]

## TransformerOverviewService

### 问题 1
error_type: "类问题"
error_code: "类问题"
missing_class: "EquipmentConditionDTO"
calling_class: "TransformerOverviewService"
old_dependency: "com.cet.eem.fusion.transformer.core.service"
current_dependency: "eem-solution-transformer-core"
line: ["[3]"]

### 问题 2
error_type: "类问题"
error_code: "类问题"
missing_class: "EquipmentFormDTO"
calling_class: "TransformerOverviewService"
old_dependency: "com.cet.eem.fusion.transformer.core.service"
current_dependency: "eem-solution-transformer-core"
line: ["[4]"]

### 问题 3
error_type: "类问题"
error_code: "类问题"
missing_class: "OverviewDataVO"
calling_class: "TransformerOverviewService"
old_dependency: "com.cet.eem.fusion.transformer.core.service"
current_dependency: "eem-solution-transformer-core"
line: ["[5]"]

### 问题 4
error_type: "类问题"
error_code: "类问题"
missing_class: "OverviewDataVo"
calling_class: "TransformerOverviewService"
old_dependency: "com.cet.eem.fusion.transformer.core.service"
current_dependency: "eem-solution-transformer-core"
line: ["[15, 15, 29, 29, 36, 36, 22, 22]"]

### 问题 5
error_type: "类问题"
error_code: "类问题"
missing_class: "EquipmentCondition"
calling_class: "TransformerOverviewService"
old_dependency: "com.cet.eem.fusion.transformer.core.service"
current_dependency: "eem-solution-transformer-core"
line: ["[44, 44]"]

### 问题 6
error_type: "类问题"
error_code: "类问题"
missing_class: "EquipmentForm"
calling_class: "TransformerOverviewService"
old_dependency: "com.cet.eem.fusion.transformer.core.service"
current_dependency: "eem-solution-transformer-core"
line: ["[44, 44]"]

## TransformerOverviewServiceImpl

### 问题 1
error_type: "类问题"
error_code: "类问题"
missing_class: "PecEventExtendVo"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[11, 552, 552, 557, 557, 742, 742, 749, 749]"]

### 问题 2
error_type: "类问题"
error_code: "类问题"
missing_class: "SystemEventWithText"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[12]"]

### 问题 3
error_type: "类问题"
error_code: "类问题"
missing_class: "ConnectionSearchVo"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[13, 241, 241, 241]"]

### 问题 4
error_type: "类问题"
error_code: "类问题"
missing_class: "SystemEventCountVo"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[18, 654, 654, 654]"]

### 问题 5
error_type: "类问题"
error_code: "类问题"
missing_class: "AlarmEventService"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[19]"]

### 问题 6
error_type: "类问题"
error_code: "类问题"
missing_class: "ConfirmCountResult"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[31]"]

### 问题 7
error_type: "类问题"
error_code: "类问题"
missing_class: "EventCountSearchVo"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[32]"]

### 问题 8
error_type: "类问题"
error_code: "类问题"
missing_class: "PecEventCountVo"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[33, 672, 672, 672]"]

### 问题 9
error_type: "类问题"
error_code: "类问题"
missing_class: "PecEventService"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[34, 94, 94]"]

### 问题 10
error_type: "类问题"
error_code: "类问题"
missing_class: "ExpertAnalysisBffService"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[35]"]

### 问题 11
error_type: "类问题"
error_code: "类问题"
missing_class: "PecCoreEventBffService"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[36]"]

### 问题 12
error_type: "类问题"
error_code: "类问题"
missing_class: "Topology1Service"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[41, 80, 80]"]

### 问题 13
error_type: "类问题"
error_code: "类问题"
missing_class: "TopologyCommonService"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[42, 92, 92]"]

### 问题 14
error_type: "类问题"
error_code: "类问题"
missing_class: "QuantityObjectDao"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[44, 84, 84]"]

### 问题 15
error_type: "类问题"
error_code: "类问题"
missing_class: "QuantityDataBatchSearchVo"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[45, 365, 365]"]

### 问题 16
error_type: "类问题"
error_code: "类问题"
missing_class: "QuantityManageService"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[47, 88, 88]"]

### 问题 17
error_type: "类问题"
error_code: "类问题"
missing_class: "PowerTransformerDao"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[49, 76, 76]"]

### 问题 18
error_type: "类问题"
error_code: "类问题"
missing_class: "TransformerindexDataDao"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[50, 90, 90]"]

### 问题 19
error_type: "类问题"
error_code: "类问题"
missing_class: "Constant"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[51, 504, 505, 506, 507, 434, 550, 550, 552, 247, 395, 213, 215, 220, 220, 221, 221, 222, 225, 225, 226, 226, 227, 230, 230, 231, 231, 232, 176, 179, 182, 185, 188, 733, 734, 735, 736, 737, 743, 744, 745, 746, 747]"]

### 问题 20
error_type: "类问题"
error_code: "类问题"
missing_class: "EquipmentStatus"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[52, 170, 170, 170]"]

### 问题 21
error_type: "类问题"
error_code: "类问题"
missing_class: "TransformerlevelEnum"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[53, 509, 509, 509]"]

### 问题 22
error_type: "类问题"
error_code: "类问题"
missing_class: "TransformerOverviewService"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[54, 72]"]

### 问题 23
error_type: "类问题"
error_code: "类问题"
missing_class: "Quantity"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[501, 501, 502, 502, 502]"]

### 问题 24
error_type: "类问题"
error_code: "类问题"
missing_class: "PowerTransformerDto"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[501, 501, 508, 508, 102, 102, 433, 433, 433, 433, 540, 540, 542, 601, 601, 364, 364, 370, 370, 370, 373, 373, 240, 240, 209, 209, 168, 168, 193, 194, 194, 570, 570, 457, 457, 466, 469, 469, 642, 642, 129, 129, 622, 622, 141, 141, 259, 259, 154, 154]"]

### 问题 25
error_type: "类问题"
error_code: "类问题"
missing_class: "ZERO"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[504, 505, 506, 507, 434, 213, 215, 226, 182, 185]"]

### 问题 26
error_type: "类问题"
error_code: "类问题"
missing_class: "LOWTRANSFORMER"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[515]"]

### 问题 27
error_type: "类问题"
error_code: "类问题"
missing_class: "MIDTRANSFORMER"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[518]"]

### 问题 28
error_type: "类问题"
error_code: "类问题"
missing_class: "HIGHTRANSFORMER"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[521]"]

### 问题 29
error_type: "类问题"
error_code: "类问题"
missing_class: "OverviewDataVo"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[99, 99, 101, 101, 101, 125, 125, 128, 128, 128, 138, 138, 140, 140, 140, 150, 150, 153, 153, 153]"]

### 问题 30
error_type: "类问题"
error_code: "类问题"
missing_class: "Operation"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[105, 105, 457, 457, 458, 458, 458, 157, 157]"]

### 问题 31
error_type: "类问题"
error_code: "类问题"
missing_class: "BaseVo"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[671, 671, 688, 688, 608, 608, 240, 240, 243, 243, 243, 248, 210, 210, 335, 335, 335, 335, 580, 580, 642, 642, 643, 653, 653, 628, 628, 265, 265, 266, 266, 267, 267, 269, 269, 270, 270, 273]"]

### 问题 32
error_type: "类问题"
error_code: "类问题"
missing_class: "AggregationResult"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[678, 678, 679, 659, 659, 660]"]

### 问题 33
error_type: "类问题"
error_code: "类问题"
missing_class: "Event"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[540, 540, 541, 541, 541, 601, 601, 602, 602, 602, 570, 570, 571, 571, 571, 622, 622, 623, 623, 623]"]

### 问题 34
error_type: "类问题"
error_code: "类问题"
missing_class: "TWO"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[550, 179]"]

### 问题 35
error_type: "类问题"
error_code: "类问题"
missing_class: "POWERTRANSFORMER"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[550, 552, 247]"]

### 问题 36
error_type: "类问题"
error_code: "类问题"
missing_class: "EquipmentForm"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[314, 314, 364, 364, 166, 166, 259, 259]"]

### 问题 37
error_type: "类问题"
error_code: "类问题"
missing_class: "EquipmentCondition"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[364, 364, 364, 364, 372, 372, 372, 394, 394, 394, 394, 166, 166, 197, 197, 197, 259, 259, 259, 259, 287, 287, 287]"]

### 问题 38
error_type: "类问题"
error_code: "类问题"
missing_class: "NumberCalcUtils"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[380, 471]"]

### 问题 39
error_type: "类问题"
error_code: "类问题"
missing_class: "ONE"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[395, 221, 222, 227, 231, 232, 176, 188]"]

### 问题 40
error_type: "类问题"
error_code: "类问题"
missing_class: "FEN_ZHA"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[220, 225, 226, 230]"]

### 问题 41
error_type: "类问题"
error_code: "类问题"
missing_class: "HE_ZHA"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[220, 221, 225, 230, 231]"]

### 问题 42
error_type: "类问题"
error_code: "类问题"
missing_class: "LOADRATE"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[174]"]

### 问题 43
error_type: "类问题"
error_code: "类问题"
missing_class: "AVERAGEPOWERFACTOR"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[178]"]

### 问题 44
error_type: "类问题"
error_code: "类问题"
missing_class: "OPERATIONRATE"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[181]"]

### 问题 45
error_type: "类问题"
error_code: "类问题"
missing_class: "TransformerindexData"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[195, 195]"]

### 问题 46
error_type: "类问题"
error_code: "类问题"
missing_class: "SYSTEMEVENT"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[733]"]

### 问题 47
error_type: "类问题"
error_code: "类问题"
missing_class: "ENERGYTYPE"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[734]"]

### 问题 48
error_type: "类问题"
error_code: "类问题"
missing_class: "OBJECT_LABEL"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[735]"]

### 问题 49
error_type: "类问题"
error_code: "类问题"
missing_class: "EVENTTIME"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[736, 737, 746, 747]"]

### 问题 50
error_type: "类问题"
error_code: "类问题"
missing_class: "PECEVENTEXTEND"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[743]"]

### 问题 51
error_type: "类问题"
error_code: "类问题"
missing_class: "MONITOREDLABEL"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[744]"]

### 问题 52
error_type: "类问题"
error_code: "类问题"
missing_class: "MONITOREDID"
calling_class: "TransformerOverviewServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[745]"]

### 问题 53
error_type: "类问题"
error_code: "QuantityDataBatchSearchVo_issues"
calling_class: "TransformerOverviewServiceImpl"
usage_pattern: "QuantityDataBatchSearchVo"
suggest: "QuantityDataBatchSearchVo废弃"
line: ["45"]

### 问题 54
error_type: "类问题"
error_code: "QuantityDataBatchSearchVo_issues"
calling_class: "TransformerOverviewServiceImpl"
usage_pattern: "QuantityDataBatchSearchVo"
suggest: "QuantityDataBatchSearchVo废弃"
line: ["365"]

### 问题 55
error_type: "类问题"
error_code: "PecEventExtendVo_issues"
calling_class: "TransformerOverviewServiceImpl"
usage_pattern: "PecEventExtendVo"
line: ["11"]

### 问题 56
error_type: "类问题"
error_code: "PecEventExtendVo_issues"
calling_class: "TransformerOverviewServiceImpl"
usage_pattern: "PecEventExtendVo"
line: ["552"]

### 问题 57
error_type: "类问题"
error_code: "PecEventExtendVo_issues"
calling_class: "TransformerOverviewServiceImpl"
usage_pattern: "PecEventExtendVo"
line: ["557"]

### 问题 58
error_type: "类问题"
error_code: "PecEventExtendVo_issues"
calling_class: "TransformerOverviewServiceImpl"
usage_pattern: "PecEventExtendVo"
line: ["742"]

### 问题 59
error_type: "类问题"
error_code: "PecEventExtendVo_issues"
calling_class: "TransformerOverviewServiceImpl"
usage_pattern: "PecEventExtendVo"
line: ["749"]

### 问题 60
error_type: "类问题"
error_code: "ConnectionSearchVo_issues"
calling_class: "TransformerOverviewServiceImpl"
usage_pattern: "ConnectionSearchVo"
line: ["13"]

### 问题 61
error_type: "类问题"
error_code: "ConnectionSearchVo_issues"
calling_class: "TransformerOverviewServiceImpl"
usage_pattern: "ConnectionSearchVo"
line: ["241"]

### 问题 62
error_type: "类问题"
error_code: "ConnectionSearchVo_issues"
calling_class: "TransformerOverviewServiceImpl"
usage_pattern: "ConnectionSearchVo"
line: ["241"]

### 问题 63
error_type: "类问题"
error_code: "PecEventCountVo_issues"
calling_class: "TransformerOverviewServiceImpl"
usage_pattern: "PecEventCountVo"
line: ["33"]

### 问题 64
error_type: "类问题"
error_code: "PecEventCountVo_issues"
calling_class: "TransformerOverviewServiceImpl"
usage_pattern: "PecEventCountVo"
line: ["672"]

### 问题 65
error_type: "类问题"
error_code: "PecEventCountVo_issues"
calling_class: "TransformerOverviewServiceImpl"
usage_pattern: "PecEventCountVo"
line: ["672"]

### 问题 66
error_type: "物理量查询服务"
error_code: "target_detection"
calling_class: "TransformerOverviewServiceImpl"
calling_method: "TransformerOverviewServiceImpl -> QuantityObjectDao"
suggest: "请使用新的QuantityObjectService替代QuantityObjectDao"
line: ["{'声明': 84, '使用': [280]}"]

### 问题 67
error_type: "物理量查询服务"
error_code: "target_detection"
calling_class: "TransformerOverviewServiceImpl"
calling_method: "TransformerOverviewServiceImpl -> QuantityAggregationDataDao"
suggest: "请使用新的QuantityAggregationDataService替代QuantityAggregationDataDao"
line: ["{'声明': 86, '使用': [317]}"]

### 问题 68
error_type: "物理量查询服务"
error_code: "target_detection"
calling_class: "TransformerOverviewServiceImpl"
calling_method: "TransformerOverviewServiceImpl -> QuantityManageService"
suggest: "QuantityManageService已经废弃"
line: ["{'声明': 88, '使用': [691]}"]

### 问题 69
error_type: "类问题"
error_code: "target_detection"
calling_class: "TransformerOverviewServiceImpl"
calling_method: "TransformerOverviewServiceImpl -> Topology1Service"
suggest: "Topology1Service已经废弃（平台提供了PipeNetworkConnectionServiceImpl需要重构）"
line: ["{'声明': 80, '使用': [442]}"]

### 问题 70
error_type: "类问题"
error_code: "target_detection"
calling_class: "TransformerOverviewServiceImpl"
calling_method: "TransformerOverviewServiceImpl -> PecEventService"
suggest: "PecEventService已经废弃（需要重构）"
line: ["{'声明': 94, '使用': [678]}"]

### 问题 71
error_type: "类问题"
error_code: "target_detection"
calling_class: "TransformerOverviewServiceImpl"
calling_method: "TransformerOverviewServiceImpl -> TopologyCommonService"
suggest: "TopologyCommonService已经废弃"
line: ["{'声明': 92, '使用': [246]}"]

## TransformerTaskServiceImpl

### 问题 1
error_type: "类问题"
error_code: "类问题"
missing_class: "Topology1Service"
calling_class: "TransformerTaskServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[20, 52, 52]"]

### 问题 2
error_type: "类问题"
error_code: "类问题"
missing_class: "QuantityDataBatchSearchVo"
calling_class: "TransformerTaskServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[21, 102, 102, 132, 132, 133, 133, 133]"]

### 问题 3
error_type: "类问题"
error_code: "类问题"
missing_class: "QuantityManageService"
calling_class: "TransformerTaskServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[23, 50, 50]"]

### 问题 4
error_type: "类问题"
error_code: "类问题"
missing_class: "HistoricalLoadBO"
calling_class: "TransformerTaskServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[24]"]

### 问题 5
error_type: "类问题"
error_code: "类问题"
missing_class: "PowerTransformerDTO"
calling_class: "TransformerTaskServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[28]"]

### 问题 6
error_type: "类问题"
error_code: "类问题"
missing_class: "HistoricalLoadVo"
calling_class: "TransformerTaskServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[65, 65, 74, 74, 76, 76, 100, 100, 100, 100, 101, 101, 111, 111, 120, 120, 92, 92, 94, 94, 147, 147, 147, 147, 152, 152, 152, 167, 167, 167]"]

### 问题 7
error_type: "类问题"
error_code: "类问题"
missing_class: "PowerTransformerDto"
calling_class: "TransformerTaskServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[73, 73, 100, 100, 104, 104, 116, 116, 147, 147]"]

### 问题 8
error_type: "类问题"
error_code: "类问题"
missing_class: "NumberCalcUtils"
calling_class: "TransformerTaskServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[156, 171]"]

### 问题 9
error_type: "类问题"
error_code: "QuantityDataBatchSearchVo_issues"
calling_class: "TransformerTaskServiceImpl"
usage_pattern: "QuantityDataBatchSearchVo"
suggest: "QuantityDataBatchSearchVo废弃"
line: ["21"]

### 问题 10
error_type: "类问题"
error_code: "QuantityDataBatchSearchVo_issues"
calling_class: "TransformerTaskServiceImpl"
usage_pattern: "QuantityDataBatchSearchVo"
suggest: "QuantityDataBatchSearchVo废弃"
line: ["102"]

### 问题 11
error_type: "类问题"
error_code: "QuantityDataBatchSearchVo_issues"
calling_class: "TransformerTaskServiceImpl"
usage_pattern: "QuantityDataBatchSearchVo"
suggest: "QuantityDataBatchSearchVo废弃"
line: ["132"]

### 问题 12
error_type: "类问题"
error_code: "QuantityDataBatchSearchVo_issues"
calling_class: "TransformerTaskServiceImpl"
usage_pattern: "QuantityDataBatchSearchVo"
suggest: "QuantityDataBatchSearchVo废弃"
line: ["133"]

### 问题 13
error_type: "类问题"
error_code: "QuantityDataBatchSearchVo_issues"
calling_class: "TransformerTaskServiceImpl"
usage_pattern: "QuantityDataBatchSearchVo"
suggest: "QuantityDataBatchSearchVo废弃"
line: ["133"]

### 问题 14
error_type: "物理量查询服务"
error_code: "target_detection"
calling_class: "TransformerTaskServiceImpl"
calling_method: "TransformerTaskServiceImpl -> QuantityManageService"
suggest: "QuantityManageService已经废弃"
line: ["{'声明': 50}"]

### 问题 15
error_type: "类问题"
error_code: "target_detection"
calling_class: "TransformerTaskServiceImpl"
calling_method: "TransformerTaskServiceImpl -> Topology1Service"
suggest: "Topology1Service已经废弃（平台提供了PipeNetworkConnectionServiceImpl需要重构）"
line: ["{'声明': 52}"]

## TransformerindexData

### 问题 1
error_type: "类问题"
error_code: "类问题"
missing_class: "EntityWithName"
calling_class: "TransformerindexData"
old_dependency: "com.cet.eem.fusion.transformer.core.entity.po"
current_dependency: "eem-solution-transformer-core"
line: ["[11]"]

## TransformerindexDataDaoImpl

### 问题 1
error_type: "类问题"
error_code: "类问题"
missing_class: "QueryCondition"
calling_class: "TransformerindexDataDaoImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.dao.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[50, 50, 31, 31, 39, 39]"]

### 问题 2
error_type: "类问题"
error_code: "类问题"
missing_class: "ParentQueryConditionBuilder"
calling_class: "TransformerindexDataDaoImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.dao.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[50, 31, 39]"]

### 问题 3
error_type: "类问题"
error_code: "类问题"
missing_class: "Constant"
calling_class: "TransformerindexDataDaoImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.dao.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[50, 51, 52, 53, 31, 32, 39, 40, 41, 42, 43]"]

### 问题 4
error_type: "类问题"
error_code: "类问题"
missing_class: "TRANSFORMERINDEXDATA"
calling_class: "TransformerindexDataDaoImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.dao.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[50, 31, 39]"]

### 问题 5
error_type: "类问题"
error_code: "类问题"
missing_class: "LOGTIME"
calling_class: "TransformerindexDataDaoImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.dao.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[51, 32, 40]"]

### 问题 6
error_type: "类问题"
error_code: "类问题"
missing_class: "COLUMN_AGGREGATION_CYCLE"
calling_class: "TransformerindexDataDaoImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.dao.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[52]"]

### 问题 7
error_type: "类问题"
error_code: "类问题"
missing_class: "TYPE"
calling_class: "TransformerindexDataDaoImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.dao.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[53, 42]"]

### 问题 8
error_type: "类问题"
error_code: "类问题"
missing_class: "AGGREGATIONCYCLE"
calling_class: "TransformerindexDataDaoImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.dao.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[41]"]

### 问题 9
error_type: "类问题"
error_code: "类问题"
missing_class: "VALUE"
calling_class: "TransformerindexDataDaoImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.dao.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[43]"]

### 问题 10
error_type: "类问题"
error_code: "类问题"
missing_class: "EemPoiRecord"
calling_class: "TransformerindexDataDaoImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.dao.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[59, 59, 59, 59, 60, 60]"]

### 问题 11
error_type: "类问题"
error_code: "类问题"
missing_class: "ModelServiceUtils"
calling_class: "TransformerindexDataDaoImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.dao.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[22, 22]"]

## TransformerindexDataServiceImpl

### 问题 1
error_type: "类问题"
error_code: "类问题"
missing_class: "ProjectService"
calling_class: "TransformerindexDataServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[10]"]

### 问题 2
error_type: "类问题"
error_code: "类问题"
missing_class: "Topology1Service"
calling_class: "TransformerindexDataServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[27, 81, 81]"]

### 问题 3
error_type: "类问题"
error_code: "类问题"
missing_class: "QuantityObjectDao"
calling_class: "TransformerindexDataServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[29, 75, 75]"]

### 问题 4
error_type: "类问题"
error_code: "类问题"
missing_class: "QuantityDataBatchSearchVo"
calling_class: "TransformerindexDataServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[30, 465, 465]"]

### 问题 5
error_type: "类问题"
error_code: "类问题"
missing_class: "TransformerindexDataPOService"
calling_class: "TransformerindexDataServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[31]"]

### 问题 6
error_type: "类问题"
error_code: "类问题"
missing_class: "PowerTransformerDao"
calling_class: "TransformerindexDataServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[33, 69, 69]"]

### 问题 7
error_type: "类问题"
error_code: "类问题"
missing_class: "TransformerindexDataPODao"
calling_class: "TransformerindexDataServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[34]"]

### 问题 8
error_type: "类问题"
error_code: "类问题"
missing_class: "PowerTransformerDto"
calling_class: "TransformerindexDataServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[35, 457, 457, 460, 460, 468, 468, 100, 100, 111, 111, 123, 123, 162, 162, 162, 162, 142, 142, 176, 176, 540, 540, 252, 252, 257, 257, 380, 380]"]

### 问题 9
error_type: "类问题"
error_code: "类问题"
missing_class: "TransformerindexDataPOPO"
calling_class: "TransformerindexDataServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[36]"]

### 问题 10
error_type: "类问题"
error_code: "类问题"
missing_class: "Constant"
calling_class: "TransformerindexDataServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[37, 460, 472, 184, 191, 194, 196, 583, 381]"]

### 问题 11
error_type: "类问题"
error_code: "类问题"
missing_class: "DateUtil"
calling_class: "TransformerindexDataServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[38, 638, 318]"]

### 问题 12
error_type: "类问题"
error_code: "类问题"
missing_class: "TransformerAnalysisServiceImpl"
calling_class: "TransformerindexDataServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[39, 73, 73, 542, 543]"]

### 问题 13
error_type: "类问题"
error_code: "类问题"
missing_class: "TransformerOverviewServiceImpl"
calling_class: "TransformerindexDataServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[40, 67, 67]"]

### 问题 14
error_type: "类问题"
error_code: "类问题"
missing_class: "TransformerTaskServiceImpl"
calling_class: "TransformerindexDataServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[55]"]

### 问题 15
error_type: "类问题"
error_code: "类问题"
missing_class: "TransformerindexData"
calling_class: "TransformerindexDataServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[277, 277, 278, 278, 457, 457, 458, 458, 470, 470, 471, 232, 232, 239, 204, 204, 204, 204, 207, 207, 209, 209, 209, 212, 212, 184, 184, 190, 190, 192, 192, 194, 194, 195, 195, 197, 197, 656, 656, 656, 656, 540, 540, 567, 567, 575, 575, 583, 583, 583, 380, 380]"]

### 问题 16
error_type: "类问题"
error_code: "类问题"
missing_class: "ONEHUNDRED"
calling_class: "TransformerindexDataServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[460]"]

### 问题 17
error_type: "类问题"
error_code: "类问题"
missing_class: "NumberCalcUtils"
calling_class: "TransformerindexDataServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[471]"]

### 问题 18
error_type: "类问题"
error_code: "类问题"
missing_class: "ONE"
calling_class: "TransformerindexDataServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[472, 184, 191, 381]"]

### 问题 19
error_type: "类问题"
error_code: "类问题"
missing_class: "TransformerindexDataDao"
calling_class: "TransformerindexDataServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[71, 71]"]

### 问题 20
error_type: "类问题"
error_code: "类问题"
missing_class: "TWO"
calling_class: "TransformerindexDataServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[194, 196, 583]"]

### 问题 21
error_type: "类问题"
error_code: "类问题"
missing_class: "DATAINFO"
calling_class: "TransformerindexDataServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[542]"]

### 问题 22
error_type: "类问题"
error_code: "类问题"
missing_class: "DATA_LINK"
calling_class: "TransformerindexDataServiceImpl"
old_dependency: "com.cet.eem.fusion.transformer.core.service.impl"
current_dependency: "eem-solution-transformer-core"
line: ["[543]"]

### 问题 23
error_type: "类问题"
error_code: "QuantityDataBatchSearchVo_issues"
calling_class: "TransformerindexDataServiceImpl"
usage_pattern: "QuantityDataBatchSearchVo"
suggest: "QuantityDataBatchSearchVo废弃"
line: ["30"]

### 问题 24
error_type: "类问题"
error_code: "QuantityDataBatchSearchVo_issues"
calling_class: "TransformerindexDataServiceImpl"
usage_pattern: "QuantityDataBatchSearchVo"
suggest: "QuantityDataBatchSearchVo废弃"
line: ["465"]

### 问题 25
error_type: "类问题"
error_code: "DateUtil_issues"
calling_class: "TransformerindexDataServiceImpl"
usage_pattern: "DateUtil"
suggest: "请使用TimeUtil重构"
line: ["38"]

### 问题 26
error_type: "类问题"
error_code: "DateUtil_issues"
calling_class: "TransformerindexDataServiceImpl"
usage_pattern: "DateUtil"
suggest: "请使用TimeUtil重构"
line: ["318"]

### 问题 27
error_type: "类问题"
error_code: "DateUtil_issues"
calling_class: "TransformerindexDataServiceImpl"
usage_pattern: "DateUtil"
suggest: "请使用TimeUtil重构"
line: ["638"]

### 问题 28
error_type: "物理量查询服务"
error_code: "target_detection"
calling_class: "TransformerindexDataServiceImpl"
calling_method: "TransformerindexDataServiceImpl -> QuantityObjectDao"
suggest: "请使用新的QuantityObjectService替代QuantityObjectDao"
line: ["{'声明': 75, '使用': [562]}"]

### 问题 29
error_type: "物理量查询服务"
error_code: "target_detection"
calling_class: "TransformerindexDataServiceImpl"
calling_method: "TransformerindexDataServiceImpl -> QuantityAggregationDataDao"
suggest: "请使用新的QuantityAggregationDataService替代QuantityAggregationDataDao"
line: ["{'声明': 77, '使用': [615]}"]

### 问题 30
error_type: "类问题"
error_code: "target_detection"
calling_class: "TransformerindexDataServiceImpl"
calling_method: "TransformerindexDataServiceImpl -> Topology1Service"
suggest: "Topology1Service已经废弃（平台提供了PipeNetworkConnectionServiceImpl需要重构）"
line: ["{'声明': 81, '使用': [593]}"]
