"""
统一日志配置系统

提供统一的日志配置、格式化和管理功能。
基于error_prone_json_reporter的日志系统，适配源码上下文提取器的需求。
"""

import os
import sys
import logging
import logging.handlers
import time
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any
from contextlib import contextmanager

from .models import ExtractorConfig, ProcessingStats


class ColoredFormatter(logging.Formatter):
    """带颜色的日志格式化器（仅在控制台输出时使用）"""
    
    # ANSI颜色代码
    COLORS = {
        'DEBUG': '\033[36m',    # 青色
        'INFO': '\033[32m',     # 绿色
        'WARNING': '\033[33m',  # 黄色
        'ERROR': '\033[31m',    # 红色
        'CRITICAL': '\033[35m', # 紫色
        'RESET': '\033[0m'      # 重置
    }
    
    def format(self, record):
        # 添加颜色
        if hasattr(record, 'levelname') and record.levelname in self.COLORS:
            record.levelname = f"{self.COLORS[record.levelname]}{record.levelname}{self.COLORS['RESET']}"
        
        return super().format(record)


class ProgressReporter:
    """进度报告器"""
    
    def __init__(self, logger: logging.Logger):
        self.logger = logger
        self.start_time = None
        self.last_report_time = None
        self.report_interval = 10  # 每10秒报告一次进度
    
    def start(self, total_items: int, task_name: str = "处理"):
        """开始进度跟踪"""
        self.total_items = total_items
        self.processed_items = 0
        self.task_name = task_name
        self.start_time = time.time()
        self.last_report_time = self.start_time
        
        self.logger.info(f"开始{task_name}，总计 {total_items} 项")
    
    def update(self, processed_count: int = 1):
        """更新进度"""
        self.processed_items += processed_count
        current_time = time.time()
        
        # 检查是否需要报告进度
        if (current_time - self.last_report_time) >= self.report_interval:
            self._report_progress()
            self.last_report_time = current_time
    
    def finish(self):
        """完成进度跟踪"""
        if self.start_time:
            total_time = time.time() - self.start_time
            rate = self.processed_items / total_time if total_time > 0 else 0
            
            self.logger.info(
                f"{self.task_name}完成: {self.processed_items}/{self.total_items} 项, "
                f"耗时 {total_time:.2f}秒, 速度 {rate:.2f} 项/秒"
            )
    
    def _report_progress(self):
        """报告当前进度"""
        if self.total_items > 0:
            progress_percent = (self.processed_items / self.total_items) * 100
            elapsed_time = time.time() - self.start_time
            
            if self.processed_items > 0:
                estimated_total_time = elapsed_time * self.total_items / self.processed_items
                remaining_time = estimated_total_time - elapsed_time
                
                self.logger.info(
                    f"{self.task_name}进度: {self.processed_items}/{self.total_items} "
                    f"({progress_percent:.1f}%), 预计剩余时间: {remaining_time:.1f}秒"
                )


class LoggingManager:
    """日志管理器"""
    
    def __init__(self):
        self.loggers = {}
        self.progress_reporters = {}
        self.configured = False
    
    def setup_logging(self, config: ExtractorConfig) -> logging.Logger:
        """
        设置日志系统
        
        Args:
            config: 配置对象
            
        Returns:
            根日志器
        """
        if self.configured:
            return logging.getLogger()
        
        # 确保日志目录存在
        log_dir = os.path.dirname(config.log_file)
        if log_dir:
            Path(log_dir).mkdir(parents=True, exist_ok=True)
        
        # 获取根日志器
        root_logger = logging.getLogger()
        root_logger.setLevel(getattr(logging, config.log_level.upper()))
        
        # 清除现有处理器
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
        
        # 创建格式化器
        file_formatter = logging.Formatter(config.log_format)
        console_formatter = ColoredFormatter(config.log_format)
        
        # 控制台处理器
        if config.console_output:
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setFormatter(console_formatter)
            console_handler.setLevel(getattr(logging, config.log_level.upper()))
            root_logger.addHandler(console_handler)
        
        # 文件处理器
        if config.file_output:
            # 使用RotatingFileHandler避免日志文件过大
            file_handler = logging.handlers.RotatingFileHandler(
                config.log_file,
                maxBytes=10*1024*1024,  # 10MB
                backupCount=5,
                encoding='utf-8'
            )
            file_handler.setFormatter(file_formatter)
            file_handler.setLevel(getattr(logging, config.log_level.upper()))
            root_logger.addHandler(file_handler)
        
        # 添加未捕获异常处理
        sys.excepthook = self._handle_uncaught_exception
        
        self.configured = True
        
        # 记录日志系统启动信息
        root_logger.info("=" * 60)
        root_logger.info("源码上下文提取器启动")
        root_logger.info(f"日志级别: {config.log_level}")
        root_logger.info(f"控制台输出: {config.console_output}")
        root_logger.info(f"文件输出: {config.file_output}")
        if config.file_output:
            root_logger.info(f"日志文件: {config.log_file}")
        root_logger.info("=" * 60)
        
        return root_logger
    
    def get_logger(self, name: str) -> logging.Logger:
        """
        获取指定名称的日志器
        
        Args:
            name: 日志器名称
            
        Returns:
            日志器实例
        """
        if name not in self.loggers:
            self.loggers[name] = logging.getLogger(name)
        return self.loggers[name]
    
    def get_progress_reporter(self, name: str) -> ProgressReporter:
        """
        获取进度报告器
        
        Args:
            name: 报告器名称
            
        Returns:
            进度报告器实例
        """
        if name not in self.progress_reporters:
            logger = self.get_logger(name)
            self.progress_reporters[name] = ProgressReporter(logger)
        return self.progress_reporters[name]
    
    def _handle_uncaught_exception(self, exc_type, exc_value, exc_traceback):
        """处理未捕获的异常"""
        if issubclass(exc_type, KeyboardInterrupt):
            # 用户中断，正常处理
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
            return
        
        # 记录未捕获的异常
        logger = logging.getLogger()
        logger.critical(
            "未捕获的异常",
            exc_info=(exc_type, exc_value, exc_traceback)
        )
    
    @contextmanager
    def log_execution_time(self, logger: logging.Logger, operation_name: str):
        """
        记录操作执行时间的上下文管理器
        
        Args:
            logger: 日志器
            operation_name: 操作名称
        """
        start_time = time.time()
        logger.info(f"开始{operation_name}")
        
        try:
            yield
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"{operation_name}失败，耗时 {execution_time:.2f}秒: {e}")
            raise
        else:
            execution_time = time.time() - start_time
            logger.info(f"{operation_name}完成，耗时 {execution_time:.2f}秒")
    
    def log_system_info(self, logger: logging.Logger):
        """记录系统信息"""
        import platform
        try:
            import psutil
            memory_info = True
        except ImportError:
            memory_info = False
        
        logger.info("系统信息:")
        logger.info(f"  操作系统: {platform.system()} {platform.release()}")
        logger.info(f"  Python版本: {platform.python_version()}")
        
        if memory_info:
            logger.info(f"  CPU核心数: {psutil.cpu_count()}")
            logger.info(f"  内存总量: {psutil.virtual_memory().total / (1024**3):.1f} GB")
            logger.info(f"  可用内存: {psutil.virtual_memory().available / (1024**3):.1f} GB")
    
    def log_config_summary(self, logger: logging.Logger, config: ExtractorConfig):
        """记录配置摘要"""
        logger.info("配置摘要:")
        logger.info(f"  源码路径: {config.src_path}")
        logger.info(f"  遗留源码路径: {config.legacy_src_path}")
        logger.info(f"  知识库路径: {config.knowledge_base_path}")
        logger.info(f"  输出路径: {config.output_path}")
        logger.info(f"  启用AST解析: {config.enable_ast_parsing}")
        logger.info(f"  启用文本回退: {config.enable_text_fallback}")
        logger.info(f"  最大上下文行数: {config.max_context_lines}")
        logger.info(f"  超时时间: {config.timeout_seconds}秒")


class StatisticsReporter:
    """统计信息报告器"""
    
    def __init__(self, logger: logging.Logger):
        self.logger = logger
        self.stats = ProcessingStats()
        self.start_time = None
    
    def start_processing(self):
        """开始处理统计"""
        self.start_time = time.time()
        self.stats = ProcessingStats()
    
    def update_stats(self, **kwargs):
        """更新统计信息"""
        for key, value in kwargs.items():
            if hasattr(self.stats, key):
                setattr(self.stats, key, value)
    
    def increment_stats(self, **kwargs):
        """增量更新统计信息"""
        for key, value in kwargs.items():
            if hasattr(self.stats, key):
                current_value = getattr(self.stats, key)
                setattr(self.stats, key, current_value + value)
    
    def finish_processing(self):
        """完成处理统计"""
        if self.start_time:
            self.stats.processing_time = time.time() - self.start_time
    
    def log_final_statistics(self):
        """记录最终统计信息"""
        self.logger.info("=" * 60)
        self.logger.info("执行统计摘要")
        self.logger.info("=" * 60)
        
        if self.stats.total_errors > 0:
            self.logger.info(f"错误处理: {self.stats.processed_errors}/{self.stats.total_errors} "
                           f"({(self.stats.processed_errors/self.stats.total_errors)*100:.1f}%)")
        
        if self.stats.processed_errors > 0:
            success_rate = self.stats.get_success_rate() * 100
            self.logger.info(f"提取成功率: {success_rate:.1f}% "
                           f"({self.stats.successful_extractions}/{self.stats.processed_errors})")
        
        if self.stats.processing_time > 0:
            processing_rate = self.stats.get_processing_rate()
            self.logger.info(f"处理速度: {processing_rate:.2f} 项/秒")
            self.logger.info(f"总耗时: {self.stats.processing_time:.2f} 秒")
        
        self.logger.info("=" * 60)


# 全局日志管理器实例
logging_manager = LoggingManager()


def setup_logging(config: ExtractorConfig) -> logging.Logger:
    """设置日志系统的便捷函数"""
    return logging_manager.setup_logging(config)


def get_logger(name: str) -> logging.Logger:
    """获取日志器的便捷函数"""
    return logging_manager.get_logger(name)


def get_progress_reporter(name: str) -> ProgressReporter:
    """获取进度报告器的便捷函数"""
    return logging_manager.get_progress_reporter(name)