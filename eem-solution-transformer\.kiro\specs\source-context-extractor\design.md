# Design Document

## Overview

源码上下文提取器是一个专门用于Java代码迁移分析的工具，它能够从错误报告JSON文件中提取详细的源码信息。该工具通过AST语法树分析和文本解析相结合的方式，准确定位缺失方法的原始实现，并生成包含完整上下文信息的结构化文档。

核心功能包括：
- 解析target_method_test.json格式的错误报告
- 在新框架和迁移前代码中定位源文件
- 使用AST分析提取方法源码和上下文
- 生成结构化的Markdown分析报告

## Architecture

### 系统架构图

```mermaid
graph TB
    A[JSON错误报告] --> B[输入解析器]
    B --> C[文件定位器]
    C --> D[源码分析器]
    D --> E[AST解析器]
    D --> F[文本解析器]
    E --> G[上下文提取器]
    F --> G
    G --> H[报告生成器]
    H --> I[Markdown输出]
    
    J[新框架源码] --> C
    K[迁移前源码] --> C
    L[知识库] --> G
```

### 核心组件

1. **输入解析器 (InputParser)**
   - 解析target_method_test.json文件
   - 验证JSON格式和必需字段
   - 提取错误位置信息

2. **文件定位器 (FileLocator)**
   - 在新框架源码中查找错误文件
   - 在迁移前源码中搜索原始实现
   - 处理路径映射和文件查找

3. **源码分析器 (SourceAnalyzer)**
   - 协调AST和文本解析
   - 提取方法定义和上下文
   - 处理解析异常和备选方案

4. **AST解析器 (ASTParser)**
   - 使用javalang库解析Java源码
   - 精确定位类和方法
   - 提取方法签名、参数、返回类型

5. **上下文提取器 (ContextExtractor)**
   - 提取方法源码和注释
   - 分析方法依赖和调用关系
   - 结合知识库信息

6. **报告生成器 (ReportGenerator)**
   - 生成结构化的Markdown文档
   - 格式化JSON输出
   - 处理多个错误项的批量输出

## Components and Interfaces

### 核心接口定义

```python
from abc import ABC, abstractmethod
from typing import List, Dict, Optional
from dataclasses import dataclass

@dataclass
class ErrorItem:
    """错误项数据模型"""
    package: str
    class_name: str
    missing_method: str
    in_param: Dict[str, str]
    out_return: str
    line: List[int]
    context: str
    method_signature: str
    location: Dict[str, any]

@dataclass
class MethodInfo:
    """方法信息数据模型"""
    missing_method: str
    in_param: Dict[str, str]
    out_return: str
    context: str
    content: str
    notes: str
    source_file: str
    line_number: int

class InputParserInterface(ABC):
    @abstractmethod
    def parse_json_file(self, file_path: str) -> List[ErrorItem]:
        """解析JSON错误报告文件"""
        pass

class FileLocatorInterface(ABC):
    @abstractmethod
    def find_source_file(self, file_path: str, search_paths: List[str]) -> Optional[str]:
        """查找源文件"""
        pass
    
    @abstractmethod
    def read_file_content(self, file_path: str) -> str:
        """读取文件内容"""
        pass

class SourceAnalyzerInterface(ABC):
    @abstractmethod
    def analyze_method(self, file_content: str, method_name: str, line_number: int) -> Optional[MethodInfo]:
        """分析方法信息"""
        pass

class ReportGeneratorInterface(ABC):
    @abstractmethod
    def generate_report(self, method_infos: List[MethodInfo], output_path: str) -> str:
        """生成分析报告"""
        pass
```

### 配置管理

```python
@dataclass
class ExtractorConfig:
    """提取器配置"""
    src_path: str = "E:/work/project/fusion/new-code"
    legacy_src_path: str = "E:/work/project/fusion/old-code"
    knowledge_base_path: str = "知识库"
    output_path: str = "source_context_analysis.md"
    enable_ast_parsing: bool = True
    enable_text_fallback: bool = True
    max_context_lines: int = 10
    timeout_seconds: int = 30
```

## Data Models

### 输入数据模型

```python
@dataclass
class ErrorLocation:
    """错误位置信息"""
    file: str
    line: int
    column: int

@dataclass
class ErrorItem:
    """错误项完整信息"""
    package: str
    class_name: str
    missing_method: str
    in_param: Dict[str, str]
    out_return: str
    line: List[int]
    context: str
    method_signature: str
    location: ErrorLocation
    
    @classmethod
    def from_dict(cls, data: dict) -> 'ErrorItem':
        """从字典创建ErrorItem对象"""
        location = ErrorLocation(**data['location'])
        return cls(
            package=data['package'],
            class_name=data['class'],
            missing_method=data['missing_method'],
            in_param=data['in_param'],
            out_return=data['out_return'],
            line=data['line'],
            context=data['context'],
            method_signature=data['method_signature'],
            location=location
        )
```

### 输出数据模型

```python
@dataclass
class MethodAnalysisResult:
    """方法分析结果"""
    missing_method: str
    in_param: Dict[str, str]
    out_return: str
    context: str
    content: str
    notes: str
    source_file: str
    line_number: int
    analysis_status: str  # "success", "partial", "failed"
    error_message: Optional[str] = None
    
    def to_dict(self) -> dict:
        """转换为字典格式"""
        return {
            "missing_method": self.missing_method,
            "in_param": self.in_param,
            "out_return": self.out_return,
            "context": self.context,
            "content": self.content,
            "notes": self.notes
        }
```

### AST节点模型

```python
@dataclass
class MethodNode:
    """方法AST节点信息"""
    name: str
    parameters: List[Dict[str, str]]
    return_type: str
    modifiers: List[str]
    annotations: List[str]
    body: str
    javadoc: Optional[str]
    start_line: int
    end_line: int
    
@dataclass
class ClassNode:
    """类AST节点信息"""
    name: str
    package: str
    imports: List[str]
    methods: List[MethodNode]
    fields: List[Dict[str, str]]
    annotations: List[str]
```

## Error Handling

### 异常处理策略

1. **文件访问异常**
   ```python
   class FileAccessError(Exception):
       """文件访问异常"""
       pass
   
   class SourceFileNotFoundError(FileAccessError):
       """源文件未找到异常"""
       pass
   ```

2. **解析异常处理**
   ```python
   class ParseError(Exception):
       """解析异常基类"""
       pass
   
   class ASTParseError(ParseError):
       """AST解析异常"""
       pass
   
   class JSONParseError(ParseError):
       """JSON解析异常"""
       pass
   ```

3. **异常处理流程**
   - AST解析失败时自动切换到文本解析
   - 文件未找到时记录错误并继续处理其他项
   - 提供详细的错误日志和处理建议

### 容错机制

1. **多路径搜索**
   - 在多个可能的源码目录中搜索文件
   - 支持相对路径和绝对路径的灵活匹配


3. **数据验证**
   - 输入JSON格式验证
   - 必需字段完整性检查
   - 输出数据格式验证

## Testing Strategy

### 单元测试

1. **输入解析测试**
   ```python
   def test_parse_valid_json():
       """测试有效JSON解析"""
       pass
   
   def test_parse_invalid_json():
       """测试无效JSON处理"""
       pass
   
   def test_missing_required_fields():
       """测试缺失必需字段处理"""
       pass
   ```

2. **文件定位测试**
   ```python
   def test_find_existing_file():
       """测试查找存在的文件"""
       pass
   
   def test_file_not_found():
       """测试文件未找到处理"""
       pass
   
   def test_multiple_search_paths():
       """测试多路径搜索"""
       pass
   ```

3. **AST解析测试**
   ```python
   def test_parse_simple_method():
       """测试解析简单方法"""
       pass
   
   def test_parse_complex_method():
       """测试解析复杂方法"""
       pass
   
   def test_parse_with_annotations():
       """测试解析带注解的方法"""
       pass
   ```

### 集成测试

1. **端到端测试**
   - 使用真实的target_method_test.json文件
   - 验证完整的处理流程
   - 检查输出文档的格式和内容

2. **性能测试**
   - 大文件处理性能
   - 批量错误项处理效率
   - 内存使用优化

### 测试数据

1. **示例输入文件**
   ```json
   [
     {
       "package": "com.cet.piem.service.impl",
       "class": "NodeServiceImpl",
       "missing_method": "getProjectTree",
       "in_param": {"energyType": "java.lang.String"},
       "out_return": "java.util.List<ProjectNode>",
       "line": [466],
       "context": "Method call in service implementation",
       "method_signature": "getProjectTree(energyType: java.lang.String) -> java.util.List",
       "location": {
         "file": "/src/main/java/com/cet/piem/service/impl/NodeServiceImpl.java",
         "line": 466,
         "column": 25
       }
     }
   ]
   ```

2. **预期输出格式**
   ```markdown
   # 源码上下文分析报告
   
   ## 方法分析结果
   
   ### 1. getProjectTree 方法
   
   ```json
   {
     "missing_method": "getProjectTree",
     "in_param": {"energyType": "java.lang.String"},
     "out_return": "java.util.List<ProjectNode>",
     "context": "Method call in service implementation",
     "content": "public List<ProjectNode> getProjectTree(String energyType) { ... }",
     "notes": "获取项目树结构的方法，根据能源类型过滤"
   }
   ```
   ```

## Implementation Plan

### 开发阶段

1. **第一阶段：核心框架**
   - 实现基本的数据模型
   - 创建配置管理系统
   - 建立日志和异常处理机制

2. **第二阶段：输入处理**
   - 实现JSON解析器
   - 添加数据验证功能
   - 创建错误项数据模型

3. **第三阶段：文件定位**
   - 实现文件搜索功能
   - 支持多路径查找
   - 添加文件内容读取

4. **第四阶段：AST解析**
   - 集成javalang库
   - 实现方法定位和提取
   - 添加上下文分析功能

5. **第五阶段：报告生成**
   - 实现Markdown生成器
   - 格式化JSON输出
   - 添加批量处理支持

6. **第六阶段：测试和优化**
   - 完善单元测试
   - 进行集成测试
   - 性能优化和错误处理改进

### 技术选型

1. **Python库依赖**
   - `javalang`: Java AST解析
   - `pathlib`: 路径处理
   - `json`: JSON数据处理
   - `re`: 正则表达式文本解析
   - `logging`: 日志记录
   - `dataclasses`: 数据模型定义

2. **开发工具**
   - 类型提示支持
   - 单元测试框架
   - 代码格式化工具
   - 文档生成工具

这个设计提供了一个完整的、可扩展的架构，能够满足源码上下文提取的所有需求，同时保持良好的可维护性和可测试性。