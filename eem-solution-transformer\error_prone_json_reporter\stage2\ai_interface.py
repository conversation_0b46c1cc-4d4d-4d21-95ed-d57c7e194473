"""
AI接口模块

提供与AI助手进行语义分析交互的接口
"""

import json
import os
import time
import logging
from typing import Dict, Any, Optional
from pathlib import Path


class AISemanticInterface:
    """AI语义分析接口"""
    
    def __init__(self, interface_dir: str = "ai_interface"):
        """
        初始化AI接口
        
        Args:
            interface_dir: 接口文件目录
        """
        self.logger = logging.getLogger(__name__)
        self.interface_dir = Path(interface_dir)
        self.interface_dir.mkdir(exist_ok=True)
        
        # 请求和响应文件路径
        self.request_file = self.interface_dir / "semantic_request.json"
        self.response_file = self.interface_dir / "semantic_response.json"
        self.status_file = self.interface_dir / "status.json"
        
        self.logger.info(f"AI接口初始化完成，目录: {self.interface_dir}")
    
    def request_semantic_analysis(self, analysis_context: Dict[str, Any], 
                                timeout: int = 30) -> Optional[Dict[str, Any]]:
        """
        请求AI进行语义分析
        
        Args:
            analysis_context: 分析上下文
            timeout: 超时时间（秒）
            
        Returns:
            AI分析结果，如果超时或失败则返回None
        """
        try:
            # 生成请求ID
            request_id = f"req_{int(time.time() * 1000)}"
            
            # 构建请求数据
            request_data = {
                "request_id": request_id,
                "timestamp": time.time(),
                "type": "semantic_analysis",
                "context": analysis_context,
                "instructions": self._build_analysis_instructions(analysis_context)
            }
            
            # 写入请求文件
            with open(self.request_file, 'w', encoding='utf-8') as f:
                json.dump(request_data, f, ensure_ascii=False, indent=2)
            
            # 更新状态
            self._update_status("waiting_for_ai", request_id)
            
            self.logger.info(f"已发送AI语义分析请求: {request_id}")
            
            # 等待AI响应
            response = self._wait_for_response(request_id, timeout)
            
            if response:
                self.logger.info(f"收到AI分析结果: {request_id}")
                return response.get('analysis_result')
            else:
                self.logger.warning(f"AI分析请求超时: {request_id}")
                return None
                
        except Exception as e:
            self.logger.error(f"AI语义分析请求失败: {e}")
            return None
    
    def _build_analysis_instructions(self, context: Dict[str, Any]) -> str:
        """构建分析指令"""
        method_name = context.get('method_name', '')
        class_name = context.get('class_name', '')
        parameters = context.get('parameters', {})
        return_type = context.get('return_type', '')
        
        instructions = f"""
请对以下Java方法进行深度语义分析：

方法信息：
- 方法名: {method_name}
- 所属类: {class_name}
- 参数: {json.dumps(parameters, ensure_ascii=False)}
- 返回类型: {return_type}

上下文信息：
- 当前代码上下文: {context.get('current_code_context')}
- 原始方法代码: {context.get('original_method_code')}
- 错误位置: {context.get('error_location')}

请分析并返回以下内容：
1. business_purpose: 业务目的（这个方法解决什么业务问题？）
2. functional_description: 功能描述（方法具体做什么？）
3. parameter_meanings: 参数含义（每个参数的业务含义）
4. return_value_meaning: 返回值含义（返回值代表什么？）
5. usage_scenarios: 使用场景（在什么情况下会调用这个方法？）
6. business_tags: 业务标签（方法的分类标签）
7. complexity_assessment: 复杂度评估（方法的复杂程度和原因）

请基于你的AI理解能力，而不是简单的规则匹配，进行深度语义分析。
"""
        return instructions
    
    def _wait_for_response(self, request_id: str, timeout: int) -> Optional[Dict[str, Any]]:
        """等待AI响应"""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                if self.response_file.exists():
                    with open(self.response_file, 'r', encoding='utf-8') as f:
                        response_data = json.load(f)
                    
                    if response_data.get('request_id') == request_id:
                        # 清理文件
                        self.response_file.unlink()
                        self._update_status("completed", request_id)
                        return response_data
                
                time.sleep(0.5)  # 等待0.5秒后重试
                
            except Exception as e:
                self.logger.debug(f"等待响应时出错: {e}")
                time.sleep(0.5)
        
        return None
    
    def _update_status(self, status: str, request_id: str):
        """更新状态"""
        try:
            status_data = {
                "status": status,
                "request_id": request_id,
                "timestamp": time.time()
            }
            
            with open(self.status_file, 'w', encoding='utf-8') as f:
                json.dump(status_data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            self.logger.debug(f"更新状态失败: {e}")
    
    def provide_response(self, request_id: str, analysis_result: Dict[str, Any]):
        """
        提供AI分析响应（由AI助手调用）
        
        Args:
            request_id: 请求ID
            analysis_result: 分析结果
        """
        try:
            response_data = {
                "request_id": request_id,
                "timestamp": time.time(),
                "analysis_result": analysis_result
            }
            
            with open(self.response_file, 'w', encoding='utf-8') as f:
                json.dump(response_data, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"AI响应已提供: {request_id}")
            
        except Exception as e:
            self.logger.error(f"提供AI响应失败: {e}")


# 全局AI接口实例
_ai_interface = None

def get_ai_interface() -> AISemanticInterface:
    """获取AI接口实例"""
    global _ai_interface
    if _ai_interface is None:
        _ai_interface = AISemanticInterface()
    return _ai_interface