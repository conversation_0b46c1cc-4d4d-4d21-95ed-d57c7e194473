"""
模糊查找实现
"""
import re
import logging
from typing import List, Dict, Tuple
try:
    from .interfaces import Fuzzy<PERSON>atcherInterface
    from .models import SimilarityResult
except ImportError:
    from interfaces import FuzzyMatcherInterface
    from models import SimilarityResult


class FuzzyMatcher(FuzzyMatcherInterface):
    """模糊查找实现"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 常见的Java类名模式
        self.common_patterns = {
            'service': r'.*Service$',
            'dao': r'.*Dao$',
            'impl': r'.*Impl$',
            'dto': r'.*DTO$',
            'vo': r'.*VO$',
            'entity': r'.*Entity$',
            'po': r'.*Po$',
            'controller': r'.*Controller$',
            'util': r'.*Util[s]?$',
            'helper': r'.*Helper$',
            'manager': r'.*Manager$',
            'config': r'.*Config$'
        }
    
    def find_similar_classes(self, target_class: str, candidates: List[str]) -> List[SimilarityResult]:
        """查找相似的类"""
        self.logger.info(f"开始查找与 {target_class} 相似的类，候选数量: {len(candidates)}")
        
        results = []
        
        for candidate in candidates:
            similarity_score = self._calculate_class_similarity(target_class, candidate)
            
            if similarity_score > 0.3:  # 相似度阈值
                reasons = self._analyze_similarity_reasons(target_class, candidate)
                
                result = SimilarityResult(
                    class_name=self._extract_class_name(candidate),
                    similarity_score=similarity_score,
                    reasons=reasons,
                    full_qualified_name=candidate
                )
                results.append(result)
        
        # 按相似度排序
        results.sort(key=lambda x: x.similarity_score, reverse=True)
        
        self.logger.info(f"找到 {len(results)} 个相似类")
        return results
    
    def calculate_edit_distance_similarity(self, str1: str, str2: str) -> float:
        """计算编辑距离相似度"""
        return self._edit_distance_similarity(str1, str2)
    
    def calculate_substring_similarity(self, str1: str, str2: str) -> float:
        """计算子字符串相似度"""
        return self._substring_similarity(str1, str2)
    
    def compare_class_structures(self, original_class: str, candidate_class: str) -> float:
        """比较类结构相似度 - 包含AI语义理解"""
        self.logger.debug(f"比较类结构: {original_class} vs {candidate_class}")
        
        # 提取类名（去掉包名）
        orig_name = self._extract_class_name(original_class)
        cand_name = self._extract_class_name(candidate_class)
        
        # 计算多个维度的相似度
        name_similarity = self._calculate_name_similarity(orig_name, cand_name)
        pattern_similarity = self._calculate_pattern_similarity(orig_name, cand_name)
        semantic_similarity = self._calculate_semantic_similarity(orig_name, cand_name)
        
        # 新增：AI语义理解相似度
        ai_semantic_similarity = self._calculate_ai_semantic_similarity(orig_name, cand_name)
        
        # 如果能读取到类文件内容，进行结构分析
        structure_similarity = self._analyze_class_structure_similarity(original_class, candidate_class)
        
        # 加权平均 - 增加AI语义理解的权重
        total_similarity = (
            name_similarity * 0.3 +
            pattern_similarity * 0.2 +
            semantic_similarity * 0.15 +
            ai_semantic_similarity * 0.25 +
            structure_similarity * 0.1
        )
        
        self.logger.debug(f"结构相似度: {total_similarity:.3f} (名称:{name_similarity:.2f}, 模式:{pattern_similarity:.2f}, 语义:{semantic_similarity:.2f}, AI语义:{ai_semantic_similarity:.2f}, 结构:{structure_similarity:.2f})")
        return total_similarity
    
    def _calculate_ai_semantic_similarity(self, name1: str, name2: str) -> float:
        """使用AI进行语义相似度分析"""
        try:
            # 基于业务领域的语义理解
            semantic_groups = {
                'energy': ['energy', 'power', 'electric', 'consumption', 'usage', 'meter'],
                'team': ['team', 'group', 'organization', 'dept', 'department'],
                'config': ['config', 'setting', 'parameter', 'option', 'preference'],
                'schedule': ['schedule', 'plan', 'calendar', 'time', 'date'],
                'data': ['data', 'info', 'information', 'record', 'entity'],
                'service': ['service', 'manager', 'handler', 'processor', 'controller'],
                'util': ['util', 'helper', 'tool', 'common', 'base'],
                'vo': ['vo', 'dto', 'po', 'entity', 'model', 'bean'],
                'dao': ['dao', 'repository', 'mapper', 'persistence'],
                'holiday': ['holiday', 'vacation', 'leave', 'break'],
                'classes': ['classes', 'class', 'course', 'lesson', 'shift']
            }
            
            # 将类名转换为小写并分解
            words1 = self._extract_semantic_words(name1.lower())
            words2 = self._extract_semantic_words(name2.lower())
            
            # 计算语义组匹配度
            group_matches = 0
            total_groups = 0
            
            for group_name, keywords in semantic_groups.items():
                has_group1 = any(keyword in word for word in words1 for keyword in keywords)
                has_group2 = any(keyword in word for word in words2 for keyword in keywords)
                
                if has_group1 or has_group2:
                    total_groups += 1
                    if has_group1 and has_group2:
                        group_matches += 1
            
            if total_groups == 0:
                return 0.0
            
            semantic_score = group_matches / total_groups
            
            # 额外的业务逻辑相似度
            business_score = self._calculate_business_logic_similarity(words1, words2)
            
            return (semantic_score * 0.7 + business_score * 0.3)
            
        except Exception as e:
            self.logger.debug(f"AI语义分析失败: {e}")
            return 0.0
    
    def _extract_semantic_words(self, class_name: str) -> List[str]:
        """提取类名中的语义词汇"""
        import re
        # 分解驼峰命名和下划线命名
        words = re.findall(r'[A-Z]?[a-z]+|[A-Z]+(?=[A-Z][a-z]|\b)', class_name)
        words.extend(class_name.split('_'))
        return [word.lower() for word in words if len(word) > 1]
    
    def _calculate_business_logic_similarity(self, words1: List[str], words2: List[str]) -> float:
        """计算业务逻辑相似度"""
        # 业务相关的词汇映射
        business_mappings = {
            'team': ['group', 'organization'],
            'energy': ['power', 'electric'],
            'config': ['setting', 'parameter'],
            'info': ['information', 'data'],
            'schedule': ['plan', 'calendar'],
            'quantity': ['amount', 'count', 'number'],
            'object': ['entity', 'model', 'item']
        }
        
        score = 0.0
        comparisons = 0
        
        for word1 in words1:
            for word2 in words2:
                comparisons += 1
                if word1 == word2:
                    score += 1.0
                else:
                    # 检查业务映射
                    for key, synonyms in business_mappings.items():
                        if (word1 == key and word2 in synonyms) or (word2 == key and word1 in synonyms):
                            score += 0.8
                            break
                        elif word1 in synonyms and word2 in synonyms:
                            score += 0.6
                            break
        
        return score / comparisons if comparisons > 0 else 0.0
    
    def _analyze_class_structure_similarity(self, original_class: str, candidate_class: str) -> float:
        """分析类结构相似度（如果能读取到源码）"""
        try:
            # 尝试读取类文件内容进行结构分析
            # 这里可以扩展为实际读取Java文件并分析方法、字段等
            return 0.0  # 暂时返回0，后续可以实现文件读取和分析
        except Exception as e:
            self.logger.debug(f"类结构分析失败: {e}")
            return 0.0
    
    def _calculate_class_similarity(self, target: str, candidate: str) -> float:
        """计算类的整体相似度"""
        # 提取类名
        target_name = self._extract_class_name(target)
        candidate_name = self._extract_class_name(candidate)
        
        # 多维度相似度计算
        similarities = []
        
        # 1. 字符串编辑距离相似度
        edit_sim = self._edit_distance_similarity(target_name, candidate_name)
        similarities.append(('edit_distance', edit_sim, 0.4))
        
        # 2. 子字符串匹配相似度
        substr_sim = self._substring_similarity(target_name, candidate_name)
        similarities.append(('substring', substr_sim, 0.3))
        
        # 3. 模式匹配相似度
        pattern_sim = self._calculate_pattern_similarity(target_name, candidate_name)
        similarities.append(('pattern', pattern_sim, 0.2))
        
        # 4. 语义相似度
        semantic_sim = self._calculate_semantic_similarity(target_name, candidate_name)
        similarities.append(('semantic', semantic_sim, 0.1))
        
        # 加权计算总相似度
        total_score = sum(score * weight for _, score, weight in similarities)
        
        return min(1.0, max(0.0, total_score))
    
    def _analyze_similarity_reasons(self, target: str, candidate: str) -> List[str]:
        """分析相似性原因"""
        reasons = []
        
        target_name = self._extract_class_name(target)
        candidate_name = self._extract_class_name(candidate)
        
        # 检查完全匹配
        if target_name.lower() == candidate_name.lower():
            reasons.append("类名完全匹配")
            return reasons
        
        # 检查包含关系
        if target_name.lower() in candidate_name.lower():
            reasons.append(f"候选类名包含目标类名")
        elif candidate_name.lower() in target_name.lower():
            reasons.append(f"目标类名包含候选类名")
        
        # 检查前缀匹配
        common_prefix = self._find_common_prefix(target_name, candidate_name)
        if len(common_prefix) >= 3:
            reasons.append(f"共同前缀: {common_prefix}")
        
        # 检查后缀匹配
        common_suffix = self._find_common_suffix(target_name, candidate_name)
        if len(common_suffix) >= 3:
            reasons.append(f"共同后缀: {common_suffix}")
        
        # 检查模式匹配
        target_pattern = self._identify_class_pattern(target_name)
        candidate_pattern = self._identify_class_pattern(candidate_name)
        
        if target_pattern and candidate_pattern and target_pattern == candidate_pattern:
            reasons.append(f"相同类型模式: {target_pattern}")
        
        # 检查驼峰分词相似度
        target_words = self._split_camel_case(target_name)
        candidate_words = self._split_camel_case(candidate_name)
        
        common_words = set(target_words) & set(candidate_words)
        if common_words:
            reasons.append(f"共同词汇: {', '.join(common_words)}")
        
        if not reasons:
            edit_sim = self._edit_distance_similarity(target_name, candidate_name)
            if edit_sim > 0.5:
                reasons.append(f"字符相似度: {edit_sim:.2f}")
        
        return reasons
    
    def _extract_class_name(self, full_name: str) -> str:
        """从完整类名中提取类名"""
        if '.' in full_name:
            return full_name.split('.')[-1]
        return full_name
    
    def _calculate_name_similarity(self, name1: str, name2: str) -> float:
        """计算类名相似度"""
        return self._edit_distance_similarity(name1, name2)
    
    def _calculate_pattern_similarity(self, name1: str, name2: str) -> float:
        """计算模式相似度"""
        pattern1 = self._identify_class_pattern(name1)
        pattern2 = self._identify_class_pattern(name2)
        
        if pattern1 and pattern2:
            return 1.0 if pattern1 == pattern2 else 0.5
        elif pattern1 or pattern2:
            return 0.3
        else:
            return 0.0
    
    def _calculate_semantic_similarity(self, name1: str, name2: str) -> float:
        """计算语义相似度"""
        # 分解驼峰命名
        words1 = set(word.lower() for word in self._split_camel_case(name1))
        words2 = set(word.lower() for word in self._split_camel_case(name2))
        
        if not words1 or not words2:
            return 0.0
        
        # 计算词汇重叠度
        intersection = words1 & words2
        union = words1 | words2
        
        return len(intersection) / len(union) if union else 0.0
    
    def _edit_distance_similarity(self, s1: str, s2: str) -> float:
        """基于编辑距离的相似度"""
        if not s1 or not s2:
            return 0.0
        
        distance = self._edit_distance(s1.lower(), s2.lower())
        max_len = max(len(s1), len(s2))
        
        return 1.0 - (distance / max_len) if max_len > 0 else 0.0
    
    def _substring_similarity(self, s1: str, s2: str) -> float:
        """子字符串相似度"""
        if not s1 or not s2:
            return 0.0
        
        s1_lower = s1.lower()
        s2_lower = s2.lower()
        
        # 检查包含关系
        if s1_lower in s2_lower or s2_lower in s1_lower:
            shorter = min(len(s1), len(s2))
            longer = max(len(s1), len(s2))
            return shorter / longer
        
        # 查找最长公共子序列
        lcs_length = self._longest_common_subsequence(s1_lower, s2_lower)
        max_len = max(len(s1), len(s2))
        
        return lcs_length / max_len if max_len > 0 else 0.0
    
    def _identify_class_pattern(self, class_name: str) -> str:
        """识别类的模式类型"""
        class_name_lower = class_name.lower()
        
        for pattern_name, pattern_regex in self.common_patterns.items():
            if re.match(pattern_regex, class_name, re.IGNORECASE):
                return pattern_name
        
        return None
    
    def _split_camel_case(self, name: str) -> List[str]:
        """分解驼峰命名"""
        # 使用正则表达式分解驼峰命名
        words = re.findall(r'[A-Z]?[a-z]+|[A-Z]+(?=[A-Z][a-z]|\b)', name)
        return [word for word in words if len(word) > 1]
    
    def _find_common_prefix(self, s1: str, s2: str) -> str:
        """查找公共前缀"""
        common = ""
        for i in range(min(len(s1), len(s2))):
            if s1[i].lower() == s2[i].lower():
                common += s1[i]
            else:
                break
        return common
    
    def _find_common_suffix(self, s1: str, s2: str) -> str:
        """查找公共后缀"""
        common = ""
        for i in range(1, min(len(s1), len(s2)) + 1):
            if s1[-i].lower() == s2[-i].lower():
                common = s1[-i] + common
            else:
                break
        return common
    
    def _edit_distance(self, s1: str, s2: str) -> int:
        """计算编辑距离"""
        if len(s1) < len(s2):
            return self._edit_distance(s2, s1)
        
        if len(s2) == 0:
            return len(s1)
        
        previous_row = list(range(len(s2) + 1))
        for i, c1 in enumerate(s1):
            current_row = [i + 1]
            for j, c2 in enumerate(s2):
                insertions = previous_row[j + 1] + 1
                deletions = current_row[j] + 1
                substitutions = previous_row[j] + (c1 != c2)
                current_row.append(min(insertions, deletions, substitutions))
            previous_row = current_row
        
        return previous_row[-1]
    
    def _longest_common_subsequence(self, s1: str, s2: str) -> int:
        """计算最长公共子序列长度"""
        m, n = len(s1), len(s2)
        dp = [[0] * (n + 1) for _ in range(m + 1)]
        
        for i in range(1, m + 1):
            for j in range(1, n + 1):
                if s1[i-1] == s2[j-1]:
                    dp[i][j] = dp[i-1][j-1] + 1
                else:
                    dp[i][j] = max(dp[i-1][j], dp[i][j-1])
        
        return dp[m][n]
    
    def get_best_matches(self, target_class: str, candidates: List[str], max_results: int = 5) -> List[SimilarityResult]:
        """获取最佳匹配结果"""
        all_results = self.find_similar_classes(target_class, candidates)
        
        # 过滤低相似度结果
        filtered_results = [r for r in all_results if r.similarity_score > 0.4]
        
        # 返回前N个结果
        return filtered_results[:max_results]