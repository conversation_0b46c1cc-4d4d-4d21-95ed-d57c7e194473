"""
配置管理器实现

负责配置文件的加载、保存和验证。
支持环境变量覆盖和配置模板生成。
"""

import os
import yaml
import shutil
from typing import Dict, Any, Optional
from pathlib import Path

from ..common.models import Configuration
from ..common.interfaces import ConfigManagerInterface


class ConfigManager(ConfigManagerInterface):
    """配置管理器实现"""
    
    def __init__(self):
        self.default_config_path = "error_prone_json_reporter/config/config.yaml"
        self.env_prefix = "MIGRATION_TOOL_"  # 环境变量前缀
    
    def load_config(self, config_path: str = None) -> Configuration:
        """
        加载配置文件，支持环境变量覆盖
        
        Args:
            config_path: 配置文件路径，如果为None则使用默认路径
            
        Returns:
            配置对象
        """
        if config_path is None:
            config_path = self.default_config_path
        
        # 如果配置文件不存在，创建默认配置
        if not os.path.exists(config_path):
            config = self._create_default_config()
        else:
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    config_data = yaml.safe_load(f)
                
                # 将YAML数据转换为Configuration对象
                config = self._dict_to_config(config_data)
                
            except Exception as e:
                raise ValueError(f"Failed to load config from {config_path}: {e}")
        
        # 应用环境变量覆盖
        config = self._apply_env_overrides(config)
        
        # 验证配置
        config.validate()
        return config
    
    def save_config(self, config: Configuration, config_path: str) -> None:
        """
        保存配置文件
        
        Args:
            config: 配置对象
            config_path: 配置文件路径
        """
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(config_path), exist_ok=True)
            
            # 将Configuration对象转换为字典
            config_dict = self._config_to_dict(config)
            
            with open(config_path, 'w', encoding='utf-8') as f:
                yaml.dump(config_dict, f, default_flow_style=False, 
                         allow_unicode=True, indent=2)
                         
        except Exception as e:
            raise ValueError(f"Failed to save config to {config_path}: {e}")
    
    def merge_cli_args(self, config: Configuration, cli_args: Dict[str, Any]) -> Configuration:
        """
        合并命令行参数到配置
        
        Args:
            config: 基础配置
            cli_args: 命令行参数
            
        Returns:
            合并后的配置
        """
        # 创建配置副本
        merged_config = Configuration(**config.__dict__)
        
        # 合并命令行参数
        for key, value in cli_args.items():
            if value is not None and hasattr(merged_config, key):
                setattr(merged_config, key, value)
        
        merged_config.validate()
        return merged_config
    
    def _create_default_config(self) -> Configuration:
        """创建默认配置"""
        return Configuration(
            project_path="",
            src_path="",
            error_detector="existing_tool",
            existing_tool_output="JavaAnnotator.xml"
        )
    
    def _dict_to_config(self, config_data: Dict[str, Any]) -> Configuration:
        """将字典转换为Configuration对象"""
        # 提取各个部分的配置
        stage1 = config_data.get('stage1', {})
        stage2 = config_data.get('stage2', {})
        output = config_data.get('output', {})
        performance = config_data.get('performance', {})
        logging = config_data.get('logging', {})
        
        return Configuration(
            # 第一阶段配置
            project_path=stage1.get('project_path', ''),
            error_detector=stage1.get('error_detector', 'existing_tool'),
            existing_tool_output=stage1.get('existing_tool_output', 'JavaAnnotator.xml'),
            
            # 第二阶段配置
            src_path=stage2.get('src_path', ''),
            legacy_src_path=stage2.get('legacy_src_path', ''),
            knowledge_base_path=stage2.get('knowledge_base_path', 'knowledge_base'),
            use_ai_enhancement=stage2.get('use_ai_enhancement', True),
            base_init=stage2.get('base_init', False),
            model_name=stage2.get('model_name', 'microsoft/graphcodebert-base'),
            top_k=stage2.get('top_k', 3),
            batch_size=stage2.get('batch_size', 32),
            similarity_threshold=stage2.get('similarity_threshold', 0.5),
            
            # 输出配置
            errors_json=output.get('errors_json', 'output/errors.json'),
            output_json=output.get('migration_json', 'output/migration_suggestions.json'),
            log_file=output.get('log_file', 'logs/migration.log'),
            output_dir=output.get('output_dir', 'output'),
            
            # 性能配置
            use_gpu=performance.get('use_gpu', False),
            cache_vectors=performance.get('cache_vectors', True),
            vector_cache_dir=performance.get('vector_cache_dir', ''),
            max_workers=performance.get('max_workers', 4),
            memory_limit=performance.get('memory_limit', 8192),
            
            # 日志配置
            log_level=logging.get('level', 'INFO'),
            log_format=logging.get('format', '%(asctime)s - %(name)s - %(levelname)s - %(message)s'),
            console_output=logging.get('console', True),
            file_output=logging.get('file', True)
        )
    
    def _config_to_dict(self, config: Configuration) -> Dict[str, Any]:
        """将Configuration对象转换为字典"""
        return {
            'stage1': {
                'error_detector': config.error_detector,
                'existing_tool_output': config.existing_tool_output,
                'project_path': config.project_path
            },
            'stage2': {
                'model_name': config.model_name,
                'src_path': config.src_path,
                'legacy_src_path': config.legacy_src_path,
                'knowledge_base_path': config.knowledge_base_path,
                'use_ai_enhancement': config.use_ai_enhancement,
                'top_k': config.top_k,
                'batch_size': config.batch_size,
                'similarity_threshold': config.similarity_threshold
            },
            'output': {
                'errors_json': config.errors_json,
                'migration_json': config.output_json,
                'log_file': config.log_file,
                'output_dir': config.output_dir
            },
            'performance': {
                'use_gpu': config.use_gpu,
                'cache_vectors': config.cache_vectors,
                'vector_cache_dir': config.vector_cache_dir,
                'max_workers': config.max_workers,
                'memory_limit': config.memory_limit
            },
            'logging': {
                'level': config.log_level,
                'format': config.log_format,
                'console': config.console_output,
                'file': config.file_output
            }
        }
    
    def _apply_env_overrides(self, config: Configuration) -> Configuration:
        """
        应用环境变量覆盖配置
        
        环境变量命名规则：MIGRATION_TOOL_<SECTION>_<KEY>
        例如：MIGRATION_TOOL_STAGE1_PROJECT_PATH
        
        Args:
            config: 基础配置对象
            
        Returns:
            应用环境变量覆盖后的配置对象
        """
        # 环境变量映射表
        env_mappings = {
            # Stage1 配置
            f"{self.env_prefix}STAGE1_PROJECT_PATH": "project_path",
            f"{self.env_prefix}STAGE1_ERROR_DETECTOR": "error_detector",
            f"{self.env_prefix}STAGE1_EXISTING_TOOL_OUTPUT": "existing_tool_output",
            
            # Stage2 配置
            f"{self.env_prefix}STAGE2_SRC_PATH": "src_path",
            f"{self.env_prefix}STAGE2_MODEL_NAME": "model_name",
            f"{self.env_prefix}STAGE2_TOP_K": "top_k",
            f"{self.env_prefix}STAGE2_BATCH_SIZE": "batch_size",
            f"{self.env_prefix}STAGE2_SIMILARITY_THRESHOLD": "similarity_threshold",
            
            # 输出配置
            f"{self.env_prefix}OUTPUT_ERRORS_JSON": "errors_json",
            f"{self.env_prefix}OUTPUT_MIGRATION_JSON": "output_json",
            f"{self.env_prefix}OUTPUT_LOG_FILE": "log_file",
            f"{self.env_prefix}OUTPUT_DIR": "output_dir",
            
            # 性能配置
            f"{self.env_prefix}PERFORMANCE_USE_GPU": "use_gpu",
            f"{self.env_prefix}PERFORMANCE_CACHE_VECTORS": "cache_vectors",
            f"{self.env_prefix}PERFORMANCE_VECTOR_CACHE_DIR": "vector_cache_dir",
            f"{self.env_prefix}PERFORMANCE_MAX_WORKERS": "max_workers",
            f"{self.env_prefix}PERFORMANCE_MEMORY_LIMIT": "memory_limit",
            
            # 日志配置
            f"{self.env_prefix}LOGGING_LEVEL": "log_level",
            f"{self.env_prefix}LOGGING_FORMAT": "log_format",
            f"{self.env_prefix}LOGGING_CONSOLE": "console_output",
            f"{self.env_prefix}LOGGING_FILE": "file_output"
        }
        
        # 创建配置副本
        overridden_config = Configuration(**config.__dict__)
        
        # 应用环境变量覆盖
        for env_var, config_attr in env_mappings.items():
            env_value = os.getenv(env_var)
            if env_value is not None:
                # 类型转换
                converted_value = self._convert_env_value(env_value, config_attr)
                setattr(overridden_config, config_attr, converted_value)
        
        return overridden_config
    
    def _convert_env_value(self, env_value: str, config_attr: str) -> Any:
        """
        转换环境变量值为适当的类型
        
        Args:
            env_value: 环境变量值
            config_attr: 配置属性名
            
        Returns:
            转换后的值
        """
        # 布尔类型字段
        bool_fields = {
            "use_gpu", "cache_vectors", "console_output", "file_output"
        }
        
        # 整数类型字段
        int_fields = {
            "top_k", "batch_size", "max_workers", "memory_limit"
        }
        
        # 浮点数类型字段
        float_fields = {
            "similarity_threshold"
        }
        
        if config_attr in bool_fields:
            return env_value.lower() in ("true", "1", "yes", "on")
        elif config_attr in int_fields:
            try:
                return int(env_value)
            except ValueError:
                raise ValueError(f"Invalid integer value for {config_attr}: {env_value}")
        elif config_attr in float_fields:
            try:
                return float(env_value)
            except ValueError:
                raise ValueError(f"Invalid float value for {config_attr}: {env_value}")
        else:
            return env_value
    
    def generate_config_template(self, output_path: str, include_comments: bool = True) -> None:
        """
        生成配置文件模板
        
        Args:
            output_path: 输出文件路径
            include_comments: 是否包含注释说明
        """
        try:
            # 获取当前模块的目录
            current_dir = os.path.dirname(os.path.abspath(__file__))
            template_path = os.path.join(current_dir, "config.yaml")
            
            # 确保输出目录存在
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            if include_comments and os.path.exists(template_path):
                # 复制带注释的模板文件
                shutil.copy2(template_path, output_path)
            else:
                # 生成基本配置模板
                default_config = self._create_default_config()
                config_dict = self._config_to_dict(default_config)
                
                with open(output_path, 'w', encoding='utf-8') as f:
                    yaml.dump(config_dict, f, default_flow_style=False, 
                             allow_unicode=True, indent=2)
            
            print(f"配置模板已生成: {output_path}")
            
        except Exception as e:
            raise ValueError(f"Failed to generate config template: {e}")
    
    def validate_config_file(self, config_path: str) -> Dict[str, Any]:
        """
        验证配置文件的有效性
        
        Args:
            config_path: 配置文件路径
            
        Returns:
            验证结果字典，包含是否有效和错误信息
        """
        result = {
            "valid": True,
            "errors": [],
            "warnings": []
        }
        
        try:
            # 检查文件是否存在
            if not os.path.exists(config_path):
                result["valid"] = False
                result["errors"].append(f"配置文件不存在: {config_path}")
                return result
            
            # 尝试加载配置
            config = self.load_config(config_path)
            
            # 检查必要的路径是否存在
            if config.project_path and not os.path.exists(config.project_path):
                result["warnings"].append(f"项目路径不存在: {config.project_path}")
            
            if config.src_path and not os.path.exists(config.src_path):
                result["warnings"].append(f"源码路径不存在: {config.src_path}")
            
            if config.existing_tool_output and not os.path.exists(config.existing_tool_output):
                result["warnings"].append(f"现有工具输出文件不存在: {config.existing_tool_output}")
            
            # 检查输出目录是否可写
            output_dir = os.path.dirname(config.errors_json)
            if output_dir and not os.access(output_dir, os.W_OK):
                if not os.path.exists(output_dir):
                    result["warnings"].append(f"输出目录不存在: {output_dir}")
                else:
                    result["warnings"].append(f"输出目录不可写: {output_dir}")
            
        except Exception as e:
            result["valid"] = False
            result["errors"].append(str(e))
        
        return result
    
    def get_env_var_help(self) -> str:
        """
        获取环境变量使用帮助信息
        
        Returns:
            环境变量帮助文本
        """
        help_text = """
环境变量配置覆盖:

可以使用环境变量覆盖配置文件中的设置。环境变量命名规则：
MIGRATION_TOOL_<SECTION>_<KEY>

第一阶段配置:
  MIGRATION_TOOL_STAGE1_PROJECT_PATH         - Java项目路径
  MIGRATION_TOOL_STAGE1_ERROR_DETECTOR       - 错误检测器类型
  MIGRATION_TOOL_STAGE1_EXISTING_TOOL_OUTPUT - 现有工具输出文件

第二阶段配置:
  MIGRATION_TOOL_STAGE2_SRC_PATH             - 新框架源码路径
  MIGRATION_TOOL_STAGE2_MODEL_NAME           - GraphCodeBERT模型名称
  MIGRATION_TOOL_STAGE2_TOP_K                - TOP-K候选数量
  MIGRATION_TOOL_STAGE2_BATCH_SIZE           - 批处理大小
  MIGRATION_TOOL_STAGE2_SIMILARITY_THRESHOLD - 相似度阈值

输出配置:
  MIGRATION_TOOL_OUTPUT_ERRORS_JSON          - 错误清单JSON文件路径
  MIGRATION_TOOL_OUTPUT_MIGRATION_JSON       - 迁移建议JSON文件路径
  MIGRATION_TOOL_OUTPUT_LOG_FILE             - 日志文件路径
  MIGRATION_TOOL_OUTPUT_DIR                  - 输出目录

性能配置:
  MIGRATION_TOOL_PERFORMANCE_USE_GPU         - 是否使用GPU (true/false)
  MIGRATION_TOOL_PERFORMANCE_CACHE_VECTORS   - 是否缓存向量 (true/false)
  MIGRATION_TOOL_PERFORMANCE_VECTOR_CACHE_DIR - 向量缓存目录
  MIGRATION_TOOL_PERFORMANCE_MAX_WORKERS     - 最大工作线程数
  MIGRATION_TOOL_PERFORMANCE_MEMORY_LIMIT    - 内存限制(MB)

日志配置:
  MIGRATION_TOOL_LOGGING_LEVEL               - 日志级别
  MIGRATION_TOOL_LOGGING_FORMAT              - 日志格式
  MIGRATION_TOOL_LOGGING_CONSOLE             - 控制台输出 (true/false)
  MIGRATION_TOOL_LOGGING_FILE                - 文件输出 (true/false)

使用示例:
  export MIGRATION_TOOL_STAGE1_PROJECT_PATH="/path/to/project"
  export MIGRATION_TOOL_STAGE2_SRC_PATH="/path/to/framework"
  export MIGRATION_TOOL_PERFORMANCE_USE_GPU=true
        """
        return help_text.strip()