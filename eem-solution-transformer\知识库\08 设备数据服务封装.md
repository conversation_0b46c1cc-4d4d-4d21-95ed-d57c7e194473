# 08 设备数据服务封装

##  PecCoreNodeManageService

1.  描述该类提供了peccore节点树相关的查询操作（包括权限），包括厂站、通道、设备节点以及映射方案的查询；
    
2.  具体方法：
    

1.  根据测点id查询节点树：queryTreeNodeByMeasureNodes(PecCoreTreeQueryByMeasureNodeDTO pecCoreTreeSearchVo)
    
2.  获取所有的监测设备信息：getAllDevices()
    
3.  根据场站获取监测设备信息：getDeviceByStationId(long stationId, String name)
    
4.  根据通道获取监测设备信息：getDeviceByChannelId(long channelId, String name)
    
5.  查询peccore节点树：queryCloudTreeNodes(long nodeId, long nodeType, boolean async, Collection<Long> tenantIds, boolean loadDevice)
    
6.  从缓存查询所有表计通信状态：queryNodesConnectionCache(PecCoreTreeSearchExtendVO searchVo)
    
7.  区分通信表计与非通信表计，并构建缓存：buildNodesConnectionCache()
    
8.  从节点树中提取表计节点：getOnlyDevice(List<Meter> meterTrees)
    
9.  缓存符合条件的表计节点：cacheMeasureNodeDeviceIds(String key, Set<Integer> dataIdSet)
    
10.  查询关联关系的节点树：queryTreeNodesWithConnection(PecCoreTreeSearchExtendVO searchVo, Long userId)
    
11.  根据节点信息查询节点树(已废弃)：queryTreeNodes(long nodeId, long nodeType, boolean async, Long tenantId, long userId, boolean loadDevice)
    
12.  根据权限查询节点树：queryTreeNodesAuth(PecCoreTreeSearchVo searchVo)