# Java 代码迁移问题处理任务计划

## 工作流概述

本任务计划用于处理 Java 代码迁移后的编译错误和兼容性问题，按照从底层到上层的顺序进行修复。生成一个详细的可以直接执行的修复任务列表。

## 阶段 1: 分析问题并生成修复任务

- [ ] 1. 深度分析每个问题并确定解决方案

  - [x] 1.1 类问题分析和解决方案确定

    - **数据来源**: 直接读取 out\问题列表.md，判断是否属于类问题
    - **完整性要求**: 必须处理所有 类问题，不允许遗漏任何一个问题

    - **第一步：文件结构全面分析**:

      - **文件总览**: 首先使用 grepSearch 工具搜索 out\问题列表.md 中所有的文件名（## 开头的行），建立完整的文件清单
      - **问题类型统计**: 搜索所有 "类问题" 类型的问题，统计总数和分布
      - **文件范围确认**: 确认文件从开头到结尾的完整范围
      - **行号范围记录**: 记录每个文件在 out\问题列表.md 中的起始和结束行号

    - **第二步：系统化分段处理策略**:

      - **严禁一次性加载**: 绝对不要一次性加载整个 out\问题列表.md 文件（2274 行）
      - **按文件分段读取**: 根据第一步建立的文件清单，逐个文件进行处理
      - **每个文件独立处理**: 每次只读取一个文件的所有问题（通常 10-30 个问题）
      - **进度追踪**: 处理每个文件后立即记录进度，确保不跳过任何文件
      - **实时验证**: 每处理完一个文件立即验证问题数量和质量

    - **第三步：逐文件处理流程**:

      - **文件问题提取**: 读取指定文件的所有 类问题 类型问题
      - **知识库查找**: 对每个类问题先在知识库中查找解决方案
      - **类名查找**: 使用 class_name_finder.py 查找替代类
      - **解决方案确定**: 根据查找结果确定具体的修复方案
      - **质量检查**: 确保每个问题都有具体的类名、行号、解决方案

    - **第四步：解决方案分类**:

      - **绿色标记 (🟢)**: 知识库有明确解决方案或单一匹配结果
      - **黄色标记 (🟡)**: 多个候选类，需要使用 class_file_reader.py 进行 AI 智能判断
      - **红色标记 (🔴)**: 已废弃类或无法确定最佳匹配的问题

    - **第五步：输出格式要求**:

      - **文件维度组织**: 按文件名分组，与 out\问题列表.md 保持一致的顺序
      - **详细问题信息**: 每个问题包含位置、解决方案、修复操作、分类依据
      - **格式示例**:

        ```markdown
        ## ClassesConfig.java

        ### 类问题 1: ModelLabel 类导入 (🟢 绿色标记)

        - **问题位置**: 行号 3, 19
        - **缺失类名**: ModelLabel
        - **解决方案**: import com.cet.eem.fusion.common.modelutils.annotation.ModelLabel;
        - **修复操作**: 在文件顶部添加导入语句
        - **分类依据**: 知识库明确指导
        ```

    - **第六步：强制完整性验证**:

      - **处理前统计**: 统计 out\问题列表.md 中所有 类问题 类型问题的总数
      - **文件覆盖验证**: 确认所有包含 类问题 的文件都被处理
      - **数量精确匹配**: 处理的问题总数必须等于源文件中的问题总数
      - **遗漏检查**: 如发现数量不一致，必须找出遗漏的具体文件和问题
      - **端到端验证**: 从第一个文件到最后一个文件（包括 TeamGroupEnergy 等）的完整验证

    - **第七步：防遗漏保障措施**:

      - **文件清单核对**: 建立并维护完整的文件处理清单
      - **进度实时跟踪**: 每处理一个文件立即更新进度状态
      - **交叉验证机制**: 通过多种方式验证处理的完整性
      - **质量检查**: 验证每个解决方案的具体性和可执行性
      - **最终审核**: 处理完成后进行全面的质量和完整性审核

    - _输出: 完整的 out\task-import.md 文件，包含所有 类问题的详细分析和解决方案，确保 100%覆盖_

  - [ ] 1.1.1 类问题解决方案独立验证和完整性检查

    - **任务性质**: 独立验证任务，不依赖任何现有统计数据或报告
    - **验证原则**: 假设 task-import.md 中的统计数据可能不准确，从源头重新验证
    - **完整性要求**: 必须实现 100% 的问题覆盖，不允许任何遗漏

    - **第一步：源文件独立统计**:

      - **直接统计源文件**: 使用 grepSearch 直接从 out\问题列表.md 统计所有 类问题 类型问题
      - **建立权威基准**: 建立独立的、权威的问题总数和文件清单
      - **不信任现有统计**: 完全忽略 task-import.md 中的统计数据，假设其可能不准确
      - **文件覆盖检查**: 确认所有包含 类问题 的文件都在清单中
      - **问题分布分析**: 分析问题在各文件中的分布情况

    - **第二步：逐文件独立验证**:

      - **按文件逐一核对**: 对每个文件的每个 类问题 问题进行独立验证
      - **问题映射检查**: 使用唯一标识（文件名+行号+缺失类名）进行精确匹配
      - **解决方案质量检查**: 验证每个问题是否有具体的类名、行号、解决方案
      - **格式规范检查**: 确保所有问题都遵循统一的详细格式要求

    - **第三步：数量精确匹配验证**:

      - **总数核对**: 验证 task-import.md 中处理的问题总数是否等于源文件中的实际问题总数
      - **文件级核对**: 验证每个文件的问题数量是否完全匹配
      - **分类统计核对**: 验证 🟢🟡🔴 各类别的统计是否准确
      - **遗漏问题识别**: 精确识别任何遗漏的问题并记录详细信息

    - **第四步：质量标准严格检查**:

      - **禁止笼统描述检查**: 识别并标记所有笼统、概括的描述
      - **必需信息完整性检查**: 确保每个问题包含具体的缺失类名、精确行号、完整解决方案
      - **解决方案可执行性检查**: 验证每个解决方案是否具体、可执行
      - **分类准确性验证**: 验证 🟢🟡🔴 标记的准确性和依据

    - **第五步：生成详细验证报告**:

      - **按文件逐一报告**: 为每个文件生成详细的验证结果
      - **问题数量确认**: 每个文件的问题数量和源文件做精确对比
      - **遗漏问题清单**: 如发现遗漏，提供具体的文件名、行号、缺失类名
      - **验证结论**: 明确说明验证是否通过，如不通过则详细说明问题

    - **严格验证标准**:

      - **数量匹配**: 处理问题数 = 源文件实际问题数（必须 100% 匹配）
      - **文件覆盖**: 所有包含 类问题 的文件都必须被处理
      - **质量标准**: 每个问题都有具体的类名、行号、解决方案
      - **格式规范**: 统一的详细格式，无笼统描述
      - **可执行性**: 所有解决方案都具体、详细、可执行

    - _目标: 通过独立验证确保 task-import.md 100% 覆盖所有类问题，每个问题都有具体、详细、可执行的解决方案，杜绝任何笼统概括_
    - **严格验证要求**: 必须确保每个具体的 类 问题都有详细的解决方案，绝对不允许笼统概括
    - **文件级别验证流程**:

      - **步骤 1**: 从 out\问题列表.md 中提取所有包含“类问题”的文件清单
      - **步骤 2**: 逐个文件进行验证，对每个文件的每个 类 问题执行以下检查：

        **必须的详细格式要求**:

        ```markdown
        ### TeamConfigServiceImpl.java

        #### 类 问题 1: ParamUtils 类导入 (🟢 绿色标记)

        - **问题位置**: 行号 3
        - **缺失类名**: ParamUtils
        - **解决方案**: import com.cet.eem.fusion.common.utils.ParamUtils;
        - **修复操作**: 在文件顶部添加导入语句
        - **分类依据**: 建议明确且唯一

        #### 类 问题 2: BusinessBaseException 类导入 (🟢 绿色标记)

        - **问题位置**: 行号 4, 76, 140, 158, 297, 302, 315, 344, 360, 429, 450, 465, 475, 502
        - **缺失类名**: BusinessBaseException
        - **解决方案**: import com.cet.eem.fusion.common.exception.BusinessBaseException;
        - **修复操作**: 在文件顶部添加导入语句
        - **分类依据**: 建议明确且唯一
        ```

    - **严格检查项目**:

      - **问题数量精确核对**: 确认每个文件类所有 类 问题在 task-import.md 中都有对应的详细解决方案
      - **问题映射精确匹配**: 使用唯一标识（文件名+行号+缺失类名）进行一对一精确匹配
      - **解决方案具体性检查**: 每个问题必须包含：缺失类名、具体行号、完整 import 语句、修复操作、分类依据
      - **禁止笼统描述**: 绝对不允许"其他各种类导入"、"统一使用项目内部包路径"等笼统描述
      - **分类准确性**: 验证 🟢🟡🔴 标记的准确性和依据
      - **知识库匹配验证**: 根据知识库\能管代码迁移知识库.md 验证解决方案的正确性

    - **全局汇总验证**:

      - **文件覆盖验证**: 确认所有包含 类问题 的文件都在 task-import.md 中有对应
      - **总数验证**: 所有文件的 类问题数量之和 = out\问题列表.md 中的 类问题 总数
      - **分类统计验证**: 🟢🟡🔴 各类别的总数统计正确

    - **生成详细验证报告**:

      - **按文件逐一报告**: 为每个类文件生成详细的验证结果，列出每个问题的处理状态
      - **问题数量确认**：每个类文件的问题数量和 out\问题列表.md 的问题数量做对比，确认类问题数量一致
      - **遗漏问题清单**: 如发现遗漏，必须提供具体的文件名、行号、缺失类名和需要补充的解决方案
      - **笼统描述识别**: 识别并标记所有笼统、概括的描述，要求具体化
      - **全局统计**: 汇总所有 类 问题的处理统计和质量评估

    - _目标: 确保每个文件的每个 类问题都有具体、详细、可执行的解决方案，杜绝任何笼统概括_

  - [ ] 1.1.2 类问题解决方案独立验证和完整性校验

    - **执行条件**: 仅当 1.1.1 验证检查发现遗漏或不一致时才执行此任务
    - **判断标准**: 如果 1.1.1 验证报告显示"验证通过"，则跳过此任务
    - **严格修复要求**: 绝对不允许概括、笼统的描述，每个问题必须具体到类名、行号、解决方案

    - **具体修复内容**:

      - **逐个问题补充**: 根据 1.1.1 验证报告中的遗漏清单，逐个补充每个具体的 类 问题
      - **必须包含的详细信息**:

        - 具体的缺失类名（如 ParamUtils、BusinessBaseException 等）
        - 精确的行号位置（如行号 3、行号 4,76,140 等）
        - 完整的 import 语句（如 import com.cet.eem.fusion.common.utils.ParamUtils;）
        - 具体的修复操作（如"在文件顶部添加导入语句"）
        - 明确的分类依据（如"建议明确且唯一"、"知识库明确指导"等）

      - **禁止的笼统描述**:

        - ❌ "其他各种类导入"
        - ❌ "统一使用项目内部包路径"
        - ❌ "根据具体类型使用相应的导入路径"
        - ❌ "Import 问题 5-N"

      - **必须的具体描述**:
        - ✅ "类 问题 5: ColumnDef 类导入"
        - ✅ "类 问题 6: BaseVo 类导入"
        - ✅ "类 问题 7: TimeUtil 类导入"

    - **修复执行步骤**:

      1. **读取验证报告**: 从 1.1.1 验证报告中提取所有遗漏问题的详细信息
      2. **逐个文件修复**: 按文件维度，逐个补充遗漏的 类 问题解决方案
      3. **确保格式一致**: 所有补充的问题必须遵循统一的详细格式
      4. **数量精确匹配**: 确保修复后的问题数量与源文件完全匹配，无遗漏
      5. **质量检查**: 检查每个补充的解决方案是否具体、可执行

    - **重新验证**: 修复完成后重新执行 1.1.1 验证，确保：

      - 所有 类 问题都有具体的解决方案
      - 没有任何笼统、概括的描述
      - 问题数量完全匹配
      - 解决方案质量符合要求

    - _输出: 修复后的完整 out\task-import.md 文件，每个 类 问题都有具体、详细、可执行的解决方案_

  - [ ] 1.3 消息推送变更问题分析和解决方案确定

    - **数据来源**: 直接读取 out\问题列表.md，处理消息推送变更相关的问题
    - **问题范围**: 基于知识库第 3 类"消息推送变更"，包括：
      - MessagePushUtils 完全废弃问题
      - messagePushUtils.pushToWeb 方法调用问题
      - 新的 WebNotification 服务替换方案
    - **完整性要求**: 必须处理所有消息推送变更相关的问题，不允许遗漏任何一个问题

    - **第一步：文件结构全面分析**:

      - **问题类型搜索**: 使用 grepSearch 工具搜索 out\问题列表.md 中所有 MessagePushUtils 相关问题
      - **文件范围确认**: 确认所有包含消息推送变更问题的文件清单
      - **问题分布统计**: 统计消息推送变更问题的总数和分布情况
      - **行号范围记录**: 记录每个相关文件在源文件中的位置

    - **第二步：系统化分段处理策略**:

      - **严禁一次性加载**: 绝对不要一次性加载整个 out\问题列表.md 文件
      - **按文件分段读取**: 根据第一步建立的文件清单，逐个文件进行处理
      - **每个文件独立处理**: 每次只读取一个文件的所有消息推送问题
      - **进度追踪**: 处理每个文件后立即记录进度，确保不跳过任何文件
      - **实时验证**: 每处理完一个文件立即验证问题数量和质量

    - **第三步：逐文件处理流程**:

      - **问题识别**: 识别文件中所有 MessagePushUtils 相关问题
      - **知识库查找**: 基于知识库第 3 类"消息推送变更"的解决方案
      - **解决方案确定**: 根据知识库指导确定具体的修复方案
      - **质量检查**: 确保每个问题都有具体的类名、方法名、行号、解决方案

    - **第四步：解决方案分类**:

      - **绿色标记 (🟢)**: 知识库有明确解决方案的问题
      - **红色标记 (🔴)**: 无法确定解决方案或需要业务逻辑修改的问题

    - **第五步：输出格式要求**:

      - **文件维度组织**: 按文件名分组，与 out\问题列表.md 保持一致的顺序
      - **详细问题信息**: 每个问题包含位置、解决方案、修复操作、分类依据

    - **第六步：强制完整性验证**:

      - **处理前统计**: 统计所有消息推送变更问题的总数
      - **文件覆盖验证**: 确认所有包含相关问题的文件都被处理
      - **数量精确匹配**: 处理的问题总数必须等于源文件中的问题总数
      - **端到端验证**: 完整验证所有消息推送变更问题的处理情况

    - **第七步：防遗漏保障措施**:

      - **文件清单核对**: 建立并维护完整的文件处理清单
      - **进度实时跟踪**: 每处理一个文件立即更新进度状态
      - **交叉验证机制**: 通过多种方式验证处理的完整性
      - **质量检查**: 验证每个解决方案的具体性和可执行性
      - **最终审核**: 处理完成后进行全面的质量和完整性审核

    - _输出: 完整的 out\task-message.md 文件，包含所有消息推送变更问题的详细分析和解决方案，确保 100%覆盖_

  - [ ] 1.3.1 消息推送变更问题独立验证和完整性检查

    - **任务性质**: 独立验证任务，不依赖任何现有统计数据或报告
    - **验证原则**: 假设 task-message.md 中的统计数据可能不准确，从源头重新验证
    - **完整性要求**: 必须实现 100% 的问题覆盖，不允许任何遗漏
    
    - **第一步：源文件独立统计**:
      - **直接统计源文件**: 使用 grepSearch 直接从 out\问题列表.md 统计所有 MessagePushUtils 相关问题
      - **建立权威基准**: 建立独立的、权威的问题总数和文件清单
      - **不信任现有统计**: 完全忽略 task-message.md 中的统计数据，假设其可能不准确
      - **文件覆盖检查**: 确认所有包含消息推送变更问题的文件都在清单中
    
    - **第二步：逐文件独立验证**:
      - **按文件逐一核对**: 对每个文件的每个消息推送变更问题进行独立验证
      - **问题映射检查**: 使用唯一标识（文件名+行号+废弃类名/方法名）进行精确匹配
      - **解决方案质量检查**: 验证每个问题是否有具体的类名、方法名、行号、解决方案
      - **格式规范检查**: 确保所有问题都遵循统一的详细格式要求
    
    - **第三步：数量精确匹配验证**:
      - **总数核对**: 验证 task-message.md 中处理的问题总数是否等于源文件中的实际问题总数
      - **文件级核对**: 验证每个文件的问题数量是否完全匹配
      - **分类统计核对**: 验证 🟢🔴 各类别的统计是否准确
      - **遗漏问题识别**: 精确识别任何遗漏的问题并记录详细信息
    
    - **第四步：质量标准严格检查**:
      - **禁止笼统描述检查**: 识别并标记所有笼统、概括的描述
      - **必需信息完整性检查**: 确保每个问题包含具体的废弃类名/方法名、精确行号、完整解决方案
      - **解决方案可执行性检查**: 验证每个解决方案是否具体、可执行
      - **知识库匹配验证**: 根据知识库第 3 条"消息推送变更"验证解决方案的正确性
    
    - **第五步：生成详细验证报告**:
      - **按文件逐一报告**: 为每个文件生成详细的验证结果
      - **问题数量确认**: 每个文件的问题数量和源文件做精确对比
      - **遗漏问题清单**: 如发现遗漏，提供具体的文件名、行号、废弃类名/方法名
      - **验证结论**: 明确说明验证是否通过，如不通过则详细说明问题
    
    - **严格验证标准**:
      - **数量匹配**: 处理问题数 = 源文件实际问题数（必须 100% 匹配）
      - **文件覆盖**: 所有包含消息推送变更问题的文件都必须被处理
      - **质量标准**: 每个问题都有具体的类名、方法名、行号、解决方案
      - **格式规范**: 统一的详细格式，无笼统描述
      - **可执行性**: 所有解决方案都具体、详细、可执行

    - **全局汇总验证**:

      - **文件覆盖验证**: 确认所有包含消息推送变更问题的文件都在 task-message.md 中有对应
      - **总数验证**: 所有文件的消息推送问题数量之和 = out\问题列表.md 中的消息推送问题总数
      - **分类统计验证**: 🟢🔴 各类别的总数统计正确

    - **生成详细验证报告**:

      - **按文件逐一报告**: 为每个文件生成详细的验证结果，列出每个消息推送问题的处理状态
      - **遗漏问题清单**: 如发现遗漏，必须提供具体的文件名、行号、废弃类名/方法名和需要补充的解决方案
      - **笼统描述识别**: 识别并标记所有笼统、概括的描述，要求具体化
      - **全局统计**: 汇总所有消息推送问题的处理统计和质量评估

    - _目标: 确保每个文件的每个消息推送变更问题都有具体、详细、可执行的解决方案，杜绝任何笼统概括_

  - [ ] 1.3.2 修复 task-message.md 遗漏问题 (条件执行)

    - **执行条件**: 仅当 1.3.1 验证检查发现遗漏或不一致时才执行此任务
    - **判断标准**: 如果 1.3.1 验证报告显示"验证通过"，则跳过此任务
    - **严格修复要求**: 绝对不允许概括、笼统的描述，每个消息推送问题必须具体到类名、方法名、行号、解决方案

    - **具体修复内容**:

      - **逐个问题补充**: 根据 1.3.1 验证报告中的遗漏清单，逐个补充每个具体的消息推送变更问题
      - **必须包含的详细信息**:

        - 具体的废弃类名/方法名（如 MessagePushUtils、messagePushUtils.pushToWeb 等）
        - 精确的行号位置（如行号 45、行号 67,89 等）
        - 完整的替换方案（如使用 WebNotification 服务替换）
        - 具体的修复操作（如"替换导入语句"、"修改服务调用"等）
        - 明确的分类依据（如"知识库第 3 条明确废弃"等）

      - **禁止的笼统描述**:

        - ❌ "消息推送相关问题"
        - ❌ "统一替换为新服务"
        - ❌ "消息推送变更问题 1-N"

      - **必须的具体描述**:
        - ✅ "消息推送问题 1: MessagePushUtils 完全废弃"
        - ✅ "消息推送问题 2: messagePushUtils.pushToWeb 方法废弃"
        - ✅ "消息推送问题 3: 特定业务场景的推送逻辑修改"

    - **修复执行步骤**:

      1. **读取验证报告**: 从 1.3.1 验证报告中提取所有遗漏的消息推送问题详细信息
      2. **逐个文件修复**: 按文件维度，逐个补充遗漏的消息推送问题解决方案
      3. **确保格式一致**: 所有补充的问题必须遵循统一的详细格式
      4. **数量精确匹配**: 确保修复后的问题数量与源文件完全匹配，无遗漏
      5. **质量检查**: 检查每个补充的解决方案是否具体、可执行

    - **重新验证**: 修复完成后重新执行 1.3.1 验证，确保：

      - 所有消息推送问题都有具体的解决方案
      - 没有任何笼统、概括的描述
      - 问题数量完全匹配
      - 解决方案质量符合要求

    - _输出: 修复后的完整 out\task-message.md 文件，每个消息推送问题都有具体、详细、可执行的解决方案_

  - [ ] 1.4 权限 ID 调整问题分析和解决方案确定

    - **数据来源**: 直接读取 out\问题列表.md，处理权限 ID 调整相关的问题
    - **问题范围**: 基于知识库第 4 类"权限 ID 调整"，包括：
      - @OperationLog(operationType 相关的权限 ID 问题
      - 权限 ID 需要在 10000-20000 之间的调整
      - 常量类中权限 ID 值的修改
    - **完整性要求**: 必须处理所有权限 ID 调整相关的问题，不允许遗漏任何一个问题

    - **第一步：文件结构全面分析**:

      - **问题类型搜索**: 使用 grepSearch 工具搜索 out\问题列表.md 中所有权限 ID 相关问题
      - **文件范围确认**: 确认所有包含权限 ID 调整问题的文件清单
      - **问题分布统计**: 统计权限 ID 调整问题的总数和分布情况
      - **行号范围记录**: 记录每个相关文件在源文件中的位置

    - **第二步：系统化分段处理策略**:

      - **严禁一次性加载**: 绝对不要一次性加载整个 out\问题列表.md 文件
      - **按文件分段读取**: 根据第一步建立的文件清单，逐个文件进行处理
      - **每个文件独立处理**: 每次只读取一个文件的所有权限 ID 问题
      - **进度追踪**: 处理每个文件后立即记录进度，确保不跳过任何文件
      - **实时验证**: 每处理完一个文件立即验证问题数量和质量

    - **第三步：逐文件处理流程**:

      - **问题识别**: 识别文件中所有权限 ID 调整相关问题
      - **知识库查找**: 基于知识库第 4 类"权限 ID 调整"的解决方案
      - **解决方案确定**: 根据知识库指导确定具体的修复方案
      - **质量检查**: 确保每个问题都有具体的方法名、当前 ID、建议 ID、解决方案

    - **第四步：解决方案分类**:

      - **绿色标记 (🟢)**: 知识库有明确解决方案的问题
      - **红色标记 (🔴)**: 无法确定解决方案的问题

    - **第五步：输出格式要求**:

      - **文件维度组织**: 按文件名分组，与 out\问题列表.md 保持一致的顺序
      - **详细问题信息**: 每个问题包含位置、解决方案、修复操作、分类依据

    - **第六步：强制完整性验证**:

      - **处理前统计**: 统计所有权限 ID 调整问题的总数
      - **文件覆盖验证**: 确认所有包含相关问题的文件都被处理
      - **数量精确匹配**: 处理的问题总数必须等于源文件中的问题总数
      - **端到端验证**: 完整验证所有权限 ID 调整问题的处理情况

    - **第七步：防遗漏保障措施**:

      - **文件清单核对**: 建立并维护完整的文件处理清单
      - **进度实时跟踪**: 每处理一个文件立即更新进度状态
      - **交叉验证机制**: 通过多种方式验证处理的完整性
      - **质量检查**: 验证每个解决方案的具体性和可执行性
      - **最终审核**: 处理完成后进行全面的质量和完整性审核

    - _输出: 完整的 out\task-permission.md 文件，包含所有权限 ID 调整问题的详细分析和解决方案，确保 100%覆盖_

  - [ ] 1.4.1 权限 ID 调整问题独立验证和完整性检查

    - **任务性质**: 独立验证任务，不依赖任何现有统计数据或报告
    - **验证原则**: 假设 task-permission.md 中的统计数据可能不准确，从源头重新验证
    - **完整性要求**: 必须实现 100% 的问题覆盖，不允许任何遗漏
    
    - **第一步：源文件独立统计**:
      - **直接统计源文件**: 使用 grepSearch 直接从 out\问题列表.md 统计所有权限 ID 相关问题
      - **建立权威基准**: 建立独立的、权威的问题总数和文件清单
      - **不信任现有统计**: 完全忽略 task-permission.md 中的统计数据，假设其可能不准确
      - **文件覆盖检查**: 确认所有包含权限 ID 调整问题的文件都在清单中
    
    - **第二步：逐文件独立验证**:
      - **按文件逐一核对**: 对每个文件的每个权限 ID 调整问题进行独立验证
      - **问题映射检查**: 使用唯一标识（文件名+行号+方法名+当前权限 ID）进行精确匹配
      - **解决方案质量检查**: 验证每个问题是否有具体的方法名、当前 ID、建议 ID、解决方案
      - **格式规范检查**: 确保所有问题都遵循统一的详细格式要求
    
    - **第三步：数量精确匹配验证**:
      - **总数核对**: 验证 task-permission.md 中处理的问题总数是否等于源文件中的实际问题总数
      - **文件级核对**: 验证每个文件的问题数量是否完全匹配
      - **分类统计核对**: 验证 🟢🔴 各类别的统计是否准确
      - **遗漏问题识别**: 精确识别任何遗漏的问题并记录详细信息
    
    - **第四步：质量标准严格检查**:
      - **禁止笼统描述检查**: 识别并标记所有笼统、概括的描述
      - **必需信息完整性检查**: 确保每个问题包含具体的方法名、当前权限 ID、建议权限 ID、完整解决方案
      - **解决方案可执行性检查**: 验证每个解决方案是否具体、可执行
      - **知识库匹配验证**: 根据知识库第 4 条"权限 ID 调整"验证解决方案的正确性
    
    - **第五步：生成详细验证报告**:
      - **按文件逐一报告**: 为每个文件生成详细的验证结果
      - **问题数量确认**: 每个文件的问题数量和源文件做精确对比
      - **遗漏问题清单**: 如发现遗漏，提供具体的文件名、行号、方法名、当前权限 ID
      - **验证结论**: 明确说明验证是否通过，如不通过则详细说明问题
    
    - **严格验证标准**:
      - **数量匹配**: 处理问题数 = 源文件实际问题数（必须 100% 匹配）
      - **文件覆盖**: 所有包含权限 ID 调整问题的文件都必须被处理
      - **质量标准**: 每个问题都有具体的方法名、当前 ID、建议 ID、解决方案
      - **格式规范**: 统一的详细格式，无笼统描述
      - **可执行性**: 所有解决方案都具体、详细、可执行

    - **全局汇总验证**:

      - **文件覆盖验证**: 确认所有包含权限 ID 调整问题的文件都在 task-permission.md 中有对应
      - **总数验证**: 所有文件的权限 ID 调整问题数量之和 = out\问题列表.md 中的权限 ID 调整问题总数
      - **分类统计验证**: 🟢🔴 各类别的总数统计正确

    - **生成详细验证报告**:

      - **按文件逐一报告**: 为每个文件生成详细的验证结果，列出每个权限 ID 问题的处理状态
      - **遗漏问题清单**: 如发现遗漏，必须提供具体的文件名、行号、方法名、当前权限 ID 和需要补充的解决方案
      - **笼统描述识别**: 识别并标记所有笼统、概括的描述，要求具体化
      - **全局统计**: 汇总所有权限 ID 问题的处理统计和质量评估

    - _目标: 确保每个文件的每个权限 ID 调整问题都有具体、详细、可执行的解决方案，杜绝任何笼统概括_

  - [ ] 1.4.2 修复 task-permission.md 遗漏问题 (条件执行)

    - **执行条件**: 仅当 1.4.1 验证检查发现遗漏或不一致时才执行此任务
    - **判断标准**: 如果 1.4.1 验证报告显示"验证通过"，则跳过此任务
    - **严格修复要求**: 绝对不允许概括、笼统的描述，每个权限 ID 问题必须具体到方法名、当前 ID、建议 ID、解决方案

    - **具体修复内容**:

      - **逐个问题补充**: 根据 1.4.1 验证报告中的遗漏清单，逐个补充每个具体的权限 ID 调整问题
      - **必须包含的详细信息**:

        - 具体的问题方法名（如 addOrUpdateSchedulingScheme、deleteSchedulingScheme 等）
        - 精确的行号位置（如行号 40、行号 68 等）
        - 当前权限 ID 值（如 25001、30002 等）
        - 建议的权限 ID 值（如 15001、15002 等，必须在 10000-20000 范围内）
        - 具体的修复操作（如@OperationLog(operationType = 15001)）
        - 明确的分类依据（如"知识库第 4 条权限 ID 调整规则"等）

      - **禁止的笼统描述**:

        - ❌ "权限 ID 相关问题"
        - ❌ "统一调整权限 ID"
        - ❌ "权限 ID 问题 1-N"

      - **必须的具体描述**:
        - ✅ "权限 ID 问题 1: addOrUpdateSchedulingScheme 方法权限 ID 超范围"
        - ✅ "权限 ID 问题 2: deleteSchedulingScheme 方法权限 ID 超范围"
        - ✅ "权限 ID 问题 3: saveSchedulingSchemeRelatedHoliday 方法权限 ID 超范围"

    - **修复执行步骤**:

      1. **读取验证报告**: 从 1.4.1 验证报告中提取所有遗漏的权限 ID 问题详细信息
      2. **逐个文件修复**: 按文件维度，逐个补充遗漏的权限 ID 问题解决方案
      3. **确保格式一致**: 所有补充的问题必须遵循统一的详细格式
      4. **数量精确匹配**: 确保修复后的问题数量与源文件完全匹配，无遗漏
      5. **质量检查**: 检查每个补充的解决方案是否具体、可执行

    - **重新验证**: 修复完成后重新执行 1.4.1 验证，确保：

      - 所有权限 ID 问题都有具体的解决方案
      - 没有任何笼统、概括的描述
      - 问题数量完全匹配
      - 解决方案质量符合要求

    - _输出: 修复后的完整 out\task-permission.md 文件，每个权限 ID 问题都有具体、详细、可执行的解决方案_

  - [ ] 1.5 单位服务变更问题分析和解决方案确定

    - **数据来源**: 直接读取 out\问题列表.md，处理单位服务变更相关的问题
    - **问题范围**: 基于知识库第 5 类"单位服务变更"，包括：
      - UnitService → EnergyUnitService 的包和类名变更
      - UserDefineUnit → UserDefineUnitDTO 的实体变更
      - getUnit 方法 → queryUnitCoef 方法的签名变更
      - 业务规则适配（能耗、产量分开查询）
    - **完整性要求**: 必须处理所有单位服务变更相关的问题，不允许遗漏任何一个问题

    - **第一步：文件结构全面分析**:

      - **问题类型搜索**: 使用 grepSearch 工具搜索 out\问题列表.md 中所有 UnitService、UserDefineUnit 相关问题
      - **文件范围确认**: 确认所有包含单位服务变更问题的文件清单
      - **问题分布统计**: 统计单位服务变更问题的总数和分布情况
      - **行号范围记录**: 记录每个相关文件在源文件中的位置

    - **第二步：系统化分段处理策略**:

      - **严禁一次性加载**: 绝对不要一次性加载整个 out\问题列表.md 文件
      - **按文件分段读取**: 根据第一步建立的文件清单，逐个文件进行处理
      - **每个文件独立处理**: 每次只读取一个文件的所有单位服务问题
      - **进度追踪**: 处理每个文件后立即记录进度，确保不跳过任何文件
      - **实时验证**: 每处理完一个文件立即验证问题数量和质量

    - **第三步：逐文件处理流程**:

      - **问题识别**: 识别文件中所有单位服务变更相关问题
      - **知识库查找**: 基于知识库第 5 类"单位服务变更"的解决方案
      - **解决方案确定**: 根据知识库指导确定具体的修复方案
      - **质量检查**: 确保每个问题都有具体的服务名、实体名、方法名、解决方案

    - **第四步：解决方案分类**:

      - **绿色标记 (🟢)**: 知识库有明确解决方案的问题
      - **红色标记 (🔴)**: 无法确定解决方案或需要业务逻辑修改的问题

    - **第五步：输出格式要求**:

      - **文件维度组织**: 按文件名分组，与 out\问题列表.md 保持一致的顺序
      - **详细问题信息**: 每个问题包含位置、解决方案、修复操作、分类依据

    - **第六步：强制完整性验证**:

      - **处理前统计**: 统计所有单位服务变更问题的总数
      - **文件覆盖验证**: 确认所有包含相关问题的文件都被处理
      - **数量精确匹配**: 处理的问题总数必须等于源文件中的问题总数
      - **端到端验证**: 完整验证所有单位服务变更问题的处理情况

    - **第七步：防遗漏保障措施**:

      - **文件清单核对**: 建立并维护完整的文件处理清单
      - **进度实时跟踪**: 每处理一个文件立即更新进度状态
      - **交叉验证机制**: 通过多种方式验证处理的完整性
      - **质量检查**: 验证每个解决方案的具体性和可执行性
      - **最终审核**: 处理完成后进行全面的质量和完整性审核

    - _输出: 完整的 out\task-unit.md 文件，包含所有单位服务变更问题的详细分析和解决方案，确保 100%覆盖_

  - [ ] 1.5.1 单位服务变更问题独立验证和完整性检查

    - **任务性质**: 独立验证任务，不依赖任何现有统计数据或报告
    - **验证原则**: 假设 task-unit.md 中的统计数据可能不准确，从源头重新验证
    - **完整性要求**: 必须实现 100% 的问题覆盖，不允许任何遗漏
    
    - **第一步：源文件独立统计**:
      - **直接统计源文件**: 使用 grepSearch 直接从 out\问题列表.md 统计所有 UnitService、UserDefineUnit 相关问题
      - **建立权威基准**: 建立独立的、权威的问题总数和文件清单
      - **不信任现有统计**: 完全忽略 task-unit.md 中的统计数据，假设其可能不准确
      - **文件覆盖检查**: 确认所有包含单位服务变更问题的文件都在清单中
    
    - **第二步：逐文件独立验证**:
      - **按文件逐一核对**: 对每个文件的每个单位服务变更问题进行独立验证
      - **问题映射检查**: 使用唯一标识（文件名+行号+废弃服务名/实体名）进行精确匹配
      - **解决方案质量检查**: 验证每个问题是否有具体的服务名、实体名、方法名、解决方案
      - **格式规范检查**: 确保所有问题都遵循统一的详细格式要求
    
    - **第三步：数量精确匹配验证**:
      - **总数核对**: 验证 task-unit.md 中处理的问题总数是否等于源文件中的实际问题总数
      - **文件级核对**: 验证每个文件的问题数量是否完全匹配
      - **分类统计核对**: 验证 🟢🟡🔴 各类别的统计是否准确
      - **遗漏问题识别**: 精确识别任何遗漏的问题并记录详细信息
    
    - **第四步：质量标准严格检查**:
      - **禁止笼统描述检查**: 识别并标记所有笼统、概括的描述
      - **必需信息完整性检查**: 确保每个问题包含具体的服务名、实体名、方法名、完整解决方案
      - **解决方案可执行性检查**: 验证每个解决方案是否具体、可执行
      - **知识库匹配验证**: 根据知识库第 5 条"单位服务变更"验证解决方案的正确性
    
    - **第五步：生成详细验证报告**:
      - **按文件逐一报告**: 为每个文件生成详细的验证结果
      - **问题数量确认**: 每个文件的问题数量和源文件做精确对比
      - **遗漏问题清单**: 如发现遗漏，提供具体的文件名、行号、废弃服务名/实体名
      - **验证结论**: 明确说明验证是否通过，如不通过则详细说明问题
    
    - **严格验证标准**:
      - **数量匹配**: 处理问题数 = 源文件实际问题数（必须 100% 匹配）
      - **文件覆盖**: 所有包含单位服务变更问题的文件都必须被处理
      - **质量标准**: 每个问题都有具体的服务名、实体名、方法名、解决方案
      - **格式规范**: 统一的详细格式，无笼统描述
      - **可执行性**: 所有解决方案都具体、详细、可执行
        - **废弃实体**: UserDefineUnit
        - **解决方案**: 使用 UserDefineUnitDTO 替换
        - **修复操作**:
          1. 替换导入语句: import com.cet.eem.fusion.config.sdk.entity.unit.UserDefineUnitDTO;
          2. 修改代码中的类型引用: UserDefineUnit → UserDefineUnitDTO
        - **分类依据**: 知识库第 5 条实体变更，需要修改类型引用
        ```

    - **严格检查项目**:

      - **问题数量精确核对**: 确认该文件的每个单位服务变更问题在 task-unit.md 中都有对应的详细解决方案
      - **问题映射精确匹配**: 使用唯一标识（文件名+行号+废弃服务名/实体名）进行一对一精确匹配
      - **解决方案具体性检查**: 每个问题必须包含：废弃服务/实体名、具体行号、替换方案、修复操作、分类依据
      - **禁止笼统描述**: 绝对不允许"单位服务相关问题"、"统一替换为新服务"等笼统描述
      - **知识库匹配验证**: 根据知识库第 5 条"单位服务变更"验证解决方案的正确性

    - **全局汇总验证**:

      - **文件覆盖验证**: 确认所有包含单位服务变更问题的文件都在 task-unit.md 中有对应
      - **总数验证**: 所有文件的单位服务问题数量之和 = out\问题列表.md 中的单位服务问题总数
      - **分类统计验证**: 🟢🟡🔴 各类别的总数统计正确

    - **生成详细验证报告**:

      - **按文件逐一报告**: 为每个文件生成详细的验证结果，列出每个单位服务问题的处理状态
      - **遗漏问题清单**: 如发现遗漏，必须提供具体的文件名、行号、废弃服务/实体名和需要补充的解决方案
      - **笼统描述识别**: 识别并标记所有笼统、概括的描述，要求具体化
      - **全局统计**: 汇总所有单位服务问题的处理统计和质量评估

    - _目标: 确保每个文件的每个单位服务变更问题都有具体、详细、可执行的解决方案，杜绝任何笼统概括_

  - [ ] 1.5.2 修复 task-unit.md 遗漏问题 (条件执行)

    - **执行条件**: 仅当 1.5.1 验证检查发现遗漏或不一致时才执行此任务
    - **判断标准**: 如果 1.5.1 验证报告显示"验证通过"，则跳过此任务
    - **严格修复要求**: 绝对不允许概括、笼统的描述，每个单位服务问题必须具体到服务名、实体名、方法名、行号、解决方案

    - **具体修复内容**:

      - **逐个问题补充**: 根据 1.5.1 验证报告中的遗漏清单，逐个补充每个具体的单位服务变更问题
      - **必须包含的详细信息**:

        - 具体的废弃服务/实体名（如 UnitService、UserDefineUnit、getUnit 等）
        - 精确的行号位置（如行号 23、行号 4,88,186 等）
        - 完整的替换方案（如 EnergyUnitService、UserDefineUnitDTO、queryUnitCoef 等）
        - 具体的修复操作（如"替换导入语句"、"修改服务注入"、"修改方法调用"等）
        - 明确的分类依据（如"知识库第 5 条单位服务变更"等）

      - **禁止的笼统描述**:

        - ❌ "单位服务相关问题"
        - ❌ "统一替换为新服务"
        - ❌ "单位服务问题 1-N"

      - **必须的具体描述**:
        - ✅ "单位服务问题 1: UnitService 服务废弃"
        - ✅ "单位服务问题 2: UserDefineUnit 实体变更"
        - ✅ "单位服务问题 3: getUnit 方法签名变更"

    - **修复执行步骤**:

      1. **读取验证报告**: 从 1.5.1 验证报告中提取所有遗漏的单位服务问题详细信息
      2. **逐个文件修复**: 按文件维度，逐个补充遗漏的单位服务问题解决方案
      3. **确保格式一致**: 所有补充的问题必须遵循统一的详细格式
      4. **数量精确匹配**: 确保修复后的问题数量与源文件完全匹配，无遗漏
      5. **质量检查**: 检查每个补充的解决方案是否具体、可执行

    - **重新验证**: 修复完成后重新执行 1.5.1 验证，确保：

      - 所有单位服务问题都有具体的解决方案
      - 没有任何笼统、概括的描述
      - 问题数量完全匹配
      - 解决方案质量符合要求

    - _输出: 修复后的完整 out\task-unit.md 文件，每个单位服务问题都有具体、详细、可执行的解决方案_

  - [ ] 1.6 物理量查询服务问题分析和解决方案确定

    - **数据来源**: 直接读取 out\问题列表.md，处理物理量查询服务相关的问题
    - **问题范围**: 基于知识库第 6 类"物理量查询服务"，包括：
      - QuantityObjectService 相关问题
      - QuantityObjectMapService 相关问题
      - QuantityObjectDataService 相关问题
      - 需要依赖 eem-base-fusion-energy-sdk 的问题
    - **完整性要求**: 必须处理所有物理量查询服务相关的问题，不允许遗漏任何一个问题

    - **第一步：文件结构全面分析**:

      - **问题类型搜索**: 使用 grepSearch 工具搜索 out\问题列表.md 中所有 QuantityObject 相关问题
      - **文件范围确认**: 确认所有包含物理量查询服务问题的文件清单
      - **问题分布统计**: 统计物理量查询服务问题的总数和分布情况
      - **行号范围记录**: 记录每个相关文件在源文件中的位置

    - **第二步：系统化分段处理策略**:

      - **严禁一次性加载**: 绝对不要一次性加载整个 out\问题列表.md 文件
      - **按文件分段读取**: 根据第一步建立的文件清单，逐个文件进行处理
      - **每个文件独立处理**: 每次只读取一个文件的所有物理量服务问题
      - **进度追踪**: 处理每个文件后立即记录进度，确保不跳过任何文件
      - **实时验证**: 每处理完一个文件立即验证问题数量和质量

    - **第三步：逐文件处理流程**:

      - **问题识别**: 识别文件中所有物理量查询服务相关问题
      - **知识库查找**: 基于知识库第 6 类"物理量查询服务"的解决方案
      - **解决方案确定**: 根据知识库指导确定具体的修复方案
      - **质量检查**: 确保每个问题都有具体的服务名、依赖信息、解决方案

    - **第四步：解决方案分类**:

      - **绿色标记 (🟢)**: 知识库有明确解决方案的问题
      - **红色标记 (🔴)**: 无法确定解决方案或需要添加 SDK 依赖的问题

    - **第五步：输出格式要求**:

      - **文件维度组织**: 按文件名分组，与 out\问题列表.md 保持一致的顺序
      - **详细问题信息**: 每个问题包含位置、解决方案、修复操作、分类依据

    - **第六步：强制完整性验证**:

      - **处理前统计**: 统计所有物理量查询服务问题的总数
      - **文件覆盖验证**: 确认所有包含相关问题的文件都被处理
      - **数量精确匹配**: 处理的问题总数必须等于源文件中的问题总数
      - **端到端验证**: 完整验证所有物理量查询服务问题的处理情况

    - **第七步：防遗漏保障措施**:

      - **文件清单核对**: 建立并维护完整的文件处理清单
      - **进度实时跟踪**: 每处理一个文件立即更新进度状态
      - **交叉验证机制**: 通过多种方式验证处理的完整性
      - **质量检查**: 验证每个解决方案的具体性和可执行性
      - **最终审核**: 处理完成后进行全面的质量和完整性审核

    - _输出: 完整的 out\task-quantity.md 文件，包含所有物理量查询服务问题的详细分析和解决方案，确保 100%覆盖_

  - [ ] 1.6.1 物理量查询服务问题独立验证和完整性检查

    - **任务性质**: 独立验证任务，不依赖任何现有统计数据或报告
    - **验证原则**: 假设 task-quantity.md 中的统计数据可能不准确，从源头重新验证
    - **完整性要求**: 必须实现 100% 的问题覆盖，不允许任何遗漏
    
    - **第一步：源文件独立统计**:
      - **直接统计源文件**: 使用 grepSearch 直接从 out\问题列表.md 统计所有 QuantityObject 相关问题
      - **建立权威基准**: 建立独立的、权威的问题总数和文件清单
      - **不信任现有统计**: 完全忽略 task-quantity.md 中的统计数据，假设其可能不准确
      - **文件覆盖检查**: 确认所有包含物理量查询服务问题的文件都在清单中
    
    - **第二步：逐文件独立验证**:
      - **按文件逐一核对**: 对每个文件的每个物理量查询服务问题进行独立验证
      - **问题映射检查**: 使用唯一标识（文件名+行号+缺失服务名）进行精确匹配
      - **解决方案质量检查**: 验证每个问题是否有具体的服务名、依赖信息、解决方案
      - **格式规范检查**: 确保所有问题都遵循统一的详细格式要求
    
    - **第三步：数量精确匹配验证**:
      - **总数核对**: 验证 task-quantity.md 中处理的问题总数是否等于源文件中的实际问题总数
      - **文件级核对**: 验证每个文件的问题数量是否完全匹配
      - **分类统计核对**: 验证 🟢🔴 各类别的统计是否准确
      - **遗漏问题识别**: 精确识别任何遗漏的问题并记录详细信息
    
    - **第四步：质量标准严格检查**:
      - **禁止笼统描述检查**: 识别并标记所有笼统、概括的描述
      - **必需信息完整性检查**: 确保每个问题包含具体的服务名、依赖信息、完整解决方案
      - **解决方案可执行性检查**: 验证每个解决方案是否具体、可执行
      - **知识库匹配验证**: 根据知识库第 6 条"物理量查询服务"验证解决方案的正确性
    
    - **第五步：生成详细验证报告**:
      - **按文件逐一报告**: 为每个文件生成详细的验证结果
      - **问题数量确认**: 每个文件的问题数量和源文件做精确对比
      - **遗漏问题清单**: 如发现遗漏，提供具体的文件名、行号、缺失服务名
      - **验证结论**: 明确说明验证是否通过，如不通过则详细说明问题
    
    - **严格验证标准**:
      - **数量匹配**: 处理问题数 = 源文件实际问题数（必须 100% 匹配）
      - **文件覆盖**: 所有包含物理量查询服务问题的文件都必须被处理
      - **质量标准**: 每个问题都有具体的服务名、依赖信息、解决方案
      - **格式规范**: 统一的详细格式，无笼统描述
      - **可执行性**: 所有解决方案都具体、详细、可执行
      - **禁止笼统描述**: 绝对不允许"物理量服务相关问题"、"统一添加 SDK 依赖"等笼统描述
      - **知识库匹配验证**: 根据知识库第 6 条"物理量查询服务"验证解决方案的正确性

    - **全局汇总验证**:

      - **文件覆盖验证**: 确认所有包含物理量服务问题的文件都在 task-quantity.md 中有对应
      - **总数验证**: 所有文件的物理量服务问题数量之和 = out\问题列表.md 中的物理量服务问题总数
      - **分类统计验证**: 🟢🔴 各类别的总数统计正确

    - **生成详细验证报告**:

      - **按文件逐一报告**: 为每个文件生成详细的验证结果，列出每个物理量服务问题的处理状态
      - **遗漏问题清单**: 如发现遗漏，必须提供具体的文件名、行号、缺失服务名和需要补充的解决方案
      - **笼统描述识别**: 识别并标记所有笼统、概括的描述，要求具体化
      - **全局统计**: 汇总所有物理量服务问题的处理统计和质量评估

    - _目标: 确保每个文件的每个物理量查询服务问题都有具体、详细、可执行的解决方案，杜绝任何笼统概括_

  - [ ] 1.6.2 修复 task-quantity.md 遗漏问题 (条件执行)

    - **执行条件**: 仅当 1.6.1 验证检查发现遗漏或不一致时才执行此任务
    - **判断标准**: 如果 1.6.1 验证报告显示"验证通过"，则跳过此任务
    - **执行内容**: 根据 1.6.1 验证报告发现的遗漏问题，修复 out\task-quantity.md 文件
    - 按文件逐个补充遗漏的物理量查询服务问题解决方案
    - 确保问题数量与源文件完全匹配，无遗漏
    - **重新验证**: 修复完成后重新执行 1.6.1 验证，确保通过
    - _输出: 修复后的完整 out\task-quantity.md 文件_

  - [ ] 1.7 其他类型问题分析和解决方案确定

    - **数据来源**: 直接读取 out\问题列表.md，处理不在知识库中的其他类型问题
    - **问题范围**: 处理除 1.1-1.6 任务范围外的所有其他问题，包括：
      - 不在知识库 7 类问题中的编译错误
      - 新出现的未知问题类型
      - 复杂的架构层面问题
      - 业务逻辑相关的兼容性问题
      - 第三方依赖相关问题
      - 配置文件相关问题
    - **完整性要求**: 必须处理所有其他类型问题，确保 out\问题列表.md 中的所有问题都被覆盖，不允许遗漏任何一个问题

    - **第一步：文件结构全面分析**:

      - **剩余问题识别**: 使用 grepSearch 工具识别 out\问题列表.md 中所有未被 1.1-1.6 任务处理的问题
      - **文件范围确认**: 确认所有包含其他类型问题的文件清单
      - **问题分布统计**: 统计其他类型问题的总数和分布情况
      - **全覆盖验证**: 确保 1.1-1.7 所有任务处理的问题总数等于源文件中的问题总数

    - **第二步：系统化分段处理策略**:

      - **严禁一次性加载**: 绝对不要一次性加载整个 out\问题列表.md 文件
      - **按文件分段读取**: 根据第一步建立的文件清单，逐个文件进行处理
      - **每个文件独立处理**: 每次只读取一个文件的所有其他类型问题
      - **进度追踪**: 处理每个文件后立即记录进度，确保不跳过任何文件
      - **实时验证**: 每处理完一个文件立即验证问题数量和质量

    - **第三步：逐文件处理流程**:

      - **问题识别**: 识别文件中所有其他类型问题
      - **问题分类**: 对剩余问题进行分类和分析
      - **解决方案确定**: 基于编程最佳实践和通用解决方案确定修复方案
      - **质量检查**: 确保每个问题都有具体的问题类型、行号、解决方案

    - **第四步：解决方案分类**:

      - **黄色标记 (🟡)**: 能够确定解决方案的问题
      - **红色标记 (🔴)**: 无法确定解决方案的问题

    - **第五步：输出格式要求**:

      - **文件维度组织**: 按文件名分组，与 out\问题列表.md 保持一致的顺序
      - **详细问题信息**: 每个问题包含位置、解决方案、修复操作、分类依据

    - **第六步：强制完整性验证**:

      - **处理前统计**: 统计所有其他类型问题的总数
      - **文件覆盖验证**: 确认所有包含相关问题的文件都被处理
      - **数量精确匹配**: 处理的问题总数必须等于源文件中的问题总数
      - **全覆盖验证**: 确保 1.1-1.7 所有任务处理的问题总数等于 out\问题列表.md 中的问题总数

    - **第七步：防遗漏保障措施**:

      - **文件清单核对**: 建立并维护完整的文件处理清单
      - **进度实时跟踪**: 每处理一个文件立即更新进度状态
      - **交叉验证机制**: 通过多种方式验证处理的完整性
      - **质量检查**: 验证每个解决方案的具体性和可执行性
      - **最终审核**: 处理完成后进行全面的质量和完整性审核

    - _输出: 完整的 out\task-other.md 文件，包含所有其他类型问题的详细分析和解决方案，确保 100%覆盖_

  - [ ] 1.7.1 其他类型问题独立验证和完整性检查

    - **任务性质**: 独立验证任务，不依赖任何现有统计数据或报告
    - **验证原则**: 假设 task-other.md 中的统计数据可能不准确，从源头重新验证
    - **完整性要求**: 必须实现 100% 的问题覆盖，确保所有问题都被处理
    
    - **第一步：源文件独立统计**:
      - **剩余问题识别**: 使用 grepSearch 识别 out\问题列表.md 中所有未被 1.1-1.6 任务处理的问题
      - **建立权威基准**: 建立独立的、权威的问题总数和文件清单
      - **不信任现有统计**: 完全忽略 task-other.md 中的统计数据，假设其可能不准确
      - **全覆盖验证**: 确保 1.1-1.7 所有任务处理的问题总数等于源文件中的问题总数
    
    - **第二步：逐文件独立验证**:
      - **按文件逐一核对**: 对每个文件的每个其他类型问题进行独立验证
      - **问题映射检查**: 使用唯一标识（文件名+行号+问题类型+具体内容）进行精确匹配
      - **解决方案质量检查**: 验证每个问题是否有具体的问题类型、行号、解决方案
      - **格式规范检查**: 确保所有问题都遵循统一的详细格式要求
    
    - **第三步：数量精确匹配验证**:
      - **总数核对**: 验证 task-other.md 中处理的问题总数是否等于源文件中的实际问题总数
      - **文件级核对**: 验证每个文件的问题数量是否完全匹配
      - **分类统计核对**: 验证 🟢🟡🔴 各类别的统计是否准确
      - **全覆盖验证**: 确保 1.1-1.7 所有任务处理的问题总数等于 out\问题列表.md 中的问题总数
    
    - **第四步：质量标准严格检查**:
      - **禁止笼统描述检查**: 识别并标记所有笼统、概括的描述
      - **必需信息完整性检查**: 确保每个问题包含具体的问题类型、精确行号、完整解决方案
      - **解决方案可执行性检查**: 验证每个解决方案是否具体、可执行
      - **排除重复验证**: 确认没有与 1.1-1.6 任务重复处理的问题
    
    - **第五步：生成详细验证报告**:
      - **按文件逐一报告**: 为每个文件生成详细的验证结果
      - **问题数量确认**: 每个文件的问题数量和源文件做精确对比
      - **遗漏问题清单**: 如发现遗漏，提供具体的文件名、行号、问题类型
      - **全覆盖统计**: 汇总 1.1-1.7 所有任务的问题处理统计，确保 100% 覆盖
      - **验证结论**: 明确说明验证是否通过，如不通过则详细说明问题
    
    - **严格验证标准**:
      - **数量匹配**: 处理问题数 = 源文件实际问题数（必须 100% 匹配）
      - **文件覆盖**: 所有包含其他类型问题的文件都必须被处理
      - **质量标准**: 每个问题都有具体的问题类型、行号、解决方案
      - **格式规范**: 统一的详细格式，无笼统描述
      - **可执行性**: 所有解决方案都具体、详细、可执行
      - **全覆盖**: 1.1-1.7 所有任务处理的问题总数 = out\问题列表.md 中的问题总数
      - **问题映射精确匹配**: 使用唯一标识（文件名+行号+问题类型+具体内容）进行一对一精确匹配
      - **解决方案具体性检查**: 每个问题必须包含：具体问题内容、精确行号、替换方案、修复操作、分类依据
      - **禁止笼统描述**: 绝对不允许"其他相关问题"、"统一处理"、"批量修复"等笼统描述
      - **排除重复验证**: 确认没有与 1.1-1.6 任务重复处理的问题

    - **全局汇总验证**:

      - **文件覆盖验证**: 确认所有包含其他问题的文件都在 task-other.md 中有对应
      - **总数验证**: 所有文件的其他问题数量之和 = out\问题列表.md 中的其他问题总数
      - **全覆盖验证**: 验证 1.1-1.7 所有任务处理的问题总数 = out\问题列表.md 中的问题总数
      - **分类统计验证**: 🟢🟡🔴 各类别的总数统计正确

    - **生成详细验证报告**:

      - **按文件逐一报告**: 为每个文件生成详细的验证结果，列出每个其他问题的处理状态
      - **遗漏问题清单**: 如发现遗漏，必须提供具体的文件名、行号、问题类型和需要补充的解决方案
      - **笼统描述识别**: 识别并标记所有笼统、概括的描述，要求具体化
      - **全覆盖统计**: 汇总 1.1-1.7 所有任务的问题处理统计，确保 100%覆盖
      - **质量评估**: 评估所有解决方案的具体性和可执行性

    - _目标: 确保每个文件的每个其他类型问题都有具体、详细、可执行的解决方案，并验证所有问题都被完整覆盖，杜绝任何笼统概括_

  - [ ] 1.7.2 修复 task-other.md 遗漏问题 (条件执行)

    - **执行条件**: 仅当 1.7.1 验证检查发现遗漏或不一致时才执行此任务
    - **判断标准**: 如果 1.7.1 验证报告显示"验证通过"，则跳过此任务
    - **执行内容**: 根据 1.7.1 验证报告发现的遗漏问题，修复 out\task-other.md 文件
    - 按文件逐个补充遗漏的其他类型问题解决方案
    - 确保问题数量与源文件完全匹配，无遗漏
    - 确保 1.1-1.7 所有任务的问题总数等于源问题总数
    - **重新验证**: 修复完成后重新执行 1.7.1 验证，确保通过
    - _输出: 修复后的完整 out\task-other.md 文件_

- [ ] 2. 生成按文件组织的详细修复任务

  - [ ] 2.1 按文件优先级排序和组织

    - 读取所有第一轮处理的任务文件整理问题和解决方案：
      - out\task-import.md（类问题解决方案）
      - out\task-message.md（消息推送变更问题解决方案）
      - out\task-permission.md（权限 ID 调整问题解决方案）
      - out\task-unit.md（单位服务变更问题解决方案）
      - out\task-quantity.md（物理量查询服务问题解决方案）
      - out\task-other.md（其他类型问题解决方案）
    - 由大模型来思考和使用这些工具，以及何时使用
    - 按照常量类、实体类、工具类、DAO、Service、Controller 的顺序排列
    - 采用从底层到上层的修复策略
    - 识别文件间的依赖关系
    - 确定修复的先后顺序
    - _输出: 优先级排序的文件列表_

  - [ ] 2.2 生成详细的 task-step.md 修复任务文件

    - **来源文件整合**: 将所有已确定的解决方案按文件和优先级组织，必须包含：
      - out\task-import.md 中的所有 类 问题解决方案
      - out\task-message.md 中的所有消息推送变更问题解决方案
      - out\task-permission.md 中的所有权限 ID 调整问题解决方案
      - out\task-unit.md 中的所有单位服务变更问题解决方案
      - out\task-quantity.md 中的所有物理量查询服务问题解决方案
      - out\task-other.md 中的所有其他类型问题解决方案
    - **任务格式要求**: 生成的 task-step.md 必须使用 checkbox 格式，**按文件维度组织**：
      - **主结构**: 按文件名分组，与前面所有输出文件保持一致的文件维度组织
      - **文件任务**: 每个文件是一个主任务 `- [ ] 修复 ClassesConfig.java 的所有问题`
      - **子任务**: 每个具体问题是一个子任务 `- [ ] 2.1 添加 ModelLabel 导入 (行号: 3, 19)`
      - **格式示例**:
        ```markdown
        - [ ] 2. 修复 ClassesConfig.java 的 Import 和继承问题
          - [ ] 2.1 添加 ModelLabel 导入 (行号: 3, 19)
          - [ ] 2.2 修复继承关系变更 (行号: 20)
        ```
    - **详细步骤生成**: 为每个文件的每个问题生成具体的修复步骤：
      - 问题描述和位置（具体行号）
      - 具体的修复操作（替换内容、添加代码等）
      - 修复后的验证方法
      - 预期结果
    - **优先级排序**: 按照常量类 → 实体类 → 工具类 → DAO → Service → Controller 的顺序组织
    - **未识别问题**: 将未识别的问题单独分类，放在任务列表最后
    - **可执行性**: 每个子任务包含完整的执行指令，无需额外分析
    - **状态管理**: 生成的任务文件应该支持：
      - 每个任务可以独立执行和完成
      - 状态可以通过 checkbox 进行跟踪
      - 支持部分完成和增量处理
    - **完整性验证机制**:
      - 在文档开头记录各来源文件的问题总数
      - 在文档结尾进行数量核对，确保所有问题都转换为修复步骤
      - 按文件分组统计，确保每个文件的问题都有对应的修复步骤
    - **格式一致性验证**:
      - **文件维度一致性**: 确保与 out\问题列表.md 的文件顺序和分组保持一致
      - **问题映射一致性**: 确保每个问题在各个文件中的描述和位置信息一致
      - **结构层次一致性**: 确保文件 → 问题 → 子任务的层次结构清晰且一致
      - **未识别问题处理**：未识别的问题放在最后，先不处理
    - _输出: 完整的 out\task-step.md 文件，包含所有可执行的具体修复子任务，每个任务都有独立的 checkbox 状态_

  - [ ] 2.3 task-step.md 完整性验证检查

    - **按文件维度验证策略**: 逐个文件检查 task-step.md 中的修复任务完整性
    - **文件级别验证流程**:

      - **步骤 1**: 从各来源文件（task-import.md、task-abandon.md、task-other.md）中提取所有文件的问题清单
      - **步骤 2**: 逐个文件进行验证，对每个文件执行以下检查：

        ```markdown
        ## 对 ClassesConfig.java 的修复任务验证

        ### 来源问题统计

        - task-import.md: 3 个 类 问题
        - task-other.md: 1 个继承关系变更问题
        - 总计: 4 个问题

        ### 修复任务统计 (来源: task-step.md)

        - 主任务: 1 个 (修复 ClassesConfig.java 的所有问题)
        - 子任务: 4 个 (2.1-2.4 对应各个具体问题)
        - 总计: 4 个修复步骤

        ### 验证结果: ✅ 数量一致，任务完整
        ```

    - **文件级别检查项目**:
      - **问题数量核对**: 确认该文件在来源文件中的问题总数与 task-step.md 中的修复步骤数量一致
      - **问题映射核对**: 使用唯一标识（文件名+行号+问题描述）确保每个源问题都有对应的修复步骤
      - **任务完整性**: 检查每个问题是否都转换为了具体的可执行修复步骤
      - **格式正确性**: 验证该文件的任务是否使用了正确的 checkbox 格式和层级结构
      - **内容质量**: 验证修复步骤是否包含问题描述、位置、修复操作、验证方法
    - **全局汇总验证**:
      - **文件覆盖验证**: 确认所有来源文件中涉及的文件都在 task-step.md 中有对应的修复任务
      - **总数验证**: 所有文件的修复步骤数量之和 = 各来源文件的问题总数之和
      - **优先级排序验证**: 确认文件优先级排序正确（常量类 → 实体类 → 工具类 → DAO → Service → Controller）
    - **生成按文件维度的验证报告**:
      - 为每个文件生成详细的修复任务验证结果
      - 汇总全局修复任务统计信息
      - 如发现遗漏或不一致，提供具体的文件名、问题描述和缺失的修复步骤
    - **状态管理验证**: 确认生成的任务文件支持独立的状态跟踪和增量处理
    - _目标: 确保每个文件的所有问题都转换为可执行的修复任务，通过按文件维度的系统化验证防止遗漏_

## 阶段 3: 执行修复任务

- [ ] 7. 按 out\task-step.md 逐个执行第一轮修复

  - [ ] 7.1 执行第一轮修复任务

    - 按照生成的 out\task-step.md 文件中的子任务顺序逐一执行
    - 对每个子任务应用已确定的具体修复方案
    - 一个任务一个任务执行，不要一次性加载 out\task-step.md。加载一个任务执行一个任务
    - 不要遗漏
    - 验证每个修复的正确性（编译通过、功能正常）
    - 使用 git-commit-helper.ps1 提交每个任务的修改，这个自动执行。子任务完成不提交，等任务完成在提交
    - **状态更新**: 在 out\task-step.md 中将完成的子任务状态从 `- [ ]` 更新为 `- [x]`
    - **独立处理**: 每个子任务可以独立执行，支持部分完成和增量处理
    - **进度跟踪**: 通过 checkbox 状态可以清晰看到整体修复进度
    - **完成校验**：如果 out\task-step.md 所有的任务没有完成，则这个任务不算完成，必须保证 out\task-step.md 所有的任务状态为`- [x]`
    - _执行原则: 严格按照 out\task-step.md 的顺序和方案执行，支持任务级别的状态管理_

  - [ ] 7.2 处理第一轮未识别问题
    - 所有已识别问题修复完成后，处理"未识别"类别的问题
    - 对未识别问题进行进一步研究和分析
    - 尝试找到解决方案或制定临时处理策略
    - 记录处理结果和遗留问题
    - _处理原则: 放在最后处理，不影响主要修复进度_

## 🚨 重要执行原则和防遗漏措施

### ⚠️ 关键执行要求

**基于实际经验教训，以下原则必须严格遵守：**

1. **严禁一次性加载大文件**

   - 绝对不要一次性读取整个 `out\问题列表.md` 文件
   - 该文件包含 很多个问题，内容庞大，一次性加载会导致内容截断和遗漏
   - 必须采用分段读取策略，每次处理 10-20 个问题

2. **强制分段处理机制**

   - 按文件名、问题类型或行号范围分段读取
   - 每处理完一段立即进行数量验证
   - 建立处理进度跟踪，确保覆盖所有问题

3. **实时验证和追踪**

   - 处理前：统计源问题总数
   - 处理中：实时统计已处理问题数量
   - 处理后：进行最终数量核对
   - 建立问题唯一标识符进行精确追踪

4. **多重验证机制**

   - 数量验证：确保处理数量与源数量一致
   - 映射验证：确保每个问题都有对应解决方案
   - 文件覆盖验证：确保所有涉及文件都被处理
   - 质量验证：确保解决方案的完整性和可执行性

5. **遗漏检查和补救**

   - 如发现数量不一致，必须立即停止并排查
   - 逐一对比源文件和输出文件，找出遗漏问题
   - 补充遗漏问题后重新进行完整性验证

6. **格式一致性要求**

   - **统一按文件维度组织**: 所有输出文件（、task-import.md、task-other.md、task-step.md 等）都必须按文件维度组织
   - **保持文件顺序一致**: 各文件中的文件顺序必须与 out\问题列表.md 保持一致
   - **问题描述一致**: 同一个问题在不同文件中的描述、行号、位置信息必须完全一致
   - **便于追溯和处理**: 确保从原始问题到最终任务的完整追溯链路

7. **条件执行机制**
   - **智能跳过**: 验证通过的任务自动跳过对应的修复任务，提高执行效率
   - **按需修复**: 只有发现问题时才执行修复，避免不必要的操作
   - **重新验证**: 修复完成后必须重新验证，确保问题得到解决
   - **状态追踪**: 清晰记录每个验证和修复任务的执行状态

## 执行原则

### 🔄 迭代执行流程

1. **按序执行**: 严格按照 out\task-step.md 中的子任务顺序逐一执行
2. **条件判断**: 对于标记为"条件执行"的任务，先检查前置验证结果
   - 如果验证通过，跳过修复任务
   - 如果验证不通过，执行修复任务并重新验证
3. **应用方案**: 直接应用每个子任务中已确定的具体修复方案
4. **验证结果**: 验证修复后的编译和功能正确性
5. **提交修改**: 使用 git-commit-helper.ps1 提交每个子任务的修改
6. **更新状态**: 在 out\task-step.md 中将完成的子任务从 `- [ ]` 更新为 `- [x]`
7. **独立处理**: 每个任务可以独立执行，支持部分完成和增量处理
8. **进度跟踪**: 通过 checkbox 状态清晰跟踪整体修复进度
9. **处理未识别**: 所有已识别问题修复完成后，最后处理未识别问题

### 🎯 优先级标记说明

- **🟢 绿色**: 确定性修复，可直接执行
  - 单一匹配的 类 问题
  - 知识库有明确解决方案的废弃 API 问题
- **🟡 黄色**: 需要 AI 智能判断或测试验证
  - 多候选匹配的 类 问题（需要 class_file_reader.py 分析）
  - 方法签名变更和配置更新问题
- **⚪ 未识别**: 无法确定解决方案，需要进一步研究
  - AI 无法判断最佳匹配的 类 问题
  - 知识库没有解决方案的废弃 API 问题
  - 复杂的架构层面问题

### 📋 统一输出格式说明

**所有输出文件统一采用按文件维度组织的格式，确保一致性和处理效率：**

#### 1. **out\问题列表.md** (原始问题，按文件维度)

```markdown
## ClassesConfig.java

### 问题 1

error_code: "类问题"
missing_class: "ModelLabel"
line: [3, 19]
```

#### 2. **out\task-import.md** (Import 问题解决方案，按文件维度)

```markdown
## ClassesConfig.java

### 类 问题 1: ModelLabel 类导入 (🟢 绿色标记)

- **问题位置**: 行号 3, 19
- **解决方案**: import com.cet.eem.fusion.common.modelutils.annotation.ModelLabel;
- **修复操作**: 在文件顶部添加导入语句
```

#### 3. **out\task-abandon.md** (废弃 API 问题解决方案，按文件维度)

```markdown
## TeamEnergyServiceImpl.java

### 废弃 API 问题 1: CommonUtils.calcDouble 废弃 (🟢 绿色标记)

- **问题位置**: 行号 94
- **解决方案**: 使用 NumberCalcUtils.calcDouble 替代
- **修复操作**: 替换方法调用
```

#### 4. **out\task-message.md** (消息推送变更问题解决方案，按文件维度)

#### 5. **out\task-permission.md** (权限 ID 调整问题解决方案，按文件维度)

#### 6. **out\task-unit.md** (单位服务变更问题解决方案，按文件维度)

#### 7. **out\task-quantity.md** (物理量查询服务问题解决方案，按文件维度)

#### 8. **out\task-other.md** (其他类型问题解决方案，按文件维度)

#### 9. **out\task-step.md** (第一轮执行任务，按文件维度 + checkbox)

```markdown
- [x] 2. 修复 ClassesConfig.java 的 类问题
  - [x] 2.1 添加 ModelLabel 导入 (行号: 3, 19)
  - [x] 2.4 修复继承关系变更 (行号: 20)
```

#### 10. **out\task-refactor.md** (第二轮方法重构问题解决方案，按文件维度)

#### 11. **out\task-multitenant.md** (第二轮多租户问题解决方案，按文件维度)

#### 12. **out\task-step-round2.md** (第二轮执行任务，按文件维度 + checkbox)

````

**格式统一的优势：**

- ✅ **一致性**: 所有文件都按相同的文件维度组织
- ✅ **可追溯性**: 从原始问题到最终任务，文件结构保持一致
- ✅ **处理效率**: 便于按文件逐个处理和验证
- ✅ **状态管理**: 支持文件级别的进度跟踪

## 工具使用说明

### 核心分析工具

- `error_parser.py`: 解析 JavaAnnotator.xml 错误文件
- `error_grouper.py`: 错误分组和去重处理
- `class_name_finder.py`: 查找替代类名
- `fuzzy_matcher.py`: 查找相似类，如果有多个需要大模型介入进行决策
- `class_file_reader.py`: 读取类文件详细信息（支持源码和 jar 包）
- `git-commit-helper.ps1`: 自动化代码提交

### 参考资料

- `能管代码迁移知识库.md`: 已知问题解决方案
- `JavaAnnotator.xml`: 编译错误源文件
- 项目源码: 需要修复的目标代码

## 📚 经验教训和改进措施

### 🔍 问题根源分析

基于任务 1.2 执行过程中发现的遗漏问题，总结以下经验教训：

#### 1. **文件处理策略问题**

- **问题**: 一次性读取 `out\问题列表.md` 导致内容截断
- **原因**: 文件包含 多个个问题，内容过于庞大
- **教训**: 大文件必须分段处理，不能依赖一次性加载

#### 2. **验证机制不足**

- **问题**: 缺少实时验证，只在最后进行检查
- **原因**: 没有建立处理过程中的检查点
- **教训**: 必须建立多层次、实时的验证机制

#### 3. **问题追踪缺失**

- **问题**: 没有建立问题的唯一标识和追踪机制
- **原因**: 依赖人工记忆和估算，缺少系统化追踪
- **教训**: 必须为每个问题建立唯一标识符

#### 4. **处理范围不完整**

- **问题**: 遗漏了 TeamEnergyController、TeamEnergyService 等文件的问题
- **原因**: 处理策略不够系统化，存在盲区
- **教训**: 必须建立完整的文件清单和覆盖验证

### 🛠️ 改进措施

#### 1. **强制分段处理**

```markdown
- 每次最多处理 10-20 个问题
- 按文件名或问题类型分段
- 每段处理完立即验证
- 建立处理进度跟踪表
````

#### 2. **多重验证机制**

```markdown
- 处理前验证：统计源问题总数
- 处理中验证：实时统计已处理数量
- 处理后验证：最终数量核对
- 质量验证：检查解决方案完整性
```

#### 3. **问题追踪系统**

```markdown
- 唯一标识符：文件名+行号+问题描述
- 处理状态跟踪：未处理/处理中/已完成
- 映射关系验证：源问题 ↔ 解决方案
- 遗漏检查机制：自动发现未处理问题
```

#### 4. **文件覆盖保证**

```markdown
- 建立涉及文件的完整清单
- 逐文件验证问题处理情况
- 交叉验证：按文件、按类型、按行号
- 盲区检查：确保没有遗漏的文件或问题类型
```

### 📈 质量保证措施

#### 1. **执行前检查**

- [ ] 确认分段处理策略
- [ ] 建立问题统计基线
- [ ] 准备验证检查清单
- [ ] 设置处理进度跟踪

#### 2. **执行中监控**

- [ ] 实时统计处理数量
- [ ] 每段处理后立即验证
- [ ] 记录处理进度和状态
- [ ] 及时发现和处理异常

#### 3. **执行后验证**

- [ ] 最终数量核对
- [ ] 问题映射关系验证
- [ ] 解决方案质量检查
- [ ] 生成完整性验证报告

### 🎯 成功标准

一个任务被认为成功完成，必须满足：

1. **数量完整性**: 处理问题数量 = 源问题数量
2. **映射完整性**: 每个源问题都有对应的解决方案
3. **文件完整性**: 所有涉及文件都被完整处理
4. **质量完整性**: 所有解决方案都可执行且准确
5. **验证完整性**: 通过多重验证机制确认

### 🔄 持续改进

基于每次任务执行的经验：

1. **记录问题**: 详细记录遇到的问题和解决方案
2. **更新规范**: 及时更新任务执行规范和要求
3. **优化流程**: 持续优化处理流程和验证机制
4. **分享经验**: 将经验教训应用到后续类似任务中

**最终目标**: 建立一套可靠、完整、高质量的任务执行体系，确保零遗漏、零错误。
