package com.cet.eem.fusion.transformer.core.service.impl;

import com.cet.eem.fusion.common.def.quantity.FrequencyDef;
import com.cet.eem.fusion.common.def.quantity.PhasorDef;
import com.cet.eem.fusion.common.def.quantity.QuantityCategoryDef;
import com.cet.eem.fusion.common.def.quantity.QuantityTypeDef;
import com.cet.electric.baseconfig.common.entity.PipeNetworkConnectionModel;
import com.cet.electric.modelsdk.event.model.SystemEvent;
import com.cet.electric.modelsdk.quantity.model.QuantityAggregationData;
import com.cet.electric.baseconfig.common.entity.QuantityObject;
import com.cet.eem.bll.common.model.domain.perception.logicaldevice.PecEventExtendVo;
import com.cet.eem.bll.common.model.ext.objective.event.SystemEventWithText;
import com.cet.eem.bll.common.model.topology.vo.ConnectionSearchVo;
import com.cet.eem.fusion.common.model.topology.bo.LinkNode;
import com.cet.eem.fusion.common.model.topology.bo.PointNode;
import com.cet.eem.fusion.config.sdk.utils.GlobalInfoUtils;
import com.cet.eem.fusion.energy.sdk.dao.SystemEventDao;
import com.cet.eem.bll.energy.model.event.SystemEventCountVo;
import com.cet.eem.bll.energy.service.event.AlarmEventService;
import com.cet.eem.fusion.common.utils.CommonUtils;
import com.cet.eem.fusion.energy.sdk.def.AggregationType;
import com.cet.eem.fusion.common.def.base.EnergyTypeDef;
import com.cet.eem.fusion.common.def.common.EnumOperationType;
import com.cet.eem.fusion.common.def.common.ColumnDef;
import com.cet.eem.fusion.common.def.label.NodeLabelDef;
import com.cet.eem.fusion.common.model.datalog.DataLogData;
import com.cet.eem.fusion.common.model.objective.physicalquantity.AggregationCycle;
import com.cet.eem.fusion.common.model.realtime.RealTimeValue;
import com.cet.eem.fusion.common.utils.JsonTransferUtils;
import com.cet.electric.baseconfig.sdk.common.utils.TimeUtil;
import com.cet.eem.event.model.analysis.ConfirmCountResult;
import com.cet.eem.event.model.expert.EventCountSearchVo;
import com.cet.eem.event.model.pecevent.PecEventCountVo;
import com.cet.eem.event.service.PecEventService;
import com.cet.eem.event.service.expert.ExpertAnalysisBffService;
import com.cet.eem.event.service.expert.PecCoreEventBffService;
import com.cet.eem.fusion.common.modelutils.model.base.QueryCondition;
import com.cet.eem.fusion.common.modelutils.model.model.EntityWithName;
import com.cet.eem.fusion.common.modelutils.model.tool.ModelServiceUtils;
import com.cet.eem.fusion.common.modelutils.model.tool.ParentQueryConditionBuilder;
import com.cet.eem.node.service.Topology1Service;
import com.cet.eem.node.service.TopologyCommonService;
import com.cet.eem.fusion.energy.sdk.dao.QuantityAggregationDataDao;
import com.cet.eem.quantity.dao.QuantityObjectDao;
import com.cet.eem.quantity.model.quantity.QuantityDataBatchSearchVo;
import com.cet.eem.fusion.common.model.quantity.QuantitySearchVo;
import com.cet.eem.quantity.service.QuantityManageService;
import org.apache.commons.collections4.CollectionUtils;
import com.cet.eem.transformer.dao.PowerTransformerDao;
import com.cet.eem.transformer.dao.TransformerindexDataDao;
import com.cet.eem.transformer.model.constant.Constant;
import com.cet.eem.transformer.model.enums.EquipmentStatus;
import com.cet.eem.transformer.model.enums.TransformerlevelEnum;
import com.cet.eem.transformer.service.TransformerOverviewService;
import com.cet.electric.model.enums.ControlMeanType;
import org.apache.poi.ss.formula.functions.T;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName : TransformerOverviewServiceImpl
 * <AUTHOR> yangy
 * @Date: 2022-03-15 15:42
 */
@Service
public class TransformerOverviewServiceImpl implements TransformerOverviewService {
    private static final Logger log = LoggerFactory.getLogger(TransformerOverviewServiceImpl.class);

    @Autowired
    PowerTransformerDao powerTransformerDao;
    @Autowired
    TransformerAnalysisServiceImpl transformerAnalysisService;
    @Autowired
    Topology1Service topology1Service;
    @Autowired
    ModelServiceUtils modelServiceUtils;
    @Autowired
    QuantityObjectDao quantityObjectDao;
    @Autowired
    QuantityAggregationDataDao quantityAggregationDataDao;
    @Autowired
    QuantityManageService quantityManageService;
    @Autowired
    TransformerindexDataDao transformerindexDataDao;
    @Autowired
    TopologyCommonService topologyService;
    @Autowired
    PecEventService pecEventService;
    @Autowired
    SystemEventDao systemEventDao;

    @Override
    public OverviewDataVo aboveData(Long projectId) {
        long startTime = System.currentTimeMillis();
        OverviewDataVo data = new OverviewDataVo();
        List<PowerTransformerDto> transformers = powerTransformerDao.queryByProjectId(projectId);
        log.info("总览台账查询数据库变压器时间为: {}", System.currentTimeMillis() - startTime);
        long startTime1 = System.currentTimeMillis();
        Operation operation = runningState(transformers,projectId);
        //运行的数量
        try {
            operation.setTotal(getCount(transformers));
        } catch (Throwable throwable) {
            log.error("当前查询运行失败");
        }
        data.setOperation(operation);
        log.info("总览台账查询运行和所有负载率时间为: {}s", (System.currentTimeMillis() - startTime1)/1000);
        long startTime2 = System.currentTimeMillis();
        data.setQuantity(getBasicData(transformers));
        log.info("变压器总览主变中压配电低压查询时间为: {}s", (System.currentTimeMillis() - startTime2)/1000);
        long startTime4 = System.currentTimeMillis();
        data.setEvent(getEvent1(transformers));
        log.info("事件查询时间为: {}s", (System.currentTimeMillis() - startTime4)/1000);
        log.info("总览台账执行总时间为: {}s", (System.currentTimeMillis() - startTime)/1000);
        return data;
    }

    @Override
    public OverviewDataVo aboveDataForDayEvent(Long projectId) {

        long startTime = System.currentTimeMillis();
        OverviewDataVo data = new OverviewDataVo();
        List<PowerTransformerDto> transformers = powerTransformerDao.queryByProjectId(projectId);

        data.setEvent(getEventForDay(transformers));
        log.info("变压器日事件统计用时: {}", System.currentTimeMillis() - startTime);

        return data;
    }

    @Override
    public OverviewDataVo aboveDataForMonthEvent(Long projectId) {
        long startTime = System.currentTimeMillis();
        OverviewDataVo data = new OverviewDataVo();
        List<PowerTransformerDto> transformers = powerTransformerDao.queryByProjectId(projectId);

        data.setEvent(getEventForMonth(transformers));
        log.info("变压器月事件统计用时: {}", System.currentTimeMillis() - startTime);

        return data;
    }

    @Override
    public OverviewDataVo aboveDataForStatus(Long projectId) {

        long startTime = System.currentTimeMillis();
        OverviewDataVo data = new OverviewDataVo();
        List<PowerTransformerDto> transformers = powerTransformerDao.queryByProjectId(projectId);
        log.info("总览台账查询数据库变压器时间为: {}", System.currentTimeMillis() - startTime);
        long startTime1 = System.currentTimeMillis();
        Operation operation = runningState(transformers,projectId);
        data.setOperation(operation);
        data.setQuantity(getBasicData(transformers));
        log.info("变压器状态统计用时: {}s", (System.currentTimeMillis() - startTime1));

        return data;
    }

    @Override
    public List<EquipmentCondition> equipmentStatus(EquipmentForm form,Long projectId) {
        long startTime = System.currentTimeMillis();
        List<PowerTransformerDto> transformers = powerTransformerDao.queryByProjectId(projectId);
        transformers = filterTransformers(transformers, form.getTransformerType());
        EquipmentStatus status = EquipmentStatus.getEnum(form.getDataType());
        boolean desc;
        Integer type;
        switch (status) {
            case LOADRATE:
                //负载率
                type = Constant.ONE;
                break;
            case AVERAGEPOWERFACTOR:
                type = Constant.TWO;
                break;
            case OPERATIONRATE:
                type = Constant.ZERO;
                break;
            default:
                type = Constant.ZERO;
                break;
        }
        if (Objects.equals(form.getOrder(), Constant.ONE)) {
            desc = false;
        } else {
            desc = true;
        }
        List<Long> transformerIds = transformers.stream().map(PowerTransformerDto::getId).collect(Collectors.toList());
        Map<Long, String> transName = transformers.stream().collect(Collectors.toMap(PowerTransformerDto::getId, PowerTransformerDto::getName));
        List<TransformerindexData> dataList = transformerindexDataDao.queryByParams(form.getStartTime(), form.getAggregationCycle(), type, desc);
        dataList.removeIf(item -> !transformerIds.contains(item.getPowertransformerid()));
        List<EquipmentCondition> datas = dataList.stream().map(item -> new EquipmentCondition(item.getPowertransformerid(), item.getValue())).collect(Collectors.toList());
        datas.forEach(data -> data.setName(transName.get(data.getId())));
        log.info("监测设备状态执行总时间为: {}", System.currentTimeMillis() - startTime);
        return sortedTransformers(datas, form.getOrder());
    }

    /**
     * 判断多少在运行的中的
     *
     * @param PowerTransformerDtos
     * @return
     */
    public Integer getCount(List<PowerTransformerDto> PowerTransformerDtos) {
        List<BaseVo> lineBaseVos = getLineBaseVos(PowerTransformerDtos);
        Map<Integer, List<RealTimeValue>> realTimeValueDatas = realTimeValueDatas(lineBaseVos, Arrays.asList(getTransformerOpening(), getTransformerClosing()));
        Map<Long, List<RealTimeValue>> listMap = realTimeValueDatas.values().stream().filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).distinct().collect(Collectors.groupingBy(RealTimeValue::getMonitoredId));
        int count = Constant.ZERO;
        for (Long key : listMap.keySet()) {
            int sum = Constant.ZERO;
            Map<Long, Double> data = listMap.get(key).stream().filter(item->Objects.nonNull(item.getValue()))
                    .collect(Collectors.toMap(RealTimeValue::getDataId, RealTimeValue::getValue, (v1, v2) -> v1));
            // Map<Long, Double> data = listMap.get(key).stream().distinct().collect(Collectors.toMap(RealTimeValue::getDataId, RealTimeValue::getValue));
            //分闸和合闸共在  取合闸为1 则为运行
            if (data.containsKey(Constant.FEN_ZHA) && data.containsKey(Constant.HE_ZHA)) {
                if (Objects.equals(data.get(Constant.HE_ZHA).intValue(), Constant.ONE)) {
                    sum = Constant.ONE;
                }
                //只有分闸 则取分闸的值为0 为运行
            } else if (data.containsKey(Constant.FEN_ZHA) && !data.containsKey(Constant.HE_ZHA)) {
                if (Objects.equals(data.get(Constant.FEN_ZHA).intValue(), Constant.ZERO)) {
                    sum = Constant.ONE;
                }
                //只有合闸 则取合闸的值为1 为运行
            } else if (!data.containsKey(Constant.FEN_ZHA) && data.containsKey(Constant.HE_ZHA)) {
                if (Objects.equals(data.get(Constant.HE_ZHA).intValue(), Constant.ONE)) {
                    sum = Constant.ONE;
                }
            }
            count += sum;
        }
        return count;
    }

    public List<BaseVo> getLineBaseVos(List<PowerTransformerDto> PowerTransformerDtos) {
        ConnectionSearchVo searchVo = new ConnectionSearchVo();
        searchVo.setEnergyType(EnergyTypeDef.ELECTRIC);
        List<BaseVo> baseVos = PowerTransformerDtos.stream().map(PowerTransformerDto -> new BaseVo(PowerTransformerDto.getId(), NodeLabelDef.POWER_TRANS_FORMER))
                .distinct().collect(Collectors.toList());
        searchVo.setNodes(baseVos);
        List<PipeNetworkConnectionModel> connections = topologyService.queryConnectRelations(searchVo);
        return connections.stream().filter(item->Objects.equals(item.getOutflowLabel(),Constant.POWERTRANSFORMER)).map(item ->
                new BaseVo(item.getInflowId(), item.getInflowLabel())).collect(Collectors.toList());
    }

    /**
     * 查询平均功率因数
     *
     * @param transformers
     * @param datas
     * @param form
     * @return
     */
    public List<EquipmentCondition> getAveragepowerfactor(List<PowerTransformerDto> transformers, List<EquipmentCondition> datas, EquipmentForm form,Long projectId) {
        Map<String, Object> nodeRelation = getNodeRelation(projectId);
        List<PointNode> nodes = JsonTransferUtils.transferList(getList(TransformerAnalysisServiceImpl.DATAINFO, nodeRelation), PointNode.class);
        List<LinkNode> links = JsonTransferUtils.transferList(getList(TransformerAnalysisServiceImpl.DATA_LINK, nodeRelation), LinkNode.class);
        //设备的id 已变压器id分组
        Map<Long, List<Long>> lineMap = new HashMap<>();
        List<BaseVo> allNodes = new ArrayList<>();
        Map<Long, List<BaseVo>> tops = new HashMap<>();
        Map<Long, List<BaseVo>> downs = new HashMap<>();
        transformers.forEach(transformer -> {
            List<BaseVo> top = transformerAnalysisService.getTopOrDownNodes(transformer.getId(), nodes, links, true);
            List<BaseVo> down = transformerAnalysisService.getTopOrDownNodes(transformer.getId(), nodes, links, false);
            allNodes.addAll(top);
            allNodes.addAll(down);
            List<Long> linIds = allNodes.stream().map(BaseVo::getId).collect(Collectors.toList());
            lineMap.put(transformer.getId(), linIds);
            tops.put(transformer.getId(), top);
            downs.put(transformer.getId(), down);

        });
        //查询什么的总数据
        List<QuantityObject> quantityObjects = quantityObjectDao.queryQuantityObject(allNodes);
        //对总数据分组
        Map<Long, List<QuantityObject>> quantityObjectsMap = quantityObjects(quantityObjects, lineMap);

        Map<Long, List<QuantityAggregationData>> quantityDatas = getQuantityDatas(quantityObjects, quantityObjectsMap, form);
        transformers.forEach(transformer -> {
            Long id = transformer.getId();
            EquipmentCondition data = new EquipmentCondition();
            data.setId(transformer.getId());
            data.setName(transformer.getName());
            Double powerFactor = getPowerFactor(quantityObjectsMap.get(id), quantityDatas.get(id), tops.get(id), downs.get(id));
            data.setData(powerFactor);
            datas.add(data);
        });
        return datas;
    }

    public Map<Long, List<QuantityObject>> quantityObjects(List<QuantityObject> quantityObjects, Map<Long, List<Long>> lineMap) {
        Map<Long, List<QuantityObject>> quantityMap = new HashMap<>();
        Map<Long, List<QuantityObject>> objectMap = quantityObjects.stream().collect(Collectors.groupingBy(QuantityObject::getMonitoredid));
        lineMap.forEach((key, val) -> {
            List<QuantityObject> data = new ArrayList<>();
            val.forEach(monitorId -> {
                List<QuantityObject> quantityObjects1 = objectMap.get(monitorId);
                if (CollectionUtils.isEmpty(quantityObjects1)) {
                    return;
                }
                data.addAll(quantityObjects1);
            });
            quantityMap.put(key, data);
        });
        return quantityMap;
    }

    public Map<Long, List<QuantityAggregationData>> getQuantityDatas(List<QuantityObject> quantityObjects, Map<Long, List<QuantityObject>> quantityObjectsMap, EquipmentForm form) {

        List<Long> quantityObjectId = quantityObjects.stream().map(QuantityObject::getId).collect(Collectors.toList());
        List<QuantityAggregationData> quantityAggregationData = quantityAggregationDataDao.queryQuantityData(
                TimeUtil.timestamp2LocalDateTime(form.getStartTime()), TimeUtil.timestamp2LocalDateTime(form.getEndTime()),
                quantityObjectId, form.getAggregationCycle(), AggregationType.STEP_ACCUMULATION);
        //根据quantityobject_id分组
        Map<Long, List<QuantityAggregationData>> maps = new HashMap<>();
        quantityObjectsMap.forEach((key, quantitys) -> {
            List<Long> quantityIds = quantitys.stream().map(QuantityObject::getId).collect(Collectors.toList());
            List<QuantityAggregationData> list = new ArrayList<>();
            quantityAggregationData.forEach(data -> {
                if (quantityIds.contains(data.getQuantityobject_id())) {
                    list.add(data);
                }
            });
            maps.put(key, list);
        });
        return maps;
    }

    public Double getPowerFactor(List<QuantityObject> quantityObjects, List<QuantityAggregationData> quantityAggregationData, List<BaseVo> top, List<BaseVo> down) {
        Map<Integer, List<QuantityAggregationData>> map = transformerAnalysisService.assembleQuantityAggregationData(quantityObjects, top, quantityAggregationData);

        Map<Integer, List<QuantityAggregationData>> mapDown = transformerAnalysisService.assembleQuantityAggregationData(quantityObjects, down, quantityAggregationData);

        //高压侧的正向有功电能
        Double topActive = transformerAnalysisService.getQuantityAggregationData(map, true);
        //高压侧的正向无功电能
        Double topForward = transformerAnalysisService.getQuantityAggregationData(map, false);

        Double value = transformerAnalysisService.calculateApparentPower(topActive, topForward);
        if (Objects.isNull(value)) {
            //低压侧的正向有功电能
            Double downActive = transformerAnalysisService.getQuantityAggregationData(mapDown, true);
            //低压侧的正向无功电能
            Double downForward = transformerAnalysisService.getQuantityAggregationData(mapDown, false);
            value = transformerAnalysisService.calculateApparentPower(downActive, downForward);
        }
        return value;
    }

    /**
     * 获取平均负载率数据
     *
     * @param transformers
     * @param datas
     * @param form
     * @return
     */
    public List<EquipmentCondition> getLoadrate(List<PowerTransformerDto> transformers, List<EquipmentCondition> datas, EquipmentForm form) {
        QuantityDataBatchSearchVo searchVo = transformerAnalysisService.createQuantityDataBatchSearchVo(null, form.getStartTime(),
                form.getEndTime(), form.getAggregationCycle(), Arrays
                        .asList(transformerAnalysisService.getPositiveActiveElectric(), transformerAnalysisService.getReactivePowerElectric()));
        Map<Long, List<DataLogData>> listMap = transformerAnalysisService.queryLoadRateBatch(transformers, searchVo, GlobalInfoUtils.getTenantId());
        //list转为map .
        Map<Long, PowerTransformerDto> transMap = transformers.stream().collect(Collectors.toMap(PowerTransformerDto::getId, data -> data));
        listMap.forEach((key, val) -> {
            EquipmentCondition data = new EquipmentCondition();
            PowerTransformerDto transformerDto = transMap.get(key);
            //额定容量
            Double ratedcapacity = transformerDto.getRatedcapacity();
            //负载值
            double sum = val.stream().map(DataLogData::getValue).filter(Objects::nonNull).mapToDouble(value -> value).sum();
            data.setId(key);
            //负载率
            data.setData(NumberCalcUtils.calcDouble(sum, ratedcapacity, EnumOperationType.DIVISION.getId()));
            data.setName(transformerDto.getName());
            datas.add(data);
        });
        return datas;
    }

    /**
     * 给数据排序
     *
     * @param datas
     * @param order
     * @return
     */
    List<EquipmentCondition> sortedTransformers(List<EquipmentCondition> datas, Integer order) {
        if (Objects.equals(order, Constant.ONE)) {
            datas.sort((o1, o2) -> {
                if (o1.getData() == null) {
                    if (o2.getData() == null) {
                        return 0;
                    }
                    return 1;
                }
                if (o2.getData() == null) {
                    return -1;
                }
                return o2.getData().compareTo(o1.getData());
            });

        } else {
            datas.sort((o1, o2) -> {
                if (o2.getData() == null) {
                    if (o1.getData() == null) {
                        return 0;
                    }
                    return 1;
                }
                if (o1.getData() == null) {
                    return -1;
                }
                return o1.getData().compareTo(o2.getData());
            });
        }
        return datas;
    }

    /**
     * 根据变压器类型 过滤变压器
     *
     * @param transformers
     * @param transformerType
     * @return
     */
    List<PowerTransformerDto> filterTransformers(List<PowerTransformerDto> transformers, Integer transformerType) {
        if (!Objects.equals(transformerType, Constant.ZERO)) {
            return transformers.stream().filter(item -> Objects.equals(item.getTransformerlevel(), transformerType)).collect(Collectors.toList());
        }
        return transformers;
    }

    public Map<String, Object> getNodeRelation(Long projectId) {
        try {
            return topology1Service.queryTopology(projectId, EnergyTypeDef.ELECTRIC);
        } catch (InstantiationException e) {
            log.error("获取节点失败 {}", e.getMessage());
        } catch (IllegalAccessException e) {
            log.error("获取节点失败 {}", e.getMessage());
        }
        return new HashMap<>();
    }

    /**
     * 运行情况赋值
     *
     * @param transformers
     * @return
     */
    public Operation runningState(List<PowerTransformerDto> transformers,Long projectId) {
        Operation operation = new Operation();
        Map<String, Object> nodeRelation = getNodeRelation(projectId);
        List<PointNode> nodes = JsonTransferUtils.transferList(getList(TransformerAnalysisServiceImpl.DATAINFO, nodeRelation), PointNode.class);
        List<LinkNode> links = JsonTransferUtils.transferList(getList(TransformerAnalysisServiceImpl.DATA_LINK, nodeRelation), LinkNode.class);
        Integer lightLow = 0;
        Integer mediumLoad = 0;
        Integer heavyLoad = 0;
        Integer unknown = 0;
        List<Long> ids = transformers.stream().map(PowerTransformerDto::getId).collect(Collectors.toList());
        //每个变压器的实时负载
        Map<Long, Double> realTimeBatchs = transformerAnalysisService.calculateLoadRealTimeBatch(ids, nodes, links);
        for (PowerTransformerDto transformer : transformers) {
            Double loadRealValue = realTimeBatchs.get(transformer.getId());
            loadRealValue = NumberCalcUtils.calcDouble(loadRealValue, transformer.getRatedcapacity(), EnumOperationType.DIVISION.getId());
            if (Objects.isNull(loadRealValue) || Objects.isNull(transformer.getSectionlowlimit()) || Objects.isNull(transformer.getSectionupperlimit())) {
                unknown++;
                continue;
            }
            //轻载运行台数
            if (loadRealValue < transformer.getSectionlowlimit()) {
                lightLow++;
                //中载运行台数
            } else if (loadRealValue >= transformer.getSectionlowlimit() && loadRealValue <= transformer.getSectionupperlimit()) {
                //重载运行台数
                mediumLoad++;
            } else if (loadRealValue > transformer.getSectionupperlimit()) {
                heavyLoad++;
            }
        }
        operation.setLightLow(lightLow);
        operation.setMediumLoad(mediumLoad);
        operation.setHeavyLoad(heavyLoad);
        operation.setUnknown(unknown);
        operation.setTotal(lightLow + mediumLoad + heavyLoad + unknown);
        return operation;
    }

    /**
     * 基础信息的赋值
     *
     * @param transformers
     * @return
     */
    public Quantity getBasicData(List<PowerTransformerDto> transformers) {
        Quantity quantity = new Quantity();
        quantity.setTotal(transformers.size());
        Integer high = Constant.ZERO;
        Integer medium = Constant.ZERO;
        Integer light = Constant.ZERO;
        Integer unknown = Constant.ZERO;
        for (PowerTransformerDto transformer : transformers) {
            TransformerlevelEnum anEnum = TransformerlevelEnum.getEnum(transformer.getTransformerlevel());
            if (Objects.isNull(anEnum)) {
                unknown++;
                continue;
            }
            switch (anEnum) {
                case LOWTRANSFORMER:
                    light++;
                    break;
                case MIDTRANSFORMER:
                    medium++;
                    break;
                case HIGHTRANSFORMER:
                    high++;
                    break;
                default:
                    break;
            }
        }
        quantity.setHigh(high);
        quantity.setMedium(medium);
        quantity.setLight(light);
        quantity.setUnknown(unknown);
        return quantity;
    }

    /**
     * 获取事件
     *
     * @return
     */
    public Event getEvent(List<PowerTransformerDto> transformers) {
        Event event = new Event();
        List<Long> transformerIds = transformers.stream().map(PowerTransformerDto::getId).collect(Collectors.toList());
        LocalDateTime firstDayOfThisMonth = TimeUtil.getFirstDayOfThisMonth(LocalDateTime.now());
        LocalDateTime firstDayOfNextMonth = TimeUtil.getFirstDayOfNextMonth(LocalDateTime.now());
        //今天的零点零时
        LocalDateTime firstTimeOfDay = TimeUtil.getFirstTimeOfDay(LocalDateTime.now());
        //明天的零点零时
        LocalDateTime tomorrow = LocalDateTime.now().withNano(0).plusDays(+1);
        //系统事件
        List<SystemEvent> events = getSystemEvents(Constant.TWO, Constant.POWERTRANSFORMER, TimeUtil.localDateTime2timestamp(firstDayOfThisMonth), TimeUtil.localDateTime2timestamp(firstDayOfNextMonth));
        //pec事件
        List<PecEventExtendVo> pecEvents = getPecEventExtendVos(transformerIds, Constant.POWERTRANSFORMER, TimeUtil.localDateTime2timestamp(firstDayOfThisMonth), TimeUtil.localDateTime2timestamp(firstDayOfNextMonth));
        event.setMonthEventCount(events.size() + pecEvents.size());
        List<SystemEvent> todayEvents = events.stream().filter(item -> (item.getEventtime().isAfter(firstTimeOfDay) || item.getEventtime().isEqual(firstTimeOfDay))
                && item.getEventtime().isBefore(tomorrow)).collect(Collectors.toList());

        List<PecEventExtendVo> todayPecEvents = pecEvents.stream().filter(item -> (TimeUtil.timestamp2LocalDateTime(item.getEventtime()).isAfter(firstTimeOfDay)
                || TimeUtil.timestamp2LocalDateTime(item.getEventtime()).isEqual(firstTimeOfDay))
                && TimeUtil.timestamp2LocalDateTime(item.getEventtime()).isBefore(tomorrow)).collect(Collectors.toList());
        event.setDayEventCount(todayEvents.size() + todayPecEvents.size());
        return event;
    }

    /**
     * 获取事件
     *
     * @param transformers
     * @return
     */
    public Event getEvent1(List<PowerTransformerDto> transformers) {
        Event event = new Event();

        //月初月末事件.
        LocalDateTime firstDayOfThisMonth = TimeUtil.getFirstDayOfThisMonth(LocalDateTime.now());
        LocalDateTime firstDayOfNextMonth = TimeUtil.getFirstDayOfNextMonth(LocalDateTime.now());
        //今天的零点零时
        LocalDateTime firstTimeOfDay = TimeUtil.getFirstTimeOfDay(LocalDateTime.now());
        //明天的零点零时
        LocalDateTime tomorrow = LocalDateTime.now().withNano(0).plusDays(+1);
        List<BaseVo> baseVos = getBaseVos(transformers);
        //系统事件 -月度事件
        Long st1 = System.currentTimeMillis();
        Integer sysEventCountMonth = getSysEventCount(firstDayOfThisMonth, firstDayOfNextMonth, baseVos);
        log.info("系统月度事件: {}s", (System.currentTimeMillis() - st1)/1000);
        Long st2 = System.currentTimeMillis();
        Integer sysEventCountToday = getSysEventCount(firstTimeOfDay, tomorrow, baseVos);
        log.info("系统日度事件: {}s", (System.currentTimeMillis() - st2)/1000);
        //percore事件 -月度事件
        Long st3 = System.currentTimeMillis();
        Integer pecEventCountMonth = getPecEventCount(firstDayOfThisMonth, firstDayOfNextMonth, baseVos);
        log.info("核心月度事件: {}s", (System.currentTimeMillis() - st3)/1000);
        //percore事件 -当日事件
        Long st4 = System.currentTimeMillis();
        Integer pecEventCountToday = getPecEventCount(firstTimeOfDay, tomorrow, baseVos);
        log.info("核心日度事件: {}s", (System.currentTimeMillis() - st4)/1000);
        event.setMonthEventCount(sysEventCountMonth + pecEventCountMonth);
        event.setDayEventCount(sysEventCountToday + pecEventCountToday);
        return event;
    }

    public Event getEventForDay(List<PowerTransformerDto> transformers){
        Event event = new Event();

        //今天的零点零时
        LocalDateTime firstTimeOfDay = TimeUtil.getFirstTimeOfDay(LocalDateTime.now());
        //明天的零点零时
        LocalDateTime tomorrow = LocalDateTime.now().withNano(0).plusDays(+1);
        List<BaseVo> baseVos = getBaseVos(transformers);

        Long st2 = System.currentTimeMillis();
        Integer sysEventCountToday = getSysEventCount(firstTimeOfDay, tomorrow, baseVos);
        log.info("系统日度事件: {}s", (System.currentTimeMillis() - st2)/1000);

        Long st4 = System.currentTimeMillis();
        Integer pecEventCountToday = getPecEventCount(firstTimeOfDay, tomorrow, baseVos);
        log.info("核心日度事件: {}s", (System.currentTimeMillis() - st4)/1000);

        event.setDayEventCount(sysEventCountToday + pecEventCountToday);
        return event;
    }

    public Event getEventForMonth(List<PowerTransformerDto> transformers){
        Event event = new Event();

        LocalDateTime firstDayOfThisMonth = TimeUtil.getFirstDayOfThisMonth(LocalDateTime.now());
        LocalDateTime firstDayOfNextMonth = TimeUtil.getFirstDayOfNextMonth(LocalDateTime.now());

        List<BaseVo> baseVos = getBaseVos(transformers);

        Long st1 = System.currentTimeMillis();
        Integer sysEventCountMonth = getSysEventCount(firstDayOfThisMonth, firstDayOfNextMonth, baseVos);
        log.info("系统月度事件: {}s", (System.currentTimeMillis() - st1)/1000);

        Long st3 = System.currentTimeMillis();
        Integer pecEventCountMonth = getPecEventCount(firstDayOfThisMonth, firstDayOfNextMonth, baseVos);
        log.info("核心月度事件: {}s", (System.currentTimeMillis() - st3)/1000);

        event.setMonthEventCount(sysEventCountMonth + pecEventCountMonth);
        return event;
    }

    List<BaseVo> getBaseVos(List<PowerTransformerDto> transformers) {
        return transformers.stream().map(item -> new BaseVo(item.getId(), item.getModelLabel())).collect(Collectors.toList());
    }
    /**
     * 获取系统事件
     *
     * @param st
     * @param end
     * @param baseVos
     * @return
     */
    public Integer getSysEventCount(LocalDateTime st, LocalDateTime end, List<BaseVo> baseVos) {
        SystemEventCountVo countV = new SystemEventCountVo();
        countV.setStartTime(st);
        countV.setEndTime(end);
        countV.setGroupField(ColumnDef.CONFIRM_EVENT_STATUS);
        countV.setNodes(baseVos);
        List<AggregationResult<Integer>> aggregationResults = systemEventDao.countEvent(countV);
        return aggregationResults.stream().mapToInt(AggregationResult::getCount).sum();
    }

    /**
     * 获取percore事件
     *
     * @param st
     * @param end
     * @param baseVos
     * @return
     */
    public Integer getPecEventCount(LocalDateTime st, LocalDateTime end, List<BaseVo> baseVos) {
        PecEventCountVo pecEventCountVo = new PecEventCountVo();
        pecEventCountVo.setStartTime(st);
        pecEventCountVo.setEndTime(end);
        pecEventCountVo.setQueryEndEvent(true);
        pecEventCountVo.setGroupField(ColumnDef.CONFIRM_EVENT_STATUS);
        pecEventCountVo.setNodes(baseVos);
        List<AggregationResult<Integer>> aggregationResults = pecEventService.countEvent(pecEventCountVo);
        return aggregationResults.stream().mapToInt(AggregationResult::getCount).sum();
    }
    /**
     * 获取实时数据
     *
     * @param baseVos
     * @param quantitySettings
     * @return
     */
    public Map<Integer, List<RealTimeValue>> realTimeValueDatas(List<BaseVo> baseVos, List<QuantitySearchVo> quantitySettings) {
        Long st = transformerAnalysisService.handleTime();
        Long et = TimeUtil.addDateTimeByCycle(st, AggregationCycle.ONE_MINUTE, 1);
        return quantityManageService.queryRealTimeBath(transformerAnalysisService.createQuantityDataBatchSearchVoWithRealTime(baseVos, st, et, quantitySettings));
    }

    public List<T> getList(String s, Map<String, Object> nodeRelation) {
        return (List) nodeRelation.get(s);
    }

    /**
     * 变压器分闸
     *
     * @return
     */
    public QuantitySearchVo getTransformerOpening() {
        QuantitySearchVo searchVo = new QuantitySearchVo(9010001,
                QuantityCategoryDef.STATUS,
                QuantityTypeDef.INSTANTANEOUS,
                FrequencyDef.NONE,
                PhasorDef.NONE,
                EnergyTypeDef.ELECTRIC);
        searchVo.setControlMeanType(ControlMeanType.NONE.getValue());
        searchVo.setStateMeanType(16);
        return searchVo;
    }

    /**
     * 变压器合闸
     *
     * @return
     */
    public QuantitySearchVo getTransformerClosing() {
        QuantitySearchVo searchVo = new QuantitySearchVo(9010002,
                QuantityCategoryDef.STATUS,
                QuantityTypeDef.INSTANTANEOUS,
                FrequencyDef.NONE,
                PhasorDef.NONE,
                EnergyTypeDef.ELECTRIC);
        searchVo.setControlMeanType(ControlMeanType.NONE.getValue());
        searchVo.setStateMeanType(17);
        return searchVo;
    }

    public List<SystemEvent> getSystemEvents(Integer energytype, String object_label, Long st, Long end) {
        QueryCondition condition = ParentQueryConditionBuilder.of(Constant.SYSTEMEVENT)
                .eq(Constant.ENERGYTYPE, energytype)
                .eq(Constant.OBJECT_LABEL, object_label)
                .ge(Constant.EVENTTIME, st)
                .lt(Constant.EVENTTIME, end)
                .build();
        return modelServiceUtils.query(condition, SystemEvent.class);
    }

    public List<PecEventExtendVo> getPecEventExtendVos(List<Long> ids, String monitoredlabel, Long st, Long end) {
        QueryCondition condition = ParentQueryConditionBuilder.of(Constant.PECEVENTEXTEND)
                .eq(Constant.MONITOREDLABEL, monitoredlabel)
                .in(Constant.MONITOREDID, ids)
                .ge(Constant.EVENTTIME, st)
                .lt(Constant.EVENTTIME, end)
                .build();
        return modelServiceUtils.query(condition, PecEventExtendVo.class);

    }
}
