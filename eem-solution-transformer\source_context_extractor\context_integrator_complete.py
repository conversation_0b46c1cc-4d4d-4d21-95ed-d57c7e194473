"""
完整的上下文整合器实现

实现上下文整合功能，包括：
1. 当前项目错误上下文分析
2. 迁移前方法实现分析
3. 知识库匹配
4. 业务描述生成
5. 使用场景分析
6. 迁移建议生成
"""

import logging
import re
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from pathlib import Path

try:
    from .models import ErrorItem, MethodAnalysisResult
    from .legacy_code_searcher import OriginalMethod
    from .context_analyzer import MethodContextAnalyzer, MethodContext, IntegratedContext
except ImportError:
    from models import ErrorItem, MethodAnalysisResult
    from legacy_code_searcher import OriginalMethod
    from context_analyzer import MethodContextAnalyzer, MethodContext, IntegratedContext


class ContextIntegratorComplete:
    """完整的上下文整合器 - 整合当前项目错误上下文和迁移前方法实现"""
    
    def __init__(self, knowledge_base_path: str = "知识库"):
        """初始化上下文整合器"""
        self.logger = logging.getLogger(__name__)
        self.knowledge_base_path = Path(knowledge_base_path)
        self.knowledge_base_cache = {}
        self.method_context_analyzer = MethodContextAnalyzer()
        
        # 加载知识库
        self._load_knowledge_base()
    
    def _load_knowledge_base(self):
        """加载知识库文件"""
        try:
            if not self.knowledge_base_path.exists():
                self.logger.warning(f"知识库路径不存在: {self.knowledge_base_path}")
                return
            
            for md_file in self.knowledge_base_path.glob("*.md"):
                try:
                    with open(md_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                    self.knowledge_base_cache[md_file.name] = content
                    self.logger.debug(f"加载知识库文件: {md_file.name}")
                except Exception as e:
                    self.logger.warning(f"加载知识库文件失败 {md_file}: {str(e)}")
            
            self.logger.info(f"知识库加载完成，共 {len(self.knowledge_base_cache)} 个文件")
            
        except Exception as e:
            self.logger.error(f"加载知识库失败: {str(e)}")
    
    def integrate_context(self, error_item: ErrorItem, current_context: str, 
                         original_method: Optional[OriginalMethod] = None) -> IntegratedContext:
        """
        整合上下文信息
        
        Args:
            error_item: 错误项信息
            current_context: 当前项目中的错误上下文
            original_method: 迁移前的原始方法（可选）
            
        Returns:
            IntegratedContext: 整合后的上下文信息
        """
        try:
            self.logger.debug(f"开始整合上下文: {error_item.missing_method}")
            
            # 分析当前项目上下文
            current_method_context = self._analyze_current_context(current_context, error_item)
            
            # 分析原始方法上下文
            original_method_context = None
            if original_method:
                original_method_context = self._analyze_original_method_context(original_method)
            
            # 匹配知识库信息
            knowledge_matches = self._match_knowledge_base(error_item, original_method)
            
            # 生成业务描述
            business_description = self._generate_business_description(
                error_item, current_method_context, original_method_context, knowledge_matches
            )
            
            # 生成使用场景
            usage_scenarios = self._generate_usage_scenarios(
                error_item, current_method_context, original_method_context
            )
            
            # 生成技术上下文
            technical_context = self._generate_technical_context(
                current_method_context, original_method_context
            )
            
            # 生成迁移注意事项
            migration_notes = self._generate_migration_notes(
                error_item, original_method, knowledge_matches
            )
            
            # 生成建议
            recommendations = self._generate_recommendations(
                error_item, original_method, knowledge_matches
            )
            
            # 复杂度分析
            complexity_analysis = self._analyze_complexity(
                error_item, original_method, current_method_context
            )
            
            return IntegratedContext(
                business_description=business_description,
                usage_scenarios=usage_scenarios,
                technical_context=technical_context,
                migration_notes=migration_notes,
                recommendations=recommendations,
                knowledge_base_matches=knowledge_matches,
                complexity_analysis=complexity_analysis
            )
            
        except Exception as e:
            self.logger.error(f"整合上下文失败: {str(e)}")
            return IntegratedContext(
                business_description=f"方法 {error_item.missing_method} 的上下文分析",
                usage_scenarios=["未知使用场景"],
                technical_context="技术上下文分析失败",
                migration_notes="迁移注意事项分析失败",
                recommendations=["建议手动分析该方法"],
                knowledge_base_matches=[],
                complexity_analysis={"level": "unknown", "factors": []}
            )
    
    def _analyze_current_context(self, current_context: str, error_item: ErrorItem) -> Optional[MethodContext]:
        """分析当前项目上下文"""
        try:
            if not current_context:
                return None
            
            return self.method_context_analyzer.analyze_method_context(
                current_context, error_item.missing_method, error_item.class_name
            )
            
        except Exception as e:
            self.logger.warning(f"分析当前上下文失败: {str(e)}")
            return None
    
    def _analyze_original_method_context(self, original_method: OriginalMethod) -> Optional[MethodContext]:
        """分析原始方法上下文"""
        try:
            # 读取原始方法所在文件的完整内容
            with open(original_method.file_path, 'r', encoding='utf-8', errors='ignore') as f:
                file_content = f.read()
            
            return self.method_context_analyzer.analyze_method_context(
                file_content, original_method.method_code.split('(')[0].split()[-1], original_method.class_name
            )
            
        except Exception as e:
            self.logger.warning(f"分析原始方法上下文失败: {str(e)}")
            return None
    
    def _match_knowledge_base(self, error_item: ErrorItem, original_method: Optional[OriginalMethod] = None) -> List[Dict[str, Any]]:
        """匹配知识库信息"""
        try:
            matches = []
            
            # 搜索关键词
            search_terms = [
                error_item.missing_method,
                error_item.class_name,
                error_item.package.split('.')[-1] if error_item.package else "",
            ]
            
            if original_method:
                search_terms.extend([
                    original_method.class_name,
                    original_method.package_name.split('.')[-1] if original_method.package_name else ""
                ])
            
            # 在知识库中搜索匹配项
            for kb_file, kb_content in self.knowledge_base_cache.items():
                file_matches = []
                
                for term in search_terms:
                    if term and term.lower() in kb_content.lower():
                        # 提取相关段落
                        paragraphs = self._extract_relevant_paragraphs(kb_content, term)
                        file_matches.extend(paragraphs)
                
                if file_matches:
                    matches.append({
                        'file': kb_file,
                        'matches': file_matches,
                        'relevance_score': len(file_matches)
                    })
            
            # 按相关性排序
            matches.sort(key=lambda x: x['relevance_score'], reverse=True)
            
            return matches[:5]  # 返回前5个最相关的匹配
            
        except Exception as e:
            self.logger.warning(f"匹配知识库失败: {str(e)}")
            return []
    
    def _extract_relevant_paragraphs(self, content: str, search_term: str) -> List[str]:
        """提取相关段落"""
        try:
            paragraphs = []
            lines = content.split('\n')
            
            for i, line in enumerate(lines):
                if search_term.lower() in line.lower():
                    # 提取前后几行作为上下文
                    start = max(0, i - 2)
                    end = min(len(lines), i + 3)
                    paragraph = '\n'.join(lines[start:end]).strip()
                    if paragraph:
                        paragraphs.append(paragraph)
            
            return paragraphs
            
        except Exception as e:
            self.logger.warning(f"提取相关段落失败: {str(e)}")
            return []
    
    def _generate_business_description(self, error_item: ErrorItem, 
                                     current_context: Optional[MethodContext],
                                     original_context: Optional[MethodContext],
                                     knowledge_matches: List[Dict[str, Any]]) -> str:
        """生成业务描述"""
        try:
            description_parts = []
            
            # 基本信息
            description_parts.append(f"方法 {error_item.missing_method} 位于类 {error_item.class_name}")
            
            # 从当前上下文获取业务信息
            if current_context and current_context.business_context:
                description_parts.append(f"当前上下文: {current_context.business_context}")
            
            # 从原始方法获取业务信息
            if original_context and original_context.business_context:
                description_parts.append(f"原始实现: {original_context.business_context}")
            
            # 从知识库获取业务信息
            if knowledge_matches:
                kb_info = []
                for match in knowledge_matches[:2]:  # 取前2个最相关的
                    if match['matches']:
                        kb_info.append(match['matches'][0][:200] + "...")
                if kb_info:
                    description_parts.append(f"知识库信息: {'; '.join(kb_info)}")
            
            # 参数和返回值信息
            if error_item.in_param:
                param_desc = ", ".join([f"{k}: {v}" for k, v in error_item.in_param.items()])
                description_parts.append(f"参数: {param_desc}")
            
            if error_item.out_return:
                description_parts.append(f"返回值: {error_item.out_return}")
            
            return ". ".join(description_parts) + "."
            
        except Exception as e:
            self.logger.warning(f"生成业务描述失败: {str(e)}")
            return f"方法 {error_item.missing_method} 的业务描述生成失败"
    
    def _generate_usage_scenarios(self, error_item: ErrorItem,
                                current_context: Optional[MethodContext],
                                original_context: Optional[MethodContext]) -> List[str]:
        """生成使用场景"""
        try:
            scenarios = []
            
            # 从当前上下文获取使用场景
            if current_context and current_context.usage_scenarios:
                scenarios.extend(current_context.usage_scenarios)
            
            # 从原始上下文获取使用场景
            if original_context and original_context.usage_scenarios:
                scenarios.extend(original_context.usage_scenarios)
            
            # 根据错误上下文推断使用场景
            if error_item.context:
                if "service" in error_item.context.lower():
                    scenarios.append("服务层业务处理")
                if "controller" in error_item.context.lower():
                    scenarios.append("控制器接口调用")
                if "dao" in error_item.context.lower():
                    scenarios.append("数据访问层操作")
            
            # 去重并返回
            return list(set(scenarios)) if scenarios else ["通用业务方法调用"]
            
        except Exception as e:
            self.logger.warning(f"生成使用场景失败: {str(e)}")
            return ["使用场景分析失败"]
    
    def _generate_technical_context(self, current_context: Optional[MethodContext],
                                  original_context: Optional[MethodContext]) -> str:
        """生成技术上下文"""
        try:
            context_parts = []
            
            # 当前项目技术信息
            if current_context:
                if current_context.imports:
                    context_parts.append(f"当前项目导入: {', '.join(current_context.imports[:5])}")
                if current_context.method_dependencies:
                    context_parts.append(f"方法依赖: {', '.join(current_context.method_dependencies[:3])}")
            
            # 原始方法技术信息
            if original_context:
                if original_context.imports:
                    context_parts.append(f"原始实现导入: {', '.join(original_context.imports[:5])}")
                if original_context.called_methods:
                    context_parts.append(f"调用方法: {', '.join(original_context.called_methods[:3])}")
            
            return "; ".join(context_parts) if context_parts else "技术上下文信息不足"
            
        except Exception as e:
            self.logger.warning(f"生成技术上下文失败: {str(e)}")
            return "技术上下文生成失败"
    
    def _generate_migration_notes(self, error_item: ErrorItem, 
                                original_method: Optional[OriginalMethod],
                                knowledge_matches: List[Dict[str, Any]]) -> str:
        """生成迁移注意事项"""
        try:
            notes = []
            
            # 基本迁移注意事项
            notes.append(f"需要实现方法 {error_item.missing_method}")
            
            if error_item.in_param:
                notes.append(f"注意参数类型匹配: {list(error_item.in_param.keys())}")
            
            if error_item.out_return:
                notes.append(f"注意返回值类型: {error_item.out_return}")
            
            # 从知识库获取迁移建议
            migration_suggestions = self._extract_migration_suggestions(knowledge_matches)
            if migration_suggestions:
                notes.extend(migration_suggestions)
            
            # 从原始方法分析迁移复杂度
            if original_method:
                if original_method.dependencies:
                    notes.append(f"注意依赖项: {', '.join(original_method.dependencies[:3])}")
                
                if len(original_method.method_code) > 1000:
                    notes.append("方法较复杂，建议分步实现")
            
            return "; ".join(notes)
            
        except Exception as e:
            self.logger.warning(f"生成迁移注意事项失败: {str(e)}")
            return "迁移注意事项生成失败"
    
    def _generate_recommendations(self, error_item: ErrorItem,
                                original_method: Optional[OriginalMethod],
                                knowledge_matches: List[Dict[str, Any]]) -> List[str]:
        """生成建议"""
        try:
            recommendations = []
            
            # 基本建议
            recommendations.append(f"参考原始方法实现 {error_item.missing_method}")
            
            if original_method:
                recommendations.append(f"查看原始文件: {original_method.file_path}")
                
                if original_method.javadoc:
                    recommendations.append("参考原始方法的Javadoc文档")
                
                if original_method.annotations:
                    recommendations.append(f"注意原始注解: {', '.join(original_method.annotations[:2])}")
            
            # 从知识库获取建议
            kb_recommendations = self._extract_recommendations_from_kb(knowledge_matches)
            recommendations.extend(kb_recommendations)
            
            # 根据方法类型给出建议
            method_name = error_item.missing_method.lower()
            if method_name.startswith('get') or method_name.startswith('query'):
                recommendations.append("确保查询方法的性能和安全性")
            elif method_name.startswith('save') or method_name.startswith('update'):
                recommendations.append("注意数据验证和事务处理")
            elif method_name.startswith('delete'):
                recommendations.append("确保删除操作的安全性和完整性")
            
            return recommendations[:5]  # 返回前5个建议
            
        except Exception as e:
            self.logger.warning(f"生成建议失败: {str(e)}")
            return ["建议手动分析该方法的实现需求"]
    
    def _analyze_complexity(self, error_item: ErrorItem,
                          original_method: Optional[OriginalMethod],
                          current_context: Optional[MethodContext]) -> Dict[str, Any]:
        """分析复杂度"""
        try:
            complexity_factors = []
            complexity_score = 0
            
            # 参数复杂度
            param_count = len(error_item.in_param) if error_item.in_param else 0
            if param_count > 3:
                complexity_factors.append(f"参数较多({param_count}个)")
                complexity_score += param_count
            
            # 返回值复杂度
            if error_item.out_return and ('List' in error_item.out_return or 'Map' in error_item.out_return):
                complexity_factors.append("返回复杂类型")
                complexity_score += 2
            
            # 原始方法复杂度
            if original_method:
                method_length = len(original_method.method_code)
                if method_length > 1000:
                    complexity_factors.append("原始方法较长")
                    complexity_score += 3
                elif method_length > 500:
                    complexity_factors.append("原始方法中等长度")
                    complexity_score += 1
                
                if len(original_method.dependencies) > 5:
                    complexity_factors.append("依赖较多")
                    complexity_score += 2
            
            # 上下文复杂度
            if current_context:
                if len(current_context.imports) > 10:
                    complexity_factors.append("导入类较多")
                    complexity_score += 1
                
                if len(current_context.called_methods) > 5:
                    complexity_factors.append("调用方法较多")
                    complexity_score += 1
            
            # 确定复杂度等级
            if complexity_score >= 8:
                level = "high"
            elif complexity_score >= 4:
                level = "medium"
            else:
                level = "low"
            
            return {
                "level": level,
                "score": complexity_score,
                "factors": complexity_factors,
                "estimated_effort": self._estimate_effort(level, complexity_score)
            }
            
        except Exception as e:
            self.logger.warning(f"分析复杂度失败: {str(e)}")
            return {"level": "unknown", "score": 0, "factors": [], "estimated_effort": "未知"}
    
    def _estimate_effort(self, level: str, score: int) -> str:
        """估算工作量"""
        if level == "high":
            return "高 (预计需要4-8小时)"
        elif level == "medium":
            return "中 (预计需要2-4小时)"
        else:
            return "低 (预计需要1-2小时)"
    
    def _extract_migration_suggestions(self, knowledge_matches: List[Dict[str, Any]]) -> List[str]:
        """从知识库提取迁移建议"""
        try:
            suggestions = []
            
            for match in knowledge_matches[:2]:  # 只处理前2个最相关的匹配
                for paragraph in match['matches'][:2]:  # 每个匹配只取前2个段落
                    # 查找包含"替换"、"迁移"、"新代码"等关键词的段落
                    if any(keyword in paragraph for keyword in ['替换', '迁移', '新代码', '修改', '变更']):
                        # 提取简短的建议
                        lines = paragraph.split('\n')
                        for line in lines:
                            if any(keyword in line for keyword in ['替换', '迁移', '新代码']):
                                suggestion = line.strip()[:100]  # 限制长度
                                if suggestion:
                                    suggestions.append(suggestion)
                                break
            
            return suggestions[:3]  # 最多返回3个建议
            
        except Exception as e:
            self.logger.warning(f"提取迁移建议失败: {str(e)}")
            return []
    
    def _extract_recommendations_from_kb(self, knowledge_matches: List[Dict[str, Any]]) -> List[str]:
        """从知识库提取建议"""
        try:
            recommendations = []
            
            for match in knowledge_matches[:2]:
                for paragraph in match['matches'][:1]:  # 每个匹配只取第1个段落
                    # 查找包含建议性关键词的内容
                    if any(keyword in paragraph for keyword in ['建议', '推荐', '注意', '确保']):
                        lines = paragraph.split('\n')
                        for line in lines:
                            if any(keyword in line for keyword in ['建议', '推荐', '注意']):
                                rec = line.strip()[:80]  # 限制长度
                                if rec:
                                    recommendations.append(rec)
                                break
            
            return recommendations[:2]  # 最多返回2个建议
            
        except Exception as e:
            self.logger.warning(f"从知识库提取建议失败: {str(e)}")
            return []