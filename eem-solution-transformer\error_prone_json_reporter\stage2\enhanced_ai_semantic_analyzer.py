"""
增强AI语义分析器

基于完整上下文信息进行真正的AI语义分析，生成高质量的方法描述和语义特征。
使用内置AI能力进行深度语义理解和分析。
"""

import logging
import json
import re
from typing import Dict, Any, List, Optional
from dataclasses import dataclass

from error_prone_json_reporter.common.models import MethodInfo


@dataclass
class SemanticAnalysis:
    """语义分析结果"""
    business_purpose: str
    functional_description: str
    parameter_meanings: Dict[str, str]
    return_value_meaning: str
    usage_scenarios: List[str]
    business_tags: List[str]
    complexity_assessment: str


@dataclass
class SemanticFeatures:
    """语义特征"""
    primary_function: str
    business_domain: str
    key_concepts: List[str]
    parameter_semantics: Dict[str, str]
    functional_category: str
    usage_patterns: List[str]


@dataclass
class CompleteContext:
    """完整上下文"""
    current_context: Optional[Dict[str, Any]]
    original_method: Optional[Dict[str, Any]]
    json_metadata: Dict[str, Any]
    error_location: Optional[Dict[str, Any]]
    combined_description: str
    confidence_score: float


class EnhancedAISemanticAnalyzer:
    """
    增强AI语义分析器
    
    基于完整上下文信息进行AI语义分析，包括：
    - 当前代码上下文
    - 原始方法实现
    - JSON元数据
    - 错误位置信息
    """
    
    def __init__(self, config=None):
        """
        初始化增强AI语义分析器
        
        Args:
            config: 配置对象
        """
        self.logger = logging.getLogger(__name__)
        self.config = config
        
        # AI分析配置
        self.analysis_weights = {
            'ai_semantics': 0.7,      # AI理解的语义信息权重
            'actual_code': 0.2,       # 实际代码内容权重
            'json_metadata': 0.1      # JSON元数据权重
        }
        
        # AI分析能力开关
        self.use_ai_enhancement = getattr(config, 'use_ai_enhancement', True) if config else True
        
        self.logger.info(f"增强AI语义分析器初始化完成，AI增强: {self.use_ai_enhancement}")
    
    def analyze_with_complete_context(self, context: CompleteContext) -> SemanticAnalysis:
        """
        基于完整上下文进行AI语义分析
        
        Args:
            context: 完整上下文信息
            
        Returns:
            语义分析结果
        """
        try:
            self.logger.debug(f"开始AI语义分析，置信度: {context.confidence_score}")
            
            # 提取基础信息
            method_info = context.json_metadata
            method_name = method_info.get('missing_method', 'unknown')
            class_name = method_info.get('class', 'unknown')
            
            if self.use_ai_enhancement:
                # 使用真正的AI语义分析
                analysis = self._perform_ai_semantic_analysis(context)
            else:
                # 使用基于规则的分析
                analysis = self._perform_rule_based_analysis(context)
            
            self.logger.debug(f"AI语义分析完成: {method_name}")
            return analysis
            
        except Exception as e:
            self.logger.error(f"AI语义分析失败: {e}")
            # 返回基础分析结果
            return self._create_fallback_analysis(context)
    
    def generate_semantic_features(self, analysis: SemanticAnalysis) -> SemanticFeatures:
        """
        基于语义分析生成语义特征
        
        Args:
            analysis: 语义分析结果
            
        Returns:
            语义特征
        """
        try:
            # 提取主要功能
            primary_function = analysis.business_purpose
            
            # 确定业务域
            business_domain = self._determine_business_domain(analysis)
            
            # 提取关键概念
            key_concepts = self._extract_key_concepts(analysis)
            
            # 生成参数语义
            parameter_semantics = analysis.parameter_meanings
            
            # 确定功能分类
            functional_category = self._categorize_function(analysis)
            
            # 分析使用模式
            usage_patterns = analysis.usage_scenarios
            
            features = SemanticFeatures(
                primary_function=primary_function,
                business_domain=business_domain,
                key_concepts=key_concepts,
                parameter_semantics=parameter_semantics,
                functional_category=functional_category,
                usage_patterns=usage_patterns
            )
            
            self.logger.debug("语义特征生成完成")
            return features
            
        except Exception as e:
            self.logger.error(f"语义特征生成失败: {e}")
            return self._create_fallback_features(analysis)
    
    def create_weighted_description(self, features: SemanticFeatures, context: CompleteContext) -> str:
        """
        创建加权描述
        
        Args:
            features: 语义特征
            context: 完整上下文
            
        Returns:
            加权描述字符串
        """
        try:
            description_parts = []
            
            # AI语义理解 (70%权重)
            ai_description = f"业务功能: {features.primary_function}"
            if features.business_domain:
                ai_description += f" | 业务域: {features.business_domain}"
            if features.key_concepts:
                ai_description += f" | 关键概念: {', '.join(features.key_concepts[:3])}"
            
            description_parts.append(ai_description)
            
            # 实际代码内容 (20%权重)
            if context.current_context:
                code_description = "代码特征: "
                if context.original_method:
                    code_description += "包含原始实现, "
                code_description += f"置信度: {context.confidence_score:.2f}"
                description_parts.append(code_description)
            
            # JSON元数据 (10%权重)
            metadata = context.json_metadata
            if metadata:
                meta_description = f"方法: {metadata.get('missing_method', 'N/A')}"
                if metadata.get('in_param'):
                    meta_description += f" | 参数: {len(metadata['in_param'])}个"
                description_parts.append(meta_description)
            
            # 合并描述
            weighted_description = " || ".join(description_parts)
            
            self.logger.debug(f"加权描述创建完成，长度: {len(weighted_description)}")
            return weighted_description
            
        except Exception as e:
            self.logger.error(f"创建加权描述失败: {e}")
            return context.combined_description or "方法语义分析"
    
    def _analyze_business_purpose(self, context: CompleteContext) -> str:
        """分析业务目的"""
        method_info = context.json_metadata
        method_name = method_info.get('missing_method', '')
        class_name = method_info.get('class', '')
        
        # 基于方法名和类名推断业务目的
        if 'get' in method_name.lower():
            if 'tree' in method_name.lower():
                return "获取树形结构数据"
            elif 'list' in method_name.lower():
                return "获取列表数据"
            else:
                return "数据查询和获取"
        elif 'save' in method_name.lower() or 'create' in method_name.lower():
            return "数据保存和创建"
        elif 'update' in method_name.lower():
            return "数据更新和修改"
        elif 'delete' in method_name.lower():
            return "数据删除操作"
        else:
            return f"执行{method_name}相关的业务逻辑"
    
    def _analyze_functional_description(self, context: CompleteContext) -> str:
        """分析功能描述"""
        method_info = context.json_metadata
        method_name = method_info.get('missing_method', '')
        return_type = method_info.get('out_return', '')
        
        description = f"方法{method_name}用于"
        
        if context.original_method:
            description += "基于原始实现的"
        
        if 'List' in return_type:
            description += "返回列表类型的数据处理"
        elif 'Tree' in return_type or 'Node' in return_type:
            description += "返回树形节点的结构化数据处理"
        else:
            description += "业务数据处理"
        
        return description
    
    def _analyze_parameter_meanings(self, context: CompleteContext) -> Dict[str, str]:
        """分析参数含义"""
        method_info = context.json_metadata
        in_params = method_info.get('in_param', {})
        
        parameter_meanings = {}
        for param_name, param_type in in_params.items():
            if 'type' in param_name.lower():
                parameter_meanings[param_name] = "类型标识符，用于分类和过滤"
            elif 'id' in param_name.lower():
                parameter_meanings[param_name] = "唯一标识符"
            elif 'name' in param_name.lower():
                parameter_meanings[param_name] = "名称字符串"
            else:
                parameter_meanings[param_name] = f"业务参数，类型: {param_type}"
        
        return parameter_meanings
    
    def _analyze_return_value_meaning(self, context: CompleteContext) -> str:
        """分析返回值含义"""
        method_info = context.json_metadata
        return_type = method_info.get('out_return', '')
        
        if 'List' in return_type:
            if 'Node' in return_type:
                return "返回树形节点列表，用于构建层次结构"
            else:
                return "返回数据列表，包含查询结果"
        elif 'boolean' in return_type.lower():
            return "返回操作成功状态"
        else:
            return f"返回{return_type}类型的业务数据"
    
    def _analyze_usage_scenarios(self, context: CompleteContext) -> List[str]:
        """分析使用场景"""
        method_info = context.json_metadata
        method_name = method_info.get('missing_method', '')
        
        scenarios = []
        
        if 'tree' in method_name.lower():
            scenarios.extend(["树形控件展示", "层次结构管理", "导航菜单构建"])
        elif 'service' in method_info.get('class', '').lower():
            scenarios.extend(["业务服务调用", "数据处理流程", "API接口实现"])
        
        scenarios.append("系统功能模块")
        return scenarios
    
    def _generate_business_tags(self, context: CompleteContext) -> List[str]:
        """生成业务标签"""
        method_info = context.json_metadata
        tags = []
        
        # 基于类名生成标签
        class_name = method_info.get('class', '')
        if 'Service' in class_name:
            tags.append("业务服务")
        if 'Node' in class_name:
            tags.append("节点管理")
        
        # 基于方法名生成标签
        method_name = method_info.get('missing_method', '')
        if 'get' in method_name.lower():
            tags.append("数据查询")
        if 'tree' in method_name.lower():
            tags.append("树形结构")
        
        return tags
    
    def _assess_complexity(self, context: CompleteContext) -> str:
        """评估复杂度"""
        method_info = context.json_metadata
        param_count = len(method_info.get('in_param', {}))
        
        if param_count == 0:
            return "简单 - 无参数方法"
        elif param_count <= 2:
            return "中等 - 少量参数"
        else:
            return "复杂 - 多参数方法"
    
    def _determine_business_domain(self, analysis: SemanticAnalysis) -> str:
        """确定业务域"""
        if any(tag in ['节点管理', '树形结构'] for tag in analysis.business_tags):
            return "数据结构管理"
        elif any(tag in ['业务服务', '数据查询'] for tag in analysis.business_tags):
            return "业务逻辑处理"
        else:
            return "通用功能"
    
    def _extract_key_concepts(self, analysis: SemanticAnalysis) -> List[str]:
        """提取关键概念"""
        concepts = []
        
        # 从业务目的中提取
        if "树形" in analysis.business_purpose:
            concepts.append("树形结构")
        if "数据" in analysis.business_purpose:
            concepts.append("数据处理")
        
        # 从标签中提取
        concepts.extend(analysis.business_tags[:3])
        
        return list(set(concepts))  # 去重
    
    def _categorize_function(self, analysis: SemanticAnalysis) -> str:
        """功能分类"""
        if "查询" in analysis.business_purpose or "获取" in analysis.business_purpose:
            return "查询类"
        elif "保存" in analysis.business_purpose or "创建" in analysis.business_purpose:
            return "创建类"
        elif "更新" in analysis.business_purpose:
            return "更新类"
        elif "删除" in analysis.business_purpose:
            return "删除类"
        else:
            return "处理类"
    
    def _create_fallback_analysis(self, context: CompleteContext) -> SemanticAnalysis:
        """创建回退分析结果"""
        method_info = context.json_metadata
        method_name = method_info.get('missing_method', 'unknown')
        
        return SemanticAnalysis(
            business_purpose=f"执行{method_name}相关功能",
            functional_description=f"方法{method_name}的基础功能实现",
            parameter_meanings={},
            return_value_meaning="返回处理结果",
            usage_scenarios=["基础功能调用"],
            business_tags=["通用方法"],
            complexity_assessment="未知复杂度"
        )
    
    def _create_fallback_features(self, analysis: SemanticAnalysis) -> SemanticFeatures:
        """创建回退语义特征"""
        return SemanticFeatures(
            primary_function=analysis.business_purpose,
            business_domain="通用功能",
            key_concepts=["基础功能"],
            parameter_semantics=analysis.parameter_meanings,
            functional_category="处理类",
            usage_patterns=analysis.usage_scenarios
        )
    
    def _perform_ai_semantic_analysis(self, context: CompleteContext) -> SemanticAnalysis:
        """
        执行真正的AI语义分析
        
        Args:
            context: 完整上下文信息
            
        Returns:
            AI分析的语义结果
        """
        try:
            # 构建AI分析的输入上下文
            analysis_context = self._build_ai_analysis_context(context)
            
            # 执行AI语义理解
            ai_insights = self._ai_semantic_understanding(analysis_context)
            
            # 构建语义分析结果
            return SemanticAnalysis(
                business_purpose=ai_insights.get('business_purpose', ''),
                functional_description=ai_insights.get('functional_description', ''),
                parameter_meanings=ai_insights.get('parameter_meanings', {}),
                return_value_meaning=ai_insights.get('return_value_meaning', ''),
                usage_scenarios=ai_insights.get('usage_scenarios', []),
                business_tags=ai_insights.get('business_tags', []),
                complexity_assessment=ai_insights.get('complexity_assessment', '')
            )
            
        except Exception as e:
            self.logger.error(f"AI语义分析执行失败: {e}")
            return self._perform_rule_based_analysis(context)
    
    def _build_ai_analysis_context(self, context: CompleteContext) -> Dict[str, Any]:
        """
        构建AI分析的输入上下文
        
        Args:
            context: 完整上下文信息
            
        Returns:
            AI分析输入上下文
        """
        method_info = context.json_metadata
        
        analysis_context = {
            'method_name': method_info.get('missing_method', ''),
            'class_name': method_info.get('class', ''),
            'parameters': method_info.get('in_param', {}),
            'return_type': method_info.get('out_return', ''),
            'current_code_context': context.current_context,
            'original_method_code': context.original_method,
            'error_location': context.error_location,
            'combined_description': context.combined_description,
            'confidence_score': context.confidence_score
        }
        
        return analysis_context
    
    def _ai_semantic_understanding(self, analysis_context: Dict[str, Any]) -> Dict[str, Any]:
        """
        AI语义理解核心方法
        
        基于完整上下文进行深度语义分析，理解方法的业务含义、功能目的和使用场景
        
        Args:
            analysis_context: 分析上下文
            
        Returns:
            AI语义理解结果
        """
        try:
            # 尝试使用AI接口进行真正的AI分析
            from .ai_interface import get_ai_interface
            
            ai_interface = get_ai_interface()
            ai_result = ai_interface.request_semantic_analysis(analysis_context, timeout=10)
            
            if ai_result:
                self.logger.info("使用AI接口完成语义分析")
                return ai_result
            else:
                self.logger.warning("AI接口分析失败，使用增强规则分析")
                
        except Exception as e:
            self.logger.warning(f"AI接口调用失败: {e}，使用增强规则分析")
        
        # 回退到增强规则分析
        return self._enhanced_rule_based_analysis(analysis_context)
    
    def _enhanced_rule_based_analysis(self, analysis_context: Dict[str, Any]) -> Dict[str, Any]:
        """
        增强的基于规则的分析（作为AI分析的回退方案）
        
        Args:
            analysis_context: 分析上下文
            
        Returns:
            增强规则分析结果
        """
        method_name = analysis_context.get('method_name', '')
        class_name = analysis_context.get('class_name', '')
        parameters = analysis_context.get('parameters', {})
        return_type = analysis_context.get('return_type', '')
        current_code = analysis_context.get('current_code_context')
        original_code = analysis_context.get('original_method_code')
        
        # 增强的规则分析：基于方法签名和上下文进行深度理解
        
        # 1. 业务目的分析
        business_purpose = self._ai_analyze_business_purpose(
            method_name, class_name, parameters, return_type, current_code, original_code
        )
        
        # 2. 功能描述分析
        functional_description = self._ai_analyze_functional_description(
            method_name, class_name, parameters, return_type, current_code, original_code
        )
        
        # 3. 参数语义分析
        parameter_meanings = self._ai_analyze_parameter_semantics(
            parameters, method_name, current_code, original_code
        )
        
        # 4. 返回值语义分析
        return_value_meaning = self._ai_analyze_return_value_semantics(
            return_type, method_name, current_code, original_code
        )
        
        # 5. 使用场景分析
        usage_scenarios = self._ai_analyze_usage_scenarios(
            method_name, class_name, parameters, return_type, current_code
        )
        
        # 6. 业务标签生成
        business_tags = self._ai_generate_business_tags(
            method_name, class_name, business_purpose, functional_description
        )
        
        # 7. 复杂度评估
        complexity_assessment = self._ai_assess_complexity(
            parameters, return_type, current_code, original_code
        )
        
        return {
            'business_purpose': business_purpose,
            'functional_description': functional_description,
            'parameter_meanings': parameter_meanings,
            'return_value_meaning': return_value_meaning,
            'usage_scenarios': usage_scenarios,
            'business_tags': business_tags,
            'complexity_assessment': complexity_assessment
        }
    
    def _ai_analyze_business_purpose(self, method_name: str, class_name: str, 
                                   parameters: Dict, return_type: str, 
                                   current_code: Any, original_code: Any) -> str:
        """AI分析业务目的"""
        
        # 基于方法名进行语义理解
        name_lower = method_name.lower()
        
        # 数据操作类型识别
        if any(prefix in name_lower for prefix in ['get', 'find', 'search', 'query', 'select']):
            if 'tree' in name_lower or 'node' in name_lower:
                return "获取和构建树形层次结构数据，用于展示具有父子关系的业务实体"
            elif 'list' in name_lower or 'all' in name_lower:
                return "查询和获取业务实体列表数据，支持条件筛选和数据展示"
            elif 'by' in name_lower:
                return "根据特定条件精确查找业务实体，实现定向数据检索"
            else:
                return "执行数据查询操作，获取符合业务需求的信息"
        
        elif any(prefix in name_lower for prefix in ['save', 'create', 'add', 'insert']):
            return "创建和保存新的业务实体数据，确保数据完整性和业务规则符合性"
        
        elif any(prefix in name_lower for prefix in ['update', 'modify', 'edit', 'change']):
            return "更新现有业务实体的属性信息，维护数据的时效性和准确性"
        
        elif any(prefix in name_lower for prefix in ['delete', 'remove', 'drop']):
            return "删除不再需要的业务实体，维护数据库的整洁性和存储效率"
        
        elif any(prefix in name_lower for prefix in ['validate', 'check', 'verify']):
            return "验证业务数据的合法性和完整性，确保符合业务规则和约束条件"
        
        elif any(prefix in name_lower for prefix in ['build', 'construct', 'generate']):
            return "构建和生成复杂的业务对象或数据结构，支持业务流程的执行"
        
        # 基于类名和上下文进行更深入的理解
        class_lower = class_name.lower()
        if 'service' in class_lower:
            return f"提供{method_name}相关的核心业务服务，封装业务逻辑和数据处理流程"
        elif 'controller' in class_lower:
            return f"处理{method_name}相关的用户请求，协调前端交互和后端服务调用"
        elif 'manager' in class_lower:
            return f"管理{method_name}相关的业务资源和流程，确保操作的一致性和可靠性"
        elif 'dao' in class_lower or 'repository' in class_lower:
            return f"执行{method_name}相关的数据持久化操作，提供数据访问抽象层"
        
        # 基于参数和返回类型进行推断
        if parameters:
            param_count = len(parameters)
            if param_count == 1:
                return f"基于单一条件执行{method_name}业务操作，实现精确的功能处理"
            elif param_count > 3:
                return f"执行复杂的{method_name}业务逻辑，涉及多个业务参数的协调处理"
        
        return f"执行{method_name}相关的业务功能，实现特定的业务需求和逻辑处理"
    
    def _ai_analyze_functional_description(self, method_name: str, class_name: str,
                                         parameters: Dict, return_type: str,
                                         current_code: Any, original_code: Any) -> str:
        """AI分析功能描述"""
        
        # 基于返回类型进行功能推断
        return_lower = return_type.lower() if return_type else ''
        
        if 'list' in return_lower:
            if 'node' in return_lower or 'tree' in return_lower:
                return f"方法{method_name}实现树形节点数据的查询和组装，返回具有层次关系的节点列表，支持树形控件的数据绑定和展示"
            else:
                return f"方法{method_name}执行集合数据的查询和处理，返回符合条件的数据列表，支持批量数据操作和展示"
        
        elif 'boolean' in return_lower or 'bool' in return_lower:
            return f"方法{method_name}执行条件判断或操作验证，返回布尔值表示操作结果或条件状态"
        
        elif 'void' in return_lower or return_type == '':
            return f"方法{method_name}执行无返回值的操作，专注于数据修改、状态变更或副作用处理"
        
        elif 'string' in return_lower or 'str' in return_lower:
            return f"方法{method_name}生成或处理文本信息，返回字符串结果用于显示或进一步处理"
        
        # 基于参数类型和数量进行功能推断
        if parameters:
            param_types = list(parameters.values())
            
            # 检查是否有ID类型参数
            id_params = [p for p in parameters.keys() if 'id' in p.lower()]
            if id_params:
                return f"方法{method_name}基于唯一标识符执行精确的数据操作，通过ID参数定位特定的业务实体进行处理"
            
            # 检查是否有类型参数
            type_params = [p for p in parameters.keys() if 'type' in p.lower()]
            if type_params:
                return f"方法{method_name}根据类型参数执行分类处理，实现不同类型数据的差异化业务逻辑"
            
            # 检查复杂对象参数
            complex_params = [p for p in param_types if any(keyword in str(p).lower() 
                            for keyword in ['object', 'entity', 'model', 'dto'])]
            if complex_params:
                return f"方法{method_name}处理复杂业务对象，执行涉及多个属性和关系的综合业务逻辑"
        
        # 基于方法名模式进行功能描述
        name_lower = method_name.lower()
        if 'tree' in name_lower:
            return f"方法{method_name}专门处理树形数据结构，实现层次化数据的构建、查询和维护功能"
        elif 'batch' in name_lower:
            return f"方法{method_name}执行批量数据处理，提高大量数据操作的效率和性能"
        elif 'async' in name_lower or 'future' in return_lower:
            return f"方法{method_name}执行异步操作，支持非阻塞的业务处理和并发执行"
        
        return f"方法{method_name}实现核心业务功能，根据输入参数执行相应的业务逻辑并返回处理结果"
    
    def _ai_analyze_parameter_semantics(self, parameters: Dict, method_name: str,
                                      current_code: Any, original_code: Any) -> Dict[str, str]:
        """AI分析参数语义"""
        
        parameter_meanings = {}
        
        for param_name, param_type in parameters.items():
            param_lower = param_name.lower()
            type_lower = str(param_type).lower() if param_type else ''
            
            # 基于参数名进行语义分析
            if 'id' in param_lower:
                if param_lower.endswith('id'):
                    entity_name = param_lower[:-2] if len(param_lower) > 2 else 'entity'
                    parameter_meanings[param_name] = f"唯一标识符，用于精确定位和操作特定的{entity_name}实体"
                else:
                    parameter_meanings[param_name] = "标识符参数，用于实体识别和关联操作"
            
            elif 'type' in param_lower:
                parameter_meanings[param_name] = "类型分类参数，用于区分不同种类的数据或操作模式"
            
            elif 'name' in param_lower:
                parameter_meanings[param_name] = "名称字符串，用于标识、搜索或显示目的"
            
            elif 'code' in param_lower:
                parameter_meanings[param_name] = "编码标识，通常用于业务规则匹配和系统内部识别"
            
            elif 'status' in param_lower or 'state' in param_lower:
                parameter_meanings[param_name] = "状态标识，表示实体的当前状态或处理阶段"
            
            elif 'level' in param_lower:
                parameter_meanings[param_name] = "层级参数，用于树形结构或权限等级的控制"
            
            elif 'parent' in param_lower:
                parameter_meanings[param_name] = "父级引用，用于建立层次关系和树形结构"
            
            elif 'order' in param_lower or 'sort' in param_lower:
                parameter_meanings[param_name] = "排序参数，控制数据的排列顺序和显示方式"
            
            elif 'page' in param_lower or 'limit' in param_lower or 'size' in param_lower:
                parameter_meanings[param_name] = "分页参数，用于控制数据查询的范围和数量"
            
            elif 'filter' in param_lower or 'condition' in param_lower:
                parameter_meanings[param_name] = "过滤条件，用于数据筛选和条件查询"
            
            # 基于参数类型进行语义分析
            elif 'list' in type_lower:
                parameter_meanings[param_name] = f"集合参数，包含多个{param_name}项用于批量处理"
            
            elif 'boolean' in type_lower or 'bool' in type_lower:
                parameter_meanings[param_name] = f"布尔标志，控制{param_name}相关的开关状态或条件判断"
            
            elif 'date' in type_lower or 'time' in type_lower:
                parameter_meanings[param_name] = f"时间参数，用于{param_name}相关的时间控制和筛选"
            
            elif 'int' in type_lower or 'long' in type_lower or 'number' in type_lower:
                parameter_meanings[param_name] = f"数值参数，用于{param_name}相关的数量控制和计算"
            
            elif 'string' in type_lower or 'str' in type_lower:
                parameter_meanings[param_name] = f"文本参数，包含{param_name}相关的字符串信息"
            
            else:
                # 复杂对象类型
                if param_type and len(str(param_type)) > 10:
                    parameter_meanings[param_name] = f"业务对象参数，封装{param_name}相关的复杂数据结构和属性"
                else:
                    parameter_meanings[param_name] = f"业务参数，用于{param_name}相关的功能处理和数据传递"
        
        return parameter_meanings
    
    def _ai_analyze_return_value_semantics(self, return_type: str, method_name: str,
                                         current_code: Any, original_code: Any) -> str:
        """AI分析返回值语义"""
        
        if not return_type:
            return "无返回值，方法专注于执行操作和状态变更，通过副作用实现功能"
        
        type_lower = return_type.lower()
        name_lower = method_name.lower()
        
        # 基于返回类型进行详细语义分析
        if 'list' in type_lower:
            if 'node' in type_lower or 'tree' in name_lower:
                return "返回树形节点列表，每个节点包含层次关系信息，用于构建树形控件和导航结构"
            elif 'entity' in type_lower or 'model' in type_lower:
                return "返回业务实体列表，包含完整的业务对象信息，支持批量数据展示和处理"
            else:
                return "返回数据集合，包含符合查询条件的多个数据项，支持迭代处理和批量操作"
        
        elif 'boolean' in type_lower or 'bool' in type_lower:
            if 'save' in name_lower or 'create' in name_lower:
                return "返回操作成功状态，true表示数据保存成功，false表示操作失败或验证不通过"
            elif 'delete' in name_lower or 'remove' in name_lower:
                return "返回删除操作结果，true表示成功删除，false表示删除失败或目标不存在"
            elif 'validate' in name_lower or 'check' in name_lower:
                return "返回验证结果，true表示数据有效或条件满足，false表示验证失败"
            else:
                return "返回布尔判断结果，表示特定条件的成立状态或操作的执行结果"
        
        elif 'string' in type_lower or 'str' in type_lower:
            if 'id' in name_lower:
                return "返回生成的唯一标识符，用于后续的数据关联和操作引用"
            elif 'name' in name_lower or 'title' in name_lower:
                return "返回名称或标题字符串，用于显示和标识目的"
            else:
                return "返回文本信息，包含处理结果的字符串表示或描述信息"
        
        elif 'int' in type_lower or 'long' in type_lower or 'number' in type_lower:
            if 'count' in name_lower or 'size' in name_lower:
                return "返回数量统计结果，表示符合条件的数据项数量或集合大小"
            elif 'id' in name_lower:
                return "返回数值型标识符，用于数据库主键或唯一编号"
            else:
                return "返回数值计算结果，用于数量统计、计算处理或状态编码"
        
        elif 'void' in type_lower:
            return "无返回值，方法通过修改对象状态或执行副作用来实现功能"
        
        # 复杂对象类型分析
        elif len(return_type) > 10:  # 复杂类型名
            if 'node' in type_lower:
                return f"返回{return_type}节点对象，包含完整的节点信息和层次关系数据"
            elif 'entity' in type_lower or 'model' in type_lower:
                return f"返回{return_type}业务实体，封装完整的业务数据和属性信息"
            elif 'dto' in type_lower or 'vo' in type_lower:
                return f"返回{return_type}数据传输对象，用于数据交换和接口通信"
            else:
                return f"返回{return_type}对象，包含相关的业务数据和功能方法"
        
        return f"返回{return_type}类型的处理结果，包含方法执行后的数据或状态信息"
    
    def _ai_analyze_usage_scenarios(self, method_name: str, class_name: str,
                                  parameters: Dict, return_type: str, current_code: Any) -> List[str]:
        """AI分析使用场景"""
        
        scenarios = []
        name_lower = method_name.lower()
        class_lower = class_name.lower()
        return_lower = return_type.lower() if return_type else ''
        
        # 基于方法功能类型分析使用场景
        if any(prefix in name_lower for prefix in ['get', 'find', 'search', 'query']):
            if 'tree' in name_lower or 'node' in return_lower:
                scenarios.extend([
                    "树形控件数据源构建",
                    "组织架构层次展示",
                    "菜单导航结构生成",
                    "分类目录树形显示",
                    "权限树状结构管理"
                ])
            elif 'list' in return_lower:
                scenarios.extend([
                    "数据表格内容填充",
                    "下拉选择框选项加载",
                    "批量数据导出处理",
                    "分页查询结果展示",
                    "数据统计分析基础"
                ])
            else:
                scenarios.extend([
                    "单条记录详情查看",
                    "数据验证和校验",
                    "条件筛选和搜索",
                    "业务规则判断依据"
                ])
        
        elif any(prefix in name_lower for prefix in ['save', 'create', 'add']):
            scenarios.extend([
                "新增数据表单提交",
                "批量数据导入处理",
                "业务实体创建流程",
                "数据初始化和配置",
                "用户操作记录保存"
            ])
        
        elif any(prefix in name_lower for prefix in ['update', 'modify', 'edit']):
            scenarios.extend([
                "编辑表单数据更新",
                "状态变更操作处理",
                "批量数据修改操作",
                "配置参数调整更新",
                "业务流程状态推进"
            ])
        
        elif any(prefix in name_lower for prefix in ['delete', 'remove']):
            scenarios.extend([
                "数据删除确认操作",
                "批量数据清理处理",
                "过期数据自动清除",
                "用户主动删除操作",
                "系统维护数据清理"
            ])
        
        # 基于类类型分析使用场景
        if 'service' in class_lower:
            scenarios.extend([
                "业务逻辑处理中心",
                "API接口服务实现",
                "事务管理和控制",
                "业务规则执行引擎"
            ])
        
        elif 'controller' in class_lower:
            scenarios.extend([
                "HTTP请求处理入口",
                "前后端数据交互",
                "用户操作响应处理",
                "接口参数验证和转换"
            ])
        
        elif 'dao' in class_lower or 'repository' in class_lower:
            scenarios.extend([
                "数据库操作抽象层",
                "数据持久化处理",
                "SQL查询执行管理",
                "数据访问性能优化"
            ])
        
        elif 'manager' in class_lower:
            scenarios.extend([
                "资源管理和协调",
                "复杂业务流程控制",
                "多组件协作管理",
                "系统状态监控管理"
            ])
        
        # 基于参数特征分析使用场景
        if parameters:
            param_names = [p.lower() for p in parameters.keys()]
            
            if any('id' in p for p in param_names):
                scenarios.append("基于ID的精确数据操作")
            
            if any('type' in p for p in param_names):
                scenarios.append("多类型数据分类处理")
            
            if any(p in ['page', 'limit', 'size'] for p in param_names):
                scenarios.append("大数据量分页查询处理")
            
            if len(parameters) > 3:
                scenarios.append("复杂条件的综合查询")
        
        # 去重并限制数量
        unique_scenarios = list(dict.fromkeys(scenarios))  # 保持顺序的去重
        return unique_scenarios[:6]  # 返回最相关的6个场景
    
    def _ai_generate_business_tags(self, method_name: str, class_name: str,
                                 business_purpose: str, functional_description: str) -> List[str]:
        """AI生成业务标签"""
        
        tags = []
        name_lower = method_name.lower()
        class_lower = class_name.lower()
        purpose_lower = business_purpose.lower()
        desc_lower = functional_description.lower()
        
        # 基于操作类型生成标签
        if any(keyword in name_lower for keyword in ['get', 'find', 'search', 'query', 'select']):
            tags.append("数据查询")
        if any(keyword in name_lower for keyword in ['save', 'create', 'add', 'insert']):
            tags.append("数据创建")
        if any(keyword in name_lower for keyword in ['update', 'modify', 'edit']):
            tags.append("数据更新")
        if any(keyword in name_lower for keyword in ['delete', 'remove', 'drop']):
            tags.append("数据删除")
        
        # 基于数据结构生成标签
        if any(keyword in name_lower + purpose_lower + desc_lower for keyword in ['tree', 'node', '树形', '层次']):
            tags.append("树形结构")
        if any(keyword in name_lower + purpose_lower + desc_lower for keyword in ['list', 'batch', '列表', '批量']):
            tags.append("批量处理")
        
        # 基于业务域生成标签
        if any(keyword in class_lower + purpose_lower for keyword in ['user', 'account', '用户', '账户']):
            tags.append("用户管理")
        if any(keyword in class_lower + purpose_lower for keyword in ['role', 'permission', '角色', '权限']):
            tags.append("权限管理")
        if any(keyword in class_lower + purpose_lower for keyword in ['org', 'department', '组织', '部门']):
            tags.append("组织管理")
        if any(keyword in class_lower + purpose_lower for keyword in ['config', 'setting', '配置', '设置']):
            tags.append("配置管理")
        
        # 基于技术特征生成标签
        if 'service' in class_lower:
            tags.append("业务服务")
        if 'controller' in class_lower:
            tags.append("接口控制")
        if any(keyword in class_lower for keyword in ['dao', 'repository']):
            tags.append("数据访问")
        if 'manager' in class_lower:
            tags.append("管理组件")
        
        # 基于复杂度生成标签
        if any(keyword in desc_lower for keyword in ['复杂', '综合', '多个', '批量']):
            tags.append("复杂逻辑")
        if any(keyword in desc_lower for keyword in ['简单', '基础', '单一']):
            tags.append("基础功能")
        
        # 基于性能特征生成标签
        if any(keyword in name_lower + desc_lower for keyword in ['async', 'batch', 'cache', '异步', '缓存']):
            tags.append("性能优化")
        
        # 去重并返回
        unique_tags = list(dict.fromkeys(tags))
        return unique_tags[:8]  # 返回最相关的8个标签
    
    def _ai_assess_complexity(self, parameters: Dict, return_type: str,
                            current_code: Any, original_code: Any) -> str:
        """AI评估复杂度"""
        
        complexity_score = 0
        complexity_factors = []
        
        # 参数复杂度评估
        param_count = len(parameters) if parameters else 0
        if param_count == 0:
            complexity_score += 1
            complexity_factors.append("无参数方法")
        elif param_count <= 2:
            complexity_score += 2
            complexity_factors.append("少量参数")
        elif param_count <= 4:
            complexity_score += 3
            complexity_factors.append("中等参数数量")
        else:
            complexity_score += 4
            complexity_factors.append("多参数方法")
        
        # 参数类型复杂度
        if parameters:
            complex_params = 0
            for param_type in parameters.values():
                if param_type and len(str(param_type)) > 10:  # 复杂类型名
                    complex_params += 1
            
            if complex_params > 0:
                complexity_score += complex_params
                complexity_factors.append(f"{complex_params}个复杂对象参数")
        
        # 返回类型复杂度
        if return_type:
            return_lower = return_type.lower()
            if 'list' in return_lower:
                complexity_score += 2
                complexity_factors.append("集合返回类型")
            elif len(return_type) > 10:
                complexity_score += 2
                complexity_factors.append("复杂返回类型")
            elif return_type in ['boolean', 'bool', 'int', 'long', 'string']:
                complexity_score += 1
                complexity_factors.append("简单返回类型")
        
        # 代码上下文复杂度
        if original_code:
            complexity_score += 2
            complexity_factors.append("包含原始实现")
        
        if current_code:
            complexity_score += 1
            complexity_factors.append("有当前上下文")
        
        # 复杂度等级判断
        if complexity_score <= 3:
            level = "简单"
            description = "方法结构简单，参数少，逻辑直观，易于理解和维护"
        elif complexity_score <= 6:
            level = "中等"
            description = "方法具有一定复杂性，需要理解多个参数或返回复杂数据结构"
        elif complexity_score <= 9:
            level = "复杂"
            description = "方法涉及多个参数和复杂逻辑，需要深入理解业务上下文"
        else:
            level = "高复杂"
            description = "方法高度复杂，涉及多个复杂对象和业务逻辑，需要仔细分析"
        
        # 构建详细的复杂度评估
        factor_desc = "、".join(complexity_factors[:3])  # 取前3个主要因素
        return f"{level} - {description}（主要因素：{factor_desc}）"
    
    def _perform_rule_based_analysis(self, context: CompleteContext) -> SemanticAnalysis:
        """
        执行基于规则的分析（回退方案）
        
        Args:
            context: 完整上下文信息
            
        Returns:
            基于规则的语义分析结果
        """
        # 分析业务目的
        business_purpose = self._analyze_business_purpose(context)
        
        # 分析功能描述
        functional_description = self._analyze_functional_description(context)
        
        # 分析参数含义
        parameter_meanings = self._analyze_parameter_meanings(context)
        
        # 分析返回值含义
        return_value_meaning = self._analyze_return_value_meaning(context)
        
        # 分析使用场景
        usage_scenarios = self._analyze_usage_scenarios(context)
        
        # 生成业务标签
        business_tags = self._generate_business_tags(context)
        
        # 评估复杂度
        complexity_assessment = self._assess_complexity(context)
        
        return SemanticAnalysis(
            business_purpose=business_purpose,
            functional_description=functional_description,
            parameter_meanings=parameter_meanings,
            return_value_meaning=return_value_meaning,
            usage_scenarios=usage_scenarios,
            business_tags=business_tags,
            complexity_assessment=complexity_assessment
        )