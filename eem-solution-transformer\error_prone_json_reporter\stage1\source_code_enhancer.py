"""
源码解析增强器

使用inspect_method.py和class_file_reader.py获取详细的方法信息
"""

import os
import re
import sys
from pathlib import Path
from typing import List, Dict, Optional, Any, Tuple
from dataclasses import dataclass

# 添加java_error_analyzer到路径
current_dir = Path(__file__).parent
project_root = current_dir.parent.parent
java_analyzer_path = project_root / "java_error_analyzer"
if str(java_analyzer_path) not in sys.path:
    sys.path.insert(0, str(java_analyzer_path))

try:
    from class_file_reader import ClassFileReader
    from models import ClassFileInfo
except ImportError as e:
    print(f"Warning: Could not import java_error_analyzer modules: {e}")
    ClassFileReader = None
    ClassFileInfo = None

from ..common.models import ErrorReport


@dataclass
class MethodSignatureInfo:
    """方法签名详细信息"""
    method_name: str
    parameters: Dict[str, str]  # 参数名 -> 类型
    return_type: str
    modifiers: List[str]
    annotations: List[str]
    context_lines: List[str]
    file_path: str
    line_number: int
    column_number: int = 0


class SourceCodeEnhancer:
    """源码解析增强器"""
    
    def __init__(self, project_path: str):
        self.project_path = Path(project_path)
        self.class_reader = ClassFileReader(project_path) if ClassFileReader else None
        
    def enhance_error_report(self, error_report: ErrorReport) -> ErrorReport:
        """
        增强错误报告，添加详细的源码信息
        
        Args:
            error_report: 原始错误报告
            
        Returns:
            增强后的错误报告
        """
        if not self.class_reader:
            print("ClassFileReader not available, returning original error report")
            return error_report
        
        try:
            # 获取类文件信息
            class_info = self._get_class_info(error_report)
            if not class_info:
                return error_report
            
            # 提取方法详细信息
            method_info = self._extract_method_details(error_report, class_info)
            if not method_info:
                return error_report
            
            # 更新错误报告
            enhanced_report = self._update_error_report(error_report, method_info, class_info)
            return enhanced_report
            
        except Exception as e:
            print(f"Error enhancing error report: {e}")
            return error_report
    
    def _get_class_info(self, error_report: ErrorReport) -> Optional[Any]:
        """获取类文件信息"""
        # 尝试不同的类名组合
        class_names_to_try = [
            error_report.class_name,
            f"{error_report.package}.{error_report.class_name}",
        ]
        
        for class_name in class_names_to_try:
            class_info = self.class_reader.read_class_file(class_name)
            if class_info:
                return class_info
        
        return None
    
    def _extract_method_details(self, error_report: ErrorReport, 
                              class_info: Any) -> Optional[MethodSignatureInfo]:
        """提取方法详细信息"""
        try:
            method_name = error_report.missing_method.split('(')[0]
            
            # 从源码内容中查找方法
            if hasattr(class_info, 'content') and class_info.content:
                method_info = self._parse_method_from_source(
                    method_name, class_info.content, class_info.file_path
                )
                if method_info:
                    return method_info
            
            # 从调用上下文中推断方法信息
            context_method_info = self._infer_method_from_context(
                error_report, class_info
            )
            if context_method_info:
                return context_method_info
            
            # 创建基本方法信息
            return self._create_basic_method_info(error_report, class_info)
            
        except Exception as e:
            print(f"Error extracting method details: {e}")
            return None
    
    def _parse_method_from_source(self, method_name: str, source_content: str, 
                                file_path: str) -> Optional[MethodSignatureInfo]:
        """从源码中解析方法信息"""
        try:
            lines = source_content.split('\n')
            
            # 查找方法调用位置
            for line_num, line in enumerate(lines, 1):
                if method_name in line and ('(' in line or '.' in line):
                    # 找到可能的方法调用
                    method_info = self._analyze_method_call(
                        method_name, line, lines, line_num, file_path
                    )
                    if method_info:
                        return method_info
            
            return None
            
        except Exception as e:
            print(f"Error parsing method from source: {e}")
            return None
    
    def _analyze_method_call(self, method_name: str, call_line: str, 
                           all_lines: List[str], line_num: int, 
                           file_path: str) -> Optional[MethodSignatureInfo]:
        """分析方法调用，提取参数和返回类型信息"""
        try:
            # 提取上下文行
            start_line = max(0, line_num - 3)
            end_line = min(len(all_lines), line_num + 2)
            context_lines = all_lines[start_line:end_line]
            
            # 分析方法调用模式
            parameters = self._extract_parameters_from_call(call_line, method_name)
            return_type = self._infer_return_type_from_context(
                call_line, context_lines, method_name
            )
            
            # 查找列号
            column_number = call_line.find(method_name)
            
            return MethodSignatureInfo(
                method_name=method_name,
                parameters=parameters,
                return_type=return_type,
                modifiers=[],
                annotations=[],
                context_lines=context_lines,
                file_path=file_path,
                line_number=line_num,
                column_number=column_number
            )
            
        except Exception as e:
            print(f"Error analyzing method call: {e}")
            return None
    
    def _extract_parameters_from_call(self, call_line: str, 
                                    method_name: str) -> Dict[str, str]:
        """从方法调用中提取参数信息"""
        parameters = {}
        
        try:
            # 查找方法调用的参数部分
            method_pattern = rf'{re.escape(method_name)}\s*\((.*?)\)'
            match = re.search(method_pattern, call_line)
            
            if match:
                params_str = match.group(1).strip()
                if params_str:
                    # 简单的参数分割（不处理嵌套括号）
                    param_parts = [p.strip() for p in params_str.split(',')]
                    
                    for i, param in enumerate(param_parts):
                        if param:
                            # 尝试推断参数类型
                            param_type = self._infer_parameter_type(param)
                            parameters[f"param{i+1}"] = param_type
            
        except Exception as e:
            print(f"Error extracting parameters: {e}")
        
        return parameters
    
    def _infer_parameter_type(self, param_expr: str) -> str:
        """推断参数类型"""
        param_expr = param_expr.strip()
        
        # 字符串字面量
        if param_expr.startswith('"') and param_expr.endswith('"'):
            return "java.lang.String"
        
        # 数字字面量
        if param_expr.isdigit():
            return "int"
        
        if '.' in param_expr and param_expr.replace('.', '').isdigit():
            return "double"
        
        # 布尔值
        if param_expr.lower() in ['true', 'false']:
            return "boolean"
        
        # null
        if param_expr.lower() == 'null':
            return "java.lang.Object"
        
        # 变量名 - 尝试从上下文推断
        if param_expr.isidentifier():
            return "java.lang.Object"  # 默认类型
        
        # 方法调用
        if '(' in param_expr and ')' in param_expr:
            return "java.lang.Object"  # 方法返回值
        
        return "unknown"
    
    def _infer_return_type_from_context(self, call_line: str, 
                                      context_lines: List[str], 
                                      method_name: str) -> str:
        """从上下文推断返回类型"""
        try:
            # 查看赋值语句
            if '=' in call_line and method_name in call_line:
                # 查找变量声明
                var_pattern = r'(\w+(?:<[^>]+>)?)\s+(\w+)\s*='
                match = re.search(var_pattern, call_line)
                if match:
                    return match.group(1)
            
            # 查看方法调用链
            if '.' in call_line and method_name in call_line:
                # 如果是链式调用，可能返回对象
                return "java.lang.Object"
            
            # 查看if语句或条件表达式
            if 'if' in call_line and method_name in call_line:
                return "boolean"
            
            # 查看return语句
            if 'return' in call_line and method_name in call_line:
                # 查找方法声明来确定返回类型
                for line in context_lines:
                    method_decl_pattern = r'(\w+(?:<[^>]+>)?)\s+\w+\s*\([^)]*\)\s*\{'
                    match = re.search(method_decl_pattern, line)
                    if match:
                        return match.group(1)
            
            return "java.lang.Object"
            
        except Exception as e:
            print(f"Error inferring return type: {e}")
            return "unknown"
    
    def _infer_method_from_context(self, error_report: ErrorReport, 
                                 class_info: Any) -> Optional[MethodSignatureInfo]:
        """从上下文推断方法信息"""
        try:
            method_name = error_report.missing_method.split('(')[0]
            
            # 如果有行号信息，尝试从源码中获取上下文
            if error_report.line and hasattr(class_info, 'content'):
                line_num = error_report.line[0]
                lines = class_info.content.split('\n')
                
                if 0 < line_num <= len(lines):
                    target_line = lines[line_num - 1]
                    
                    # 分析目标行
                    method_info = self._analyze_method_call(
                        method_name, target_line, lines, line_num, 
                        class_info.file_path
                    )
                    if method_info:
                        return method_info
            
            return None
            
        except Exception as e:
            print(f"Error inferring method from context: {e}")
            return None
    
    def _create_basic_method_info(self, error_report: ErrorReport, 
                                class_info: Any) -> MethodSignatureInfo:
        """创建基本方法信息"""
        method_name = error_report.missing_method.split('(')[0]
        
        # 从错误报告中提取已有的参数信息
        parameters = error_report.in_param.copy() if error_report.in_param else {}
        
        # 如果没有参数信息，尝试从方法名中提取
        if not parameters and '(' in error_report.missing_method:
            param_part = error_report.missing_method[
                error_report.missing_method.find('(')+1:
                error_report.missing_method.find(')')
            ]
            if param_part.strip():
                param_types = [p.strip() for p in param_part.split(',')]
                for i, param_type in enumerate(param_types):
                    parameters[f"param{i+1}"] = param_type
        
        return MethodSignatureInfo(
            method_name=method_name,
            parameters=parameters,
            return_type=error_report.out_return or "unknown",
            modifiers=[],
            annotations=[],
            context_lines=[],
            file_path=class_info.file_path if hasattr(class_info, 'file_path') else "",
            line_number=error_report.line[0] if error_report.line else 0
        )
    
    def _update_error_report(self, error_report: ErrorReport, 
                           method_info: MethodSignatureInfo, 
                           class_info: Any) -> ErrorReport:
        """更新错误报告"""
        # 创建新的错误报告
        enhanced_report = ErrorReport(
            package=error_report.package or (
                class_info.package_name if hasattr(class_info, 'package_name') 
                else error_report.package
            ),
            class_name=error_report.class_name,
            missing_method=method_info.method_name,
            in_param=method_info.parameters,
            out_return=method_info.return_type,
            line=[method_info.line_number] if method_info.line_number > 0 else error_report.line,
            context=self._build_enhanced_context(error_report, method_info, class_info),
            error_type=error_report.error_type
        )
        
        return enhanced_report
    
    def _build_enhanced_context(self, error_report: ErrorReport, 
                              method_info: MethodSignatureInfo, 
                              class_info: Any) -> str:
        """构建增强的上下文信息"""
        context_parts = []
        
        # 添加原始上下文
        if error_report.context:
            context_parts.append(f"Original: {error_report.context}")
        
        # 添加文件路径信息
        if method_info.file_path:
            context_parts.append(f"File: {method_info.file_path}")
        
        # 添加位置信息
        if method_info.line_number > 0:
            location = f"Line: {method_info.line_number}"
            if method_info.column_number > 0:
                location += f", Column: {method_info.column_number}"
            context_parts.append(location)
        
        # 添加方法签名信息
        if method_info.parameters:
            params_str = ", ".join([f"{k}: {v}" for k, v in method_info.parameters.items()])
            context_parts.append(f"Parameters: {params_str}")
        
        if method_info.return_type and method_info.return_type != "unknown":
            context_parts.append(f"Return type: {method_info.return_type}")
        
        # 添加修饰符和注解
        if method_info.modifiers:
            context_parts.append(f"Modifiers: {', '.join(method_info.modifiers)}")
        
        if method_info.annotations:
            context_parts.append(f"Annotations: {', '.join(method_info.annotations)}")
        
        # 添加上下文代码行
        if method_info.context_lines:
            context_lines_str = "\\n".join([
                f"{i}: {line.strip()}" 
                for i, line in enumerate(method_info.context_lines, 
                                       method_info.line_number - len(method_info.context_lines)//2)
            ])
            context_parts.append(f"Context code: {context_lines_str}")
        
        return "; ".join(context_parts)


def enhance_error_reports(error_reports: List[ErrorReport], 
                        project_path: str) -> List[ErrorReport]:
    """
    批量增强错误报告
    
    Args:
        error_reports: 错误报告列表
        project_path: 项目路径
        
    Returns:
        增强后的错误报告列表
    """
    enhancer = SourceCodeEnhancer(project_path)
    enhanced_reports = []
    
    for i, error_report in enumerate(error_reports):
        try:
            print(f"Enhancing error report {i+1}/{len(error_reports)}: {error_report.missing_method}")
            enhanced_report = enhancer.enhance_error_report(error_report)
            enhanced_reports.append(enhanced_report)
        except Exception as e:
            print(f"Error enhancing report {i+1}: {e}")
            enhanced_reports.append(error_report)  # 保留原始报告
    
    return enhanced_reports