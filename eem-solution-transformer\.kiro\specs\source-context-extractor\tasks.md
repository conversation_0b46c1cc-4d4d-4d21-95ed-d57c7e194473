# Implementation Plan

- [x] 1. 创建新的源码上下文提取器项目结构

  - 创建 source_context_extractor 独立目录
  - 建立基础的项目结构：models.py, config.py, main.py 等
  - 从 error_prone_json_reporter 项目中复制必要的工具类和模型
  - _Requirements: 1.1, 6.1_

- [x] 2. 实现 JSON 输入解析器

- [x] 2.1 创建输入数据模型

  - 实现 ErrorItem 数据类，对应 target_method_test.json 的结构
  - 添加数据验证和类型检查

  - 实现 JSON 文件的加载和解析功能
  - _Requirements: 1.1, 1.2, 6.5_

- [x] 2.2 实现输入验证器

  - 验证必需字段：package, class, missing_method, line, location 等
  - 检查文件路径和行号的有效性
  - 提供详细的验证错误报告
  - _Requirements: 1.2, 1.3, 6.5_

- [-] 3. 实现错误位置解析器

- [x] 3.1 创建文件定位器

  - 从 error_prone_json_reporter 提取 ErrorLocationResolver 的核心逻辑

  - 根据 JSON 中的 location 信息在当前项目中查找对应文件
  - 读取指定行号的代码内容和周围上下文
  - _Requirements: 2.1, 2.2, 2.4, 2.5_

- [x] 3.2 实现上下文提取

  - 提取错误行的完整代码内容
  - 获取错误行前后的上下文代码（如前后 5 行）
  - 识别错误所在的方法和类的基本信息
  - _Requirements: 2.5, 4.6_

- [x] 4. 实现迁移前源码搜索器

- [x] 4.1 创建遗留代码搜索器

  - 从 error_prone_json_reporter 提取 LegacyCodeSearcher 的核心功能
  - 在 legacy_src_path 中搜索对应的类文件
  - 实现基于包名和类名的文件查找算法
  - _Requirements: 3.1, 3.2, 3.3, 3.5_

- [x] 4.2 实现方法搜索功能

  - 在找到的类文件中搜索缺失的方法
  - 支持方法重载的识别和匹配
  - 处理方法参数类型的匹配逻辑
  - _Requirements: 3.4, 3.5_

- [x] 5. 实现 AST 解析和方法提取

- [x] 5.1 集成 javalang 进行 AST 解析

  - 从 error_prone_json_reporter 提取 PreciseSourceSearcher 的 AST 解析功能
  - 使用 javalang 解析 Java 源文件
  - 实现方法节点的精确定位和提取
  - _Requirements: 4.1, 4.2, 4.3_

- [x] 5.2 实现方法信息提取

  - 提取完整的方法签名：修饰符、返回类型、方法名、参数列表
  - 提取完整的方法体源码
  - 确定方法的准确行号范围（开始行和结束行）
  - _Requirements: 4.3, 4.4, 4.5_

- [x] 5.3 实现注释和文档提取

  - 提取方法的 Javadoc 注释
  - 提取方法内的行注释和块注释
  - 清理和格式化注释内容
  - _Requirements: 4.5, 4.6_

- [x] 6. 实现上下文分析器

- [x] 6.1 创建方法上下文分析

  - 分析方法所在类的基本信息
  - 提取类的 import 语句和包声明
  - 分析方法的调用关系和依赖
  - _Requirements: 4.6, 4.7_

- [x] 6.2 实现上下文整合

  - 整合当前项目中的错误上下文和迁移前的方法实现
  - 构建完整的方法上下文信息
  - 生成方法的使用场景和业务描述
  - _Requirements: 4.6, 4.7_

- [x] 7. 实现输出格式化器


- [ ] 7. 实现输出格式化器

- [x] 7.1 创建结果数据模型

  - 实现 MethodAnalysisResult 数据类
  - 包含指定的输出字段：missing_method, in_param, out_return, context, content, notes
  - 实现数据序列化和 JSON 格式化
  - _Requirements: 5.1, 5.2, 5.3, 5.4_

- [x] 7.2 实现 Markdown 报告生成器

  - 创建 MarkdownReportGenerator 类
  - 生成结构化的 Markdown 文档
  - 为每个方法生成独立的分析结果块
  - 实现代码高亮和格式化

  - _Requirements: 5.1, 5.2, 5.3, 5.4_

- [x] 8. 实现主程序和批量处理

- [x] 8.1 创建主程序入口

  - 实现 main.py，整合所有组件
  - 添加命令行参数解析：输入文件、输出文件、配置路径等
  - 实现完整的处理流程控制
  - _Requirements: 5.6, 5.7, 6.4_

- [x] 8.2 实现批量处理功能

  - 支持处理多个错误项的批量分析
  - 为每个错误项生成独立的分析结果
  - 添加处理进度显示和统计信息
  - _Requirements: 5.6, 5.7_

- [x] 9. 实现配置管理和异常处理
- [x] 9.1 创建配置管理器

  - 实现 Configuration 类，支持路径配置
  - 支持 src_path、legacy_src_path、knowledge_base_path 等配置
  - 实现配置文件加载和命令行参数覆盖
  - _Requirements: 6.4_

- [x] 9.2 实现异常处理机制

  - 处理文件不存在、读取失败等 IO 异常
  - 处理 AST 解析失败的情况
  - 处理方法未找到的情况，提供有用的错误信息
  - 实现优雅的错误恢复和继续处理
  - _Requirements: 6.1, 6.2, 6.3_

- [x] 10. 实现日志记录和测试验证
- [x] 10.1 添加日志记录

  - 配置日志系统，支持不同级别的日志输出
  - 记录处理过程的关键步骤和结果
  - 提供详细的调试信息和错误报告
  - _Requirements: 6.4_

- [x] 10.2 创建测试和验证
  - 使用 target_method_test.json 作为测试输入
  - 验证完整的处理流程：JSON 解析 → 错误定位 → 源码搜索 → 方法提取 → 结果输出
  - 测试各种边界情况和异常处理
  - 验证输出格式的正确性和完整性
  - _Requirements: 所有需求的验证_
