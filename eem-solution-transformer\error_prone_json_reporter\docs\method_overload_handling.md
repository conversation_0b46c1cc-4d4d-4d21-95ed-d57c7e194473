# 方法重载处理功能文档

## 概述

PreciseSourceSearcher现在支持完整的Java方法重载处理功能，能够根据方法名和参数类型精确定位到正确的重载方法实现。

## 核心功能

### 1. 精确参数匹配

系统能够根据提供的参数类型列表精确匹配重载方法：

```python
# 查找 findUser(String, int) 方法
result = searcher.search_method_by_error_location(
    error_file_path="test.java",
    error_line=10,
    missing_class="UserService",
    missing_method="findUser",
    package_name="com.example.service",
    method_parameters=["String", "int"]
)
```

### 2. 方法签名解析

支持从方法签名字符串中提取参数信息：

```python
# 解析方法签名
signature = "findUser(String name, int age)"
param_info = searcher._parse_method_signature(signature)
# 结果: {'param_count': 2, 'param_types': ['String', 'int']}
```

### 3. 错误信息参数提取

能够从Java编译错误信息中自动提取方法参数：

```python
# 从错误信息提取参数
error_content = "cannot find symbol: method findUser(String, int)"
params = searcher.extract_method_parameters_from_error(error_content)
# 结果: ['String', 'int']
```

### 4. 类型兼容性检查

支持多种类型匹配策略：

- **精确匹配**: `String` == `String`
- **简单类名匹配**: `java.lang.String` == `String`
- **泛型匹配**: `List<String>` == `List`
- **相似类型匹配**: `int` == `Integer`

## 重载方法选择策略

当找到多个同名方法时，系统按以下优先级选择：

1. **精确参数匹配**: 参数类型完全匹配的方法
2. **方法签名匹配**: 基于方法签名信息的匹配
3. **参数最少方法**: 选择参数数量最少的方法（通常是最简单的重载）

## 支持的Java语法

### 方法重载示例

```java
public class UserService {
    // 重载方法1: 单参数
    public User findUser(String name) {
        return findByName(name);
    }
    
    // 重载方法2: 双参数
    public User findUser(String name, int age) {
        return findByNameAndAge(name, age);
    }
    
    // 重载方法3: Long参数
    public User findUser(Long id) {
        return findById(id);
    }
    
    // 重载方法4: 无参数
    public List<User> findUser() {
        return findAll();
    }
    
    // 重载方法5: 复杂参数
    public List<User> findUser(String email, boolean active) {
        return findByEmailAndStatus(email, active);
    }
}
```

### 支持的参数类型

- **基本类型**: `int`, `long`, `double`, `boolean`, `char`, `byte`, `short`, `float`
- **包装类型**: `Integer`, `Long`, `Double`, `Boolean`, `Character`, `Byte`, `Short`, `Float`
- **字符串类型**: `String`, `java.lang.String`
- **集合类型**: `List`, `ArrayList`, `Map`, `HashMap`, `Set`, `HashSet`
- **泛型类型**: `List<String>`, `Map<String, Integer>`
- **自定义类型**: `User`, `Order`, `com.example.model.User`

## API接口

### 主要方法

#### search_method_by_error_location

```python
def search_method_by_error_location(
    self, 
    error_file_path: str, 
    error_line: int, 
    missing_class: str, 
    missing_method: str, 
    package_name: Optional[str] = None,
    method_signature: Optional[str] = None,
    method_parameters: Optional[List[str]] = None
) -> Optional[MethodLocation]:
```

**参数说明:**
- `error_file_path`: 报错文件路径
- `error_line`: 报错行号
- `missing_class`: 缺失的类名
- `missing_method`: 缺失的方法名
- `package_name`: 包名（可选，用于精确定位类）
- `method_signature`: 方法签名信息（可选，用于重载方法匹配）
- `method_parameters`: 方法参数类型列表（可选，用于重载方法匹配）

#### extract_method_parameters_from_error

```python
def extract_method_parameters_from_error(
    self, 
    error_content: str
) -> Optional[List[str]]:
```

从错误信息中提取方法参数类型列表。

### 辅助方法

#### _parse_method_signature

解析方法签名字符串，提取参数信息。

#### _is_type_compatible

检查两个类型是否兼容。

#### _select_best_method_match

从多个匹配的方法中选择最佳匹配。

## 使用示例

### 基本用法

```python
from error_prone_json_reporter.stage2.precise_source_searcher import PreciseSourceSearcher

# 初始化搜索器
searcher = PreciseSourceSearcher("/path/to/legacy/src", "/path/to/project")

# 查找重载方法
result = searcher.search_method_by_error_location(
    error_file_path="UserController.java",
    error_line=25,
    missing_class="UserService",
    missing_method="findUser",
    package_name="com.example.service",
    method_parameters=["String", "int"]
)

if result:
    print(f"找到方法: {result.file_path}:{result.start_line}")
    print(f"方法代码:\n{result.method_code}")
```

### 从错误信息提取参数

```python
# Java编译错误信息
error_message = "cannot find symbol: method updateUser(Long, String)"

# 提取参数类型
params = searcher.extract_method_parameters_from_error(error_message)
print(f"提取的参数: {params}")  # ['Long', 'String']

# 使用提取的参数查找方法
result = searcher.search_method_by_error_location(
    error_file_path="UserController.java",
    error_line=30,
    missing_class="UserService",
    missing_method="updateUser",
    method_parameters=params
)
```

## 测试覆盖

项目包含完整的测试套件，覆盖以下场景：

- ✅ 精确参数匹配
- ✅ 方法签名解析
- ✅ 错误信息参数提取
- ✅ 类型兼容性检查
- ✅ 重载方法选择
- ✅ 边界情况处理

运行测试：

```bash
python -m pytest error_prone_json_reporter/tests/test_precise_source_searcher_enhanced.py -v
```

## 性能优化

### 缓存机制

- **文件内容缓存**: 避免重复读取同一文件
- **AST缓存**: 避免重复解析同一Java文件
- **Java文件列表缓存**: 避免重复扫描目录结构

### 搜索策略

- **两阶段搜索**: 先定位类，再查找方法
- **优先级匹配**: 按匹配度排序，选择最佳结果
- **早期退出**: 找到精确匹配时立即返回

## 限制和注意事项

1. **依赖javalang库**: 需要安装javalang库进行Java代码解析
2. **语法支持**: 支持标准Java语法，不支持某些高级特性
3. **性能考虑**: 大型项目可能需要较长的初始化时间
4. **编码支持**: 支持UTF-8、GBK、Latin-1编码的Java文件

## 未来改进

- [ ] 支持更复杂的泛型类型匹配
- [ ] 支持方法返回类型匹配
- [ ] 支持注解信息匹配
- [ ] 支持Lambda表达式和方法引用
- [ ] 性能优化和并行处理