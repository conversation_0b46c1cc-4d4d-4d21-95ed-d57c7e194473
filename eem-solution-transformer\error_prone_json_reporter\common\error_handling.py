"""
错误处理和异常管理系统

提供统一的错误处理、异常分类和恢复机制。
"""

import os
import sys
import traceback
import logging
from typing import Type, Dict, Any, Optional, Callable, List
from functools import wraps
from enum import Enum


class ErrorSeverity(Enum):
    """错误严重程度"""
    LOW = "low"           # 轻微错误，可以忽略
    MEDIUM = "medium"     # 中等错误，需要记录但可以继续
    HIGH = "high"         # 严重错误，需要处理但不致命
    CRITICAL = "critical" # 致命错误，必须停止执行


class ErrorCategory(Enum):
    """错误分类"""
    FILE_IO = "file_io"                    # 文件读写错误
    NETWORK = "network"                    # 网络相关错误
    PARSING = "parsing"                    # 解析错误
    MODEL_LOADING = "model_loading"        # 模型加载错误
    CONFIGURATION = "configuration"        # 配置错误
    VALIDATION = "validation"              # 数据验证错误
    RESOURCE = "resource"                  # 资源不足错误
    PERMISSION = "permission"              # 权限错误
    DEPENDENCY = "dependency"              # 依赖错误
    UNKNOWN = "unknown"                    # 未知错误


class MigrationToolError(Exception):
    """迁移工具基础异常类"""
    
    def __init__(self, message: str, category: ErrorCategory = ErrorCategory.UNKNOWN,
                 severity: ErrorSeverity = ErrorSeverity.MEDIUM, 
                 context: Dict[str, Any] = None, cause: Exception = None):
        super().__init__(message)
        self.message = message
        self.category = category
        self.severity = severity
        self.context = context or {}
        self.cause = cause
    
    def __str__(self):
        return f"[{self.category.value.upper()}] {self.message}"
    
    def get_detailed_info(self) -> Dict[str, Any]:
        """获取详细错误信息"""
        return {
            "message": self.message,
            "category": self.category.value,
            "severity": self.severity.value,
            "context": self.context,
            "cause": str(self.cause) if self.cause else None,
            "traceback": traceback.format_exc() if self.cause else None
        }


class ConfigurationError(MigrationToolError):
    """配置相关错误"""
    
    def __init__(self, message: str, context: Dict[str, Any] = None, cause: Exception = None):
        super().__init__(message, ErrorCategory.CONFIGURATION, ErrorSeverity.HIGH, context, cause)


class FileIOError(MigrationToolError):
    """文件IO相关错误"""
    
    def __init__(self, message: str, file_path: str = None, 
                 context: Dict[str, Any] = None, cause: Exception = None):
        context = context or {}
        if file_path:
            context["file_path"] = file_path
        super().__init__(message, ErrorCategory.FILE_IO, ErrorSeverity.MEDIUM, context, cause)


class ParsingError(MigrationToolError):
    """解析相关错误"""
    
    def __init__(self, message: str, file_path: str = None, line_number: int = None,
                 context: Dict[str, Any] = None, cause: Exception = None):
        context = context or {}
        if file_path:
            context["file_path"] = file_path
        if line_number:
            context["line_number"] = line_number
        super().__init__(message, ErrorCategory.PARSING, ErrorSeverity.MEDIUM, context, cause)


class ModelLoadingError(MigrationToolError):
    """模型加载相关错误"""
    
    def __init__(self, message: str, model_name: str = None,
                 context: Dict[str, Any] = None, cause: Exception = None):
        context = context or {}
        if model_name:
            context["model_name"] = model_name
        super().__init__(message, ErrorCategory.MODEL_LOADING, ErrorSeverity.HIGH, context, cause)


class ValidationError(MigrationToolError):
    """数据验证相关错误"""
    
    def __init__(self, message: str, field_name: str = None, field_value: Any = None,
                 context: Dict[str, Any] = None, cause: Exception = None):
        context = context or {}
        if field_name:
            context["field_name"] = field_name
        if field_value is not None:
            context["field_value"] = str(field_value)
        super().__init__(message, ErrorCategory.VALIDATION, ErrorSeverity.MEDIUM, context, cause)


class ResourceError(MigrationToolError):
    """资源相关错误"""
    
    def __init__(self, message: str, resource_type: str = None,
                 context: Dict[str, Any] = None, cause: Exception = None):
        context = context or {}
        if resource_type:
            context["resource_type"] = resource_type
        super().__init__(message, ErrorCategory.RESOURCE, ErrorSeverity.HIGH, context, cause)


class ErrorRecoveryStrategy:
    """错误恢复策略"""
    
    def __init__(self, name: str, handler: Callable[[Exception, Dict[str, Any]], bool],
                 max_retries: int = 3, applicable_errors: List[Type[Exception]] = None):
        self.name = name
        self.handler = handler
        self.max_retries = max_retries
        self.applicable_errors = applicable_errors or [Exception]
        self.retry_count = 0
    
    def can_handle(self, error: Exception) -> bool:
        """检查是否可以处理指定错误"""
        return any(isinstance(error, error_type) for error_type in self.applicable_errors)
    
    def attempt_recovery(self, error: Exception, context: Dict[str, Any]) -> bool:
        """尝试错误恢复"""
        if self.retry_count >= self.max_retries:
            return False
        
        self.retry_count += 1
        try:
            return self.handler(error, context)
        except Exception:
            return False
    
    def reset(self):
        """重置重试计数"""
        self.retry_count = 0


class ErrorManager:
    """错误管理器"""
    
    def __init__(self, logger: logging.Logger):
        self.logger = logger
        self.error_counts = {}
        self.recovery_strategies = []
        self.error_history = []
        self.max_history_size = 1000
    
    def register_recovery_strategy(self, strategy: ErrorRecoveryStrategy):
        """注册错误恢复策略"""
        self.recovery_strategies.append(strategy)
        self.logger.debug(f"注册错误恢复策略: {strategy.name}")
    
    def handle_error(self, error: Exception, context: Dict[str, Any] = None,
                    operation: str = None) -> bool:
        """
        处理错误
        
        Args:
            error: 异常对象
            context: 错误上下文
            operation: 操作名称
            
        Returns:
            是否应该继续执行
        """
        context = context or {}
        if operation:
            context["operation"] = operation
        
        # 记录错误历史
        error_info = {
            "error": error,
            "context": context,
            "timestamp": time.time(),
            "operation": operation
        }
        self._add_to_history(error_info)
        
        # 统计错误
        error_type = type(error).__name__
        self.error_counts[error_type] = self.error_counts.get(error_type, 0) + 1
        
        # 确定错误严重程度
        severity = self._determine_severity(error)
        
        # 记录错误
        self._log_error(error, context, severity, operation)
        
        # 尝试错误恢复
        if severity != ErrorSeverity.CRITICAL:
            for strategy in self.recovery_strategies:
                if strategy.can_handle(error):
                    self.logger.info(f"尝试使用策略 '{strategy.name}' 恢复错误")
                    if strategy.attempt_recovery(error, context):
                        self.logger.info(f"错误恢复成功: {strategy.name}")
                        return True
                    else:
                        self.logger.warning(f"错误恢复失败: {strategy.name}")
        
        # 根据严重程度决定是否继续
        return severity in [ErrorSeverity.LOW, ErrorSeverity.MEDIUM]
    
    def _determine_severity(self, error: Exception) -> ErrorSeverity:
        """确定错误严重程度"""
        if isinstance(error, MigrationToolError):
            return error.severity
        
        # 根据异常类型确定严重程度
        critical_errors = [
            MemoryError, SystemExit, KeyboardInterrupt
        ]
        
        high_severity_errors = [
            ImportError, ModuleNotFoundError, AttributeError,
            ConfigurationError, ModelLoadingError
        ]
        
        medium_severity_errors = [
            FileNotFoundError, PermissionError, ValueError,
            FileIOError, ParsingError, ValidationError
        ]
        
        if any(isinstance(error, err_type) for err_type in critical_errors):
            return ErrorSeverity.CRITICAL
        elif any(isinstance(error, err_type) for err_type in high_severity_errors):
            return ErrorSeverity.HIGH
        elif any(isinstance(error, err_type) for err_type in medium_severity_errors):
            return ErrorSeverity.MEDIUM
        else:
            return ErrorSeverity.LOW
    
    def _log_error(self, error: Exception, context: Dict[str, Any],
                  severity: ErrorSeverity, operation: str = None):
        """记录错误日志"""
        error_msg = str(error)
        if operation:
            error_msg = f"[{operation}] {error_msg}"
        
        if context:
            context_str = ", ".join([f"{k}={v}" for k, v in context.items()])
            error_msg += f" (上下文: {context_str})"
        
        if severity == ErrorSeverity.CRITICAL:
            self.logger.critical(error_msg, exc_info=True)
        elif severity == ErrorSeverity.HIGH:
            self.logger.error(error_msg, exc_info=True)
        elif severity == ErrorSeverity.MEDIUM:
            self.logger.warning(error_msg)
        else:
            self.logger.debug(error_msg)
    
    def _add_to_history(self, error_info: Dict[str, Any]):
        """添加错误到历史记录"""
        self.error_history.append(error_info)
        if len(self.error_history) > self.max_history_size:
            self.error_history.pop(0)
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """获取错误统计信息"""
        total_errors = sum(self.error_counts.values())
        return {
            "total_errors": total_errors,
            "error_types": self.error_counts.copy(),
            "most_common_error": max(self.error_counts.items(), key=lambda x: x[1])[0] if self.error_counts else None,
            "unique_error_types": len(self.error_counts)
        }
    
    def log_error_summary(self):
        """记录错误统计摘要"""
        stats = self.get_error_statistics()
        
        if stats["total_errors"] == 0:
            self.logger.info("执行过程中未发生错误")
            return
        
        self.logger.info("=" * 50)
        self.logger.info("错误统计摘要")
        self.logger.info("=" * 50)
        self.logger.info(f"总错误数: {stats['total_errors']}")
        self.logger.info(f"错误类型数: {stats['unique_error_types']}")
        
        if stats["most_common_error"]:
            self.logger.info(f"最常见错误: {stats['most_common_error']}")
        
        self.logger.info("错误类型分布:")
        for error_type, count in sorted(stats["error_types"].items(), key=lambda x: x[1], reverse=True):
            percentage = (count / stats["total_errors"]) * 100
            self.logger.info(f"  {error_type}: {count} 次 ({percentage:.1f}%)")
        
        self.logger.info("=" * 50)


def error_handler(operation: str = None, reraise: bool = False,
                 default_return: Any = None, logger: logging.Logger = None):
    """
    错误处理装饰器
    
    Args:
        operation: 操作名称
        reraise: 是否重新抛出异常
        default_return: 发生错误时的默认返回值
        logger: 日志器
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                # 获取日志器
                if logger:
                    log = logger
                else:
                    log = logging.getLogger(func.__module__)
                
                # 构建上下文信息
                context = {
                    "function": func.__name__,
                    "args": str(args)[:200],  # 限制长度
                    "kwargs": str(kwargs)[:200]
                }
                
                # 记录错误
                op_name = operation or func.__name__
                log.error(f"函数 {op_name} 执行失败: {e}", exc_info=True)
                
                if reraise:
                    raise
                else:
                    return default_return
        
        return wrapper
    return decorator


def safe_execute(func: Callable, *args, default_return: Any = None,
                logger: logging.Logger = None, **kwargs) -> Any:
    """
    安全执行函数
    
    Args:
        func: 要执行的函数
        *args: 函数参数
        default_return: 发生错误时的默认返回值
        logger: 日志器
        **kwargs: 函数关键字参数
        
    Returns:
        函数执行结果或默认返回值
    """
    try:
        return func(*args, **kwargs)
    except Exception as e:
        if logger:
            logger.error(f"安全执行失败 {func.__name__}: {e}", exc_info=True)
        return default_return


# 预定义的错误恢复策略

def retry_file_operation(error: Exception, context: Dict[str, Any]) -> bool:
    """重试文件操作的恢复策略"""
    import time
    time.sleep(1)  # 等待1秒后重试
    return True


def skip_invalid_file(error: Exception, context: Dict[str, Any]) -> bool:
    """跳过无效文件的恢复策略"""
    if "file_path" in context:
        logging.getLogger().warning(f"跳过无效文件: {context['file_path']}")
    return True


def fallback_to_default_model(error: Exception, context: Dict[str, Any]) -> bool:
    """回退到默认模型的恢复策略"""
    if isinstance(error, ModelLoadingError):
        logging.getLogger().info("尝试使用默认模型配置")
        # 这里可以实现具体的回退逻辑
        return True
    return False


# 预定义的恢复策略实例
FILE_RETRY_STRATEGY = ErrorRecoveryStrategy(
    "file_retry",
    retry_file_operation,
    max_retries=3,
    applicable_errors=[FileIOError, FileNotFoundError, PermissionError]
)

SKIP_INVALID_FILE_STRATEGY = ErrorRecoveryStrategy(
    "skip_invalid_file",
    skip_invalid_file,
    max_retries=1,
    applicable_errors=[ParsingError, ValidationError]
)

MODEL_FALLBACK_STRATEGY = ErrorRecoveryStrategy(
    "model_fallback",
    fallback_to_default_model,
    max_retries=1,
    applicable_errors=[ModelLoadingError]
)