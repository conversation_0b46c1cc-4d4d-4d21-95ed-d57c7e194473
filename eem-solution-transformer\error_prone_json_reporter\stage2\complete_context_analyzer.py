"""
完整代码上下文分析器

集成ErrorLocationResolver、LegacyCodeSearcher和EnhancedContextBuilder，
提供完整的代码上下文分析工作流程。
"""

import logging
from typing import Optional, Dict, Any, List
from dataclasses import dataclass

from error_prone_json_reporter.common.models import Configuration, MethodInfo
from error_prone_json_reporter.stage2.error_location_resolver import ErrorLocationResolver, ErrorLocation, MethodContext
from error_prone_json_reporter.stage2.legacy_code_searcher import LegacyCodeSearcher, OriginalMethod
from error_prone_json_reporter.stage2.enhanced_context_builder import EnhancedContextBuilder, CompleteContext, SemanticFeatures


@dataclass
class ContextAnalysisResult:
    """上下文分析结果"""
    complete_context: CompleteContext
    semantic_features: SemanticFeatures
    analysis_success: bool
    error_messages: List[str]
    processing_time: float
    components_used: Dict[str, bool]


class CompleteContextAnalyzer:
    """
    完整代码上下文分析器
    
    职责：协调各个组件，提供完整的代码上下文分析工作流程
    输入：错误JSON信息
    输出：完整的上下文分析结果
    """
    
    def __init__(self, config: Configuration):
        """
        初始化完整代码上下文分析器
        
        Args:
            config: 配置对象
        """
        self.logger = logging.getLogger(__name__)
        self.config = config
        
        # 初始化各个组件
        self.error_resolver = ErrorLocationResolver(config)
        self.legacy_searcher = LegacyCodeSearcher(config)
        self.context_builder = EnhancedContextBuilder(config)
        
        # 分析统计
        self.analysis_stats = {
            'total_analyses': 0,
            'successful_analyses': 0,
            'error_location_found': 0,
            'original_method_found': 0,
            'current_context_extracted': 0
        }
        
        self.logger.info("完整代码上下文分析器初始化完成")
    
    def analyze_error_context(self, error_json: Dict[str, Any]) -> ContextAnalysisResult:
        """
        分析错误上下文
        
        Args:
            error_json: 错误JSON信息
            
        Returns:
            完整的上下文分析结果
        """
        import time
        start_time = time.time()
        
        try:
            self.logger.info(f"开始分析错误上下文: {error_json.get('class', '')}.{error_json.get('missing_method', '')}")
            
            error_messages = []
            components_used = {
                'error_location_resolver': False,
                'legacy_code_searcher': False,
                'enhanced_context_builder': False
            }
            
            # 步骤1: 解析错误位置
            error_location = None
            try:
                error_location = self.error_resolver.resolve_error_location(error_json)
                components_used['error_location_resolver'] = True
                
                if error_location:
                    self.analysis_stats['error_location_found'] += 1
                    self.logger.debug(f"成功解析错误位置: {error_location.file_path}:{error_location.line_number}")
                else:
                    error_messages.append("无法解析错误位置")
                    
            except Exception as e:
                error_messages.append(f"错误位置解析失败: {str(e)}")
                self.logger.warning(f"错误位置解析失败: {str(e)}")
            
            # 步骤2: 提取当前项目源码上下文
            current_context = None
            try:
                if error_location and error_location.absolute_file_path:
                    current_context = self.error_resolver.extract_method_context(
                        error_location.absolute_file_path, 
                        error_location.line_number
                    )
                    
                    if current_context:
                        self.analysis_stats['current_context_extracted'] += 1
                        self.logger.debug("成功提取当前项目源码上下文")
                    else:
                        error_messages.append("无法提取当前项目源码上下文")
                        
            except Exception as e:
                error_messages.append(f"当前上下文提取失败: {str(e)}")
                self.logger.warning(f"当前上下文提取失败: {str(e)}")
            
            # 步骤3: 搜索原始方法实现
            original_method = None
            try:
                original_method = self.legacy_searcher.search_method_by_error(error_json)
                components_used['legacy_code_searcher'] = True
                
                if original_method:
                    self.analysis_stats['original_method_found'] += 1
                    self.logger.debug(f"成功找到原始方法: {original_method.file_path}:{original_method.line_number}")
                else:
                    error_messages.append("未找到原始方法实现")
                    
            except Exception as e:
                error_messages.append(f"原始方法搜索失败: {str(e)}")
                self.logger.warning(f"原始方法搜索失败: {str(e)}")
            
            # 步骤4: 构建完整上下文
            complete_context = None
            semantic_features = None
            try:
                complete_context = self.context_builder.build_complete_context(
                    current_context=current_context,
                    original_method=original_method,
                    json_metadata=error_json,
                    error_location=error_location
                )
                components_used['enhanced_context_builder'] = True
                
                # 提取语义特征
                semantic_features = self.context_builder.extract_semantic_features(complete_context)
                
                self.logger.debug(f"成功构建完整上下文，质量: {complete_context.context_quality}")
                
            except Exception as e:
                error_messages.append(f"完整上下文构建失败: {str(e)}")
                self.logger.error(f"完整上下文构建失败: {str(e)}")
                
                # 创建最小上下文
                complete_context = self._create_minimal_context(error_json)
                semantic_features = self.context_builder._create_fallback_semantic_features(error_json)
            
            # 更新统计
            self.analysis_stats['total_analyses'] += 1
            analysis_success = len(error_messages) == 0 or complete_context is not None
            if analysis_success:
                self.analysis_stats['successful_analyses'] += 1
            
            processing_time = time.time() - start_time
            
            result = ContextAnalysisResult(
                complete_context=complete_context,
                semantic_features=semantic_features,
                analysis_success=analysis_success,
                error_messages=error_messages,
                processing_time=processing_time,
                components_used=components_used
            )
            
            self.logger.info(f"上下文分析完成，耗时: {processing_time:.2f}s, 成功: {analysis_success}")
            return result
            
        except Exception as e:
            processing_time = time.time() - start_time
            self.logger.error(f"上下文分析失败: {str(e)}")
            
            # 返回失败结果
            return ContextAnalysisResult(
                complete_context=self._create_minimal_context(error_json),
                semantic_features=self.context_builder._create_fallback_semantic_features(error_json),
                analysis_success=False,
                error_messages=[f"分析过程异常: {str(e)}"],
                processing_time=processing_time,
                components_used={'error': True}
            )
    
    def _create_minimal_context(self, error_json: Dict[str, Any]) -> CompleteContext:
        """创建最小上下文"""
        try:
            return self.context_builder.build_complete_context(
                current_context=None,
                original_method=None,
                json_metadata=error_json,
                error_location=None
            )
        except Exception as e:
            self.logger.error(f"创建最小上下文失败: {str(e)}")
            # 手动创建最基础的上下文
            from error_prone_json_reporter.stage2.enhanced_context_builder import CompleteContext
            return CompleteContext(
                current_context=None,
                original_method=None,
                json_metadata=error_json,
                error_location=None,
                combined_description=self.context_builder._create_fallback_description(error_json),
                confidence_score=0.1,
                context_quality='low',
                available_information={'json_only': True}
            )
    
    def batch_analyze_errors(self, error_list: List[Dict[str, Any]]) -> List[ContextAnalysisResult]:
        """
        批量分析错误上下文
        
        Args:
            error_list: 错误列表
            
        Returns:
            分析结果列表
        """
        try:
            self.logger.info(f"开始批量分析 {len(error_list)} 个错误")
            
            results = []
            for i, error_json in enumerate(error_list):
                try:
                    self.logger.debug(f"分析第 {i+1}/{len(error_list)} 个错误")
                    result = self.analyze_error_context(error_json)
                    results.append(result)
                    
                except Exception as e:
                    self.logger.error(f"分析第 {i+1} 个错误失败: {str(e)}")
                    # 添加失败结果
                    results.append(ContextAnalysisResult(
                        complete_context=self._create_minimal_context(error_json),
                        semantic_features=self.context_builder._create_fallback_semantic_features(error_json),
                        analysis_success=False,
                        error_messages=[f"批量分析异常: {str(e)}"],
                        processing_time=0.0,
                        components_used={'error': True}
                    ))
            
            self.logger.info(f"批量分析完成，成功: {sum(1 for r in results if r.analysis_success)}/{len(results)}")
            return results
            
        except Exception as e:
            self.logger.error(f"批量分析失败: {str(e)}")
            return []
    
    def analyze_with_enhanced_ai_input(self, error_json: Dict[str, Any]) -> Dict[str, Any]:
        """
        分析并生成增强的AI输入
        
        Args:
            error_json: 错误JSON信息
            
        Returns:
            增强的AI分析输入
        """
        try:
            # 执行完整上下文分析
            analysis_result = self.analyze_error_context(error_json)
            
            if not analysis_result.analysis_success:
                self.logger.warning("上下文分析失败，使用基础信息")
            
            # 构建增强的AI输入
            ai_input = {
                'basic_info': {
                    'package': error_json.get('package', ''),
                    'class': error_json.get('class', ''),
                    'missing_method': error_json.get('missing_method', ''),
                    'parameters': error_json.get('in_param', {}),
                    'return_type': error_json.get('out_return', ''),
                    'context': error_json.get('context', '')
                },
                'enhanced_context': {
                    'combined_description': analysis_result.complete_context.combined_description,
                    'confidence_score': analysis_result.complete_context.confidence_score,
                    'context_quality': analysis_result.complete_context.context_quality
                },
                'semantic_features': {
                    'primary_function': analysis_result.semantic_features.primary_function,
                    'business_domain': analysis_result.semantic_features.business_domain,
                    'key_concepts': analysis_result.semantic_features.key_concepts,
                    'functional_category': analysis_result.semantic_features.functional_category,
                    'technical_keywords': analysis_result.semantic_features.technical_keywords,
                    'business_keywords': analysis_result.semantic_features.business_keywords
                },
                'analysis_metadata': {
                    'analysis_success': analysis_result.analysis_success,
                    'processing_time': analysis_result.processing_time,
                    'components_used': analysis_result.components_used,
                    'available_information': analysis_result.complete_context.available_information
                }
            }
            
            # 添加原始方法信息（如果可用）
            if analysis_result.complete_context.original_method:
                original = analysis_result.complete_context.original_method
                ai_input['original_method'] = {
                    'method_signature': original.method_signature,
                    'javadoc': original.javadoc[:500] if original.javadoc else None,
                    'method_code_preview': original.method_code[:1000] if original.method_code else None,
                    'file_path': original.file_path,
                    'annotations': original.annotations
                }
            
            # 添加当前上下文信息（如果可用）
            if analysis_result.complete_context.current_context:
                current = analysis_result.complete_context.current_context
                ai_input['current_context'] = {
                    'method_definition': current.method_definition,
                    'calling_context': current.calling_context[:5],  # 限制数量
                    'local_variables': current.local_variables[:10],
                    'method_calls': current.method_calls[:10],
                    'imports': current.imports[:10]
                }
            
            # 添加错误位置信息（如果可用）
            if analysis_result.complete_context.error_location:
                location = analysis_result.complete_context.error_location
                ai_input['error_location'] = {
                    'file_path': location.file_path,
                    'line_number': location.line_number,
                    'surrounding_lines': location.surrounding_lines[:10]
                }
            
            return ai_input
            
        except Exception as e:
            self.logger.error(f"生成增强AI输入失败: {str(e)}")
            
            # 返回基础AI输入
            return {
                'basic_info': {
                    'package': error_json.get('package', ''),
                    'class': error_json.get('class', ''),
                    'missing_method': error_json.get('missing_method', ''),
                    'parameters': error_json.get('in_param', {}),
                    'return_type': error_json.get('out_return', ''),
                    'context': error_json.get('context', '')
                },
                'enhanced_context': {
                    'combined_description': f"基础信息: {error_json.get('context', '')}",
                    'confidence_score': 0.1,
                    'context_quality': 'low'
                },
                'analysis_metadata': {
                    'analysis_success': False,
                    'error_message': str(e)
                }
            }
    
    def create_weighted_description(self, analysis_result: ContextAnalysisResult) -> str:
        """
        创建加权描述
        
        Args:
            analysis_result: 分析结果
            
        Returns:
            加权描述文本
        """
        try:
            description_parts = []
            
            # AI语义理解 (70%权重)
            semantic_part = self._create_semantic_description(analysis_result.semantic_features)
            if semantic_part:
                description_parts.append(f"语义分析: {semantic_part}")
            
            # 实际代码内容 (20%权重)
            code_part = self._create_code_description(analysis_result.complete_context)
            if code_part:
                description_parts.append(f"代码上下文: {code_part}")
            
            # JSON元数据 (10%权重)
            metadata_part = self._create_metadata_description(analysis_result.complete_context.json_metadata)
            if metadata_part:
                description_parts.append(f"基础信息: {metadata_part}")
            
            return '\n'.join(description_parts) if description_parts else analysis_result.complete_context.combined_description
            
        except Exception as e:
            self.logger.warning(f"创建加权描述失败: {str(e)}")
            return analysis_result.complete_context.combined_description
    
    def _create_semantic_description(self, semantic_features: SemanticFeatures) -> str:
        """创建语义描述"""
        try:
            parts = []
            
            if semantic_features.primary_function:
                parts.append(f"主要功能: {semantic_features.primary_function}")
            
            if semantic_features.business_domain:
                parts.append(f"业务领域: {semantic_features.business_domain}")
            
            if semantic_features.functional_category:
                parts.append(f"功能类别: {semantic_features.functional_category}")
            
            if semantic_features.key_concepts:
                parts.append(f"关键概念: {', '.join(semantic_features.key_concepts[:5])}")
            
            return '; '.join(parts)
            
        except Exception as e:
            self.logger.warning(f"创建语义描述失败: {str(e)}")
            return ''
    
    def _create_code_description(self, complete_context: CompleteContext) -> str:
        """创建代码描述"""
        try:
            parts = []
            
            # 原始方法信息
            if complete_context.original_method:
                original = complete_context.original_method
                parts.append(f"原始方法: {original.method_signature}")
                if original.javadoc:
                    parts.append(f"文档: {original.javadoc[:100]}...")
            
            # 当前上下文信息
            if complete_context.current_context:
                current = complete_context.current_context
                if current.method_definition:
                    parts.append(f"当前定义: {current.method_definition}")
                if current.method_calls:
                    parts.append(f"调用方法: {', '.join(current.method_calls[:3])}")
            
            return '; '.join(parts)
            
        except Exception as e:
            self.logger.warning(f"创建代码描述失败: {str(e)}")
            return ''
    
    def _create_metadata_description(self, json_metadata: Dict[str, Any]) -> str:
        """创建元数据描述"""
        try:
            parts = []
            
            method_name = json_metadata.get('missing_method', '')
            if method_name:
                parts.append(f"方法: {method_name}")
            
            in_params = json_metadata.get('in_param', {})
            if in_params:
                param_count = len(in_params)
                parts.append(f"参数数量: {param_count}")
            
            out_return = json_metadata.get('out_return', '')
            if out_return:
                parts.append(f"返回类型: {out_return}")
            
            return '; '.join(parts)
            
        except Exception as e:
            self.logger.warning(f"创建元数据描述失败: {str(e)}")
            return ''
    
    def get_analysis_statistics(self) -> Dict[str, Any]:
        """获取分析统计信息"""
        stats = self.analysis_stats.copy()
        
        # 计算成功率
        if stats['total_analyses'] > 0:
            stats['success_rate'] = stats['successful_analyses'] / stats['total_analyses']
            stats['error_location_rate'] = stats['error_location_found'] / stats['total_analyses']
            stats['original_method_rate'] = stats['original_method_found'] / stats['total_analyses']
            stats['current_context_rate'] = stats['current_context_extracted'] / stats['total_analyses']
        else:
            stats['success_rate'] = 0.0
            stats['error_location_rate'] = 0.0
            stats['original_method_rate'] = 0.0
            stats['current_context_rate'] = 0.0
        
        # 添加组件统计
        stats['component_cache_stats'] = {
            'error_resolver': self.error_resolver.get_cache_stats(),
            'legacy_searcher': self.legacy_searcher.get_cache_stats()
        }
        
        return stats
    
    def clear_all_caches(self):
        """清理所有缓存"""
        try:
            self.error_resolver.clear_cache()
            self.legacy_searcher.clear_cache()
            self.logger.info("所有组件缓存已清理")
        except Exception as e:
            self.logger.warning(f"清理缓存失败: {str(e)}")
    
    def test_complete_workflow(self, test_error_json: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        测试完整工作流程
        
        Args:
            test_error_json: 测试用的错误JSON，如果为None则使用默认测试数据
            
        Returns:
            测试结果
        """
        try:
            self.logger.info("开始测试完整上下文分析工作流程")
            
            # 使用提供的测试数据或默认数据
            if test_error_json is None:
                test_error_json = {
                    "package": "com.cet.piem.service.impl",
                    "class": "NodeServiceImpl",
                    "missing_method": "getProjectTree",
                    "in_param": {"energyType": "java.lang.String"},
                    "out_return": "java.util.List<ProjectNode>",
                    "context": "Method call in service implementation",
                    "location": {
                        "file": "/src/main/java/com/cet/piem/service/impl/NodeServiceImpl.java",
                        "line": 466,
                        "column": 25
                    }
                }
            
            # 执行完整分析
            result = self.analyze_error_context(test_error_json)
            
            # 生成测试报告
            test_report = {
                'test_success': result.analysis_success,
                'processing_time': result.processing_time,
                'components_used': result.components_used,
                'context_quality': result.complete_context.context_quality,
                'confidence_score': result.complete_context.confidence_score,
                'available_information': result.complete_context.available_information,
                'error_messages': result.error_messages,
                'description_length': len(result.complete_context.combined_description),
                'semantic_features_extracted': {
                    'primary_function': bool(result.semantic_features.primary_function),
                    'business_domain': bool(result.semantic_features.business_domain),
                    'key_concepts_count': len(result.semantic_features.key_concepts),
                    'technical_keywords_count': len(result.semantic_features.technical_keywords)
                }
            }
            
            self.logger.info(f"工作流程测试完成，成功: {test_report['test_success']}")
            return test_report
            
        except Exception as e:
            self.logger.error(f"工作流程测试失败: {str(e)}")
            return {
                'test_success': False,
                'error_message': str(e),
                'processing_time': 0.0
            }