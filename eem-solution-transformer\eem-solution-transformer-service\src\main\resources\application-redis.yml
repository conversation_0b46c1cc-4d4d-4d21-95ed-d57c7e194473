spring:
  redis:
    timeout: 3000ms
    client-type: lettuce
    # Lettuce 连接池配置
    lettuce:
      pool:
        max-active: 16       # 最大连接数
        max-idle: 8          # 最大空闲连接
        min-idle: 4          # 最小空闲连接
        max-wait: 1000       # 获取连接最大等待时间(ms)
      shutdown-timeout: 100  # 关闭超时时间(ms)
    # Redisson 配置（直接使用 spring.redis.redisson 属性）
    redisson:
      config: |
        singleServerConfig:
          address: "redis://${spring.redis.host}:${spring.redis.port}"
          password: ${spring.redis.password}
          database: ${spring.redis.database}
          connectionMinimumIdleSize: 5
          connectionPoolSize: 32
          idleConnectionTimeout: 30000
          connectTimeout: 5000
          timeout: 5000
          retryAttempts: 1
          retryInterval: 500
        #protocol: RESP3
        # 线程池配置 
        # 增加I/O能力
        nettyThreads: 8
        # 增加处理能力
        threads: 4
        # 序列化配置
        codec: !<org.redisson.codec.JsonJacksonCodec> {}
        # 看门狗超时时间(ms)
        lockWatchdogTimeout: 30000       
        # 启用Lua脚本缓存
        useScriptCache: true
        transportMode: NIO
