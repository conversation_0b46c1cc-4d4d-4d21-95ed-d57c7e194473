"""
LLM语义分析器

提供标准化的LLM调用能力，固定格式进行Java方法语义分析
"""

import json
import logging
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
import requests


@dataclass
class LLMSemanticResult:
    """LLM语义分析结果"""
    business_purpose: str
    functional_description: str
    parameter_analysis: Dict[str, str]
    return_value_meaning: str
    usage_scenarios: List[str]
    business_tags: List[str]
    complexity_assessment: str
    confidence_score: float
    raw_response: Optional[Dict] = None


class LLMSemanticAnalyzer:
    """
    LLM语义分析器 - 提供标准化的LLM调用能力
    
    固定格式：method:方法完整内容, context:上下文, notes:方法注释
    """
    
    def __init__(self, config: Optional[Dict] = None):
        """
        初始化LLM语义分析器
        
        Args:
            config: 配置字典，包含LLM相关配置
        """
        self.logger = logging.getLogger(__name__)
        self.config = config or {}
        
        # LLM配置
        self.base_url = self.config.get('llm_base_url', 'http://localhost:8000')
        self.api_key = self.config.get('llm_api_key', '')
        self.timeout = self.config.get('llm_timeout', 30)
        self.model_name = self.config.get('llm_model', 'gpt-3.5-turbo')
        
        self.logger.info(f"LLM语义分析器初始化完成，模型: {self.model_name}")
    
    def analyze_method_semantics(self, 
                               method_content: str, 
                               context: str, 
                               notes: str = "") -> LLMSemanticResult:
        """
        使用LLM分析Java方法语义
        
        Args:
            method_content: 方法完整内容（必须）
            context: 上下文信息（必须）
            notes: 方法注释和文档（可选）
            
        Returns:
            LLMSemanticResult: 语义分析结果
        """
        try:
            self.logger.debug("开始LLM语义分析")
            
            # 构建固定格式的提示词
            prompt = self._build_standard_prompt(method_content, context, notes)
            
            # 调用LLM API
            llm_response = self._call_llm_api(prompt)
            
            # 解析LLM响应
            result = self._parse_llm_response(llm_response)
            
            self.logger.debug("LLM语义分析完成")
            return result
            
        except Exception as e:
            self.logger.error(f"LLM语义分析失败: {e}")
            return self._create_fallback_result(method_content, context)
    
    def _build_standard_prompt(self, method_content: str, context: str, notes: str) -> str:
        """
        构建标准化提示词
        
        固定格式：method:方法完整内容, context:上下文, notes:方法注释
        """
        prompt_template = """method:{method_content}
context:{context}
notes:{notes}

请作为Java代码专家，分析此方法的语义信息：

1. 业务目的（Business Purpose）: 这个方法解决什么业务问题？
2. 功能描述（Functional Description）: 方法的具体功能是什么？
3. 参数分析（Parameter Analysis）: 每个参数的名称、类型和业务含义
4. 返回值含义（Return Value Meaning）: 返回值的类型和业务意义
5. 使用场景（Usage Scenarios）: 在什么情况下会调用这个方法？
6. 业务标签（Business Tags）: 3-5个关键词描述业务领域
7. 复杂度评估（Complexity Assessment）: 简单/中等/复杂
8. 置信度评分（Confidence Score）: 0-1之间的评分

请以JSON格式返回分析结果：
{{
  "business_purpose": "业务目的描述",
  "functional_description": "功能描述",
  "parameter_analysis": {{"param1": "含义1", "param2": "含义2"}},
  "return_value_meaning": "返回值含义",
  "usage_scenarios": ["场景1", "场景2"],
  "business_tags": ["标签1", "标签2"],
  "complexity_assessment": "复杂度级别",
  "confidence_score": 0.85
}}"""
        
        return prompt_template.format(
            method_content=method_content,
            context=context,
            notes=notes
        )
    
    def _call_llm_api(self, prompt: str) -> Dict:
        """
        调用LLM API
        
        Args:
            prompt: 完整的提示词
            
        Returns:
            Dict: LLM响应结果
        """
        try:
            # 构建请求数据
            request_data = {
                "model": self.model_name,
                "messages": [
                    {"role": "system", "content": "你是一个专业的Java代码分析专家，擅长理解方法语义和业务逻辑。"},
                    {"role": "user", "content": prompt}
                ],
                "temperature": 0.1,
                "max_tokens": 1000
            }
            
            # 添加API密钥（如果配置了）
            headers = {"Content-Type": "application/json"}
            if self.api_key:
                headers["Authorization"] = f"Bearer {self.api_key}"
            
            # 发送请求
            response = requests.post(
                f"{self.base_url}/v1/chat/completions",
                headers=headers,
                json=request_data,
                timeout=self.timeout
            )
            
            response.raise_for_status()
            return response.json()
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"LLM API调用失败: {e}")
            raise
        except Exception as e:
            self.logger.error(f"LLM调用异常: {e}")
            raise
    
    def _parse_llm_response(self, llm_response: Dict) -> LLMSemanticResult:
        """
        解析LLM响应
        
        Args:
            llm_response: LLM API返回的原始响应
            
        Returns:
            LLMSemanticResult: 解析后的语义分析结果
        """
        try:
            # 提取LLM返回的内容
            content = llm_response['choices'][0]['message']['content']
            
            # 尝试解析JSON（LLM应该返回JSON格式）
            try:
                result_data = json.loads(content)
            except json.JSONDecodeError:
                # 如果JSON解析失败，尝试提取JSON部分
                result_data = self._extract_json_from_text(content)
            
            # 创建结果对象
            return LLMSemanticResult(
                business_purpose=result_data.get('business_purpose', ''),
                functional_description=result_data.get('functional_description', ''),
                parameter_analysis=result_data.get('parameter_analysis', {}),
                return_value_meaning=result_data.get('return_value_meaning', ''),
                usage_scenarios=result_data.get('usage_scenarios', []),
                business_tags=result_data.get('business_tags', []),
                complexity_assessment=result_data.get('complexity_assessment', '未知'),
                confidence_score=result_data.get('confidence_score', 0.7),
                raw_response=llm_response
            )
            
        except Exception as e:
            self.logger.error(f"解析LLM响应失败: {e}")
            raise
    
    def _extract_json_from_text(self, text: str) -> Dict:
        """
        从文本中提取JSON内容
        
        Args:
            text: 包含JSON的文本
            
        Returns:
            Dict: 提取的JSON数据
        """
        try:
            # 查找JSON开始和结束位置
            start_idx = text.find('{')
            end_idx = text.rfind('}')
            
            if start_idx != -1 and end_idx != -1:
                json_str = text[start_idx:end_idx+1]
                return json.loads(json_str)
            else:
                raise ValueError("未找到有效的JSON内容")
                
        except Exception as e:
            self.logger.warning(f"从文本提取JSON失败: {e}")
            # 返回默认结构
            return {
                "business_purpose": "LLM分析失败",
                "functional_description": "无法解析LLM响应",
                "parameter_analysis": {},
                "return_value_meaning": "",
                "usage_scenarios": [],
                "business_tags": [],
                "complexity_assessment": "未知",
                "confidence_score": 0.3
            }
    
    def _create_fallback_result(self, method_content: str, context: str) -> LLMSemanticResult:
        """
        创建回退结果（LLM调用失败时使用）
        """
        return LLMSemanticResult(
            business_purpose="执行方法功能",
            functional_description=f"方法基础功能: {method_content[:50]}...",
            parameter_analysis={},
            return_value_meaning="返回处理结果",
            usage_scenarios=["基础功能调用"],
            business_tags=["通用方法"],
            complexity_assessment="简单",
            confidence_score=0.5,
            raw_response=None
        )
    
    def batch_analyze_methods(self, 
                            methods: List[Dict[str, str]]) -> List[LLMSemanticResult]:
        """
        批量分析方法语义
        
        Args:
            methods: 方法列表，每个元素包含method_content, context, notes
            
        Returns:
            List[LLMSemanticResult]: 批量分析结果
        """
        results = []
        
        for i, method_info in enumerate(methods):
            try:
                result = self.analyze_method_semantics(
                    method_content=method_info.get('method_content', ''),
                    context=method_info.get('context', ''),
                    notes=method_info.get('notes', '')
                )
                results.append(result)
                
                # 进度日志
                if (i + 1) % 10 == 0:
                    self.logger.info(f"批量分析进度: {i+1}/{len(methods)}")
                    
            except Exception as e:
                self.logger.error(f"分析方法 {i} 失败: {e}")
                results.append(self._create_fallback_result(
                    method_info.get('method_content', ''),
                    method_info.get('context', '')
                ))
        
        return results


# 使用示例
def example_usage():
    """LLM语义分析器使用示例"""
    
    # 初始化分析器
    config = {
        'llm_base_url': 'http://localhost:8000',
        'llm_api_key': 'your-api-key',
        'llm_timeout': 30,
        'llm_model': 'gpt-3.5-turbo'
    }
    
    analyzer = LLMSemanticAnalyzer(config)
    
    # 示例方法内容
    method_content = """
public List<ProjectNode> getProjectTree(String energyType) {
    // 根据能源类型获取项目树
    return projectRepository.findByEnergyType(energyType);
}
"""
    
    context = "在NodeServiceImpl中调用，用于前端树形控件展示"
    notes = "@param energyType 能源类型标识符\n@return 项目节点列表"
    
    # 执行分析
    try:
        result = analyzer.analyze_method_semantics(method_content, context, notes)
        
        print("=== LLM语义分析结果 ===")
        print(f"业务目的: {result.business_purpose}")
        print(f"功能描述: {result.functional_description}")
        print(f"参数分析: {result.parameter_analysis}")
        print(f"返回值含义: {result.return_value_meaning}")
        print(f"使用场景: {result.usage_scenarios}")
        print(f"业务标签: {result.business_tags}")
        print(f"复杂度: {result.complexity_assessment}")
        print(f"置信度: {result.confidence_score}")
        
    except Exception as e:
        print(f"分析失败: {e}")


if __name__ == "__main__":
    example_usage()