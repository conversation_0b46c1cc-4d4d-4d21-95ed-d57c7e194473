"""
性能优化器

提供批量处理、内存管理、缓存优化等性能提升功能。
"""

import os
import gc
import psutil
import logging
import threading
import time
from typing import List, Dict, Any, Optional, Callable, Iterator
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor, as_completed
from pathlib import Path
import numpy as np

try:
    import torch
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False


class MemoryManager:
    """内存管理器"""
    
    def __init__(self, memory_limit_mb: int = 8192, 
                 warning_threshold: float = 0.8,
                 cleanup_threshold: float = 0.9):
        """
        初始化内存管理器
        
        Args:
            memory_limit_mb: 内存限制（MB）
            warning_threshold: 警告阈值（占用比例）
            cleanup_threshold: 清理阈值（占用比例）
        """
        self.logger = logging.getLogger(__name__)
        self.memory_limit_bytes = memory_limit_mb * 1024 * 1024
        self.warning_threshold = warning_threshold
        self.cleanup_threshold = cleanup_threshold
        
        # 监控状态
        self.monitoring = False
        self.monitor_thread = None
        self.cleanup_callbacks: List[Callable] = []
        
        # 统计信息
        self.stats = {
            'peak_memory': 0,
            'cleanup_count': 0,
            'warning_count': 0,
            'oom_count': 0
        }
    
    def start_monitoring(self, interval: float = 5.0):
        """
        开始内存监控
        
        Args:
            interval: 监控间隔（秒）
        """
        if self.monitoring:
            return
        
        self.monitoring = True
        self.monitor_thread = threading.Thread(
            target=self._monitor_memory, 
            args=(interval,),
            daemon=True
        )
        self.monitor_thread.start()
        self.logger.info(f"内存监控已启动，限制: {self.memory_limit_bytes // 1024 // 1024}MB")
    
    def stop_monitoring(self):
        """停止内存监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=1.0)
        self.logger.info("内存监控已停止")
    
    def register_cleanup_callback(self, callback: Callable):
        """
        注册清理回调函数
        
        Args:
            callback: 清理函数
        """
        self.cleanup_callbacks.append(callback)
    
    def get_memory_usage(self) -> Dict[str, float]:
        """
        获取当前内存使用情况
        
        Returns:
            内存使用信息
        """
        process = psutil.Process()
        memory_info = process.memory_info()
        
        usage = {
            'rss_mb': memory_info.rss / 1024 / 1024,
            'vms_mb': memory_info.vms / 1024 / 1024,
            'percent': process.memory_percent(),
            'available_mb': psutil.virtual_memory().available / 1024 / 1024,
            'limit_mb': self.memory_limit_bytes / 1024 / 1024
        }
        
        # 更新峰值
        if memory_info.rss > self.stats['peak_memory']:
            self.stats['peak_memory'] = memory_info.rss
        
        return usage
    
    def check_memory_pressure(self) -> bool:
        """
        检查内存压力
        
        Returns:
            是否存在内存压力
        """
        usage = self.get_memory_usage()
        current_usage = usage['rss_mb'] * 1024 * 1024
        
        if current_usage > self.memory_limit_bytes * self.cleanup_threshold:
            self.logger.warning(f"内存使用过高: {usage['rss_mb']:.1f}MB")
            self._trigger_cleanup()
            self.stats['cleanup_count'] += 1
            return True
        elif current_usage > self.memory_limit_bytes * self.warning_threshold:
            self.logger.warning(f"内存使用接近限制: {usage['rss_mb']:.1f}MB")
            self.stats['warning_count'] += 1
            return True
        
        return False
    
    def force_cleanup(self):
        """强制执行内存清理"""
        self._trigger_cleanup()
        self.stats['cleanup_count'] += 1
    
    def _monitor_memory(self, interval: float):
        """内存监控循环"""
        while self.monitoring:
            try:
                self.check_memory_pressure()
                time.sleep(interval)
            except Exception as e:
                self.logger.error(f"内存监控错误: {str(e)}")
                time.sleep(interval)
    
    def _trigger_cleanup(self):
        """触发内存清理"""
        self.logger.info("执行内存清理...")
        
        # 执行注册的清理回调
        for callback in self.cleanup_callbacks:
            try:
                callback()
            except Exception as e:
                self.logger.error(f"清理回调执行失败: {str(e)}")
        
        # 强制垃圾回收
        gc.collect()
        
        # PyTorch内存清理
        if TORCH_AVAILABLE and torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        usage_after = self.get_memory_usage()
        self.logger.info(f"内存清理完成，当前使用: {usage_after['rss_mb']:.1f}MB")


class BatchProcessor:
    """批量处理器"""
    
    def __init__(self, batch_size: int = 32, max_workers: int = 4,
                 memory_manager: Optional[MemoryManager] = None):
        """
        初始化批量处理器
        
        Args:
            batch_size: 批处理大小
            max_workers: 最大工作线程数
            memory_manager: 内存管理器
        """
        self.logger = logging.getLogger(__name__)
        self.batch_size = batch_size
        self.max_workers = max_workers
        self.memory_manager = memory_manager
        
        # 自适应批处理大小
        self.adaptive_batch_size = True
        self.min_batch_size = 1
        self.max_batch_size = batch_size * 4
        
        # 统计信息
        self.stats = {
            'total_batches': 0,
            'successful_batches': 0,
            'failed_batches': 0,
            'total_items': 0,
            'processing_time': 0.0,
            'avg_batch_time': 0.0
        }
    
    def process_batches(self, items: List[Any], 
                       process_func: Callable[[List[Any]], Any],
                       use_threading: bool = True) -> List[Any]:
        """
        批量处理数据
        
        Args:
            items: 待处理的数据列表
            process_func: 处理函数
            use_threading: 是否使用多线程
            
        Returns:
            处理结果列表
        """
        self.logger.info(f"开始批量处理 {len(items)} 个项目，批大小: {self.batch_size}")
        
        start_time = time.time()
        results = []
        current_batch_size = self.batch_size
        
        # 创建批次
        batches = self._create_batches(items, current_batch_size)
        self.stats['total_batches'] = len(batches)
        self.stats['total_items'] = len(items)
        
        if use_threading and self.max_workers > 1:
            results = self._process_batches_threaded(batches, process_func)
        else:
            results = self._process_batches_sequential(batches, process_func)
        
        # 更新统计信息
        processing_time = time.time() - start_time
        self.stats['processing_time'] = processing_time
        if self.stats['total_batches'] > 0:
            self.stats['avg_batch_time'] = processing_time / self.stats['total_batches']
        
        self.logger.info(f"批量处理完成，耗时: {processing_time:.2f}秒")
        return results
    
    def _create_batches(self, items: List[Any], batch_size: int) -> List[List[Any]]:
        """创建批次"""
        batches = []
        for i in range(0, len(items), batch_size):
            batch = items[i:i + batch_size]
            batches.append(batch)
        return batches
    
    def _process_batches_sequential(self, batches: List[List[Any]], 
                                  process_func: Callable) -> List[Any]:
        """顺序处理批次"""
        results = []
        
        for i, batch in enumerate(batches):
            try:
                # 检查内存压力
                if self.memory_manager:
                    self.memory_manager.check_memory_pressure()
                
                batch_result = process_func(batch)
                results.extend(batch_result if isinstance(batch_result, list) else [batch_result])
                self.stats['successful_batches'] += 1
                
                # 进度报告
                if (i + 1) % 10 == 0:
                    self.logger.info(f"已处理 {i + 1}/{len(batches)} 个批次")
                
            except Exception as e:
                self.logger.error(f"批次 {i} 处理失败: {str(e)}")
                self.stats['failed_batches'] += 1
                continue
        
        return results
    
    def _process_batches_threaded(self, batches: List[List[Any]], 
                                process_func: Callable) -> List[Any]:
        """多线程处理批次"""
        results = []
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交所有批次
            future_to_batch = {}
            for i, batch in enumerate(batches):
                future = executor.submit(self._safe_process_batch, batch, process_func, i)
                future_to_batch[future] = i
            
            # 收集结果
            completed = 0
            for future in as_completed(future_to_batch):
                batch_idx = future_to_batch[future]
                try:
                    batch_result = future.result()
                    if batch_result is not None:
                        results.extend(batch_result if isinstance(batch_result, list) else [batch_result])
                        self.stats['successful_batches'] += 1
                    else:
                        self.stats['failed_batches'] += 1
                    
                    completed += 1
                    if completed % 10 == 0:
                        self.logger.info(f"已完成 {completed}/{len(batches)} 个批次")
                        
                except Exception as e:
                    self.logger.error(f"批次 {batch_idx} 处理失败: {str(e)}")
                    self.stats['failed_batches'] += 1
        
        return results
    
    def _safe_process_batch(self, batch: List[Any], process_func: Callable, 
                          batch_idx: int) -> Optional[Any]:
        """安全处理单个批次"""
        try:
            # 检查内存压力
            if self.memory_manager:
                self.memory_manager.check_memory_pressure()
            
            return process_func(batch)
            
        except Exception as e:
            self.logger.error(f"批次 {batch_idx} 处理异常: {str(e)}")
            return None
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = self.stats.copy()
        if stats['total_batches'] > 0:
            stats['success_rate'] = stats['successful_batches'] / stats['total_batches']
        else:
            stats['success_rate'] = 0.0
        return stats


class VectorCache:
    """向量缓存管理器"""
    
    def __init__(self, cache_dir: str, max_cache_size_mb: int = 1024):
        """
        初始化向量缓存
        
        Args:
            cache_dir: 缓存目录
            max_cache_size_mb: 最大缓存大小（MB）
        """
        self.logger = logging.getLogger(__name__)
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self.max_cache_size = max_cache_size_mb * 1024 * 1024
        
        # 缓存索引
        self.cache_index = {}
        self._load_cache_index()
        
        # 统计信息
        self.stats = {
            'cache_hits': 0,
            'cache_misses': 0,
            'cache_size_mb': 0,
            'evictions': 0
        }
    
    def get_vector(self, key: str) -> Optional[np.ndarray]:
        """
        获取缓存的向量
        
        Args:
            key: 缓存键
            
        Returns:
            向量或None
        """
        if key in self.cache_index:
            try:
                cache_file = self.cache_dir / f"{key}.npy"
                if cache_file.exists():
                    vector = np.load(cache_file)
                    self.stats['cache_hits'] += 1
                    # 更新访问时间
                    self.cache_index[key]['last_access'] = time.time()
                    return vector
                else:
                    # 缓存文件不存在，清理索引
                    del self.cache_index[key]
            except Exception as e:
                self.logger.warning(f"读取缓存失败: {str(e)}")
                if key in self.cache_index:
                    del self.cache_index[key]
        
        self.stats['cache_misses'] += 1
        return None
    
    def set_vector(self, key: str, vector: np.ndarray):
        """
        缓存向量
        
        Args:
            key: 缓存键
            vector: 向量
        """
        try:
            cache_file = self.cache_dir / f"{key}.npy"
            np.save(cache_file, vector)
            
            # 更新索引
            file_size = cache_file.stat().st_size
            self.cache_index[key] = {
                'size': file_size,
                'created': time.time(),
                'last_access': time.time()
            }
            
            # 检查缓存大小
            self._check_cache_size()
            
        except Exception as e:
            self.logger.warning(f"缓存向量失败: {str(e)}")
    
    def clear_cache(self):
        """清空缓存"""
        try:
            import shutil
            if self.cache_dir.exists():
                shutil.rmtree(self.cache_dir)
                self.cache_dir.mkdir(parents=True, exist_ok=True)
            
            self.cache_index.clear()
            self.stats['cache_size_mb'] = 0
            self.logger.info("向量缓存已清空")
            
        except Exception as e:
            self.logger.error(f"清空缓存失败: {str(e)}")
    
    def _load_cache_index(self):
        """加载缓存索引"""
        try:
            index_file = self.cache_dir / "cache_index.json"
            if index_file.exists():
                import json
                with open(index_file, 'r') as f:
                    self.cache_index = json.load(f)
                
                # 验证缓存文件
                valid_keys = []
                for key in self.cache_index:
                    cache_file = self.cache_dir / f"{key}.npy"
                    if cache_file.exists():
                        valid_keys.append(key)
                
                # 清理无效索引
                self.cache_index = {k: v for k, v in self.cache_index.items() if k in valid_keys}
                
                self.logger.info(f"加载缓存索引，有效条目: {len(self.cache_index)}")
        except Exception as e:
            self.logger.warning(f"加载缓存索引失败: {str(e)}")
            self.cache_index = {}
    
    def _save_cache_index(self):
        """保存缓存索引"""
        try:
            import json
            index_file = self.cache_dir / "cache_index.json"
            with open(index_file, 'w') as f:
                json.dump(self.cache_index, f, indent=2)
        except Exception as e:
            self.logger.warning(f"保存缓存索引失败: {str(e)}")
    
    def _check_cache_size(self):
        """检查并管理缓存大小"""
        total_size = sum(item['size'] for item in self.cache_index.values())
        self.stats['cache_size_mb'] = total_size / 1024 / 1024
        
        if total_size > self.max_cache_size:
            self._evict_old_entries()
    
    def _evict_old_entries(self):
        """清理旧的缓存条目"""
        # 按最后访问时间排序
        sorted_items = sorted(
            self.cache_index.items(),
            key=lambda x: x[1]['last_access']
        )
        
        # 删除最旧的条目，直到缓存大小合适
        current_size = sum(item['size'] for item in self.cache_index.values())
        target_size = self.max_cache_size * 0.8  # 清理到80%
        
        for key, info in sorted_items:
            if current_size <= target_size:
                break
            
            try:
                cache_file = self.cache_dir / f"{key}.npy"
                if cache_file.exists():
                    cache_file.unlink()
                
                current_size -= info['size']
                del self.cache_index[key]
                self.stats['evictions'] += 1
                
            except Exception as e:
                self.logger.warning(f"删除缓存文件失败: {str(e)}")
        
        self.logger.info(f"缓存清理完成，删除 {self.stats['evictions']} 个条目")
        self._save_cache_index()


class PerformanceOptimizer:
    """性能优化器主类"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化性能优化器
        
        Args:
            config: 配置字典
        """
        self.logger = logging.getLogger(__name__)
        self.config = config
        
        # 初始化组件
        self.memory_manager = MemoryManager(
            memory_limit_mb=config.get('memory_limit', 8192),
            warning_threshold=config.get('memory_warning_threshold', 0.8),
            cleanup_threshold=config.get('memory_cleanup_threshold', 0.9)
        )
        
        self.batch_processor = BatchProcessor(
            batch_size=config.get('batch_size', 32),
            max_workers=config.get('max_workers', 4),
            memory_manager=self.memory_manager
        )
        
        cache_dir = config.get('cache_dir', str(Path.home() / ".cache" / "error_prone_json_reporter"))
        self.vector_cache = VectorCache(
            cache_dir=cache_dir,
            max_cache_size_mb=config.get('max_cache_size_mb', 1024)
        )
        
        # 启动内存监控
        if config.get('enable_memory_monitoring', True):
            self.memory_manager.start_monitoring()
    
    def optimize_vector_encoding(self, encoder, texts: List[str]) -> np.ndarray:
        """
        优化向量编码过程
        
        Args:
            encoder: 向量编码器
            texts: 文本列表
            
        Returns:
            编码向量
        """
        # 检查缓存
        cached_vectors = []
        uncached_texts = []
        uncached_indices = []
        
        for i, text in enumerate(texts):
            cache_key = self._get_text_cache_key(text)
            cached_vector = self.vector_cache.get_vector(cache_key)
            
            if cached_vector is not None:
                cached_vectors.append((i, cached_vector))
            else:
                uncached_texts.append(text)
                uncached_indices.append(i)
        
        # 批量编码未缓存的文本
        if uncached_texts:
            def encode_batch(batch_texts):
                return encoder._encode_batch(batch_texts)
            
            batch_results = self.batch_processor.process_batches(
                self._create_text_batches(uncached_texts),
                encode_batch,
                use_threading=False  # 向量编码通常不适合多线程
            )
            
            # 展平结果并缓存
            uncached_vectors = []
            for batch_result in batch_results:
                uncached_vectors.extend(batch_result)
            
            # 缓存新编码的向量
            for text, vector in zip(uncached_texts, uncached_vectors):
                cache_key = self._get_text_cache_key(text)
                self.vector_cache.set_vector(cache_key, vector)
            
            # 合并结果
            for i, vector in zip(uncached_indices, uncached_vectors):
                cached_vectors.append((i, vector))
        
        # 按原始顺序排序并返回
        cached_vectors.sort(key=lambda x: x[0])
        return np.array([v[1] for v in cached_vectors])
    
    def optimize_similarity_search(self, matcher, query_vectors: np.ndarray) -> List[List[Any]]:
        """
        优化相似度搜索
        
        Args:
            matcher: 相似度匹配器
            query_vectors: 查询向量
            
        Returns:
            搜索结果
        """
        # 批量搜索
        def search_batch(batch_vectors):
            return matcher.index.search(batch_vectors, matcher.top_k)
        
        # 创建批次
        batches = self._create_vector_batches(query_vectors)
        
        # 批量处理
        batch_results = self.batch_processor.process_batches(
            batches,
            search_batch,
            use_threading=True  # 搜索可以并行
        )
        
        # 合并结果
        all_distances = []
        all_indices = []
        
        for distances, indices in batch_results:
            all_distances.extend(distances)
            all_indices.extend(indices)
        
        # 转换为最终格式
        results = []
        for distances, indices in zip(all_distances, all_indices):
            query_results = []
            for distance, idx in zip(distances, indices):
                if idx != -1:
                    similarity = matcher._distance_to_similarity(distance)
                    if similarity >= matcher.similarity_threshold:
                        method = matcher.candidate_methods[idx]
                        query_results.append((method, similarity))
            
            query_results.sort(key=lambda x: x[1], reverse=True)
            results.append(query_results)
        
        return results
    
    def _create_text_batches(self, texts: List[str]) -> List[List[str]]:
        """创建文本批次"""
        batch_size = self.config.get('text_batch_size', 32)
        batches = []
        for i in range(0, len(texts), batch_size):
            batches.append(texts[i:i + batch_size])
        return batches
    
    def _create_vector_batches(self, vectors: np.ndarray) -> List[np.ndarray]:
        """创建向量批次"""
        batch_size = self.config.get('vector_batch_size', 100)
        batches = []
        for i in range(0, len(vectors), batch_size):
            batches.append(vectors[i:i + batch_size])
        return batches
    
    def _get_text_cache_key(self, text: str) -> str:
        """生成文本缓存键"""
        import hashlib
        content = f"{self.config.get('model_name', 'default')}:{text}"
        return hashlib.md5(content.encode('utf-8')).hexdigest()
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        return {
            'memory_manager': {
                'stats': self.memory_manager.stats,
                'current_usage': self.memory_manager.get_memory_usage()
            },
            'batch_processor': self.batch_processor.get_stats(),
            'vector_cache': self.vector_cache.stats
        }
    
    def cleanup(self):
        """清理资源"""
        self.memory_manager.stop_monitoring()
        self.memory_manager.force_cleanup()
        self.logger.info("性能优化器清理完成")