"""
现有工具适配器

集成现有的错误分析工具（如inspect_method.py和class_file_reader.py）
"""

import os
import sys
import re
import xml.etree.ElementTree as ET
from pathlib import Path
from typing import List, Dict, Optional, Any
from collections import defaultdict

# 添加java_error_analyzer到路径
current_dir = Path(__file__).parent
project_root = current_dir.parent.parent
java_analyzer_path = project_root / "java_error_analyzer"
if str(java_analyzer_path) not in sys.path:
    sys.path.insert(0, str(java_analyzer_path))

try:
    from inspect_method import InspectRunner
    from class_file_reader import ClassFileReader
    from models import ClassFileInfo
except ImportError as e:
    print(f"Warning: Could not import java_error_analyzer modules: {e}")
    InspectRunner = None
    ClassFileReader = None
    ClassFileInfo = None

from ..common.interfaces import ErrorDetectorInterface
from ..common.models import ErrorReport, Configuration


class ExistingToolAdapter(ErrorDetectorInterface):
    """现有工具适配器 - 集成inspect_method.py和class_file_reader.py"""
    
    def __init__(self):
        self.name = "existing_tool"
        self.inspect_runner = None
        self.class_reader = None
        
    def detect_errors(self, project_path: str, config: Configuration) -> List[ErrorReport]:
        """
        使用现有工具检测错误
        
        Args:
            project_path: 项目路径
            config: 配置对象
            
        Returns:
            错误报告列表
        """
        if not self.is_available():
            raise RuntimeError("Existing tools are not available")
        
        # 初始化工具
        self.inspect_runner = InspectRunner(
            project_path=project_path,
            output_path=config.output_dir
        )
        self.class_reader = ClassFileReader(project_path=project_path)
        
        # 1. 运行inspect_method.py获取方法问题
        method_problems = self._run_inspect_method()
        if not method_problems:
            print("No method problems found by inspect_method.py")
            return []
        
        # 2. 使用class_file_reader.py获取详细信息
        error_reports = []
        for problem in method_problems:
            try:
                enhanced_error = self._enhance_error_with_source_info(problem)
                if enhanced_error:
                    error_reports.append(enhanced_error)
            except Exception as e:
                print(f"Error enhancing problem {problem}: {e}")
                continue
        
        print(f"Generated {len(error_reports)} error reports from {len(method_problems)} problems")
        return error_reports
    
    def is_available(self) -> bool:
        """检查现有工具是否可用"""
        return InspectRunner is not None and ClassFileReader is not None
    
    def get_name(self) -> str:
        """获取检测器名称"""
        return self.name
    
    def _run_inspect_method(self) -> List[Dict[str, Any]]:
        """运行inspect_method.py获取方法问题"""
        try:
            # 运行inspect并解析结果
            if self.inspect_runner.run_inspect():
                problems = self.inspect_runner.parse_problems_manually()
                method_problems = self.inspect_runner.filter_method_problems(problems)
                return method_problems
            else:
                print("Failed to run inspect.bat")
                return []
        except Exception as e:
            print(f"Error running inspect_method: {e}")
            return []
    
    def _enhance_error_with_source_info(self, problem: Dict[str, Any]) -> Optional[ErrorReport]:
        """使用class_file_reader.py增强错误信息"""
        try:
            # 从problem中提取基本信息
            class_name = self._extract_class_name_from_problem(problem)
            package_name = problem.get('package', '')
            description = problem.get('description', '')
            line_str = problem.get('line', '0')
            highlighted_element = problem.get('highlighted_element', '')
            
            # 解析行号
            try:
                line_number = int(line_str) if line_str.isdigit() else 0
            except (ValueError, TypeError):
                line_number = 0
            
            # 提取方法名
            missing_method = self._extract_method_name(highlighted_element, description)
            if not missing_method:
                return None
            
            # 使用class_file_reader获取详细信息
            class_info = None
            if class_name:
                # 尝试完整类名
                full_class_name = f"{package_name}.{class_name}" if package_name else class_name
                class_info = self.class_reader.read_class_file(full_class_name)
                
                # 如果失败，尝试简单类名
                if not class_info:
                    class_info = self.class_reader.read_class_file(class_name)
            
            # 构建ErrorReport
            context = self._build_context(class_info, missing_method, line_number)
            in_param, out_return = self._extract_method_signature_info(
                missing_method, class_info, description
            )
            
            return ErrorReport(
                package=package_name or "unknown",
                class_name=class_name or "unknown",
                missing_method=missing_method,
                in_param=in_param,
                out_return=out_return,
                line=[line_number] if line_number > 0 else [],
                context=context,
                error_type="missing_method"
            )
            
        except Exception as e:
            print(f"Error enhancing error with source info: {e}")
            return None
    
    def _extract_class_name_from_problem(self, problem: Dict[str, Any]) -> str:
        """从problem数据中提取类名"""
        # 尝试从entry_point FQNAME提取
        if 'entry_point_fqname' in problem:
            fqname = problem['entry_point_fqname']
            if fqname:
                parts = fqname.split()
                if len(parts) > 1:
                    class_part = parts[0]
                    return class_part.split('.')[-1]
                elif '.' in fqname:
                    return fqname.split('.')[-1]
        
        # 尝试从file路径提取
        if 'file' in problem:
            file_path = problem['file']
            if file_path:
                filename = os.path.basename(file_path)
                if filename.endswith('.java'):
                    return filename[:-5]
        
        return 'Unknown'
    
    def _extract_method_name(self, highlighted_element: str, description: str) -> str:
        """提取方法名"""
        if highlighted_element and highlighted_element != 'Unknown':
            # 清理方法名，移除参数部分
            method_name = highlighted_element.split('(')[0].strip()
            return method_name
        
        # 尝试从描述中提取方法名
        method_match = re.search(r"'([^']+)'", description)
        if method_match:
            method_name = method_match.group(1).split('(')[0].strip()
            return method_name
        
        return 'unknown_method'
    
    def _build_context(self, class_info: Optional[Any], method_name: str, line_number: int) -> str:
        """构建上下文信息"""
        context_parts = []
        
        if class_info:
            context_parts.append(f"Class: {class_info.class_name}")
            if class_info.package_name:
                context_parts.append(f"Package: {class_info.package_name}")
            
            # 添加相关方法信息
            if hasattr(class_info, 'methods') and class_info.methods:
                similar_methods = [m for m in class_info.methods if method_name.lower() in m.lower()]
                if similar_methods:
                    context_parts.append(f"Similar methods: {', '.join(similar_methods[:3])}")
        
        if line_number > 0:
            context_parts.append(f"Line: {line_number}")
        
        context_parts.append(f"Missing method: {method_name}")
        
        return "; ".join(context_parts)
    
    def _extract_method_signature_info(self, method_name: str, class_info: Optional[Any], 
                                     description: str) -> tuple[Dict[str, str], str]:
        """提取方法签名信息（参数和返回类型）"""
        in_param = {}
        out_return = "unknown"
        
        # 尝试从方法名中提取参数信息
        if '(' in method_name and ')' in method_name:
            param_part = method_name[method_name.find('(')+1:method_name.find(')')]
            if param_part.strip():
                # 简单的参数解析
                params = [p.strip() for p in param_part.split(',') if p.strip()]
                for i, param in enumerate(params):
                    in_param[f"param{i+1}"] = param if param else "unknown"
        
        # 尝试从描述中提取更多信息
        if "返回" in description or "return" in description.lower():
            # 尝试提取返回类型信息
            return_match = re.search(r"返回\s*([^\s,，。]+)", description)
            if return_match:
                out_return = return_match.group(1)
        
        # 如果有类信息，尝试匹配已知方法
        if class_info and hasattr(class_info, 'methods'):
            base_method_name = method_name.split('(')[0]
            for method in class_info.methods:
                if base_method_name.lower() in method.lower():
                    # 这里可以扩展为更复杂的方法签名解析
                    break
        
        return in_param, out_return


class XMLToolAdapter(ErrorDetectorInterface):
    """XML工具适配器 - 直接解析JavaAnnotator.xml文件"""
    
    def __init__(self):
        self.name = "xml_tool"
    
    def detect_errors(self, project_path: str, config: Configuration) -> List[ErrorReport]:
        """
        从XML文件解析错误
        
        Args:
            project_path: 项目路径
            config: 配置对象
            
        Returns:
            错误报告列表
        """
        xml_file = os.path.join(config.output_dir, config.existing_tool_output)
        if not os.path.exists(xml_file):
            print(f"XML file not found: {xml_file}")
            return []
        
        try:
            return self._parse_xml_file(xml_file)
        except Exception as e:
            print(f"Error parsing XML file: {e}")
            return []
    
    def is_available(self) -> bool:
        """检查XML工具是否可用"""
        return True
    
    def get_name(self) -> str:
        """获取检测器名称"""
        return self.name
    
    def _parse_xml_file(self, xml_file: str) -> List[ErrorReport]:
        """解析XML文件"""
        error_reports = []
        
        try:
            # 尝试使用XML解析器
            tree = ET.parse(xml_file)
            root = tree.getroot()
            
            for problem in root.findall('.//problem'):
                error_report = self._parse_xml_problem(problem)
                if error_report:
                    error_reports.append(error_report)
        
        except ET.ParseError:
            # 如果XML解析失败，使用正则表达式
            print("XML parsing failed, using regex fallback")
            error_reports = self._parse_xml_with_regex(xml_file)
        
        return error_reports
    
    def _parse_xml_problem(self, problem_element) -> Optional[ErrorReport]:
        """解析XML problem元素"""
        try:
            file_elem = problem_element.find('file')
            line_elem = problem_element.find('line')
            package_elem = problem_element.find('package')
            description_elem = problem_element.find('description')
            highlighted_elem = problem_element.find('highlighted_element')
            
            # 提取基本信息
            file_path = file_elem.text if file_elem is not None else ""
            line_number = int(line_elem.text) if line_elem is not None and line_elem.text.isdigit() else 0
            package_name = package_elem.text if package_elem is not None else ""
            description = description_elem.text if description_elem is not None else ""
            highlighted_element = highlighted_elem.text if highlighted_elem is not None else ""
            
            # 过滤方法相关问题
            if '无法解析方法' not in description:
                return None
            
            # 提取类名
            class_name = os.path.basename(file_path).replace('.java', '') if file_path else 'Unknown'
            
            # 提取方法名
            method_name = highlighted_element.split('(')[0] if highlighted_element else 'unknown_method'
            
            return ErrorReport(
                package=package_name,
                class_name=class_name,
                missing_method=method_name,
                in_param={},
                out_return="unknown",
                line=[line_number] if line_number > 0 else [],
                context=f"Description: {description}",
                error_type="missing_method"
            )
            
        except Exception as e:
            print(f"Error parsing XML problem: {e}")
            return None
    
    def _parse_xml_with_regex(self, xml_file: str) -> List[ErrorReport]:
        """使用正则表达式解析XML文件"""
        error_reports = []
        
        try:
            with open(xml_file, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # 使用正则表达式提取problem块
            problem_pattern = r'<problem>(.*?)</problem>'
            problem_matches = re.findall(problem_pattern, content, re.DOTALL)
            
            for problem_content in problem_matches:
                error_report = self._parse_regex_problem(problem_content)
                if error_report:
                    error_reports.append(error_report)
        
        except Exception as e:
            print(f"Error parsing XML with regex: {e}")
        
        return error_reports
    
    def _parse_regex_problem(self, problem_content: str) -> Optional[ErrorReport]:
        """使用正则表达式解析problem内容"""
        try:
            # 提取字段
            field_patterns = {
                'file': r'<file>(.*?)</file>',
                'line': r'<line>(\d+)</line>',
                'package': r'<package>(.*?)</package>',
                'description': r'<description>(.*?)</description>',
                'highlighted_element': r'<highlighted_element>(.*?)</highlighted_element>'
            }
            
            problem_data = {}
            for field, pattern in field_patterns.items():
                match = re.search(pattern, problem_content, re.DOTALL)
                if match:
                    problem_data[field] = match.group(1).strip()
            
            # 过滤方法相关问题
            description = problem_data.get('description', '')
            if '无法解析方法' not in description:
                return None
            
            # 构建ErrorReport
            file_path = problem_data.get('file', '')
            class_name = os.path.basename(file_path).replace('.java', '') if file_path else 'Unknown'
            method_name = problem_data.get('highlighted_element', 'unknown_method').split('(')[0]
            line_str = problem_data.get('line', '0')
            line_number = int(line_str) if line_str.isdigit() else 0
            
            return ErrorReport(
                package=problem_data.get('package', ''),
                class_name=class_name,
                missing_method=method_name,
                in_param={},
                out_return="unknown",
                line=[line_number] if line_number > 0 else [],
                context=f"Description: {description}",
                error_type="missing_method"
            )
            
        except Exception as e:
            print(f"Error parsing regex problem: {e}")
            return None