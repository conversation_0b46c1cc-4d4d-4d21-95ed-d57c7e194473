"""
两阶段工作流程控制器

管理完整的两阶段执行流程，包括阶段间数据传递、状态检查、恢复机制等。
"""

import os
import json
import time
import hashlib
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional, List, Tuple
from enum import Enum
from dataclasses import dataclass, asdict

from .models import Configuration, ProcessingStats
from .logging_config import get_logger, get_progress_reporter, logging_manager
from .error_handling import <PERSON>rrorManager, MigrationToolError, ConfigurationError


class WorkflowStage(Enum):
    """工作流程阶段"""
    STAGE1 = "stage1"
    STAGE2 = "stage2"
    COMPLETED = "completed"
    FAILED = "failed"


class ExecutionStatus(Enum):
    """执行状态"""
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"


@dataclass
class StageResult:
    """阶段执行结果"""
    stage: WorkflowStage
    status: ExecutionStatus
    start_time: float
    end_time: Optional[float] = None
    output_file: Optional[str] = None
    error_message: Optional[str] = None
    stats: Optional[Dict[str, Any]] = None
    
    @property
    def duration(self) -> float:
        """执行时长"""
        if self.end_time and self.start_time:
            return self.end_time - self.start_time
        return 0.0
    
    @property
    def is_successful(self) -> bool:
        """是否执行成功"""
        return self.status == ExecutionStatus.COMPLETED


@dataclass
class WorkflowState:
    """工作流程状态"""
    workflow_id: str
    config_hash: str
    current_stage: WorkflowStage
    stage1_result: Optional[StageResult] = None
    stage2_result: Optional[StageResult] = None
    created_at: str = ""
    updated_at: str = ""
    
    def __post_init__(self):
        if not self.created_at:
            self.created_at = datetime.now().isoformat()
        self.updated_at = datetime.now().isoformat()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'WorkflowState':
        """从字典创建"""
        # 处理嵌套的StageResult对象
        if data.get('stage1_result'):
            data['stage1_result'] = StageResult(**data['stage1_result'])
        if data.get('stage2_result'):
            data['stage2_result'] = StageResult(**data['stage2_result'])
        
        # 处理枚举类型
        data['current_stage'] = WorkflowStage(data['current_stage'])
        
        return cls(**data)


class WorkflowController:
    """工作流程控制器"""
    
    def __init__(self, config: Configuration):
        self.config = config
        self.logger = get_logger(__name__)
        self.progress_reporter = get_progress_reporter("workflow")
        self.error_manager = None
        
        # 状态管理
        self.state_file = os.path.join(config.output_dir, ".workflow_state.json")
        self.workflow_state = None
        self.workflow_id = self._generate_workflow_id()
        
        # 确保输出目录存在
        Path(config.output_dir).mkdir(parents=True, exist_ok=True)
    
    def _generate_workflow_id(self) -> str:
        """生成工作流程ID"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        config_str = f"{self.config.project_path}_{self.config.src_path}"
        config_hash = hashlib.md5(config_str.encode()).hexdigest()[:8]
        return f"workflow_{timestamp}_{config_hash}"
    
    def _calculate_config_hash(self) -> str:
        """计算配置哈希值"""
        config_dict = {
            'project_path': self.config.project_path,
            'src_path': self.config.src_path,
            'error_detector': self.config.error_detector,
            'existing_tool_output': self.config.existing_tool_output,
            'model_name': self.config.model_name,
            'top_k': self.config.top_k,
            'similarity_threshold': self.config.similarity_threshold
        }
        config_str = json.dumps(config_dict, sort_keys=True)
        return hashlib.md5(config_str.encode()).hexdigest()
    
    def load_workflow_state(self) -> Optional[WorkflowState]:
        """加载工作流程状态"""
        if not os.path.exists(self.state_file):
            return None
        
        try:
            with open(self.state_file, 'r', encoding='utf-8') as f:
                state_data = json.load(f)
            
            state = WorkflowState.from_dict(state_data)
            
            # 检查配置是否发生变化
            current_config_hash = self._calculate_config_hash()
            if state.config_hash != current_config_hash:
                self.logger.warning("配置已发生变化，将重新开始工作流程")
                return None
            
            self.logger.info(f"加载工作流程状态: {state.workflow_id}")
            return state
            
        except Exception as e:
            self.logger.warning(f"加载工作流程状态失败: {e}")
            return None
    
    def save_workflow_state(self, state: WorkflowState) -> None:
        """保存工作流程状态"""
        try:
            state.updated_at = datetime.now().isoformat()
            
            with open(self.state_file, 'w', encoding='utf-8') as f:
                json.dump(state.to_dict(), f, indent=2, ensure_ascii=False, default=str)
            
            self.logger.debug(f"保存工作流程状态: {state.workflow_id}")
            
        except Exception as e:
            self.logger.error(f"保存工作流程状态失败: {e}")
    
    def create_new_workflow_state(self) -> WorkflowState:
        """创建新的工作流程状态"""
        return WorkflowState(
            workflow_id=self.workflow_id,
            config_hash=self._calculate_config_hash(),
            current_stage=WorkflowStage.STAGE1
        )
    
    def validate_stage_prerequisites(self, stage: WorkflowStage, 
                                   workflow_state: WorkflowState) -> bool:
        """
        验证阶段执行的前置条件
        
        Args:
            stage: 要执行的阶段
            workflow_state: 工作流程状态
            
        Returns:
            是否满足前置条件
        """
        if stage == WorkflowStage.STAGE1:
            # 第一阶段前置条件
            if not self.config.project_path:
                raise ConfigurationError("第一阶段需要指定项目路径")
            if not os.path.exists(self.config.project_path):
                raise ConfigurationError(f"项目路径不存在: {self.config.project_path}")
            return True
        
        elif stage == WorkflowStage.STAGE2:
            # 第二阶段前置条件
            if not self.config.src_path:
                raise ConfigurationError("第二阶段需要指定源码路径")
            if not os.path.exists(self.config.src_path):
                raise ConfigurationError(f"源码路径不存在: {self.config.src_path}")
            
            # 检查第一阶段是否完成
            if not workflow_state.stage1_result or not workflow_state.stage1_result.is_successful:
                # 检查错误清单文件是否存在
                if not os.path.exists(self.config.errors_json):
                    raise ConfigurationError(
                        f"第二阶段需要第一阶段的输出文件或指定错误清单文件: {self.config.errors_json}"
                    )
            
            return True
        
        return False
    
    def validate_stage_output(self, stage: WorkflowStage, output_file: str) -> bool:
        """
        验证阶段输出文件
        
        Args:
            stage: 阶段
            output_file: 输出文件路径
            
        Returns:
            输出文件是否有效
        """
        if not os.path.exists(output_file):
            self.logger.error(f"{stage.value}输出文件不存在: {output_file}")
            return False
        
        try:
            # 验证JSON文件格式
            with open(output_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if not isinstance(data, list):
                self.logger.error(f"{stage.value}输出文件格式无效: 应为JSON数组")
                return False
            
            if len(data) == 0:
                self.logger.warning(f"{stage.value}输出文件为空")
            
            # 第一阶段特定验证
            if stage == WorkflowStage.STAGE1:
                required_fields = ['package', 'class', 'missing_method']
                for item in data[:5]:  # 检查前5个项目
                    if not all(field in item for field in required_fields):
                        self.logger.error(f"第一阶段输出缺少必要字段: {required_fields}")
                        return False
            
            # 第二阶段特定验证
            elif stage == WorkflowStage.STAGE2:
                required_fields = ['missing_method', 'candidates']
                for item in data[:5]:  # 检查前5个项目
                    if not all(field in item for field in required_fields):
                        self.logger.error(f"第二阶段输出缺少必要字段: {required_fields}")
                        return False
            
            self.logger.info(f"{stage.value}输出文件验证通过: {len(data)} 项记录")
            return True
            
        except json.JSONDecodeError as e:
            self.logger.error(f"{stage.value}输出文件JSON格式错误: {e}")
            return False
        except Exception as e:
            self.logger.error(f"{stage.value}输出文件验证失败: {e}")
            return False
    
    def execute_stage1(self, workflow_state: WorkflowState) -> StageResult:
        """执行第一阶段"""
        self.logger.info("=" * 60)
        self.logger.info("开始执行第一阶段：错误清单生成")
        self.logger.info("=" * 60)
        
        import time as time_module  # 确保time模块可用
        start_time = time_module.time()
        result = StageResult(
            stage=WorkflowStage.STAGE1,
            status=ExecutionStatus.IN_PROGRESS,
            start_time=start_time
        )
        
        try:
            # 验证前置条件
            self.validate_stage_prerequisites(WorkflowStage.STAGE1, workflow_state)
            
            # 导入并执行第一阶段
            from ..stage1.error_list_generator import ErrorListGenerator
            
            with logging_manager.log_execution_time(self.logger, "第一阶段错误清单生成"):
                generator = ErrorListGenerator(self.config)
                output_file = generator.generate_error_list(self.config.project_path)
                
                # 验证输出
                if self.validate_stage_output(WorkflowStage.STAGE1, output_file):
                    result.status = ExecutionStatus.COMPLETED
                    result.output_file = output_file
                    result.end_time = time_module.time()
                    
                    self.logger.info(f"第一阶段完成，输出文件: {output_file}")
                else:
                    raise MigrationToolError("第一阶段输出验证失败")
        
        except Exception as e:
            import time as time_module  # 确保time模块可用
            result.status = ExecutionStatus.FAILED
            result.error_message = str(e)
            result.end_time = time_module.time()
            
            if self.error_manager:
                self.error_manager.handle_error(e, {"stage": "1"}, "第一阶段执行")
            
            self.logger.error(f"第一阶段执行失败: {e}")
            raise
        
        return result
    
    def execute_stage2(self, workflow_state: WorkflowState) -> StageResult:
        """执行第二阶段"""
        self.logger.info("=" * 60)
        self.logger.info("开始执行第二阶段：API相似度匹配")
        self.logger.info("=" * 60)
        
        import time as time_module  # 确保time模块可用
        start_time = time_module.time()
        result = StageResult(
            stage=WorkflowStage.STAGE2,
            status=ExecutionStatus.IN_PROGRESS,
            start_time=start_time
        )
        
        try:
            # 验证前置条件
            self.validate_stage_prerequisites(WorkflowStage.STAGE2, workflow_state)
            
            # 确定输入文件
            if workflow_state.stage1_result and workflow_state.stage1_result.output_file:
                errors_json_path = workflow_state.stage1_result.output_file
            else:
                errors_json_path = self.config.errors_json
            
            # 导入并执行第二阶段
            from ..stage2.api_matching_engine import APIMatchingEngine
            
            with logging_manager.log_execution_time(self.logger, "第二阶段API匹配"):
                engine = APIMatchingEngine(self.config)
                output_file = engine.match_apis(errors_json_path, self.config.src_path)
                
                # 验证输出
                if self.validate_stage_output(WorkflowStage.STAGE2, output_file):
                    result.status = ExecutionStatus.COMPLETED
                    result.output_file = output_file
                    result.end_time = time_module.time()
                    
                    self.logger.info(f"第二阶段完成，输出文件: {output_file}")
                else:
                    raise MigrationToolError("第二阶段输出验证失败")
        
        except Exception as e:
            import time as time_module  # 确保time模块可用
            result.status = ExecutionStatus.FAILED
            result.error_message = str(e)
            result.end_time = time_module.time()
            
            if self.error_manager:
                self.error_manager.handle_error(e, {"stage": "2"}, "第二阶段执行")
            
            self.logger.error(f"第二阶段执行失败: {e}")
            raise
        
        return result
    
    def execute_workflow(self, target_stage: str = "both", 
                        resume_from_checkpoint: bool = True) -> Dict[str, Any]:
        """
        执行完整工作流程
        
        Args:
            target_stage: 目标阶段 ("1", "2", "both")
            resume_from_checkpoint: 是否从检查点恢复
            
        Returns:
            执行结果字典
        """
        self.logger.info(f"开始执行工作流程，目标阶段: {target_stage}")
        
        # 加载或创建工作流程状态
        if resume_from_checkpoint:
            self.workflow_state = self.load_workflow_state()
        
        if not self.workflow_state:
            self.workflow_state = self.create_new_workflow_state()
            self.logger.info(f"创建新的工作流程: {self.workflow_state.workflow_id}")
        else:
            self.logger.info(f"恢复工作流程: {self.workflow_state.workflow_id}")
        
        execution_results = {
            "workflow_id": self.workflow_state.workflow_id,
            "target_stage": target_stage,
            "stages_executed": [],
            "total_duration": 0.0,
            "success": False
        }
        
        import time as time_module  # 确保time模块可用
        workflow_start_time = time_module.time()
        
        try:
            # 执行第一阶段
            if target_stage in ["1", "both"]:
                if (not self.workflow_state.stage1_result or 
                    not self.workflow_state.stage1_result.is_successful):
                    
                    self.logger.info("执行第一阶段")
                    stage1_result = self.execute_stage1(self.workflow_state)
                    self.workflow_state.stage1_result = stage1_result
                    self.workflow_state.current_stage = WorkflowStage.STAGE2
                    
                    execution_results["stages_executed"].append("stage1")
                    
                    # 保存检查点
                    self.save_workflow_state(self.workflow_state)
                else:
                    self.logger.info("第一阶段已完成，跳过执行")
                    execution_results["stages_executed"].append("stage1_skipped")
            
            # 执行第二阶段
            if target_stage in ["2", "both"]:
                if (not self.workflow_state.stage2_result or 
                    not self.workflow_state.stage2_result.is_successful):
                    
                    self.logger.info("执行第二阶段")
                    stage2_result = self.execute_stage2(self.workflow_state)
                    self.workflow_state.stage2_result = stage2_result
                    self.workflow_state.current_stage = WorkflowStage.COMPLETED
                    
                    execution_results["stages_executed"].append("stage2")
                    
                    # 保存最终状态
                    self.save_workflow_state(self.workflow_state)
                else:
                    self.logger.info("第二阶段已完成，跳过执行")
                    execution_results["stages_executed"].append("stage2_skipped")
            
            # 计算总时长
            import time as time_module  # 确保time模块可用
            execution_results["total_duration"] = time_module.time() - workflow_start_time
            execution_results["success"] = True
            
            # 生成执行摘要
            self._log_execution_summary(execution_results)
            
            return execution_results
            
        except Exception as e:
            # 修复：确保time模块在异常处理中可用
            import time as time_module
            execution_results["total_duration"] = time_module.time() - workflow_start_time
            execution_results["error"] = str(e)
            execution_results["success"] = False
            
            # 保存失败状态
            self.workflow_state.current_stage = WorkflowStage.FAILED
            self.save_workflow_state(self.workflow_state)
            
            self.logger.error(f"工作流程执行失败: {e}")
            raise
    
    def _log_execution_summary(self, results: Dict[str, Any]) -> None:
        """记录执行摘要"""
        self.logger.info("=" * 60)
        self.logger.info("工作流程执行摘要")
        self.logger.info("=" * 60)
        
        self.logger.info(f"工作流程ID: {results['workflow_id']}")
        self.logger.info(f"目标阶段: {results['target_stage']}")
        self.logger.info(f"执行阶段: {', '.join(results['stages_executed'])}")
        self.logger.info(f"总耗时: {results['total_duration']:.2f} 秒")
        self.logger.info(f"执行状态: {'成功' if results['success'] else '失败'}")
        
        # 阶段详细信息
        if self.workflow_state.stage1_result:
            stage1 = self.workflow_state.stage1_result
            self.logger.info(f"第一阶段: {stage1.status.value}, 耗时: {stage1.duration:.2f}秒")
            if stage1.output_file:
                self.logger.info(f"  输出文件: {stage1.output_file}")
        
        if self.workflow_state.stage2_result:
            stage2 = self.workflow_state.stage2_result
            self.logger.info(f"第二阶段: {stage2.status.value}, 耗时: {stage2.duration:.2f}秒")
            if stage2.output_file:
                self.logger.info(f"  输出文件: {stage2.output_file}")
        
        self.logger.info("=" * 60)
    
    def get_workflow_status(self) -> Dict[str, Any]:
        """获取工作流程状态信息"""
        if not self.workflow_state:
            return {"status": "not_initialized"}
        
        status_info = {
            "workflow_id": self.workflow_state.workflow_id,
            "current_stage": self.workflow_state.current_stage.value,
            "created_at": self.workflow_state.created_at,
            "updated_at": self.workflow_state.updated_at
        }
        
        if self.workflow_state.stage1_result:
            status_info["stage1"] = {
                "status": self.workflow_state.stage1_result.status.value,
                "duration": self.workflow_state.stage1_result.duration,
                "output_file": self.workflow_state.stage1_result.output_file
            }
        
        if self.workflow_state.stage2_result:
            status_info["stage2"] = {
                "status": self.workflow_state.stage2_result.status.value,
                "duration": self.workflow_state.stage2_result.duration,
                "output_file": self.workflow_state.stage2_result.output_file
            }
        
        return status_info
    
    def cleanup_workflow_state(self) -> None:
        """清理工作流程状态文件"""
        if os.path.exists(self.state_file):
            try:
                os.remove(self.state_file)
                self.logger.info("工作流程状态文件已清理")
            except Exception as e:
                self.logger.warning(f"清理工作流程状态文件失败: {e}")
    
    def set_error_manager(self, error_manager: ErrorManager) -> None:
        """设置错误管理器"""
        self.error_manager = error_manager