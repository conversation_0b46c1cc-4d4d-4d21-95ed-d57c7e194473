"""
文件定位器实现

从 error_prone_json_reporter 提取 ErrorLocationResolver 的核心逻辑，
根据 JSON 中的 location 信息在当前项目中查找对应文件，
读取指定行号的代码内容和周围上下文。
"""

import os
import logging
import re
from typing import Optional, Dict, Any, List, Tuple
from dataclasses import dataclass
from pathlib import Path

from .models import ErrorItem, ExtractorConfig


@dataclass
class MethodContext:
    """方法上下文信息"""
    method_definition: str
    method_body: str
    calling_context: List[str]
    local_variables: List[str]
    method_calls: List[str]
    imports: List[str]
    class_context: Optional[str] = None


class FileLocator:
    """
    文件定位器 - 根据错误项中的位置信息定位源文件并提取代码上下文
    
    基于 error_prone_json_reporter 的 ErrorLocationResolver 核心逻辑实现
    """
    
    def __init__(self, config: ExtractorConfig):
        self.logger = logging.getLogger(__name__)
        self.config = config
        self.project_root = getattr(config, 'project_path', os.getcwd())
        self.source_paths = self._get_source_paths()
        self.file_cache = {}
        # Error handler simplified for now
        
        self.logger.info(f"文件定位器初始化完成，项目根路径: {self.project_root}")
        self.logger.debug(f"源码搜索路径: {self.source_paths}")
    
    def _get_source_paths(self) -> List[str]:
        """获取源码搜索路径"""
        paths = []
        
        self.logger.debug(f"=== 构建源码搜索路径 ===")
        self.logger.debug(f"项目根路径: {self.project_root}")
        
        # 添加项目根路径
        if os.path.exists(self.project_root):
            paths.append(self.project_root)
            self.logger.debug(f"✓ 添加项目根路径: {self.project_root}")
        else:
            self.logger.debug(f"✗ 项目根路径不存在: {self.project_root}")
        
        # 添加配置中的路径
        if hasattr(self.config, "src_path") and self.config.src_path:
            self.logger.debug(f"配置中的src_path: {self.config.src_path}")
            # 如果是绝对路径，直接使用；否则与项目根路径拼接
            if os.path.isabs(self.config.src_path):
                src_path = self.config.src_path
            else:
                src_path = os.path.join(self.project_root, self.config.src_path)
            
            self.logger.debug(f"解析后的src_path: {src_path}")
            if os.path.exists(src_path):
                paths.append(src_path)
                self.logger.debug(f"✓ 添加配置路径: {src_path}")
            else:
                self.logger.debug(f"✗ 配置路径不存在: {src_path}")
        else:
            self.logger.debug("配置中没有src_path或为空")
        
        # 添加常见的Java源码路径
        common_java_paths = [
            'src/main/java',
            'src/test/java', 
            'src/java',
            'java',
            'src'
        ]
        
        self.logger.debug("检查常见Java源码路径:")
        for common_path in common_java_paths:
            full_path = os.path.join(self.project_root, common_path)
            if os.path.exists(full_path):
                paths.append(full_path)
                self.logger.debug(f"✓ 添加常见路径: {full_path}")
            else:
                self.logger.debug(f"✗ 常见路径不存在: {full_path}")
        
        # 搜索项目中的Java模块
        self.logger.debug("搜索项目中的Java模块:")
        try:
            for item in os.listdir(self.project_root):
                item_path = os.path.join(self.project_root, item)
                if os.path.isdir(item_path):
                    # 检查是否包含src目录
                    src_path = os.path.join(item_path, 'src')
                    if os.path.exists(src_path):
                        # 这是一个Java模块，添加其源码路径
                        paths.append(item_path)
                        self.logger.debug(f"✓ 添加Java模块根路径: {item_path}")
                        
                        # 添加模块的常见Java源码路径
                        for common_path in common_java_paths:
                            module_java_path = os.path.join(item_path, common_path)
                            if os.path.exists(module_java_path):
                                paths.append(module_java_path)
                                self.logger.debug(f"✓ 添加模块Java路径: {module_java_path}")
        except Exception as e:
            self.logger.warning(f"搜索Java模块失败: {e}")
        
        self.logger.debug(f"最终源码搜索路径列表: {paths}")
        return paths
    
    def find_source_file(self, file_path: str, search_paths: List[str] = None) -> Optional[str]:
        """
        查找源文件
        
        Args:
            file_path: 文件路径
            search_paths: 搜索路径列表
            
        Returns:
            找到的文件路径或None
        """
        if search_paths is None:
            search_paths = self.source_paths
        
        # 清理路径
        clean_path = file_path.lstrip('/')
        
        self.logger.debug(f"=== 开始查找源文件 ===")
        self.logger.debug(f"原始文件路径: {file_path}")
        self.logger.debug(f"清理后路径: {clean_path}")
        self.logger.debug(f"搜索路径列表: {search_paths}")
        
        # 在各个源码路径中搜索
        for i, source_path in enumerate(search_paths):
            self.logger.debug(f"--- 搜索路径 {i+1}/{len(search_paths)}: {source_path} ---")
            
            # 检查搜索路径是否存在
            if not os.path.exists(source_path):
                self.logger.debug(f"搜索路径不存在: {source_path}")
                continue
            
            # 直接拼接路径
            candidate_path = os.path.join(source_path, clean_path)
            self.logger.debug(f"尝试直接拼接: {candidate_path}")
            if os.path.exists(candidate_path):
                absolute_path = os.path.abspath(candidate_path)
                self.logger.debug(f"✓ 找到文件: {absolute_path}")
                return absolute_path
            else:
                self.logger.debug(f"✗ 文件不存在: {candidate_path}")
            
            # 尝试去掉src/main/java前缀
            if clean_path.startswith('src/main/java/'):
                short_path = clean_path[14:]  # 去掉'src/main/java/'
                candidate_path = os.path.join(source_path, short_path)
                self.logger.debug(f"尝试去掉前缀: {candidate_path}")
                if os.path.exists(candidate_path):
                    absolute_path = os.path.abspath(candidate_path)
                    self.logger.debug(f"✓ 找到文件（去掉前缀）: {absolute_path}")
                    return absolute_path
                else:
                    self.logger.debug(f"✗ 文件不存在（去掉前缀）: {candidate_path}")
            
            # 尝试添加src/main/java前缀
            if not clean_path.startswith('src/'):
                prefixed_path = os.path.join('src/main/java', clean_path)
                candidate_path = os.path.join(source_path, prefixed_path)
                self.logger.debug(f"尝试添加前缀: {candidate_path}")
                if os.path.exists(candidate_path):
                    absolute_path = os.path.abspath(candidate_path)
                    self.logger.debug(f"✓ 找到文件（添加前缀）: {absolute_path}")
                    return absolute_path
                else:
                    self.logger.debug(f"✗ 文件不存在（添加前缀）: {candidate_path}")
        
        self.logger.debug("=== 直接搜索失败，开始模糊搜索 ===")
        # 使用模糊搜索
        return self._fuzzy_search_file(file_path)
    
    def _fuzzy_search_file(self, relative_path: str) -> Optional[str]:
        """
        模糊搜索文件
        
        Args:
            relative_path: 相对路径
            
        Returns:
            找到的文件路径
        """
        try:
            # 提取文件名
            file_name = os.path.basename(relative_path)
            self.logger.debug(f"=== 开始模糊搜索 ===")
            self.logger.debug(f"原始路径: {relative_path}")
            self.logger.debug(f"提取文件名: {file_name}")
            self.logger.debug(f"搜索路径: {self.source_paths}")
            
            # 在所有源码路径中递归搜索
            for i, source_path in enumerate(self.source_paths):
                self.logger.debug(f"--- 模糊搜索路径 {i+1}/{len(self.source_paths)}: {source_path} ---")
                
                if not os.path.exists(source_path):
                    self.logger.debug(f"搜索路径不存在: {source_path}")
                    continue
                
                found_files = []
                for root, dirs, files in os.walk(source_path):
                    if file_name in files:
                        candidate_path = os.path.join(root, file_name)
                        found_files.append(candidate_path)
                        self.logger.debug(f"找到候选文件: {candidate_path}")
                
                if found_files:
                    # 如果找到多个文件，选择第一个
                    selected_file = found_files[0]
                    absolute_path = os.path.abspath(selected_file)
                    self.logger.debug(f"✓ 模糊搜索找到文件: {absolute_path}")
                    if len(found_files) > 1:
                        self.logger.debug(f"注意：找到多个同名文件，选择了第一个")
                        for f in found_files[1:]:
                            self.logger.debug(f"  其他候选: {f}")
                    return absolute_path
                else:
                    self.logger.debug(f"在路径 {source_path} 中未找到文件 {file_name}")
            
            self.logger.warning(f"模糊搜索未找到文件: {file_name}")
            return None
            
        except Exception as e:
            self.logger.warning(f"模糊搜索文件失败: {str(e)}")
            return None
    
    def read_file_content(self, file_path: str) -> str:
        """
        读取文件内容
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件内容
        """
        # 检查缓存
        if file_path in self.file_cache:
            return self.file_cache[file_path]
        
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # 缓存结果
            self.file_cache[file_path] = content
            self.logger.debug(f"读取文件成功: {file_path} ({len(content)} 字符)")
            return content
            
        except Exception as e:
            raise FileLocatorError(f"读取文件失败 {file_path}: {str(e)}")
    
    def get_line_content(self, file_path: str, line_number: int, context_lines: int = 5) -> Dict[str, Any]:
        """
        获取指定行的内容和上下文
        
        Args:
            file_path: 文件路径
            line_number: 行号（从1开始）
            context_lines: 上下文行数
            
        Returns:
            包含行内容和上下文的字典
        """
        try:
            content = self.read_file_content(file_path)
            lines = content.splitlines()
            
            if line_number <= 0 or line_number > len(lines):
                raise FileLocatorError(f"行号超出范围: {line_number}, 文件总行数: {len(lines)}")
            
            # 计算提取范围
            start_line = max(0, line_number - context_lines - 1)
            end_line = min(len(lines), line_number + context_lines)
            
            # 提取周围行
            surrounding_lines = []
            for i in range(start_line, end_line):
                line_content = lines[i]
                line_info = f"{i + 1:4d}: {line_content}"
                
                # 标记目标行
                if i + 1 == line_number:
                    line_info = f">>> {line_info}"
                
                surrounding_lines.append(line_info)
            
            return {
                "file_path": file_path,
                "line_number": line_number,
                "target_line": lines[line_number - 1],
                "surrounding_lines": surrounding_lines,
                "total_lines": len(lines),
                "context_start": start_line + 1,
                "context_end": end_line
            }
            
        except Exception as e:
            if isinstance(e, FileLocatorError):
                raise
            raise FileLocatorError(f"提取行内容失败 {file_path}:{line_number}: {str(e)}") 
   
    def locate_error_in_file(self, error_item: ErrorItem) -> Dict[str, Any]:
        """
        在文件中定位错误位置
        
        Args:
            error_item: 错误项
            
        Returns:
            错误位置信息字典
        """
        try:
            self.logger.debug(f"定位错误: {error_item.get_method_display_name()}")
            
            if not error_item.location:
                raise FileLocatorError("错误项缺少位置信息")
            
            file_path = error_item.location.file
            absolute_path = self.find_source_file(file_path)
            
            if not absolute_path:
                # 尝试回退策略
                absolute_path = self._fallback_file_location(error_item)
                if not absolute_path:
                    raise SourceFileNotFoundError(f"无法找到源文件: {file_path}")
            
            line_number = error_item.location.line
            context_lines = getattr(self.config, "max_context_lines", 10)
            
            line_content_result = self.get_line_content(absolute_path, line_number, context_lines)
            
            return {
                "error_item": error_item,
                "absolute_file_path": absolute_path,
                "relative_file_path": file_path,
                "line_number": line_number,
                "column_number": error_item.location.column,
                "method_name": error_item.missing_method,
                "class_name": error_item.class_name,
                "package_name": error_item.package,
                "target_line": line_content_result["target_line"],
                "surrounding_lines": line_content_result["surrounding_lines"],
                "total_lines": line_content_result["total_lines"],
                "context_start": line_content_result["context_start"],
                "context_end": line_content_result["context_end"]
            }
            
        except Exception as e:
            if isinstance(e, (FileLocatorError, SourceFileNotFoundError)):
                raise
            raise FileLocatorError(f"定位错误失败: {str(e)}")
    
    def _fallback_file_location(self, error_item: ErrorItem) -> Optional[str]:
        """
        回退文件定位策略
        
        Args:
            error_item: 错误项
            
        Returns:
            找到的文件路径或None
        """
        try:
            self.logger.info("使用回退策略定位文件")
            
            # 尝试从包名和类名构建文件路径
            package = error_item.package
            class_name = error_item.class_name
            
            if package and class_name:
                # 构建可能的文件路径
                package_path = package.replace('.', '/')
                file_name = f"{class_name}.java"
                relative_path = f"{package_path}/{file_name}"
                
                # 查找文件
                absolute_path = self.find_source_file(relative_path)
                
                if absolute_path:
                    self.logger.info(f"回退策略找到文件: {absolute_path}")
                    return absolute_path
            
            return None
            
        except Exception as e:
            self.logger.warning(f"回退文件定位失败: {str(e)}")
            return None    

    def extract_method_context(self, file_path: str, line_number: int) -> Optional[MethodContext]:
        """
        提取方法上下文
        
        Args:
            file_path: 文件路径
            line_number: 行号
            
        Returns:
            方法上下文信息
        """
        try:
            self.logger.debug(f"提取方法上下文: {file_path}:{line_number}")
            
            content = self.read_file_content(file_path)
            lines = content.splitlines()
            
            if line_number <= 0 or line_number > len(lines):
                self.logger.warning(f"行号超出范围: {line_number}")
                return None
            
            # 查找包含该行的方法
            method_start, method_end = self._find_method_boundaries(lines, line_number - 1)
            
            if method_start is None or method_end is None:
                self.logger.warning(f"无法找到包含第{line_number}行的方法")
                return None
            
            # 提取方法定义
            method_definition = self._extract_method_definition(lines, method_start)
            
            # 提取方法体
            method_body = ''.join(lines[method_start:method_end + 1])
            
            # 提取调用上下文（方法调用前后的代码）
            calling_context = self._extract_calling_context(lines, line_number - 1)
            
            # 提取局部变量
            local_variables = self._extract_local_variables(lines[method_start:method_end + 1])
            
            # 提取方法调用
            method_calls = self._extract_method_calls(lines[method_start:method_end + 1])
            
            # 提取导入语句
            imports = self._extract_imports(lines)
            
            # 提取类上下文
            class_context = self._extract_class_context(lines, method_start)
            
            method_context = MethodContext(
                method_definition=method_definition,
                method_body=method_body,
                calling_context=calling_context,
                local_variables=local_variables,
                method_calls=method_calls,
                imports=imports,
                class_context=class_context
            )
            
            self.logger.debug(f"成功提取方法上下文，方法定义: {method_definition[:100]}...")
            return method_context
            
        except Exception as e:
            self.logger.error(f"提取方法上下文失败: {str(e)}")
            return None
    
    def _find_method_boundaries(self, lines: List[str], target_line: int) -> Tuple[Optional[int], Optional[int]]:
        """
        查找包含目标行的方法边界
        
        Args:
            lines: 文件行列表
            target_line: 目标行索引（从0开始）
            
        Returns:
            (方法开始行索引, 方法结束行索引)
        """
        try:
            # 向上查找方法开始
            method_start = None
            for i in range(target_line, -1, -1):
                line = lines[i].strip()
                
                # 查找方法定义模式
                if self._is_method_definition(line):
                    method_start = i
                    break
                
                # 如果遇到类定义或其他方法，停止搜索
                if self._is_class_definition(line) or (self._is_method_definition(line) and i < target_line):
                    break
            
            if method_start is None:
                return None, None
            
            # 向下查找方法结束
            method_end = None
            brace_count = 0
            in_method = False
            
            for i in range(method_start, len(lines)):
                line = lines[i].strip()
                
                # 计算大括号
                open_braces = line.count('{')
                close_braces = line.count('}')
                
                if open_braces > 0:
                    in_method = True
                
                brace_count += open_braces - close_braces
                
                # 如果大括号平衡且已经进入方法，则找到方法结束
                if in_method and brace_count == 0:
                    method_end = i
                    break
            
            return method_start, method_end
            
        except Exception as e:
            self.logger.warning(f"查找方法边界失败: {str(e)}")
            return None, None   
 
    def _is_method_definition(self, line: str) -> bool:
        """判断是否为方法定义行"""
        # 简单的方法定义模式匹配
        method_patterns = [
            r'\b(public|private|protected|static|final|abstract|synchronized)\s+.*\s+\w+\s*\(',
            r'\b\w+\s+\w+\s*\(',  # 简单方法定义
            r'@\w+.*\n.*\s+\w+\s*\('  # 带注解的方法
        ]
        
        for pattern in method_patterns:
            if re.search(pattern, line):
                return True
        
        return False
    
    def _is_class_definition(self, line: str) -> bool:
        """判断是否为类定义行"""
        class_patterns = [
            r'\b(public|private|protected)?\s*(class|interface|enum)\s+\w+',
            r'@\w+.*\n.*(class|interface|enum)\s+\w+'
        ]
        
        for pattern in class_patterns:
            if re.search(pattern, line):
                return True
        
        return False
    
    def _extract_method_definition(self, lines: List[str], method_start: int) -> str:
        """提取方法定义"""
        try:
            # 查找方法定义的完整行（可能跨多行）
            definition_lines = []
            
            for i in range(method_start, len(lines)):
                line = lines[i].strip()
                definition_lines.append(line)
                
                # 如果找到开括号，方法定义结束
                if '{' in line:
                    break
                
                # 防止无限循环
                if i - method_start > 10:
                    break
            
            return ' '.join(definition_lines)
            
        except Exception as e:
            self.logger.warning(f"提取方法定义失败: {str(e)}")
            return ""
    
    def _extract_calling_context(self, lines: List[str], target_line: int, context_size: int = 3) -> List[str]:
        """提取调用上下文"""
        try:
            start = max(0, target_line - context_size)
            end = min(len(lines), target_line + context_size + 1)
            
            context = []
            for i in range(start, end):
                line_content = lines[i].rstrip('\n\r')
                if i == target_line:
                    context.append(f">>> {i + 1}: {line_content}")
                else:
                    context.append(f"    {i + 1}: {line_content}")
            
            return context
            
        except Exception as e:
            self.logger.warning(f"提取调用上下文失败: {str(e)}")
            return []
    
    def _extract_local_variables(self, method_lines: List[str]) -> List[str]:
        """提取局部变量"""
        try:
            variables = []
            
            for line in method_lines:
                line = line.strip()
                
                # 简单的变量声明模式
                var_patterns = [
                    r'\b(int|long|double|float|boolean|String|List|Map|Set)\s+(\w+)',
                    r'\b(\w+)\s+(\w+)\s*=',
                    r'\bfinal\s+(\w+)\s+(\w+)'
                ]
                
                for pattern in var_patterns:
                    matches = re.findall(pattern, line)
                    for match in matches:
                        if isinstance(match, tuple) and len(match) >= 2:
                            var_type, var_name = match[0], match[1]
                            variables.append(f"{var_type} {var_name}")
            
            return list(set(variables))  # 去重
            
        except Exception as e:
            self.logger.warning(f"提取局部变量失败: {str(e)}")
            return []
    
    def _extract_method_calls(self, method_lines: List[str]) -> List[str]:
        """提取方法调用"""
        try:
            method_calls = []
            
            for line in method_lines:
                line = line.strip()
                
                # 方法调用模式
                call_patterns = [
                    r'(\w+)\.(\w+)\s*\(',
                    r'(\w+)\s*\(',
                    r'this\.(\w+)\s*\(',
                    r'super\.(\w+)\s*\('
                ]
                
                for pattern in call_patterns:
                    matches = re.findall(pattern, line)
                    for match in matches:
                        if isinstance(match, tuple):
                            if len(match) == 2:
                                method_calls.append(f"{match[0]}.{match[1]}()")
                            else:
                                method_calls.append(f"{match[0]}()")
                        else:
                            method_calls.append(f"{match}()")
            
            return list(set(method_calls))  # 去重
            
        except Exception as e:
            self.logger.warning(f"提取方法调用失败: {str(e)}")
            return []
    
    def _extract_imports(self, lines: List[str]) -> List[str]:
        """提取导入语句"""
        try:
            imports = []
            
            for line in lines:
                line = line.strip()
                if line.startswith('import ') and line.endswith(';'):
                    imports.append(line)
            
            return imports
            
        except Exception as e:
            self.logger.warning(f"提取导入语句失败: {str(e)}")
            return []
    
    def _extract_class_context(self, lines: List[str], method_start: int) -> Optional[str]:
        """提取类上下文"""
        try:
            # 向上查找类定义
            for i in range(method_start, -1, -1):
                line = lines[i].strip()
                if self._is_class_definition(line):
                    # 提取类定义及其前几行（可能包含注解）
                    context_start = max(0, i - 3)
                    context_lines = lines[context_start:i + 1]
                    return ''.join(context_lines)
            
            return None
            
        except Exception as e:
            self.logger.warning(f"提取类上下文失败: {str(e)}")
            return None
    
    def find_method_in_file(self, file_path: str, method_name: str) -> Optional[int]:
        """在文件中查找方法定义行号"""
        try:
            content = self.read_file_content(file_path)
            lines = content.splitlines()
            
            for i, line in enumerate(lines):
                if method_name in line and self._is_method_definition(line):
                    return i + 1
            
            return None
            
        except Exception as e:
            self.logger.warning(f"在文件中查找方法失败: {str(e)}")
            return None
    
    def clear_cache(self):
        """清理缓存"""
        self.file_cache.clear()
        self.logger.debug("文件定位器缓存已清理")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        return {
            "cache_size": len(self.file_cache),
            "source_paths_count": len(self.source_paths),
            "project_root": self.project_root,
            "source_paths": self.source_paths
        }
    
    def get_error_summary(self) -> Dict[str, int]:
        """获取错误统计摘要"""
        return self.error_handler.get_error_summary()