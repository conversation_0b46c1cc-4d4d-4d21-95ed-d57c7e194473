"""
遗留代码搜索器

在legacy源码中搜索原始方法实现，提供完整的方法代码和上下文。
"""

import os
import logging
import re
from typing import Optional, Dict, Any, List
from dataclasses import dataclass
from pathlib import Path

from error_prone_json_reporter.common.models import Configuration, MethodInfo


@dataclass
class OriginalMethod:
    """原始方法信息"""
    file_path: str
    method_code: str
    method_signature: str
    javadoc: Optional[str]
    annotations: List[str]
    dependencies: List[str]
    class_name: str
    package_name: str
    line_number: int


@dataclass
class MethodImplementation:
    """方法实现详情"""
    full_method_code: str
    method_body: str
    parameters: List[str]
    return_type: str
    modifiers: List[str]
    throws_exceptions: List[str]
    local_variables: List[str]
    method_calls: List[str]


class LegacyCodeSearcher:
    """
    遗留代码搜索器
    
    职责：在legacy源码中搜索原始方法实现
    输入：方法信息和legacy路径
    输出：原始方法代码
    """
    
    def __init__(self, config: Configuration):
        """
        初始化遗留代码搜索器
        
        Args:
            config: 配置对象
        """
        self.logger = logging.getLogger(__name__)
        self.config = config
        
        # Legacy源码路径
        self.legacy_paths = self._get_legacy_paths()
        
        # 搜索缓存
        self.search_cache = {}
        self.file_content_cache = {}
        
        # 索引缓存
        self.method_index = {}
        self.class_index = {}
        
        self.logger.info(f"遗留代码搜索器初始化完成，legacy路径: {self.legacy_paths}")
        
        # 构建索引
        self._build_indexes()
    
    def _get_legacy_paths(self) -> List[str]:
        """获取legacy源码路径"""
        paths = []
        
        # 从配置中获取legacy_src_path
        legacy_src_path = getattr(self.config, 'legacy_src_path', '')
        if legacy_src_path and os.path.exists(legacy_src_path):
            paths.append(legacy_src_path)
        
        # 添加其他可能的legacy路径
        additional_paths = getattr(self.config, 'additional_legacy_paths', [])
        for path in additional_paths:
            if os.path.exists(path):
                paths.append(path)
        
        return paths
    
    def _build_indexes(self):
        """构建方法和类索引以提高搜索效率"""
        try:
            self.logger.info("开始构建legacy代码索引")
            
            for legacy_path in self.legacy_paths:
                self._index_directory(legacy_path)
            
            self.logger.info(f"索引构建完成，方法数: {len(self.method_index)}, 类数: {len(self.class_index)}")
            
        except Exception as e:
            self.logger.error(f"构建索引失败: {str(e)}")
    
    def _index_directory(self, directory: str):
        """索引目录中的Java文件"""
        try:
            for root, dirs, files in os.walk(directory):
                for file in files:
                    if file.endswith('.java'):
                        file_path = os.path.join(root, file)
                        self._index_java_file(file_path)
                        
        except Exception as e:
            self.logger.warning(f"索引目录失败 {directory}: {str(e)}")
    
    def _index_java_file(self, file_path: str):
        """索引单个Java文件"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # 提取包名
            package_match = re.search(r'package\s+([\w.]+);', content)
            package_name = package_match.group(1) if package_match else ''
            
            # 提取类名
            class_matches = re.finditer(r'(public\s+|private\s+|protected\s+)?(class|interface|enum)\s+(\w+)', content)
            for class_match in class_matches:
                class_name = class_match.group(3)
                full_class_name = f"{package_name}.{class_name}" if package_name else class_name
                
                # 添加到类索引
                if full_class_name not in self.class_index:
                    self.class_index[full_class_name] = []
                self.class_index[full_class_name].append(file_path)
            
            # 提取方法
            method_matches = re.finditer(
                r'(public|private|protected|static|final|abstract|synchronized|\s)+\s+(\w+(?:<[^>]+>)?)\s+(\w+)\s*\([^)]*\)\s*(?:throws\s+[^{]+)?\s*\{',
                content,
                re.MULTILINE
            )
            
            for method_match in method_matches:
                method_name = method_match.group(3)
                method_key = f"{package_name}.{method_name}" if package_name else method_name
                
                # 添加到方法索引
                if method_key not in self.method_index:
                    self.method_index[method_key] = []
                
                self.method_index[method_key].append({
                    'file_path': file_path,
                    'method_name': method_name,
                    'package_name': package_name,
                    'start_pos': method_match.start(),
                    'match_text': method_match.group(0)
                })
                
        except Exception as e:
            self.logger.warning(f"索引Java文件失败 {file_path}: {str(e)}")
    
    def search_original_method(self, method_info: MethodInfo, 
                              legacy_paths: Optional[List[str]] = None) -> Optional[OriginalMethod]:
        """
        搜索原始方法实现
        
        Args:
            method_info: 方法信息
            legacy_paths: 可选的legacy路径列表
            
        Returns:
            原始方法信息，如果找不到则返回None
        """
        try:
            self.logger.debug(f"搜索原始方法: {method_info.package}.{method_info.class_name}.{method_info.method_name}")
            
            # 构建缓存键
            cache_key = f"{method_info.package}.{method_info.class_name}.{method_info.method_name}"
            if cache_key in self.search_cache:
                return self.search_cache[cache_key]
            
            # 使用提供的路径或默认路径
            search_paths = legacy_paths or self.legacy_paths
            
            # 策略1: 基于方法调用内容的全局搜索（新增）
            original_method = self._search_by_method_call_content(method_info, search_paths)
            if original_method:
                self.search_cache[cache_key] = original_method
                return original_method
            
            # 策略2: 使用索引快速搜索
            original_method = self._search_by_index(method_info)
            if original_method:
                self.search_cache[cache_key] = original_method
                return original_method
            
            # 策略3: 基于包名和类名的精确搜索
            original_method = self._search_by_package_class(method_info, search_paths)
            if original_method:
                self.search_cache[cache_key] = original_method
                return original_method
            
            # 策略4: 模糊搜索
            original_method = self._fuzzy_search_method(method_info, search_paths)
            if original_method:
                self.search_cache[cache_key] = original_method
                return original_method
            
            self.logger.debug(f"未找到原始方法: {cache_key}")
            return None
            
        except Exception as e:
            self.logger.error(f"搜索原始方法失败: {str(e)}")
            return None
    
    def _search_by_method_call_content(self, method_info: MethodInfo, search_paths: List[str]) -> Optional[OriginalMethod]:
        """基于方法调用内容的全局搜索"""
        try:
            self.logger.debug(f"基于方法调用内容搜索: {method_info.method_name}")
            
            # 构建搜索模式
            method_call_pattern = f"{method_info.method_name}\\s*\\("
            
            # 在所有Java文件中搜索方法调用
            for legacy_path in search_paths:
                for root, dirs, files in os.walk(legacy_path):
                    for file in files:
                        if file.endswith('.java'):
                            file_path = os.path.join(root, file)
                            
                            try:
                                content = self._get_file_content(file_path)
                                if not content:
                                    continue
                                
                                # 搜索方法调用
                                import re
                                if re.search(method_call_pattern, content):
                                    self.logger.debug(f"在文件中找到方法调用: {file_path}")
                                    
                                    # 尝试从调用的上下文中推断方法定义的位置
                                    original_method = self._analyze_method_call_context(
                                        file_path, content, method_info
                                    )
                                    
                                    if original_method:
                                        return original_method
                                        
                            except Exception as e:
                                self.logger.warning(f"分析文件失败 {file_path}: {str(e)}")
                                continue
            
            return None
            
        except Exception as e:
            self.logger.warning(f"基于方法调用内容搜索失败: {str(e)}")
            return None
    
    def _analyze_method_call_context(self, file_path: str, content: str, method_info: MethodInfo) -> Optional[OriginalMethod]:
        """分析方法调用上下文，尝试找到方法定义"""
        try:
            import re
            
            # 查找方法调用行
            method_call_pattern = rf'(\w+)\.{re.escape(method_info.method_name)}\s*\('
            matches = list(re.finditer(method_call_pattern, content))
            
            if not matches:
                return None
            
            for match in matches:
                # 获取调用对象名
                caller_object = match.group(1)
                self.logger.debug(f"找到方法调用: {caller_object}.{method_info.method_name}")
                
                # 查找调用对象的类型声明
                object_type = self._find_object_type_in_file(content, caller_object)
                if object_type:
                    self.logger.debug(f"推断对象类型: {caller_object} -> {object_type}")
                    
                    # 基于推断的类型搜索方法定义
                    inferred_method = self._search_method_in_inferred_class(
                        object_type, method_info.method_name, file_path
                    )
                    
                    if inferred_method:
                        return inferred_method
            
            return None
            
        except Exception as e:
            self.logger.warning(f"分析方法调用上下文失败: {str(e)}")
            return None
    
    def _find_object_type_in_file(self, content: str, object_name: str) -> Optional[str]:
        """在文件中查找对象的类型声明"""
        try:
            import re
            
            # 查找字段声明模式
            field_patterns = [
                rf'@Resource\s+private\s+(\w+)\s+{re.escape(object_name)}\s*;',
                rf'@Autowired\s+private\s+(\w+)\s+{re.escape(object_name)}\s*;',
                rf'private\s+(\w+)\s+{re.escape(object_name)}\s*;',
                rf'(\w+)\s+{re.escape(object_name)}\s*=',
            ]
            
            for pattern in field_patterns:
                match = re.search(pattern, content, re.MULTILINE)
                if match:
                    return match.group(1)
            
            # 查找导入语句中的类型
            import_pattern = rf'import\s+[\w.]+\.(\w*{re.escape(object_name.replace("Service", "ServiceImpl"))})\s*;'
            import_match = re.search(import_pattern, content)
            if import_match:
                return import_match.group(1)
            
            return None
            
        except Exception as e:
            self.logger.warning(f"查找对象类型失败: {str(e)}")
            return None
    
    def _search_method_in_inferred_class(self, class_name: str, method_name: str, context_file: str) -> Optional[OriginalMethod]:
        """在推断的类中搜索方法"""
        try:
            self.logger.debug(f"在推断类中搜索方法: {class_name}.{method_name}")
            
            # 策略1: 在legacy路径中搜索该类
            original_method = self._search_in_legacy_paths(class_name, method_name)
            if original_method:
                return original_method
            
            # 策略2: 在当前项目中搜索该类（可能在其他模块中）
            original_method = self._search_in_current_project(class_name, method_name)
            if original_method:
                return original_method
            
            # 策略3: 创建基于调用上下文的虚拟方法信息
            original_method = self._create_virtual_method_from_context(class_name, method_name, context_file)
            if original_method:
                return original_method
            
            return None
            
        except Exception as e:
            self.logger.warning(f"在推断类中搜索方法失败: {str(e)}")
            return None
    
    def _search_in_legacy_paths(self, class_name: str, method_name: str) -> Optional[OriginalMethod]:
        """在legacy路径中搜索类和方法"""
        try:
            for legacy_path in self.legacy_paths:
                for root, dirs, files in os.walk(legacy_path):
                    for file in files:
                        if file.endswith('.java') and class_name in file:
                            file_path = os.path.join(root, file)
                            
                            # 尝试提取方法
                            original_method = self._extract_method_from_file(
                                file_path, method_name, class_name
                            )
                            
                            if original_method:
                                self.logger.info(f"在legacy路径中找到方法: {file_path}")
                                return original_method
            
            return None
            
        except Exception as e:
            self.logger.warning(f"在legacy路径中搜索失败: {str(e)}")
            return None
    
    def _search_in_current_project(self, class_name: str, method_name: str) -> Optional[OriginalMethod]:
        """在当前项目中搜索类和方法"""
        try:
            self.logger.debug(f"在当前项目中搜索: {class_name}.{method_name}")
            
            # 获取项目根路径
            project_root = getattr(self.config, 'project_path', os.getcwd())
            
            # 搜索当前项目中的Java文件
            for root, dirs, files in os.walk(project_root):
                # 跳过一些不必要的目录
                dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['target', 'build', 'node_modules']]
                
                for file in files:
                    if file.endswith('.java') and class_name in file:
                        file_path = os.path.join(root, file)
                        
                        # 尝试提取方法
                        original_method = self._extract_method_from_file(
                            file_path, method_name, class_name
                        )
                        
                        if original_method:
                            self.logger.info(f"在当前项目中找到方法: {file_path}")
                            return original_method
            
            return None
            
        except Exception as e:
            self.logger.warning(f"在当前项目中搜索失败: {str(e)}")
            return None
    
    def _create_virtual_method_from_context(self, class_name: str, method_name: str, context_file: str) -> Optional[OriginalMethod]:
        """基于调用上下文创建虚拟方法信息"""
        try:
            self.logger.debug(f"创建虚拟方法信息: {class_name}.{method_name}")
            
            # 读取调用上下文文件
            content = self._get_file_content(context_file)
            if not content:
                return None
            
            # 查找方法调用行
            import re
            method_call_pattern = rf'(\w+)\.{re.escape(method_name)}\s*\(([^)]*)\)'
            matches = list(re.finditer(method_call_pattern, content))
            
            if not matches:
                return None
            
            # 分析第一个匹配的调用
            match = matches[0]
            caller_object = match.group(1)
            call_params = match.group(2).strip()
            
            # 获取调用行的上下文
            call_line_context = self._get_call_line_context(content, match.start())
            
            # 尝试推断方法签名
            inferred_signature = self._infer_method_signature_from_call(
                method_name, call_params, call_line_context
            )
            
            # 创建虚拟的OriginalMethod对象
            virtual_method = OriginalMethod(
                file_path=f"INFERRED_FROM:{context_file}",
                method_code=f"// 推断的方法定义（基于调用上下文）\n{inferred_signature} {{\n    // 方法实现在依赖的jar包中\n    // 调用上下文:\n{self._format_context_as_comment(call_line_context)}\n}}",
                method_signature=inferred_signature,
                javadoc=f"推断的方法定义，基于在 {context_file} 中的调用上下文",
                annotations=[],
                dependencies=[],
                class_name=class_name,
                package_name="INFERRED",
                line_number=0
            )
            
            self.logger.info(f"创建了虚拟方法信息: {class_name}.{method_name}")
            return virtual_method
            
        except Exception as e:
            self.logger.warning(f"创建虚拟方法信息失败: {str(e)}")
            return None
    
    def _infer_method_signature_from_call(self, method_name: str, call_params: str, context: str) -> str:
        """从方法调用推断方法签名"""
        try:
            # 分析参数
            params = []
            if call_params.strip():
                param_parts = [p.strip() for p in call_params.split(',')]
                for i, param in enumerate(param_parts):
                    # 简单的参数类型推断
                    if param.isdigit():
                        params.append(f"int param{i}")
                    elif param.startswith('"') and param.endswith('"'):
                        params.append(f"String param{i}")
                    elif param == "true" or param == "false":
                        params.append(f"boolean param{i}")
                    else:
                        # 尝试从变量名推断类型
                        param_type = self._infer_param_type_from_name(param)
                        params.append(f"{param_type} {param}")
            
            params_str = ", ".join(params)
            
            # 推断返回类型（基于上下文）
            return_type = self._infer_return_type_from_context(context)
            
            return f"public {return_type} {method_name}({params_str})"
            
        except Exception as e:
            self.logger.warning(f"推断方法签名失败: {str(e)}")
            return f"public Object {method_name}()"
    
    def _infer_param_type_from_name(self, param_name: str) -> str:
        """从参数名推断参数类型"""
        param_lower = param_name.lower()
        
        if 'id' in param_lower:
            return 'Long'
        elif 'type' in param_lower:
            return 'Integer'
        elif 'name' in param_lower or 'label' in param_lower:
            return 'String'
        elif 'time' in param_lower or 'date' in param_lower:
            return 'Date'
        elif 'list' in param_lower:
            return 'List'
        elif 'map' in param_lower:
            return 'Map'
        else:
            return 'Object'
    
    def _infer_return_type_from_context(self, context: str) -> str:
        """从上下文推断返回类型"""
        context_lower = context.lower()
        
        if 'list<' in context_lower:
            return 'List'
        elif 'map<' in context_lower:
            return 'Map'
        elif 'string' in context_lower:
            return 'String'
        elif 'integer' in context_lower or 'int' in context_lower:
            return 'Integer'
        elif 'boolean' in context_lower:
            return 'Boolean'
        else:
            return 'Object'
    
    def _format_context_as_comment(self, context: str) -> str:
        """将上下文格式化为注释"""
        lines = context.split('\n')
        comment_lines = []
        for line in lines:
            if line.strip():
                comment_lines.append(f"    // {line.strip()}")
        return '\n'.join(comment_lines)

    def _search_by_index(self, method_info: MethodInfo) -> Optional[OriginalMethod]:
        """使用索引搜索方法"""
        try:
            # 构建搜索键
            full_class_name = f"{method_info.package}.{method_info.class_name}"
            method_key = f"{method_info.package}.{method_info.method_name}"
            
            # 首先查找类
            if full_class_name in self.class_index:
                for file_path in self.class_index[full_class_name]:
                    original_method = self._extract_method_from_file(
                        file_path, method_info.method_name, method_info.class_name
                    )
                    if original_method:
                        return original_method
            
            # 然后查找方法
            if method_key in self.method_index:
                for method_entry in self.method_index[method_key]:
                    original_method = self._extract_method_from_file(
                        method_entry['file_path'], method_info.method_name, method_info.class_name
                    )
                    if original_method:
                        return original_method
            
            return None
            
        except Exception as e:
            self.logger.warning(f"索引搜索失败: {str(e)}")
            return None
    
    def _search_by_package_class(self, method_info: MethodInfo, search_paths: List[str]) -> Optional[OriginalMethod]:
        """基于包名和类名的精确搜索"""
        try:
            # 构建可能的文件路径
            package_path = method_info.package.replace('.', '/')
            class_file = f"{method_info.class_name}.java"
            relative_path = f"{package_path}/{class_file}"
            
            # 在各个legacy路径中搜索
            for legacy_path in search_paths:
                candidate_paths = [
                    os.path.join(legacy_path, relative_path),
                    os.path.join(legacy_path, 'src/main/java', relative_path),
                    os.path.join(legacy_path, 'src', relative_path),
                    os.path.join(legacy_path, 'java', relative_path)
                ]
                
                for candidate_path in candidate_paths:
                    if os.path.exists(candidate_path):
                        original_method = self._extract_method_from_file(
                            candidate_path, method_info.method_name, method_info.class_name
                        )
                        if original_method:
                            return original_method
            
            return None
            
        except Exception as e:
            self.logger.warning(f"精确搜索失败: {str(e)}")
            return None
    
    def _fuzzy_search_method(self, method_info: MethodInfo, search_paths: List[str]) -> Optional[OriginalMethod]:
        """模糊搜索方法"""
        try:
            self.logger.debug(f"开始模糊搜索方法: {method_info.method_name}")
            
            # 在所有Java文件中搜索
            for legacy_path in search_paths:
                for root, dirs, files in os.walk(legacy_path):
                    for file in files:
                        if file.endswith('.java'):
                            file_path = os.path.join(root, file)
                            
                            # 检查文件名是否匹配类名
                            if file == f"{method_info.class_name}.java":
                                original_method = self._extract_method_from_file(
                                    file_path, method_info.method_name, method_info.class_name
                                )
                                if original_method:
                                    return original_method
            
            return None
            
        except Exception as e:
            self.logger.warning(f"模糊搜索失败: {str(e)}")
            return None
    
    def extract_method_implementation(self, file_path: str, method_name: str) -> Optional[MethodImplementation]:
        """
        提取方法实现详情
        
        Args:
            file_path: 文件路径
            method_name: 方法名
            
        Returns:
            方法实现详情
        """
        try:
            self.logger.debug(f"提取方法实现: {file_path}:{method_name}")
            
            # 读取文件内容
            content = self._get_file_content(file_path)
            if not content:
                return None
            
            # 查找方法定义
            method_pattern = rf'((?:public|private|protected|static|final|abstract|synchronized|\s)+)\s+(\w+(?:<[^>]+>)?)\s+({re.escape(method_name)})\s*\(([^)]*)\)\s*(?:throws\s+([^{{]+))?\s*\{{'
            
            method_match = re.search(method_pattern, content, re.MULTILINE | re.DOTALL)
            if not method_match:
                return None
            
            # 提取方法各部分
            modifiers_str = method_match.group(1).strip()
            return_type = method_match.group(2)
            parameters_str = method_match.group(4)
            throws_str = method_match.group(5) or ''
            
            # 解析修饰符
            modifiers = [mod.strip() for mod in modifiers_str.split() if mod.strip()]
            
            # 解析参数
            parameters = self._parse_parameters(parameters_str)
            
            # 解析异常
            throws_exceptions = [ex.strip() for ex in throws_str.split(',') if ex.strip()]
            
            # 提取完整方法代码
            method_start = method_match.start()
            method_body_start = method_match.end()
            
            # 查找方法结束位置
            method_end = self._find_method_end(content, method_body_start)
            full_method_code = content[method_start:method_end]
            
            # 提取方法体
            method_body = content[method_body_start:method_end - 1]  # 去掉最后的}
            
            # 提取局部变量和方法调用
            local_variables = self._extract_local_variables_from_code(method_body)
            method_calls = self._extract_method_calls_from_code(method_body)
            
            return MethodImplementation(
                full_method_code=full_method_code,
                method_body=method_body,
                parameters=parameters,
                return_type=return_type,
                modifiers=modifiers,
                throws_exceptions=throws_exceptions,
                local_variables=local_variables,
                method_calls=method_calls
            )
            
        except Exception as e:
            self.logger.error(f"提取方法实现失败: {str(e)}")
            return None
    
    def _extract_method_from_file(self, file_path: str, method_name: str, class_name: str) -> Optional[OriginalMethod]:
        """从文件中提取方法"""
        try:
            import re
            
            content = self._get_file_content(file_path)
            if not content:
                return None
            
            # 验证类名匹配 - 放宽匹配条件
            class_found = (
                re.search(rf'class\s+{re.escape(class_name)}\b', content) or
                re.search(rf'interface\s+{re.escape(class_name)}\b', content) or
                class_name in file_path  # 如果文件名包含类名也算匹配
            )
            
            if not class_found:
                self.logger.debug(f"类名不匹配: {class_name} in {file_path}")
                return None
            
            # 查找方法 - 使用更灵活的模式
            method_patterns = [
                # 标准方法定义
                rf'((?:/\*\*.*?\*/\s*)?(?:@\w+.*?\n\s*)*)((?:public|private|protected|static|final|abstract|synchronized|\s)+)\s+(\w+(?:<[^>]+>)?)\s+({re.escape(method_name)})\s*\(([^)]*)\)\s*(?:throws\s+([^{{]+))?\s*\{{',
                # 接口方法定义
                rf'((?:/\*\*.*?\*/\s*)?(?:@\w+.*?\n\s*)*)\s*(\w+(?:<[^>]+>)?)\s+({re.escape(method_name)})\s*\(([^)]*)\)\s*(?:throws\s+([^;]+))?\s*;',
                # 简化的方法定义
                rf'(\w+(?:<[^>]+>)?)\s+({re.escape(method_name)})\s*\(([^)]*)\)\s*\{{'
            ]
            
            method_match = None
            for method_pattern in method_patterns:
                method_match = re.search(method_pattern, content, re.MULTILINE | re.DOTALL)
                if method_match:
                    break
            
            if not method_match:
                self.logger.debug(f"未找到方法定义: {method_name} in {file_path}")
                return None
            
            method_match = re.search(method_pattern, content, re.MULTILINE | re.DOTALL)
            if not method_match:
                return None
            
            # 提取各部分
            javadoc_and_annotations = method_match.group(1) or ''
            modifiers_str = method_match.group(2).strip()
            return_type = method_match.group(3)
            parameters_str = method_match.group(5)
            throws_str = method_match.group(6) or ''
            
            # 提取Javadoc
            javadoc_match = re.search(r'/\*\*(.*?)\*/', javadoc_and_annotations, re.DOTALL)
            javadoc = javadoc_match.group(1).strip() if javadoc_match else None
            
            # 提取注解
            annotation_matches = re.findall(r'@(\w+)(?:\([^)]*\))?', javadoc_and_annotations)
            annotations = [f"@{ann}" for ann in annotation_matches]
            
            # 提取完整方法代码
            method_start = method_match.start()
            method_body_start = method_match.end()
            method_end = self._find_method_end(content, method_body_start)
            method_code = content[method_start:method_end]
            
            # 构建方法签名
            method_signature = f"{modifiers_str} {return_type} {method_name}({parameters_str})"
            if throws_str:
                method_signature += f" throws {throws_str}"
            
            # 提取包名
            package_match = re.search(r'package\s+([\w.]+);', content)
            package_name = package_match.group(1) if package_match else ''
            
            # 提取依赖（导入语句）
            import_matches = re.findall(r'import\s+([\w.]+);', content)
            dependencies = import_matches
            
            # 计算行号
            line_number = content[:method_start].count('\n') + 1
            
            return OriginalMethod(
                file_path=file_path,
                method_code=method_code,
                method_signature=method_signature,
                javadoc=javadoc,
                annotations=annotations,
                dependencies=dependencies,
                class_name=class_name,
                package_name=package_name,
                line_number=line_number
            )
            
        except Exception as e:
            self.logger.warning(f"从文件提取方法失败 {file_path}:{method_name}: {str(e)}")
            return None
    
    def _get_file_content(self, file_path: str) -> Optional[str]:
        """获取文件内容（带缓存）"""
        try:
            if file_path in self.file_content_cache:
                return self.file_content_cache[file_path]
            
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # 缓存内容（限制缓存大小）
            if len(self.file_content_cache) < 100:
                self.file_content_cache[file_path] = content
            
            return content
            
        except Exception as e:
            self.logger.warning(f"读取文件失败 {file_path}: {str(e)}")
            return None
    
    def _find_method_end(self, content: str, start_pos: int) -> int:
        """查找方法结束位置"""
        try:
            brace_count = 1  # 已经有一个开括号
            pos = start_pos
            
            while pos < len(content) and brace_count > 0:
                char = content[pos]
                if char == '{':
                    brace_count += 1
                elif char == '}':
                    brace_count -= 1
                pos += 1
            
            return pos
            
        except Exception as e:
            self.logger.warning(f"查找方法结束位置失败: {str(e)}")
            return start_pos + 100  # 返回一个合理的默认值
    
    def _parse_parameters(self, parameters_str: str) -> List[str]:
        """解析方法参数"""
        try:
            if not parameters_str.strip():
                return []
            
            parameters = []
            for param in parameters_str.split(','):
                param = param.strip()
                if param:
                    # 移除final等修饰符
                    param = re.sub(r'\bfinal\s+', '', param)
                    parameters.append(param)
            
            return parameters
            
        except Exception as e:
            self.logger.warning(f"解析参数失败: {str(e)}")
            return []
    
    def _extract_local_variables_from_code(self, code: str) -> List[str]:
        """从代码中提取局部变量"""
        try:
            variables = []
            
            # 变量声明模式
            var_patterns = [
                r'\b(int|long|double|float|boolean|String|List|Map|Set|Object)\s+(\w+)',
                r'\b(\w+(?:<[^>]+>)?)\s+(\w+)\s*=',
                r'\bfinal\s+(\w+(?:<[^>]+>)?)\s+(\w+)'
            ]
            
            for pattern in var_patterns:
                matches = re.findall(pattern, code)
                for match in matches:
                    if isinstance(match, tuple) and len(match) >= 2:
                        var_type = match[0] if match[0] else match[1]
                        var_name = match[1] if match[0] else match[0]
                        variables.append(f"{var_type} {var_name}")
            
            return list(set(variables))
            
        except Exception as e:
            self.logger.warning(f"提取局部变量失败: {str(e)}")
            return []
    
    def _extract_method_calls_from_code(self, code: str) -> List[str]:
        """从代码中提取方法调用"""
        try:
            method_calls = []
            
            # 方法调用模式
            call_patterns = [
                r'(\w+)\.(\w+)\s*\(',
                r'(\w+)\s*\(',
                r'this\.(\w+)\s*\(',
                r'super\.(\w+)\s*\('
            ]
            
            for pattern in call_patterns:
                matches = re.findall(pattern, code)
                for match in matches:
                    if isinstance(match, tuple):
                        if len(match) == 2 and match[0] and match[1]:
                            method_calls.append(f"{match[0]}.{match[1]}()")
                        elif len(match) == 1 and match[0]:
                            method_calls.append(f"{match[0]}()")
                    elif match:
                        method_calls.append(f"{match}()")
            
            return list(set(method_calls))
            
        except Exception as e:
            self.logger.warning(f"提取方法调用失败: {str(e)}")
            return []
    
    def search_method_by_error(self, error_json: Dict[str, Any]) -> Optional[OriginalMethod]:
        """
        根据错误JSON搜索方法
        
        Args:
            error_json: 错误JSON信息
            
        Returns:
            原始方法信息
        """
        try:
            # 构建MethodInfo对象
            method_info = MethodInfo(
                package=error_json.get('package', ''),
                class_name=error_json.get('class', ''),
                method_name=error_json.get('missing_method', ''),
                parameters=list(error_json.get('in_param', {}).keys()),
                return_type=error_json.get('out_return', ''),
                context=error_json.get('context', ''),
                file_path=''  # 添加必需的file_path参数
            )
            
            # 首先尝试基于错误上下文的直接搜索
            original_method = self._search_by_error_context(error_json)
            if original_method:
                return original_method
            
            # 然后使用标准方法搜索
            return self.search_original_method(method_info)
            
        except Exception as e:
            self.logger.error(f"根据错误JSON搜索方法失败: {str(e)}")
            return None
    
    def _search_by_error_context(self, error_json: Dict[str, Any]) -> Optional[OriginalMethod]:
        """基于错误上下文直接搜索方法"""
        try:
            import re
            
            method_name = error_json.get('missing_method', '')
            context = error_json.get('context', '')
            
            if not method_name:
                return None
            
            self.logger.debug(f"基于错误上下文搜索方法: {method_name}")
            
            # 构建搜索模式 - 搜索方法调用
            method_call_pattern = rf'\b{re.escape(method_name)}\s*\('
            
            # 在所有legacy文件中搜索
            for legacy_path in self.legacy_paths:
                for root, dirs, files in os.walk(legacy_path):
                    for file in files:
                        if file.endswith('.java'):
                            file_path = os.path.join(root, file)
                            
                            try:
                                content = self._get_file_content(file_path)
                                if not content:
                                    continue
                                
                                # 搜索方法调用
                                import re
                                matches = list(re.finditer(method_call_pattern, content))
                                
                                if matches:
                                    self.logger.debug(f"在文件中找到方法调用: {file_path}")
                                    
                                    # 分析每个匹配的上下文
                                    for match in matches:
                                        # 获取调用行的上下文
                                        call_context = self._get_call_line_context(content, match.start())
                                        
                                        # 尝试从调用上下文推断实际的方法定义位置
                                        original_method = self._infer_method_from_call_context(
                                            file_path, content, match, method_name, error_json
                                        )
                                        
                                        if original_method:
                                            return original_method
                                        
                            except Exception as e:
                                self.logger.warning(f"搜索文件失败 {file_path}: {str(e)}")
                                continue
            
            return None
            
        except Exception as e:
            self.logger.warning(f"基于错误上下文搜索失败: {str(e)}")
            return None
    
    def _get_call_line_context(self, content: str, match_pos: int) -> str:
        """获取方法调用行的上下文"""
        try:
            lines = content.split('\n')
            
            # 找到匹配位置所在的行
            char_count = 0
            for i, line in enumerate(lines):
                if char_count <= match_pos < char_count + len(line) + 1:
                    # 返回前后几行作为上下文
                    start_line = max(0, i - 2)
                    end_line = min(len(lines), i + 3)
                    return '\n'.join(lines[start_line:end_line])
                char_count += len(line) + 1
            
            return ""
            
        except Exception as e:
            self.logger.warning(f"获取调用行上下文失败: {str(e)}")
            return ""
    
    def _infer_method_from_call_context(self, file_path: str, content: str, match, method_name: str, error_json: Dict[str, Any]) -> Optional[OriginalMethod]:
        """从调用上下文推断方法定义"""
        try:
            # 获取调用行
            call_line = self._get_line_at_position(content, match.start())
            self.logger.debug(f"分析调用行: {call_line}")
            
            # 提取调用对象
            import re
            call_pattern = rf'(\w+)\.{re.escape(method_name)}\s*\('
            call_match = re.search(call_pattern, call_line)
            
            if call_match:
                caller_object = call_match.group(1)
                self.logger.debug(f"找到调用对象: {caller_object}")
                
                # 查找调用对象的类型
                object_type = self._find_object_type_in_file(content, caller_object)
                
                if object_type:
                    self.logger.debug(f"推断对象类型: {object_type}")
                    
                    # 搜索该类型的方法定义
                    return self._find_method_definition_by_type(object_type, method_name, error_json)
            
            return None
            
        except Exception as e:
            self.logger.warning(f"从调用上下文推断方法失败: {str(e)}")
            return None
    
    def _get_line_at_position(self, content: str, pos: int) -> str:
        """获取指定位置所在的行"""
        try:
            lines = content.split('\n')
            char_count = 0
            
            for line in lines:
                if char_count <= pos < char_count + len(line) + 1:
                    return line.strip()
                char_count += len(line) + 1
            
            return ""
            
        except Exception as e:
            self.logger.warning(f"获取指定位置行失败: {str(e)}")
            return ""
    
    def _find_method_definition_by_type(self, class_type: str, method_name: str, error_json: Dict[str, Any]) -> Optional[OriginalMethod]:
        """根据类型查找方法定义"""
        try:
            self.logger.debug(f"根据类型查找方法定义: {class_type}.{method_name}")
            
            # 在所有legacy路径中搜索包含该类型的文件
            for legacy_path in self.legacy_paths:
                for root, dirs, files in os.walk(legacy_path):
                    for file in files:
                        if file.endswith('.java'):
                            file_path = os.path.join(root, file)
                            
                            try:
                                content = self._get_file_content(file_path)
                                if not content:
                                    continue
                                
                                # 检查是否包含该类的定义
                                import re
                                class_pattern = rf'class\s+{re.escape(class_type)}\b'
                                
                                if re.search(class_pattern, content):
                                    self.logger.debug(f"找到类定义文件: {file_path}")
                                    
                                    # 尝试提取方法
                                    original_method = self._extract_method_from_file(
                                        file_path, method_name, class_type
                                    )
                                    
                                    if original_method:
                                        self.logger.info(f"成功找到方法定义: {file_path}")
                                        return original_method
                                        
                            except Exception as e:
                                self.logger.warning(f"搜索类定义文件失败 {file_path}: {str(e)}")
                                continue
            
            return None
            
        except Exception as e:
            self.logger.warning(f"根据类型查找方法定义失败: {str(e)}")
            return None
    
    def clear_cache(self):
        """清理缓存"""
        self.search_cache.clear()
        self.file_content_cache.clear()
        self.logger.debug("遗留代码搜索器缓存已清理")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        return {
            'search_cache_size': len(self.search_cache),
            'file_content_cache_size': len(self.file_content_cache),
            'method_index_size': len(self.method_index),
            'class_index_size': len(self.class_index),
            'legacy_paths_count': len(self.legacy_paths)
        }
    
    def rebuild_indexes(self):
        """重建索引"""
        self.method_index.clear()
        self.class_index.clear()
        self._build_indexes()
        self.logger.info("索引重建完成")