"""
组件集成器

将现有组件集成到主工作流程中，包括PreciseSourceSearcher和SourceContextAnalyzer。
"""

import os
import logging
from typing import Optional, Dict, Any, List
from pathlib import Path

from error_prone_json_reporter.common.models import Configuration, MethodInfo
from error_prone_json_reporter.stage2.precise_source_searcher import PreciseSourceSearcher, MethodLocation
from error_prone_json_reporter.stage2.source_context_analyzer import SourceContextAnalyzer, ContextAnalysisResult


class ComponentIntegrator:
    """
    组件集成器
    
    职责：将现有组件集成到主工作流程
    输入：APIMatchingEngine实例和配置
    输出：增强的匹配引擎
    """
    
    def __init__(self, config: Configuration):
        """
        初始化组件集成器
        
        Args:
            config: 配置对象
        """
        self.logger = logging.getLogger(__name__)
        self.config = config
        
        # 初始化组件
        self.precise_searcher: Optional[PreciseSourceSearcher] = None
        self.context_analyzer: Optional[SourceContextAnalyzer] = None
        
        # 集成状态
        self.integration_status = {
            'precise_searcher_integrated': False,
            'context_analyzer_integrated': False,
            'legacy_path_configured': False
        }
        
        self.logger.info("组件集成器初始化完成")
    
    def integrate_all_components(self, engine) -> bool:
        """
        集成所有组件到APIMatchingEngine
        
        Args:
            engine: APIMatchingEngine实例
            
        Returns:
            是否成功集成所有组件
        """
        self.logger.info("开始集成所有组件到主工作流程")
        
        success = True
        
        # 1. 集成精确源码搜索器
        if not self.integrate_precise_source_searcher(engine):
            success = False
        
        # 2. 集成源码上下文分析器
        if not self.integrate_source_context_analyzer(engine):
            success = False
        
        # 3. 配置legacy_src_path使用
        if not self.configure_legacy_src_path_usage(engine):
            success = False
        
        if success:
            self.logger.info("所有组件集成完成")
        else:
            self.logger.warning("部分组件集成失败")
        
        return success
    
    def integrate_precise_source_searcher(self, engine) -> bool:
        """
        集成精确源码搜索器到错误处理流程
        
        Args:
            engine: APIMatchingEngine实例
            
        Returns:
            是否成功集成
        """
        try:
            self.logger.info("集成PreciseSourceSearcher到错误处理流程")
            
            # 检查legacy_src_path配置
            legacy_src_path = getattr(self.config, 'legacy_src_path', '')
            if not legacy_src_path:
                self.logger.warning("legacy_src_path未配置，跳过PreciseSourceSearcher集成")
                return False
            
            if not os.path.exists(legacy_src_path):
                self.logger.warning(f"legacy_src_path路径不存在: {legacy_src_path}")
                return False
            
            # 初始化精确源码搜索器
            self.precise_searcher = PreciseSourceSearcher(
                legacy_src_path=legacy_src_path,
                project_path=getattr(self.config, 'project_path', legacy_src_path)
            )
            
            # 将搜索器集成到引擎中
            engine.precise_searcher = self.precise_searcher
            
            # 增强错误描述构建方法
            original_build_error_description = engine._build_error_description
            engine._build_error_description = self._create_enhanced_error_description_builder(
                original_build_error_description, engine
            )
            
            self.integration_status['precise_searcher_integrated'] = True
            self.logger.info(f"PreciseSourceSearcher集成成功，legacy路径: {legacy_src_path}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"集成PreciseSourceSearcher失败: {str(e)}")
            return False
    
    def integrate_source_context_analyzer(self, engine) -> bool:
        """
        集成源码上下文分析器到上下文分析流程
        
        Args:
            engine: APIMatchingEngine实例
            
        Returns:
            是否成功集成
        """
        try:
            self.logger.info("集成SourceContextAnalyzer到上下文分析流程")
            
            # 初始化源码上下文分析器
            self.context_analyzer = SourceContextAnalyzer(config=self.config)
            
            # 将分析器集成到引擎中
            engine.context_analyzer = self.context_analyzer
            
            # 增强方法编码过程，集成上下文分析
            if hasattr(engine, '_encode_methods_with_ai_analysis'):
                original_encode_with_ai = engine._encode_methods_with_ai_analysis
                engine._encode_methods_with_ai_analysis = self._create_enhanced_ai_encoding(
                    original_encode_with_ai, engine
                )
            
            self.integration_status['context_analyzer_integrated'] = True
            self.logger.info("SourceContextAnalyzer集成成功")
            
            return True
            
        except Exception as e:
            self.logger.error(f"集成SourceContextAnalyzer失败: {str(e)}")
            return False
    
    def configure_legacy_src_path_usage(self, engine) -> bool:
        """
        配置legacy_src_path在主流程中的使用
        
        Args:
            engine: APIMatchingEngine实例
            
        Returns:
            是否成功配置
        """
        try:
            self.logger.info("配置legacy_src_path在主流程中的使用")
            
            legacy_src_path = getattr(self.config, 'legacy_src_path', '')
            if not legacy_src_path:
                self.logger.warning("legacy_src_path未配置")
                return False
            
            # 验证路径存在性
            if not os.path.exists(legacy_src_path):
                self.logger.warning(f"legacy_src_path路径不存在: {legacy_src_path}")
                return False
            
            # 在引擎中设置legacy路径配置
            engine.legacy_src_path = legacy_src_path
            
            # 增强匹配流程，在匹配前尝试从legacy源码中获取更多上下文
            original_perform_matching = engine._perform_matching
            engine._perform_matching = self._create_enhanced_matching_with_legacy(
                original_perform_matching, engine
            )
            
            self.integration_status['legacy_path_configured'] = True
            self.logger.info(f"legacy_src_path配置成功: {legacy_src_path}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"配置legacy_src_path失败: {str(e)}")
            return False
    
    def _create_enhanced_error_description_builder(self, original_method, engine):
        """
        创建增强的错误描述构建器
        
        Args:
            original_method: 原始的_build_error_description方法
            engine: APIMatchingEngine实例
            
        Returns:
            增强的错误描述构建方法
        """
        def enhanced_build_error_description(error: Dict[str, Any]) -> str:
            # 首先调用原始方法获取基础描述
            base_description = original_method(error)
            
            # 如果集成了精确源码搜索器，尝试获取更多上下文
            if hasattr(engine, 'precise_searcher') and engine.precise_searcher:
                try:
                    # 从错误信息中提取关键信息
                    class_name = error.get('class', '')
                    method_name = error.get('missing_method', '')
                    package_name = error.get('package', '')
                    
                    # 搜索legacy源码中的方法实现
                    method_location = engine.precise_searcher.search_method_by_error(
                        error_content=error.get('context', ''),
                        class_name=class_name,
                        method_name=method_name
                    )
                    
                    if method_location:
                        # 将找到的方法代码添加到描述中
                        enhanced_description = f"{base_description}\n\n原始方法实现:\n{method_location.method_code[:500]}..."
                        
                        # 添加类上下文信息
                        if method_location.class_context:
                            enhanced_description += f"\n\n类上下文:\n{method_location.class_context[:200]}..."
                        
                        self.logger.debug(f"为错误 {class_name}.{method_name} 添加了legacy源码上下文")
                        return enhanced_description
                    
                except Exception as e:
                    self.logger.warning(f"获取legacy源码上下文失败: {str(e)}")
            
            return base_description
        
        return enhanced_build_error_description
    
    def _create_enhanced_ai_encoding(self, original_method, engine):
        """
        创建增强的AI编码方法
        
        Args:
            original_method: 原始的_encode_methods_with_ai_analysis方法
            engine: APIMatchingEngine实例
            
        Returns:
            增强的AI编码方法
        """
        def enhanced_encode_methods_with_ai_analysis():
            # 如果集成了上下文分析器，在AI编码前进行上下文分析
            if hasattr(engine, 'context_analyzer') and engine.context_analyzer:
                try:
                    self.logger.info("使用集成的SourceContextAnalyzer进行上下文增强")
                    
                    # 为每个候选方法进行上下文分析
                    enhanced_methods = []
                    for method in engine.candidate_methods:
                        try:
                            # 构建错误信息格式（用于上下文分析）
                            error_info = {
                                'package': method.package,
                                'class': method.class_name,
                                'missing_method': method.method_name,
                                'context': method.context,
                                'in_param': {},
                                'out_return': method.return_type
                            }
                            
                            # 解析参数
                            if method.parameters:
                                for param in method.parameters:
                                    if ':' in param:
                                        param_name, param_type = param.split(':', 1)
                                        error_info['in_param'][param_name.strip()] = param_type.strip()
                            
                            # 进行上下文分析
                            context_result = engine.context_analyzer.analyze_error_context(error_info)
                            
                            # 使用分析结果增强方法信息
                            enhanced_method = context_result.enhanced_method_info
                            enhanced_method.context = f"{enhanced_method.context}; {context_result.concise_description}"
                            
                            enhanced_methods.append(enhanced_method)
                            
                        except Exception as e:
                            self.logger.warning(f"方法上下文分析失败 {method.method_name}: {str(e)}")
                            enhanced_methods.append(method)
                    
                    # 更新候选方法列表
                    engine.candidate_methods = enhanced_methods
                    
                except Exception as e:
                    self.logger.warning(f"上下文增强失败: {str(e)}")
            
            # 调用原始的AI编码方法
            return original_method()
        
        return enhanced_encode_methods_with_ai_analysis
    
    def _create_enhanced_matching_with_legacy(self, original_method, engine):
        """
        创建增强的匹配方法，集成legacy源码搜索
        
        Args:
            original_method: 原始的_perform_matching方法
            engine: APIMatchingEngine实例
            
        Returns:
            增强的匹配方法
        """
        def enhanced_perform_matching():
            # 在执行匹配前，尝试从legacy源码中获取更多信息
            if hasattr(engine, 'precise_searcher') and engine.precise_searcher:
                try:
                    self.logger.info("在匹配前从legacy源码中获取额外上下文")
                    
                    enhanced_error_list = []
                    for error in engine.error_list:
                        enhanced_error = error.copy()
                        
                        # 尝试从legacy源码中搜索方法
                        class_name = error.get('class', '')
                        method_name = error.get('missing_method', '')
                        
                        if class_name and method_name:
                            method_location = engine.precise_searcher.search_method_by_error(
                                error_content=error.get('context', ''),
                                class_name=class_name,
                                method_name=method_name
                            )
                            
                            if method_location:
                                # 将找到的信息添加到错误描述中
                                original_context = enhanced_error.get('context', '')
                                legacy_context = f"Legacy实现: {method_location.method_code[:200]}..."
                                enhanced_error['context'] = f"{original_context}\n{legacy_context}"
                                
                                # 添加文件路径信息
                                enhanced_error['legacy_file_path'] = method_location.file_path
                                enhanced_error['legacy_method_found'] = True
                                
                                self.logger.debug(f"为 {class_name}.{method_name} 添加了legacy上下文")
                        
                        enhanced_error_list.append(enhanced_error)
                    
                    # 更新错误列表
                    engine.error_list = enhanced_error_list
                    
                except Exception as e:
                    self.logger.warning(f"Legacy上下文增强失败: {str(e)}")
            
            # 调用原始的匹配方法
            return original_method()
        
        return enhanced_perform_matching
    
    def get_integration_status(self) -> Dict[str, Any]:
        """
        获取集成状态
        
        Returns:
            集成状态信息
        """
        status = self.integration_status.copy()
        
        # 添加组件状态信息
        status['precise_searcher_available'] = self.precise_searcher is not None
        status['context_analyzer_available'] = self.context_analyzer is not None
        
        # 添加配置信息
        status['legacy_src_path'] = getattr(self.config, 'legacy_src_path', '')
        status['legacy_path_exists'] = os.path.exists(getattr(self.config, 'legacy_src_path', ''))
        
        return status
    
    def test_component_integration(self, engine) -> Dict[str, bool]:
        """
        测试组件集成效果
        
        Args:
            engine: APIMatchingEngine实例
            
        Returns:
            测试结果
        """
        test_results = {}
        
        # 测试精确源码搜索器集成
        test_results['precise_searcher_integration'] = self._test_precise_searcher_integration(engine)
        
        # 测试源码上下文分析器集成
        test_results['context_analyzer_integration'] = self._test_context_analyzer_integration(engine)
        
        # 测试legacy路径配置
        test_results['legacy_path_configuration'] = self._test_legacy_path_configuration(engine)
        
        return test_results
    
    def _test_precise_searcher_integration(self, engine) -> bool:
        """测试精确源码搜索器集成"""
        try:
            if not hasattr(engine, 'precise_searcher') or not engine.precise_searcher:
                return False
            
            # 测试搜索器是否可用
            cache_stats = engine.precise_searcher.get_cache_stats()
            self.logger.debug(f"PreciseSourceSearcher缓存状态: {cache_stats}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"测试PreciseSourceSearcher集成失败: {str(e)}")
            return False
    
    def _test_context_analyzer_integration(self, engine) -> bool:
        """测试源码上下文分析器集成"""
        try:
            if not hasattr(engine, 'context_analyzer') or not engine.context_analyzer:
                return False
            
            # 测试分析器是否可用
            test_error = {
                'package': 'com.test',
                'class': 'TestClass',
                'missing_method': 'testMethod',
                'context': 'test context',
                'in_param': {},
                'out_return': 'void'
            }
            
            result = engine.context_analyzer.analyze_error_context(test_error)
            return result is not None
            
        except Exception as e:
            self.logger.error(f"测试SourceContextAnalyzer集成失败: {str(e)}")
            return False
    
    def _test_legacy_path_configuration(self, engine) -> bool:
        """测试legacy路径配置"""
        try:
            if not hasattr(engine, 'legacy_src_path'):
                return False
            
            legacy_path = engine.legacy_src_path
            return os.path.exists(legacy_path)
            
        except Exception as e:
            self.logger.error(f"测试legacy路径配置失败: {str(e)}")
            return False
    
    def cleanup(self):
        """清理资源"""
        try:
            if self.precise_searcher:
                self.precise_searcher.clear_cache()
            
            if self.context_analyzer:
                # 清理分析器缓存
                if hasattr(self.context_analyzer, 'analysis_cache'):
                    self.context_analyzer.analysis_cache.clear()
            
            self.logger.info("组件集成器资源清理完成")
            
        except Exception as e:
            self.logger.warning(f"组件集成器资源清理失败: {str(e)}")