# 设计文档

## 概述

Java代码迁移API匹配工具是一个两阶段的Python工具系统，第一阶段生成项目错误清单的JSON格式，第二阶段使用机器学习技术进行API相似度匹配。该系统采用模块化设计，通过Error Prone或现有错误分析工具生成标准化的错误清单，然后利用GraphCodeBERT模型和FAISS向量检索技术找到最佳的API替代方案。

## 架构

### 系统架构图

```mermaid
graph TB
    A[Java项目源码] --> B[第一阶段: 错误清单生成器]
    B --> C[errors.json]
    
    D[新框架源码] --> E[第二阶段: API匹配器]
    C --> E
    F[GraphCodeBERT模型] --> E
    
    E --> G[FAISS向量索引]
    E --> H[migration_suggestions.json]
    
    subgraph "第一阶段组件"
        B1[Error Prone集成器]
        B2[现有工具适配器]
        B3[JSON格式化器]
        B --> B1
        B --> B2
        B --> B3
    end
    
    subgraph "第二阶段组件"
        E1[源码解析器]
        E2[向量编码器]
        E3[相似度匹配器]
        E4[结果生成器]
        E --> E1
        E --> E2
        E --> E3
        E --> E4
    end
    
    subgraph "输出文件"
        C
        H
        I[执行日志]
    end
```

### 分层架构

1. **命令行接口层 (CLI Layer)**
   - 两阶段命令行接口
   - 参数解析和验证
   - 执行流程控制

2. **业务逻辑层 (Business Logic Layer)**
   - 错误清单生成引擎
   - API相似度匹配引擎
   - 结果处理和格式化

3. **数据处理层 (Data Processing Layer)**
   - Java源码解析
   - 向量编码和索引
   - JSON数据处理

4. **工具集成层 (Tool Integration Layer)**
   - Error Prone集成
   - 现有工具适配
   - 机器学习模型管理

## 组件和接口

### 第一阶段：错误清单生成器

#### 1. ErrorListGenerator (错误清单生成器)
```python
class ErrorListGenerator:
    """
    职责：生成项目错误清单的JSON格式
    输入：Java项目路径、配置参数
    输出：errors.json文件
    """
    
    def generate_error_list(self, project_path: str, config: dict) -> str:
        # 选择合适的错误检测工具
        # 执行错误扫描
        # 格式化为标准JSON
        # 输出到指定文件
```

**关键功能：**
- 支持多种错误检测工具（Error Prone、现有inspect工具等）
- 标准化JSON格式输出
- 错误信息提取和清理
- 配置驱动的工具选择

#### 2. ErrorProneIntegrator (Error Prone集成器)
```python
class ErrorProneIntegrator(ErrorDetectorInterface):
    """
    职责：集成Error Prone工具进行静态分析
    输入：项目路径和Error Prone配置
    输出：原始错误报告
    """
    
    def run_error_prone(self, project_path: str) -> List[ErrorReport]:
        # 配置Error Prone参数
        # 执行静态分析
        # 解析输出结果
        # 返回结构化错误信息
```

**Error Prone集成策略：**
- 自动检测项目构建系统（Maven/Gradle）
- 配置Error Prone插件和规则
- 处理Error Prone输出格式
- 错误类型过滤和分类

#### 3. ExistingToolAdapter (现有工具适配器)
```python
class ExistingToolAdapter(ErrorDetectorInterface):
    """
    职责：适配现有的错误分析工具（如inspect_method.py）
    输入：现有工具的输出文件
    输出：标准化错误信息
    """
    
    def adapt_existing_output(self, tool_output_path: str) -> List[ErrorReport]:
        # 读取现有工具输出
        # 解析和转换格式
        # 标准化错误信息
        # 返回统一格式
```

**适配现有工具：**
- 支持JavaAnnotator.xml格式
- 支持inspect_method.py输出
- 支持自定义格式适配
- 保持向后兼容性

#### 4. JSONFormatter (JSON格式化器)
```python
class JSONFormatter:
    """
    职责：将错误信息格式化为标准JSON
    输入：结构化错误信息列表
    输出：标准JSON格式字符串
    """
    
    def format_to_json(self, errors: List[ErrorReport]) -> str:
        # 转换为目标JSON格式
        # 验证JSON结构
        # 处理特殊字符和编码
        # 返回格式化JSON
```

**JSON格式规范：**
```json
[
  {
    "package": "com.cet.piem.service.impl.NodeServiceImpl",
    "class": "NodeServiceImpl",
    "missing_method": "getProjectTree(energyType)",
    "in_param": {
      "energyType": "java.lang.String"
    },
    "out_return": "java.lang.List",
    "line": [466],
    "context": "方法调用上下文信息"
  }
]
```

### 智能化增强组件

#### 1. SourceContextAnalyzer (源码上下文智能分析器)
```python
class SourceContextAnalyzer:
    """
    职责：根据报错信息精确定位和分析源码中的方法实现
    输入：报错信息、迁移前源码路径
    输出：完整的方法实现和上下文分析
    """
    
    def __init__(self, old_source_paths: List[str], llm_client):
        self.old_source_paths = old_source_paths
        self.llm_client = llm_client
        self.source_locator = PreciseSourceLocator()
        self.ast_analyzer = ASTMethodAnalyzer()
    
    def analyze_missing_method(self, error_info: dict) -> MethodAnalysisResult:
        # 根据报错行号精确定位源码中的方法
        # 使用AST提取完整的方法实现
        # 通过大模型分析方法的功能和用途
        # 生成结构化的方法理解报告
```

**核心功能：**
- 精确源码定位：根据报错行号在旧源码中找到对应方法
- AST语法分析：提取方法的完整定义、参数、返回值
- 大模型理解：分析方法的业务逻辑和功能用途
- 上下文提取：获取方法调用的周围代码和依赖关系

#### 2. PreciseSourceSearcher (精确源码搜索器)
```python
class PreciseSourceSearcher:
    """
    职责：根据报错内容在迁移前源码中精确搜索方法
    输入：报错内容、类名、方法名、源码路径
    输出：方法的精确位置和完整代码
    """
    
    def search_method_by_error(self, error_content: str, class_name: str, 
                              method_name: str, source_path: str) -> MethodLocation:
        # 在源码路径中搜索目标类文件
        # 根据报错内容进行多维度搜索匹配
        # 使用AST分析确定方法边界
        # 提取方法的完整实现代码
```

**搜索策略：**
- 内容匹配搜索：根据报错内容在源码中进行精确搜索
- 多维度匹配：基于方法名、类名、包名进行搜索
- 方法边界识别：使用AST确定方法的开始和结束位置
- 重载方法处理：根据参数类型区分同名方法

#### 3. MethodSemanticAnalyzer (方法语义分析器)
```python
class MethodSemanticAnalyzer:
    """
    职责：使用大模型深度理解方法的功能和语义
    输入：方法源码、调用上下文
    输出：结构化的方法功能描述（知识库核心内容）
    """
    
    def analyze_method_semantics(self, method_code: str, context: str) -> MethodSemantics:
        # 直接调用当前大模型进行分析
        # 分析方法的业务逻辑和功能用途
        # 理解参数的作用和返回值的含义
        # 生成结构化的功能描述和标签
        # 评估方法的复杂度和重要性
```

**分析维度：**
- 功能描述：方法的主要功能和业务用途
- 参数分析：每个参数的作用和数据类型含义
- 返回值分析：返回值的业务含义和数据结构
- 调用场景：方法的典型使用场景和上下文
- 业务标签：方法所属的业务领域和功能分类

#### 4. KnowledgeBaseQueryEnhancer (知识库查询增强器)
```python
class KnowledgeBaseQueryEnhancer:
    """
    职责：在向量化过程中查询知识库，增强方法的语义理解
    输入：方法信息、知识库目录路径
    输出：增强的语义描述
    """
    
    def enhance_method_semantics(self, method_info: MethodInfo, 
                                knowledge_dir: str) -> EnhancedMethodInfo:
        # 根据方法名、类名、包名在知识库中查询相关信息
        # 解析匹配的MD文档内容
        # 使用当前大模型融合方法代码和知识库描述
        # 生成增强的语义描述用于向量编码
        # 计算知识库匹配的置信度和权重
```

**增强功能：**
- 实时查询：在向量化时动态查询知识库中的相关信息
- 内容匹配：基于方法名、类名、包名进行精确和模糊匹配
- 语义融合：使用当前大模型将方法代码与知识库描述进行融合
- 权重分配：根据匹配置信度分配知识库内容的权重
- 向量增强：将增强后的语义描述用于向量编码，提升匹配精度

#### 5. ParameterCompatibilityAnalyzer (参数兼容性分析器)
```python
class ParameterCompatibilityAnalyzer:
    """
    职责：分析新旧方法之间的参数兼容性，处理框架升级带来的参数差异
    输入：旧方法参数、新方法参数
    输出：兼容性分析结果和迁移建议
    """
    
    def analyze_parameter_compatibility(self, old_params: List[str], 
                                      new_params: List[str]) -> ParameterCompatibilityResult:
        # 识别框架参数（tenantId, userId等）
        # 分析核心业务参数的对应关系
        # 检测参数类型变化（如Long -> String）
        # 识别参数封装（多个参数 -> DTO对象）
        # 生成参数迁移建议
```

**分析功能：**
- 框架参数识别：自动识别tenantId、userId、projectId等框架级参数
- 业务参数匹配：专注于核心业务参数的语义匹配
- 参数类型分析：检测参数类型变化和兼容性
- 封装模式识别：识别参数封装为DTO的模式
- 迁移建议生成：提供具体的参数迁移代码建议

#### 4. KnowledgeBaseParser (MD文档知识库解析器)
```python
class KnowledgeBaseParser:
    """
    职责：解析知识库目录下的所有MD文档，提取方法相关知识
    输入：知识库目录路径
    输出：结构化的知识库索引
    """
    
    def parse_knowledge_base(self, knowledge_dir: str) -> Dict[str, KnowledgeBaseEntry]:
        # 递归扫描知识库目录下的所有MD文件
        # 解析MD文档内容，提取方法替换规则
        # 使用大模型理解文档内容，生成语义描述
        # 构建可检索的知识库索引
        # 建立方法名到知识条目的映射关系
```

**解析功能：**
- MD文档扫描：递归扫描知识库目录下的所有Markdown文件
- 内容提取：提取方法替换规则、业务逻辑说明和使用示例
- 大模型理解：使用当前大模型分析MD文档内容，生成结构化描述
- 索引构建：建立方法名、类名、包名的多维度索引
- 知识融合：将MD文档知识与当前大模型分析结果进行融合

### 第二阶段：API匹配器

#### 1. APIMatchingEngine (API匹配引擎)
```python
class APIMatchingEngine:
    """
    职责：执行API相似度匹配的主流程
    输入：errors.json和新框架源码路径
    输出：migration_suggestions.json
    """
    
    def match_apis(self, errors_json: str, src_path: str) -> str:
        # 加载错误清单
        # 解析新框架源码
        # 执行向量匹配
        # 生成迁移建议
```

#### 2. SourceCodeParser (源码解析器)
```python
class SourceCodeParser:
    """
    职责：解析新框架Java源码并提取方法信息
    输入：源码目录路径
    输出：方法信息列表
    """
    
    def parse_java_files(self, src_dir: str) -> List[MethodInfo]:
        # 递归扫描Java文件
        # 使用javalang解析语法树
        # 提取方法签名信息
        # 处理复杂类型和泛型
```

**方法信息数据结构：**
```python
@dataclass
class MethodInfo:
    package: str
    class_name: str
    method_name: str
    parameters: List[str]
    return_type: str
    context: str
    file_path: str
```

#### 3. VectorEncoder (向量编码器)
```python
class VectorEncoder:
    """
    职责：使用GraphCodeBERT将方法描述转换为向量，重点编码大模型理解内容
    输入：方法描述文本列表（包含大模型分析结果）
    输出：加权融合的向量矩阵
    """
    
    def __init__(self, model_name: str = "microsoft/graphcodebert-base"):
        self.tokenizer = AutoTokenizer.from_pretrained(model_name)
        self.model = AutoModel.from_pretrained(model_name)
        self.semantic_weight = 0.7  # 大模型理解内容权重
        self.signature_weight = 0.2  # 方法签名权重
        self.context_weight = 0.1   # 上下文权重
    
    def encode_with_enhanced_features(self, enhanced_features: EnhancedMethodFeatures) -> np.ndarray:
        # 使用增强方法特征进行向量编码
        # 获取加权描述文本，突出语义理解内容（70%权重）
        # 根据知识库匹配置信度动态调整权重
        # 将多层次特征融合为统一的向量表示
        # 实现特征权重的自适应调整
        # 返回语义丰富的高质量向量
```

**增强编码策略：**
- 大模型理解优先：将方法的功能描述和业务逻辑作为主要编码内容（权重70%）
- 知识库增强：在向量化时查询知识库，结合MD文档知识增强语义理解
- 多层次信息融合：结合方法签名、AST分析、调用上下文、知识库信息
- 加权向量合成：根据信息重要性和知识库匹配置信度分配权重
- 语义标签增强：利用大模型生成的功能标签和知识库描述提升向量质量
- 批量处理优化：支持大规模方法库的高效编码和知识库查询

#### 4. HybridSimilarityMatcher (混合相似度匹配器)
```python
class SimilarityMatcher:
    """
    职责：使用FAISS进行高效的向量相似度检索
    输入：查询向量和候选向量库
    输出：TOP-K相似结果
    """
    
    def __init__(self, top_k: int = 3):
        self.top_k = top_k
        self.index = None
    
    def build_index(self, vectors: np.ndarray) -> None:
        # 创建FAISS索引
        # 添加向量到索引
        # 优化索引结构
    
    def search_similar(self, query_vector: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        # 执行相似度检索
        # 返回距离和索引
        # 计算相似度分数
```

**相似度计算：**
- 使用L2距离进行向量检索
- 转换距离为相似度分数：`similarity = 1/(1+distance)`
- 支持批量查询优化
- 结果排序和过滤

#### 5. ResultGenerator (结果生成器)
```python
class ResultGenerator:
    """
    职责：生成最终的迁移建议JSON
    输入：原始错误和匹配结果
    输出：migration_suggestions.json
    """
    
    def generate_suggestions(self, errors: List[dict], matches: List[dict]) -> str:
        # 组合原始错误和匹配结果
        # 计算额外的匹配指标
        # 格式化为输出JSON
        # 添加元数据和统计信息
```

**输出JSON格式：**
```json
[
  {
    "missing_method": "getProjectTree",
    "in_param": {"energyType": "java.lang.String"},
    "out_return": {"return": "java.lang.List"},
    "context": "获取项目树结构",
    "candidates": [
      {
        "name": "getEnergyProjectTree",
        "class": "EnergyTreeService",
        "package": "com.cet.eem.fusion.energy.service",
        "params": ["java.lang.String"],
        "return": "java.util.List",
        "context": "获取能源项目树结构",
        "similarity": 0.89,
        "class_match": false
      }
    ]
  }
]
```

## 数据模型

### ErrorReport (错误报告)
```python
@dataclass
class ErrorReport:
    package: str
    class_name: str
    missing_method: str
    in_param: Dict[str, str]
    out_return: str
    line: List[int]
    context: str = ""
    error_type: str = "missing_method"
    source_file_path: str = ""  # 源码文件路径
    old_source_path: str = ""  # 迁移前源码路径
```

### MethodLocation (方法位置信息)
```python
@dataclass
class MethodLocation:
    file_path: str
    start_line: int
    end_line: int
    method_code: str
    class_context: str
    import_statements: List[str]
    surrounding_methods: List[str]
```

### MethodSemantics (方法语义分析结果)
```python
@dataclass
class MethodSemantics:
    function_description: str  # 主要功能描述（最高权重）
    business_purpose: str      # 业务用途
    parameter_analysis: Dict[str, str]  # 参数分析
    return_value_meaning: str  # 返回值含义
    usage_scenarios: List[str] # 使用场景
    business_tags: List[str]   # 业务标签
    complexity_score: float    # 复杂度评分
    importance_score: float    # 重要性评分
    llm_confidence: float      # 大模型分析置信度
```

### MethodAnalysisResult (方法分析结果)
```python
@dataclass
class MethodAnalysisResult:
    location: MethodLocation
    semantics: MethodSemantics
    ast_analysis: ASTAnalysisResult
    call_chain: List[str]
    dependencies: List[str]
    analysis_timestamp: datetime
```

### ASTAnalysisResult (AST分析结果)
```python
@dataclass
class ASTAnalysisResult:
    method_signature: str
    parameter_types: List[str]
    return_type: str
    local_variables: List[str]
    method_calls: List[str]
    exception_handling: List[str]
    annotations: List[str]
```

### KnowledgeBaseEntry (知识库条目)
```python
@dataclass
class KnowledgeBaseEntry:
    method_name: str
    class_name: str
    package_name: str
    description: str
    replacement_info: Dict[str, str]
    business_context: str
    usage_examples: List[str]
    migration_guide: str
    confidence_score: float
    category: str  # 如：controller_changes, service_changes等
```

### EnhancedMethodFeatures (增强方法特征)
```python
@dataclass
class EnhancedMethodFeatures:
    """增强的方法特征表示，专门用于向量编码"""
    # 基础信息
    package: str
    class_name: str
    method_name: str
    method_signature: str
    
    # 语义理解（最高权重 - 70%）
    semantic_description: str  # 大模型生成的功能描述
    business_purpose: str      # 业务用途
    functional_category: str   # 功能分类
    intent_analysis: str       # 方法意图分析
    
    # 知识库增强（动态权重 - 0-20%）
    knowledge_base_match: Optional[str]  # 匹配的知识库描述
    kb_confidence: float = 0.0           # 知识库匹配置信度
    migration_hints: List[str] = field(default_factory=list)  # 迁移提示
    business_context: str = ""           # 业务上下文
    
    # 参数分析特征（权重 - 10%）
    core_parameters: List[str] = field(default_factory=list)  # 核心业务参数
    framework_parameters: List[str] = field(default_factory=list)  # 框架参数（如tenantId）
    parameter_semantics: Dict[str, str] = field(default_factory=dict)  # 参数语义
    parameter_compatibility: Dict[str, str] = field(default_factory=dict)  # 参数兼容性分析
    return_semantics: str = ""           # 返回值语义
    
    # 上下文特征（权重 - 10%）
    usage_scenarios: List[str] = field(default_factory=list)  # 使用场景
    related_methods: List[str] = field(default_factory=list)  # 相关方法
    call_frequency: str = "unknown"      # 调用频率
    migration_complexity: str = "medium" # 迁移复杂度
    
    def get_weighted_description_for_vector(self) -> str:
        """获取用于向量编码的加权描述"""
        description_parts = []
        
        # 语义描述（最高权重 - 重复3次以增加权重）
        if self.semantic_description:
            description_parts.extend([
                f"主要功能: {self.semantic_description}",
                f"功能描述: {self.semantic_description}",
                f"核心作用: {self.semantic_description}"
            ])
        
        if self.business_purpose:
            description_parts.extend([
                f"业务用途: {self.business_purpose}",
                f"业务目的: {self.business_purpose}"
            ])
        
        if self.intent_analysis:
            description_parts.append(f"方法意图: {self.intent_analysis}")
        
        # 知识库增强（根据置信度动态权重）
        if self.knowledge_base_match and self.kb_confidence > 0.5:
            weight_factor = int(self.kb_confidence * 2)  # 0.5-1.0 -> 1-2次重复
            for _ in range(weight_factor):
                description_parts.append(f"知识库信息: {self.knowledge_base_match}")
        
        if self.business_context:
            description_parts.append(f"业务上下文: {self.business_context}")
        
        # 核心业务参数（重点关注，忽略框架参数）
        if self.core_parameters:
            core_params = "; ".join(self.core_parameters)
            description_parts.append(f"核心参数: {core_params}")
        
        # 参数语义（排除框架参数如tenantId）
        if self.parameter_semantics:
            business_params = {k: v for k, v in self.parameter_semantics.items() 
                             if k.lower() not in ['tenantid', 'userid', 'projectid']}
            if business_params:
                param_desc = "; ".join([f"{k}({v})" for k, v in business_params.items()])
                description_parts.append(f"业务参数含义: {param_desc}")
        
        # 参数兼容性分析
        if self.parameter_compatibility:
            compat_desc = "; ".join([f"{k}: {v}" for k, v in self.parameter_compatibility.items()])
            description_parts.append(f"参数兼容性: {compat_desc}")
        
        if self.return_semantics:
            description_parts.append(f"返回值含义: {self.return_semantics}")
        
        # 使用场景
        if self.usage_scenarios:
            scenarios = "; ".join(self.usage_scenarios[:3])  # 最多3个场景
            description_parts.append(f"使用场景: {scenarios}")
        
        # 基础信息（较低权重，放在最后）
        description_parts.append(f"方法签名: {self.method_signature}")
        
        return " | ".join(description_parts)
    
    def get_feature_weights(self) -> Dict[str, float]:
        """获取各特征的权重分配"""
        return {
            "semantic_features": 0.7,  # 语义理解最高权重
            "knowledge_base": min(0.2, self.kb_confidence * 0.2),  # 动态权重
            "code_structure": 0.1,     # 代码结构
            "context_features": 0.1    # 上下文特征
        }

### ParameterCompatibilityResult (参数兼容性分析结果)
```python
@dataclass
class ParameterCompatibilityResult:
    framework_params_added: List[str]  # 新增的框架参数（如tenantId）
    business_params_matched: Dict[str, str]  # 业务参数匹配关系
    parameter_type_changes: Dict[str, str]  # 参数类型变化
    encapsulation_detected: bool  # 是否检测到参数封装
    dto_mapping: Optional[str]  # DTO映射建议
    migration_code: str  # 迁移代码建议
    compatibility_score: float  # 兼容性评分
    migration_complexity: str  # 迁移复杂度评估

### EnhancedMethodInfo (增强方法信息)
```python
@dataclass
class EnhancedMethodInfo:
    basic_info: MethodInfo
    features: EnhancedMethodFeatures
    semantic_analysis: MethodSemantics
    knowledge_base_match: Optional[KnowledgeBaseEntry]
    parameter_compatibility: Optional[ParameterCompatibilityResult]
    combined_description: str  # 大模型理解 + MD文档知识的融合描述
    confidence_score: float
    source_weights: Dict[str, float]  # 各信息源的权重分配
```

### MatchResult (匹配结果)
```python
@dataclass
class MatchResult:
    original_error: ErrorReport
    candidates: List[CandidateMethod]
    match_metadata: Dict[str, Any]
```

### CandidateMethod (候选方法)
```python
@dataclass
class CandidateMethod:
    method_info: MethodInfo
    similarity_score: float
    class_match: bool
    additional_metrics: Dict[str, float]
```

### Configuration (配置)
```python
@dataclass
class Configuration:
    # 第一阶段配置
    project_path: str
    error_detector: str  # "error_prone" | "existing_tool"
    existing_tool_output: str
    
    # 第二阶段配置
    src_path: str
    model_name: str
    top_k: int
    
    # 输出配置
    errors_json: str
    output_json: str
    log_level: str
```

## 错误处理

### 异常处理策略

1. **第一阶段异常**
   - Error Prone工具不可用：回退到现有工具
   - 项目路径无效：提供清晰的错误信息
   - 权限问题：指导用户解决权限设置

2. **第二阶段异常**
   - 模型加载失败：提供模型安装指导
   - 内存不足：实施批量处理策略
   - 源码解析错误：跳过有问题的文件并记录

3. **数据处理异常**
   - JSON格式错误：提供格式验证和修复建议
   - 编码问题：自动检测和转换编码
   - 文件读写失败：检查权限和磁盘空间

### 错误恢复机制

1. **渐进式降级**
   - Error Prone不可用时使用现有工具
   - 模型加载失败时使用简单文本匹配
   - 部分文件解析失败时继续处理其他文件

2. **检查点机制**
   - 第一阶段完成后保存中间结果
   - 支持从第二阶段重新开始
   - 缓存向量编码结果

## 测试策略

### 单元测试

1. **第一阶段测试**
   - Error Prone集成测试
   - JSON格式化测试
   - 现有工具适配测试

2. **第二阶段测试**
   - 源码解析准确性测试
   - 向量编码一致性测试
   - 相似度匹配精度测试

### 集成测试

1. **端到端流程测试**
   - 完整两阶段流程验证
   - 不同项目类型测试
   - 性能基准测试

2. **兼容性测试**
   - 不同Java版本兼容性
   - 不同构建系统支持
   - 不同操作系统测试

## 部署和配置

### 环境要求

1. **Python环境**
   - Python 3.8+
   - PyTorch 1.9+
   - Transformers 4.0+
   - FAISS-CPU/GPU
   - javalang

2. **Java环境**
   - JDK 8+
   - Maven或Gradle
   - Error Prone插件

3. **系统资源**
   - 内存：至少8GB（推荐16GB）
   - 存储：模型文件约1GB
   - GPU：可选，用于加速向量计算

### 配置文件

```yaml
# config.yaml
stage1:
  error_detector: "error_prone"  # or "existing_tool"
  existing_tool_output: "JavaAnnotator.xml"
  project_path: "/path/to/java/project"
  
# 新增：智能化增强配置
intelligent_analysis:
  # 迁移前源码路径
  old_source_path: "/path/to/old/source/code"
  
  # 大模型配置（使用当前Kiro大模型）
  llm_config:
    provider: "kiro"  # 使用当前大模型
    max_analysis_length: 2000  # 单次分析的最大代码长度
    analysis_temperature: 0.1  # 分析的一致性控制
    
  # 分析配置
  enable_semantic_analysis: true
  enable_ast_analysis: true
  enable_precise_search: true
  
  # 知识库配置
  knowledge_base_dir: "知识库"
  enable_knowledge_base: true
  
  # 缓存配置
  cache_semantic_analysis: true
  cache_directory: "cache/semantic_analysis"
  
stage2:
  model_name: "microsoft/graphcodebert-base"
  src_path: "/path/to/new/framework"
  top_k: 3
  batch_size: 32
  
  # 新增：向量编码权重配置
  encoding_weights:
    semantic_weight: 0.7    # 大模型理解内容权重（最高）
    signature_weight: 0.2   # 方法签名权重
    context_weight: 0.1     # 上下文权重
  
output:
  errors_json: "output/errors.json"
  migration_json: "output/migration_suggestions.json"
  semantic_analysis_json: "output/semantic_analysis.json"  # 新增
  log_file: "logs/migration.log"
  
performance:
  use_gpu: false
  cache_vectors: true
  max_workers: 4
  llm_batch_size: 10      # 大模型批量分析大小
```

### 部署步骤

1. **环境准备**
   - 安装Python依赖
   - 下载预训练模型
   - 配置Java环境

2. **工具验证**
   - 测试Error Prone集成
   - 验证模型加载
   - 检查文件权限

3. **使用示例**
   ```bash
   # 执行完整流程
   python migration_tool.py --config config.yaml --stage both
   
   # 只执行第一阶段
   python migration_tool.py --config config.yaml --stage 1
   
   # 只执行第二阶段
   python migration_tool.py --config config.yaml --stage 2
   ```