"""
Markdown 报告生成器

生成结构化的 Markdown 文档，为每个方法生成独立的分析结果块，
实现代码高亮和格式化。
"""

import json
import os
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Optional, Union

try:
    from .models import MethodAnalysisResult, ProcessingStats
    from .interfaces import ReportGeneratorInterface
except ImportError:
    from models import MethodAnalysisResult, ProcessingStats
    # Interface removed for simplicity


class MarkdownReportGenerator:
    """
    Markdown 报告生成器
    
    生成结构化的 Markdown 文档，包含：
    - 报告头部信息
    - 处理统计信息
    - 每个方法的独立分析结果块
    - 代码高亮和格式化
    """
    
    def __init__(self, 
                 title: str = "源码上下文分析报告",
                 include_toc: bool = True,
                 include_stats: bool = True,
                 code_language: str = "java"):
        """
        初始化报告生成器
        
        Args:
            title: 报告标题
            include_toc: 是否包含目录
            include_stats: 是否包含统计信息
            code_language: 代码高亮语言
        """
        self.title = title
        self.include_toc = include_toc
        self.include_stats = include_stats
        self.code_language = code_language
        self.generation_time = datetime.now()
    
    def generate_report(self, method_results: List[MethodAnalysisResult], output_path: str) -> str:
        """
        生成完整的分析报告
        
        Args:
            method_results: 方法分析结果列表
            output_path: 输出文件路径
            
        Returns:
            生成的报告内容
        """
        if not method_results:
            raise ValueError("Method results list cannot be empty")
        
        # 生成简化报告内容
        report_content = self._generate_simplified_report_content(method_results)
        
        # 确保输出目录存在
        output_path = Path(output_path)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 写入文件
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(report_content)
        except Exception as e:
            raise IOError(f"Failed to write report to {output_path}: {e}")
        
        return report_content
    
    def _generate_simplified_report_content(self, method_results: List[MethodAnalysisResult]) -> str:
        """
        生成简化的报告内容，只包含核心问题信息
        
        Args:
            method_results: 方法分析结果列表
            
        Returns:
            简化的报告内容
        """
        sections = []
        
        # 报告标题
        sections.append(f"# {self.title}")
        sections.append(f"**生成时间**: {self.generation_time.strftime('%Y-%m-%d %H:%M:%S')}")
        sections.append("")
        
        # 处理每个方法结果
        for i, result in enumerate(method_results, 1):
            sections.append(f"## 问题 {i}: {result.missing_method}")
            sections.append("")
            
            # 生成JSON格式的问题信息，包含原始问题信息
            problem_data = {
                "missing_method": result.missing_method,
                "in_param": result.in_param or {},
                "out_return": result.out_return or "Object",
                "context": result.context or "",
                "content": result.content or "",
                "notes": result.notes or ""
            }
            
            # 如果有原始错误信息，添加到问题数据中
            if result.original_error:
                original_error = result.original_error
                problem_data["original_issue"] = {
                    "issue_id": getattr(original_error, 'issue_id', None),
                    "error_code": getattr(original_error, 'error_code', None),
                    "module": getattr(original_error, 'module', None),
                    "package": original_error.package,
                    "class": original_error.class_name,
                    "missing_method": original_error.missing_method,
                    "description": getattr(original_error, 'description', None),
                    "line": str(original_error.location.line) if original_error.location else None
                }
            
            # 格式化JSON输出
            json_str = json.dumps(problem_data, ensure_ascii=False, indent=2)
            sections.append("```json")
            sections.append(json_str)
            sections.append("```")
            sections.append("")
        
        return "\n".join(sections)
    
    def format_method_result(self, result: MethodAnalysisResult) -> str:
        """
        格式化单个方法结果
        
        Args:
            result: 方法分析结果
            
        Returns:
            格式化后的 Markdown 字符串
        """
        sections = []
        
        # 方法标题
        method_title = f"### {result.missing_method}"
        if result.source_file:
            method_title += f" ({os.path.basename(result.source_file)})"
        sections.append(method_title)
        
        # 状态指示器
        status_indicator = self._get_status_indicator(result.analysis_status)
        sections.append(f"\n**状态**: {status_indicator}")
        
        # 方法签名
        if result.in_param or result.out_return:
            signature = self._format_method_signature(result)
            sections.append(f"\n**方法签名**: `{signature}`")
        
        # JSON 数据块
        json_block = self._format_json_block(result)
        sections.append(f"\n{json_block}")
        
        # 源码内容（如果有）
        if result.has_content():
            content_block = self._format_content_block(result.content)
            sections.append(f"\n#### 源码内容\n\n{content_block}")
        
        # 注释信息（如果有）
        if result.has_notes():
            notes_block = self._format_notes_block(result.notes)
            sections.append(f"\n#### 注释信息\n\n{notes_block}")
        
        # 错误信息（如果有）
        if result.error_message:
            error_block = self._format_error_block(result.error_message)
            sections.append(f"\n#### 错误信息\n\n{error_block}")
        
        return "\n".join(sections)
    
    def _generate_report_content(self, method_results: List[MethodAnalysisResult]) -> str:
        """生成完整的报告内容"""
        sections = []
        
        # 报告头部
        sections.append(self._generate_header())
        
        # 统计信息
        if self.include_stats:
            stats = self._calculate_stats(method_results)
            sections.append(self._generate_stats_section(stats))
        
        # 目录
        if self.include_toc:
            sections.append(self._generate_toc(method_results))
        
        # 方法分析结果
        sections.append(self._generate_methods_section(method_results))
        
        # 报告尾部
        sections.append(self._generate_footer())
        
        return "\n\n".join(sections)
    
    def _generate_header(self) -> str:
        """生成报告头部"""
        header = f"# {self.title}\n"
        header += f"**生成时间**: {self.generation_time.strftime('%Y-%m-%d %H:%M:%S')}\n"
        header += f"**生成工具**: 源码上下文提取器"
        return header
    
    def _generate_stats_section(self, stats: Dict[str, Any]) -> str:
        """生成统计信息部分"""
        section = "## 处理统计\n"
        
        section += f"- **总方法数**: {stats['total_methods']}\n"
        section += f"- **成功分析**: {stats['successful']} ({stats['success_rate']:.1%})\n"
        section += f"- **部分成功**: {stats['partial']}\n"
        section += f"- **分析失败**: {stats['failed']}\n"
        
        if stats['has_content'] > 0:
            section += f"- **包含源码**: {stats['has_content']}\n"
        
        if stats['has_notes'] > 0:
            section += f"- **包含注释**: {stats['has_notes']}\n"
        
        return section
    
    def _generate_toc(self, method_results: List[MethodAnalysisResult]) -> str:
        """生成目录"""
        section = "## 目录\n"
        
        for i, result in enumerate(method_results, 1):
            method_name = result.missing_method
            status_emoji = self._get_status_emoji(result.analysis_status)
            
            # 创建锚点链接
            anchor = method_name.lower().replace(' ', '-').replace('(', '').replace(')', '')
            section += f"{i}. [{method_name}](#{anchor}) {status_emoji}\n"
        
        return section
    
    def _generate_methods_section(self, method_results: List[MethodAnalysisResult]) -> str:
        """生成方法分析结果部分"""
        section = "## 方法分析结果\n"
        
        for i, result in enumerate(method_results, 1):
            method_section = f"### {i}. {self.format_method_result(result)}"
            section += f"\n{method_section}\n"
            
            # 添加分隔线（除了最后一个）
            if i < len(method_results):
                section += "\n---\n"
        
        return section
    
    def _generate_footer(self) -> str:
        """生成报告尾部"""
        footer = "---\n"
        footer += f"*报告生成于 {self.generation_time.strftime('%Y-%m-%d %H:%M:%S')}*"
        return footer
    
    def _format_json_block(self, result: MethodAnalysisResult) -> str:
        """格式化 JSON 数据块"""
        json_data = result.to_dict()
        json_str = json.dumps(json_data, ensure_ascii=False, indent=2)
        
        return f"```json\n{json_str}\n```"
    
    def _format_content_block(self, content: str) -> str:
        """格式化源码内容块"""
        # 清理内容
        cleaned_content = content.strip()
        
        return f"```{self.code_language}\n{cleaned_content}\n```"
    
    def _format_notes_block(self, notes: str) -> str:
        """格式化注释信息块"""
        # 处理多行注释
        lines = notes.strip().split('\n')
        formatted_lines = []
        
        for line in lines:
            line = line.strip()
            if line:
                # 如果是 Javadoc 注释，保持格式
                if line.startswith('/**') or line.startswith('*') or line.startswith('*/'):
                    formatted_lines.append(line)
                else:
                    formatted_lines.append(f"> {line}")
        
        return '\n'.join(formatted_lines)
    
    def _format_error_block(self, error_message: str) -> str:
        """格式化错误信息块"""
        return f"> ⚠️ **错误**: {error_message}"
    
    def _format_method_signature(self, result: MethodAnalysisResult) -> str:
        """格式化方法签名"""
        if result.in_param:
            params = []
            for param_name, param_type in result.in_param.items():
                params.append(f"{param_type} {param_name}")
            param_str = ", ".join(params)
            return f"{result.missing_method}({param_str}) -> {result.out_return}"
        else:
            return f"{result.missing_method}() -> {result.out_return}"
    
    def _get_status_indicator(self, status: str) -> str:
        """获取状态指示器"""
        status_map = {
            "success": "✅ 成功",
            "partial": "⚠️ 部分成功", 
            "failed": "❌ 失败"
        }
        return status_map.get(status, f"❓ {status}")
    
    def _get_status_emoji(self, status: str) -> str:
        """获取状态表情符号"""
        emoji_map = {
            "success": "✅",
            "partial": "⚠️",
            "failed": "❌"
        }
        return emoji_map.get(status, "❓")
    
    def _calculate_stats(self, method_results: List[MethodAnalysisResult]) -> Dict[str, Any]:
        """计算统计信息"""
        total = len(method_results)
        successful = sum(1 for r in method_results if r.is_successful())
        partial = sum(1 for r in method_results if r.is_partial())
        failed = sum(1 for r in method_results if r.is_failed())
        has_content = sum(1 for r in method_results if r.has_content())
        has_notes = sum(1 for r in method_results if r.has_notes())
        
        success_rate = successful / total if total > 0 else 0.0
        
        return {
            'total_methods': total,
            'successful': successful,
            'partial': partial,
            'failed': failed,
            'has_content': has_content,
            'has_notes': has_notes,
            'success_rate': success_rate
        }


class JSONReportGenerator:
    """
    JSON 报告生成器
    
    生成纯 JSON 格式的分析报告
    """
    
    def __init__(self, indent: int = 2, ensure_ascii: bool = False):
        """
        初始化 JSON 报告生成器
        
        Args:
            indent: JSON 缩进级别
            ensure_ascii: 是否确保 ASCII 编码
        """
        self.indent = indent
        self.ensure_ascii = ensure_ascii
        self.generation_time = datetime.now()
    
    def generate_report(self, method_results: List[MethodAnalysisResult], output_path: str) -> str:
        """
        生成 JSON 格式的分析报告
        
        Args:
            method_results: 方法分析结果列表
            output_path: 输出文件路径
            
        Returns:
            生成的 JSON 内容
        """
        if not method_results:
            raise ValueError("Method results list cannot be empty")
        
        # 构建报告数据
        report_data = {
            "metadata": {
                "title": "源码上下文分析报告",
                "generation_time": self.generation_time.isoformat(),
                "generator": "源码上下文提取器",
                "total_methods": len(method_results)
            },
            "statistics": self._calculate_statistics(method_results),
            "results": [result.to_dict() for result in method_results]
        }
        
        # 生成 JSON 字符串
        json_content = json.dumps(
            report_data, 
            ensure_ascii=self.ensure_ascii, 
            indent=self.indent
        )
        
        # 确保输出目录存在
        output_path = Path(output_path)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 写入文件
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(json_content)
        except Exception as e:
            raise IOError(f"Failed to write JSON report to {output_path}: {e}")
        
        return json_content
    
    def _calculate_statistics(self, method_results: List[MethodAnalysisResult]) -> Dict[str, Any]:
        """计算统计信息"""
        total = len(method_results)
        successful = sum(1 for r in method_results if r.is_successful())
        partial = sum(1 for r in method_results if r.is_partial())
        failed = sum(1 for r in method_results if r.is_failed())
        has_content = sum(1 for r in method_results if r.has_content())
        has_notes = sum(1 for r in method_results if r.has_notes())
        
        return {
            "total_methods": total,
            "successful_analysis": successful,
            "partial_analysis": partial,
            "failed_analysis": failed,
            "methods_with_content": has_content,
            "methods_with_notes": has_notes,
            "success_rate": successful / total if total > 0 else 0.0
        }


class ReportGeneratorFactory:
    """
    报告生成器工厂类
    
    根据输出格式创建相应的报告生成器
    """
    
    @staticmethod
    def create_generator(output_format: str, **kwargs) -> Union[MarkdownReportGenerator, JSONReportGenerator]:
        """
        创建报告生成器
        
        Args:
            output_format: 输出格式 ("markdown" 或 "json")
            **kwargs: 生成器特定的参数
            
        Returns:
            相应的报告生成器实例
            
        Raises:
            ValueError: 不支持的输出格式
        """
        output_format = output_format.lower().strip()
        
        if output_format in ["markdown", "md"]:
            return MarkdownReportGenerator(**kwargs)
        elif output_format in ["json"]:
            return JSONReportGenerator(**kwargs)
        else:
            raise ValueError(f"Unsupported output format: {output_format}")
    
    @staticmethod
    def get_supported_formats() -> List[str]:
        """获取支持的输出格式列表"""
        return ["markdown", "md", "json"]


# 便利函数
def generate_markdown_report(method_results: List[MethodAnalysisResult], 
                           output_path: str,
                           title: str = "源码上下文分析报告",
                           **kwargs) -> str:
    """
    便利函数：生成 Markdown 报告
    
    Args:
        method_results: 方法分析结果列表
        output_path: 输出文件路径
        title: 报告标题
        **kwargs: 其他生成器参数
        
    Returns:
        生成的报告内容
    """
    generator = MarkdownReportGenerator(title=title, **kwargs)
    return generator.generate_report(method_results, output_path)


def generate_json_report(method_results: List[MethodAnalysisResult], 
                        output_path: str,
                        **kwargs) -> str:
    """
    便利函数：生成 JSON 报告
    
    Args:
        method_results: 方法分析结果列表
        output_path: 输出文件路径
        **kwargs: 其他生成器参数
        
    Returns:
        生成的 JSON 内容
    """
    generator = JSONReportGenerator(**kwargs)
    return generator.generate_report(method_results, output_path)