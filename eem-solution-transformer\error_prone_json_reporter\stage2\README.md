# 第二阶段：API匹配器实现

## 概述

第二阶段实现了基于机器学习的API相似度匹配功能，包括源码解析、向量编码、相似度匹配和结果生成四个核心组件。

## 已实现的组件

### 1. 源码解析器 (SourceCodeParser)

**文件**: `source_code_parser.py`

**功能**:
- 使用 javalang 解析 Java 源码
- 提取方法签名、参数类型、返回类型信息
- 处理泛型、内部类、注解等复杂语法
- 实现错误恢复机制（跳过有问题的文件）
- 支持递归扫描目录中的所有 Java 文件

**特性**:
- 支持多种编码格式（UTF-8, GBK, Latin-1）
- 详细的统计信息收集
- 完整的方法上下文信息提取
- 内部类和嵌套类处理

### 2. 向量编码器 (VectorEncoder)

**文件**: `vector_encoder.py`

**功能**:
- 使用 GraphCodeBERT 模型进行代码语义编码
- 实现批量编码和缓存机制
- 提供降级策略（简单哈希编码）
- 支持方法信息的文本描述构建

**特性**:
- 自动检测 GPU/CPU 设备
- 向量缓存机制（基于 MD5 哈希）
- 批量处理优化
- 模型加载失败时的降级策略
- 详细的编码统计信息

**降级策略**:
当 transformers 库不可用时，使用基于 MD5 哈希的确定性向量生成，确保相同输入产生相同输出。

### 3. 相似度匹配器 (SimilarityMatcher)

**文件**: `similarity_matcher.py`

**功能**:
- 使用 FAISS 建立高效的向量索引
- 实现 TOP-K 相似度搜索
- 支持多种索引类型（Flat, IVF, HNSW）
- 提供相似度分数计算和阈值过滤

**特性**:
- 支持批量查询优化
- 向量标准化（L2 标准化）
- 索引缓存和加载
- FAISS 不可用时的简单向量匹配降级策略
- 可配置的相似度阈值和 TOP-K 数量

**降级策略**:
当 FAISS 库不可用时，使用简单的余弦相似度计算进行匹配。

### 4. 结果生成器 (ResultGenerator)

**文件**: `result_generator.py`

**功能**:
- 生成标准化的迁移建议 JSON 输出
- 计算额外的匹配指标（类名匹配、包名匹配等）
- 实现结果排序和过滤逻辑
- 支持 CSV 格式导出

**特性**:
- 综合匹配分数计算
- 方法名相似度计算（Levenshtein 距离）
- 详细的统计信息生成
- 元数据和配置信息包含
- 多种输出格式支持

## 输出格式

### JSON 格式示例

```json
{
  "suggestions": [
    {
      "missing_method": "getUserById(id)",
      "in_param": {"id": "java.lang.Long"},
      "out_return": "com.example.model.User",
      "context": "获取用户信息的方法",
      "source_location": {
        "package": "com.old.service.impl.UserServiceImpl",
        "class": "UserServiceImpl",
        "line": [123]
      },
      "candidates": [
        {
          "name": "getUser",
          "class": "UserService",
          "package": "com.example.service",
          "params": ["id: Long"],
          "return": "User",
          "context": "获取用户",
          "similarity": 0.85,
          "file_path": "/path/to/UserService.java",
          "class_match": false,
          "package_match": true,
          "param_count_match": true,
          "return_type_match": true,
          "name_similarity": 0.72,
          "match_score": 0.68
        }
      ],
      "match_count": 1
    }
  ],
  "metadata": {
    "generated_at": "2025-08-22T15:55:38.455000",
    "generator_version": "1.0.0",
    "configuration": {
      "include_metadata": true,
      "include_statistics": true,
      "sort_by_similarity": true
    }
  },
  "statistics": {
    "total_errors": 1,
    "errors_with_matches": 1,
    "errors_without_matches": 0,
    "total_candidates": 1,
    "avg_similarity": 0.85,
    "max_similarity": 0.85,
    "min_similarity": 0.85,
    "match_rate": 1.0,
    "class_match_rate": 0.0,
    "package_match_rate": 1.0
  }
}
```

## 使用示例

### 基本使用

```python
from error_prone_json_reporter.stage2.vector_encoder import VectorEncoder
from error_prone_json_reporter.stage2.similarity_matcher import SimilarityMatcher
from error_prone_json_reporter.stage2.result_generator import ResultGenerator

# 初始化组件
encoder = VectorEncoder(cache_dir="./cache", batch_size=32)
matcher = SimilarityMatcher(top_k=3, similarity_threshold=0.1)
generator = ResultGenerator()

# 编码候选方法
candidate_vectors = encoder.encode_methods(candidate_methods)

# 构建索引
matcher.build_index(candidate_vectors, candidate_methods)

# 编码查询方法
query_vectors = encoder.encode_methods(query_methods)

# 执行搜索
search_results = matcher.search_similar(query_vectors)

# 生成建议
suggestions_json = generator.generate_suggestions(
    error_methods, search_results, "output.json"
)
```

### 演示脚本

运行 `demo_stage2.py` 可以看到完整的工作流程演示：

```bash
python error_prone_json_reporter/demo_stage2.py
```

## 测试

### 单元测试

- `test_vector_encoder.py`: 向量编码器测试
- `test_similarity_matcher.py`: 相似度匹配器测试
- `test_result_generator.py`: 结果生成器测试

### 集成测试

- `test_stage2_integration.py`: 完整流程集成测试

运行所有测试：

```bash
python -m pytest error_prone_json_reporter/tests/test_stage2_integration.py -v
```

## 依赖项

### 必需依赖

- `numpy`: 数值计算
- `pathlib`: 路径处理

### 必需依赖（机器学习功能）

- `torch`: PyTorch 深度学习框架
- `transformers`: Hugging Face Transformers 库
- `faiss-cpu` 或 `faiss-gpu`: Facebook AI Similarity Search

**注意**: 这些依赖是必需的，系统不提供降级策略。如果这些库未安装，程序将无法启动。

### 安装命令

```bash
# 安装CPU版本
pip install torch transformers faiss-cpu

# 安装GPU版本（需要CUDA支持）
pip install torch transformers faiss-gpu

# 或者使用conda
conda install pytorch transformers faiss-cpu -c pytorch -c huggingface -c conda-forge
```

## 性能特性

### 向量编码

- 支持批量处理，提高编码效率
- 向量缓存机制，避免重复计算
- GPU 加速支持（如果可用）

### 相似度匹配

- FAISS 高效向量索引
- 支持大规模候选方法库
- 可配置的索引类型和参数

### 内存管理

- 渐进式处理，避免内存溢出
- 缓存机制优化重复查询
- 可配置的批量大小

## 配置选项

### VectorEncoder 配置

- `model_name`: 预训练模型名称
- `cache_dir`: 缓存目录
- `batch_size`: 批处理大小
- `max_length`: 最大序列长度
- `use_cache`: 是否使用缓存

### SimilarityMatcher 配置

- `top_k`: 返回的最相似结果数量
- `similarity_threshold`: 相似度阈值
- `index_type`: 索引类型（"flat", "ivf", "hnsw"）
- `cache_dir`: 缓存目录

### ResultGenerator 配置

- `include_metadata`: 是否包含元数据
- `include_statistics`: 是否包含统计信息
- `sort_by_similarity`: 是否按相似度排序

## 扩展性

系统设计具有良好的扩展性：

1. **新的编码模型**: 可以轻松集成其他预训练模型
2. **新的匹配算法**: 可以添加新的相似度计算方法
3. **新的输出格式**: 可以支持更多输出格式
4. **新的匹配指标**: 可以添加更多匹配质量指标

## 故障排除

### 常见问题

1. **模型加载失败**: 检查网络连接和磁盘空间
2. **内存不足**: 减小批量大小或使用更小的模型
3. **FAISS 安装问题**: 使用 conda 安装或选择 CPU 版本

### 日志级别

设置日志级别为 DEBUG 可以获得更详细的执行信息：

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 未来改进

1. **更多预训练模型支持**: CodeBERT, CodeT5 等
2. **增量索引更新**: 支持动态添加候选方法
3. **分布式处理**: 支持多机并行处理
4. **更智能的匹配策略**: 结合语法和语义信息
5. **交互式结果筛选**: 提供用户界面进行结果调整