package com.cet.eem.fusion.transformer.core.entity.vo;

import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName : EquipmentMonitorVo
 * @Description : 设备监测返回值
 * <AUTHOR> jiang<PERSON><PERSON>uan
 * @Date: 2022-03-07 10:37
 */
@Getter
@Setter
public class EquipmentMonitorVo {
    /**
     * 额定容量
     */
    private Double ratedCapacity;
    /**
     * 运行时长
     */
    private Integer workTime;
    /**
     * 最佳经济负载率
     */
    private Double optimalLoadRate;
    /**
     * A相温度
     */
    private Double temperatureA;
    /**
     * B相温度
     */
    private Double temperatureB;
    /**
     * C相温度
     */
    private Double temperatureC;
    private String pic;
    /**
     * 变压器等级
     */
    private Integer transformerLevel;
}