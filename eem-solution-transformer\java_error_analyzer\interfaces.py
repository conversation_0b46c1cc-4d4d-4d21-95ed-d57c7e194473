"""
接口定义
"""
from abc import ABC, abstractmethod
from typing import List, Dict, Optional
from models import CompilationError, SimilarityResult, ClassFileInfo


class ErrorParserInterface(ABC):
    """错误解析器接口"""
    
    @abstractmethod
    def parse_xml(self, xml_file_path: str) -> List[CompilationError]:
        """解析XML文件，返回编译错误列表"""
        pass


class ErrorGrouperInterface(ABC):
    """错误分组器接口"""
    
    @abstractmethod
    def group_by_file(self, errors: List[CompilationError]) -> Dict[str, List[CompilationError]]:
        """按文件路径分组错误"""
        pass
    
    @abstractmethod
    def deduplicate_errors(self, errors: List[CompilationError]) -> List[CompilationError]:
        """去除重复错误"""
        pass


class ClassNameFinderInterface(ABC):
    """类名查找器接口"""
    
    @abstractmethod
    def find_exact_match(self, class_name: str) -> List[str]:
        """精确查找类名"""
        pass
    
    @abstractmethod
    def find_fuzzy_match(self, class_name: str) -> List[str]:
        """模糊查找类名"""
        pass


class FuzzyMatcherInterface(ABC):
    """模糊匹配器接口"""
    
    @abstractmethod
    def find_similar_classes(self, target_class: str, candidates: List[str]) -> List[SimilarityResult]:
        """查找相似的类"""
        pass
    
    @abstractmethod
    def compare_class_structures(self, original_class: str, candidate_class: str) -> float:
        """比较类结构相似度"""
        pass


class ClassFileReaderInterface(ABC):
    """类文件读取器接口"""
    
    @abstractmethod
    def read_class_file(self, class_name: str, project_path: str) -> Optional[ClassFileInfo]:
        """读取类文件"""
        pass
    
    @abstractmethod
    def find_class_file_path(self, class_name: str, project_path: str) -> Optional[str]:
        """查找类文件路径"""
        pass
    
    @abstractmethod
    def extract_class_structure(self, file_content: str) -> Dict[str, List[str]]:
        """提取类结构信息"""
        pass