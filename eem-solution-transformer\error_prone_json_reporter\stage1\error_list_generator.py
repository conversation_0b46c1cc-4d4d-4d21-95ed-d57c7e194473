"""
错误清单生成器

第一阶段主控制器，整合所有组件生成完整的错误清单JSON
"""

import os
import sys
import time
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Optional

from ..common.interfaces import ErrorDetectorInterface
from ..common.models import ErrorReport, Configuration, ProcessingStats
from .existing_tool_adapter import ExistingToolAdapter, XMLToolAdapter
from .source_code_enhancer import SourceCodeEnhancer, enhance_error_reports
from .json_formatter import JSONFormatter
from .complete_workflow import CompleteWorkflow


class ErrorListGenerator:
    """错误清单生成器主控制器"""
    
    def __init__(self, config: Configuration):
        self.config = config
        self.stats = ProcessingStats()
        self.error_detectors = self._initialize_detectors()
        self.json_formatter = JSONFormatter(config.output_dir)
        
        # 确保输出目录存在
        Path(config.output_dir).mkdir(parents=True, exist_ok=True)
    
    def generate_error_list(self, project_path: str) -> str:
        """
        生成项目错误清单的JSON格式
        
        Args:
            project_path: Java项目路径
            
        Returns:
            生成的JSON文件路径
        """
        print(f"Starting error list generation for project: {project_path}")
        start_time = time.time()
        
        try:
            # 检查是否使用完整工作流程
            if self.config.error_detector == "complete_workflow":
                print("Using complete workflow (inspect_method.py + class_file_reader.py)")
                workflow = CompleteWorkflow(self.config)
                return workflow.run_complete_workflow(project_path)
            
            # 原有的工作流程
            # 1. 选择并运行错误检测工具
            print("Step 1: Detecting errors...")
            raw_errors = self._detect_errors(project_path)
            if not raw_errors:
                print("No errors detected by any tool")
                return self._generate_empty_result()
            
            print(f"Detected {len(raw_errors)} raw errors")
            self.stats.total_errors = len(raw_errors)
            
            # 2. 使用源码解析器增强错误信息
            print("Step 2: Enhancing errors with source code information...")
            enhanced_errors = self._enhance_errors(raw_errors, project_path)
            print(f"Enhanced {len(enhanced_errors)} errors")
            self.stats.processed_errors = len(enhanced_errors)
            
            # 3. 数据质量验证
            print("Step 3: Validating data quality...")
            validated_errors = self._validate_errors(enhanced_errors)
            print(f"Validated {len(validated_errors)} errors")
            self.stats.successful_matches = len(validated_errors)
            
            # 4. 生成JSON输出
            print("Step 4: Generating JSON output...")
            output_path = self._generate_json_output(validated_errors)
            
            # 5. 记录统计信息
            self.stats.processing_time = time.time() - start_time
            self._log_statistics()
            
            print(f"Error list generation completed successfully!")
            print(f"Output saved to: {output_path}")
            return output_path
            
        except Exception as e:
            print(f"Error during error list generation: {e}")
            self.stats.failed_matches = self.stats.total_errors - self.stats.successful_matches
            self.stats.processing_time = time.time() - start_time
            raise
    
    def _initialize_detectors(self) -> Dict[str, ErrorDetectorInterface]:
        """初始化错误检测器"""
        detectors = {}
        
        # 现有工具适配器
        existing_adapter = ExistingToolAdapter()
        if existing_adapter.is_available():
            detectors["existing_tool"] = existing_adapter
            print("Existing tool adapter initialized")
        else:
            print("Warning: Existing tool adapter not available")
        
        # XML工具适配器
        xml_adapter = XMLToolAdapter()
        detectors["xml_tool"] = xml_adapter
        print("XML tool adapter initialized")
        
        return detectors
    
    def _detect_errors(self, project_path: str) -> List[ErrorReport]:
        """检测错误"""
        detector_name = self.config.error_detector
        
        # 尝试使用指定的检测器
        if detector_name in self.error_detectors:
            detector = self.error_detectors[detector_name]
            print(f"Using detector: {detector.get_name()}")
            
            try:
                errors = detector.detect_errors(project_path, self.config)
                if errors:
                    return errors
                else:
                    print(f"No errors found by {detector.get_name()}")
            except Exception as e:
                print(f"Error using detector {detector.get_name()}: {e}")
        
        # 回退策略：尝试其他可用的检测器
        print("Trying fallback detectors...")
        for name, detector in self.error_detectors.items():
            if name != detector_name:
                try:
                    print(f"Trying fallback detector: {detector.get_name()}")
                    errors = detector.detect_errors(project_path, self.config)
                    if errors:
                        print(f"Found {len(errors)} errors using fallback detector {detector.get_name()}")
                        return errors
                except Exception as e:
                    print(f"Fallback detector {detector.get_name()} failed: {e}")
                    continue
        
        return []
    
    def _enhance_errors(self, raw_errors: List[ErrorReport], 
                       project_path: str) -> List[ErrorReport]:
        """使用源码解析器增强错误信息"""
        try:
            # 添加时间戳和源工具信息
            timestamp = datetime.now().isoformat()
            for error in raw_errors:
                if not error.timestamp:
                    error.timestamp = timestamp
                if not error.source_tool:
                    error.source_tool = self.config.error_detector
            
            # 使用源码增强器
            enhanced_errors = enhance_error_reports(raw_errors, project_path)
            
            return enhanced_errors
            
        except Exception as e:
            print(f"Error enhancing errors: {e}")
            # 返回原始错误，确保流程继续
            return raw_errors
    
    def _validate_errors(self, errors: List[ErrorReport]) -> List[ErrorReport]:
        """验证错误数据质量"""
        validated_errors = []
        
        for i, error in enumerate(errors):
            try:
                # 基本验证
                if not error.class_name or not error.missing_method:
                    print(f"Skipping error {i+1}: missing required fields")
                    continue
                
                # 数据清理
                cleaned_error = self._clean_error_data(error)
                
                # 置信度评估
                confidence = self._calculate_confidence(cleaned_error)
                cleaned_error.confidence_score = confidence
                
                validated_errors.append(cleaned_error)
                
            except Exception as e:
                print(f"Error validating error {i+1}: {e}")
                continue
        
        return validated_errors
    
    def _clean_error_data(self, error: ErrorReport) -> ErrorReport:
        """清理错误数据"""
        # 清理包名
        if error.package == "unknown" or not error.package:
            error.package = ""
        
        # 清理方法名
        method_name = error.missing_method.strip()
        if '(' in method_name:
            # 确保方法名格式一致
            base_name = method_name.split('(')[0].strip()
            if base_name:
                error.missing_method = base_name
        
        # 清理参数信息
        if error.in_param:
            cleaned_params = {}
            for key, value in error.in_param.items():
                if value and value != "unknown":
                    cleaned_params[key.strip()] = value.strip()
            error.in_param = cleaned_params
        
        # 清理返回类型
        if error.out_return == "unknown" or not error.out_return:
            error.out_return = "java.lang.Object"
        
        return error
    
    def _calculate_confidence(self, error: ErrorReport) -> float:
        """计算错误报告的置信度"""
        confidence = 1.0
        
        # 根据信息完整性调整置信度
        if not error.package or error.package == "":
            confidence -= 0.1
        
        if not error.in_param:
            confidence -= 0.1
        
        if error.out_return == "java.lang.Object":
            confidence -= 0.05
        
        if not error.line:
            confidence -= 0.1
        
        if not error.context:
            confidence -= 0.05
        
        # 根据源工具调整置信度
        if error.source_tool == "existing_tool":
            confidence += 0.1  # 现有工具更可靠
        
        # 根据方法签名信息调整置信度
        if error.method_signature:
            confidence += 0.05
        
        if error.parameter_types:
            confidence += 0.05
        
        return max(0.0, min(1.0, confidence))
    
    def _generate_json_output(self, errors: List[ErrorReport]) -> str:
        """生成JSON输出"""
        # 生成标准格式
        standard_output = self.json_formatter.format_to_json(
            errors, self.config.errors_json, include_metadata=True
        )
        
        # 生成API匹配格式
        api_matching_file = self.config.errors_json.replace('.json', '_for_matching.json')
        self.json_formatter.format_for_api_matching(errors, api_matching_file)
        
        # 生成Schema文件
        self.json_formatter.save_schema("error_report_schema.json")
        
        # 验证生成的JSON
        valid, message = self.json_formatter.validate_json_format(standard_output)
        if not valid:
            print(f"Warning: Generated JSON validation failed: {message}")
        else:
            print("Generated JSON validation passed")
        
        return str(Path(self.config.output_dir) / self.config.errors_json)
    
    def _generate_empty_result(self) -> str:
        """生成空结果"""
        empty_errors = []
        output_path = self.json_formatter.format_to_json(
            empty_errors, self.config.errors_json, include_metadata=True
        )
        return str(Path(self.config.output_dir) / self.config.errors_json)
    
    def _log_statistics(self):
        """记录统计信息"""
        print("\n=== Error List Generation Statistics ===")
        print(f"Total errors detected: {self.stats.total_errors}")
        print(f"Errors processed: {self.stats.processed_errors}")
        print(f"Errors validated: {self.stats.successful_matches}")
        print(f"Processing time: {self.stats.processing_time:.2f} seconds")
        
        if self.stats.total_errors > 0:
            success_rate = self.stats.successful_matches / self.stats.total_errors
            print(f"Success rate: {success_rate:.2%}")
        
        if self.stats.processing_time > 0:
            processing_rate = self.stats.processed_errors / self.stats.processing_time
            print(f"Processing rate: {processing_rate:.2f} errors/second")
        
        print("=" * 45)


def run_error_list_generation(project_path: str, config: Configuration) -> str:
    """
    运行错误清单生成流程
    
    Args:
        project_path: 项目路径
        config: 配置对象
        
    Returns:
        生成的JSON文件路径
    """
    generator = ErrorListGenerator(config)
    return generator.generate_error_list(project_path)


def main():
    """命令行入口"""
    import argparse
    from ..config.config_manager import ConfigManager
    
    parser = argparse.ArgumentParser(description='Generate error list from Java project')
    parser.add_argument('project_path', help='Path to Java project')
    parser.add_argument('--config', help='Configuration file path', 
                       default='config/config.yaml')
    parser.add_argument('--output-dir', help='Output directory', default='output')
    parser.add_argument('--detector', help='Error detector to use', 
                       choices=['existing_tool', 'xml_tool'], default='existing_tool')
    
    args = parser.parse_args()
    
    try:
        # 加载配置
        config_manager = ConfigManager()
        config = config_manager.load_config(args.config)
        
        # 覆盖命令行参数
        config.project_path = args.project_path
        config.output_dir = args.output_dir
        config.error_detector = args.detector
        
        # 运行生成流程
        output_path = run_error_list_generation(args.project_path, config)
        print(f"\nSuccess! Error list generated at: {output_path}")
        
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()