# Requirements Document

## Introduction

源码上下文提取器是一个用于分析Java代码迁移错误的工具。它能够从错误报告中提取详细的源码信息，包括方法定义、上下文和注释，并生成结构化的分析文档。该工具主要用于帮助开发者理解代码迁移过程中缺失的方法和类，提供完整的源码上下文信息。

## Requirements

### Requirement 1

**User Story:** 作为一个开发者，我希望能够输入类似target_method_test.json结构的问题列表，系统能够自动分析并提取相关的源码信息，以便我能够快速了解缺失方法的完整上下文。

#### Acceptance Criteria

1. WHEN 用户提供包含错误信息的JSON文件 THEN 系统 SHALL 解析文件并提取每个错误项的位置信息
2. WHEN 系统解析错误信息 THEN 系统 SHALL 验证JSON格式的有效性并报告格式错误
3. IF JSON文件包含必需字段（package, class, missing_method, line, location） THEN 系统 SHALL 继续处理
4. WHEN 处理错误列表 THEN 系统 SHALL 为每个错误项记录文件路径和行号信息

### Requirement 2

**User Story:** 作为一个开发者，我希望系统能够在当前项目中查询报错的行信息，获取准确的源文件位置，以便进行后续的源码分析。

#### Acceptance Criteria

1. WHEN 系统获取到文件路径和行号 THEN 系统 SHALL 在当前项目目录中搜索对应的源文件
2. WHEN 找到源文件 THEN 系统 SHALL 读取指定行号的代码内容
3. IF 源文件不存在 THEN 系统 SHALL 记录错误并继续处理其他项目
4. WHEN 读取源文件 THEN 系统 SHALL 验证行号的有效性并处理越界情况
5. WHEN 获取行信息成功 THEN 系统 SHALL 提取该行的完整代码内容和周围上下文

### Requirement 3

**User Story:** 作为一个开发者，我希望系统能够在迁移前的代码文件路径下根据行信息搜索具体信息，找到原始的方法实现，以便了解迁移前的代码结构。

#### Acceptance Criteria

1. WHEN 系统需要查找迁移前代码 THEN 系统 SHALL 在oldcode目录下搜索对应的源文件
2. WHEN 在oldcode目录中找到文件 THEN 系统 SHALL 读取完整的文件内容
3. IF oldcode目录中没有找到文件 THEN 系统 SHALL 尝试在其他可能的路径中搜索
4. WHEN 找到源文件 THEN 系统 SHALL 提取与错误相关的代码段
5. WHEN 搜索完成 THEN 系统 SHALL 记录搜索结果和文件位置信息

### Requirement 4

**User Story:** 作为一个开发者，我希望系统能够使用AST语法树分析找到具体报错的类路径和方法代码，获取完整的方法源码和上下文信息，以便进行准确的代码分析。

#### Acceptance Criteria

1. WHEN 系统获取到Java源文件 THEN 系统 SHALL 使用AST解析器解析文件结构
2. WHEN AST解析成功 THEN 系统 SHALL 定位到指定的类和方法
3. WHEN 找到目标方法 THEN 系统 SHALL 提取完整的方法源码
4. WHEN 提取方法源码 THEN 系统 SHALL 包含方法签名、参数、返回类型和方法体
5. WHEN 分析方法 THEN 系统 SHALL 提取方法的注释和文档
6. WHEN 获取上下文 THEN 系统 SHALL 包含方法所在类的相关信息
7. IF AST解析失败 THEN 系统 SHALL 使用文本解析作为备选方案

### Requirement 5

**User Story:** 作为一个开发者，我希望系统能够将分析结果输出到结构化的Markdown文档，包含指定格式的JSON信息，以便我能够方便地查看和使用分析结果。

#### Acceptance Criteria

1. WHEN 系统完成源码分析 THEN 系统 SHALL 生成包含所有分析结果的Markdown文档
2. WHEN 生成输出 THEN 系统 SHALL 使用指定的JSON格式：missing_method, in_param, out_return, context, content, notes
3. WHEN 输出方法信息 THEN 系统 SHALL 包含完整的方法源码在content字段中
4. WHEN 输出注释信息 THEN 系统 SHALL 在notes字段中包含方法的注释和文档
5. WHEN 生成文档 THEN 系统 SHALL 确保JSON格式的有效性和可读性
6. WHEN 处理多个错误项 THEN 系统 SHALL 为每个项目生成独立的分析结果
7. WHEN 输出完成 THEN 系统 SHALL 保存文档到指定位置并报告处理结果

### Requirement 6

**User Story:** 作为一个开发者，我希望系统能够处理各种异常情况并提供有用的错误信息，确保工具的稳定性和可靠性。

#### Acceptance Criteria

1. WHEN 遇到文件读取错误 THEN 系统 SHALL 记录详细的错误信息并继续处理其他项目
2. WHEN AST解析失败 THEN 系统 SHALL 提供备选的文本解析方法
3. WHEN 方法未找到 THEN 系统 SHALL 记录未找到的原因并提供可能的建议
4. WHEN 处理完成 THEN 系统 SHALL 生成处理报告，包含成功和失败的统计信息
5. IF 输入文件格式错误 THEN 系统 SHALL 提供清晰的格式要求说明
6. WHEN 系统运行 THEN 系统 SHALL 提供进度指示和状态更新