"""
API匹配引擎

集成第二阶段所有组件，实现完整的API相似度匹配流程。
"""

import os
import json
import logging
import time
import gc
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
import numpy as np

from error_prone_json_reporter.common.models import MethodInfo, ErrorReport, Configuration, ProcessingStats
from error_prone_json_reporter.stage2.source_code_parser import SourceCodeParser
from error_prone_json_reporter.stage2.vector_encoder import VectorEncoder
from error_prone_json_reporter.stage2.similarity_matcher import SimilarityMatcher
from error_prone_json_reporter.stage2.result_generator import ResultGenerator
from error_prone_json_reporter.stage2.performance_optimizer import PerformanceOptimizer
from error_prone_json_reporter.stage2.vector_database import VectorDatabase
from error_prone_json_reporter.stage2.component_integrator import ComponentIntegrator


class APIMatchingEngine:
    """
    API匹配引擎
    
    集成源码解析、向量编码、相似度匹配和结果生成等组件，
    实现完整的API相似度匹配流程。支持进度跟踪、检查点恢复和性能优化。
    """
    
    def __init__(self, config: Optional[Configuration] = None):
        """
        初始化API匹配引擎
        
        Args:
            config: 配置对象
        """
        self.logger = logging.getLogger(__name__)
        self.config = config or Configuration()
        
        # 初始化组件
        self.source_parser = SourceCodeParser()
        # 确定向量缓存目录
        cache_dir = self._get_vector_cache_dir()
        
        self.vector_encoder = VectorEncoder(
            model_name=self.config.model_name,
            cache_dir=cache_dir,
            batch_size=self.config.batch_size,
            use_cache=self.config.cache_vectors
        )
        self.similarity_matcher = SimilarityMatcher(
            top_k=self.config.top_k,
            similarity_threshold=self.config.similarity_threshold
        )
        self.result_generator = ResultGenerator()
        
        # 初始化性能优化器
        optimizer_config = {
            'memory_limit': self.config.memory_limit,
            'batch_size': self.config.batch_size,
            'max_workers': self.config.max_workers,
            'model_name': self.config.model_name,
            'enable_memory_monitoring': True,
            'cache_dir': str(Path(self.config.output_dir) / "cache")
        }
        self.performance_optimizer = PerformanceOptimizer(optimizer_config)
        
        # 初始化必要的组件
        self._initialize_required_components()
        
        # 初始化组件集成器
        self.component_integrator = ComponentIntegrator(self.config)
        
        # 集成现有组件到主工作流程
        self._integrate_existing_components()
        
        # 状态管理
        self.current_stage = "initialized"
        self.checkpoint_dir = Path(self.config.output_dir) / "checkpoints"
        self.checkpoint_dir.mkdir(parents=True, exist_ok=True)
        
        # 统计信息
        self.stats = ProcessingStats()
        self.stage_times = {}
        
        # 数据存储
        self.candidate_methods: List[MethodInfo] = []
        self.candidate_vectors: Optional[np.ndarray] = None
        self.error_list: List[Dict[str, Any]] = []
        
        self.logger.info("API匹配引擎初始化完成")
    
    def _initialize_required_components(self):
        """初始化必要的组件"""
        try:
            self.logger.info("初始化必要的组件")
            
            # 1. 初始化错误位置解析器
            from error_prone_json_reporter.stage2.error_location_resolver import ErrorLocationResolver
            self.error_location_resolver = ErrorLocationResolver(self.config)
            self.logger.debug("ErrorLocationResolver初始化完成")
            
            # 2. 初始化遗留代码搜索器
            from error_prone_json_reporter.stage2.legacy_code_searcher import LegacyCodeSearcher
            self.legacy_code_searcher = LegacyCodeSearcher(self.config)
            self.logger.debug("LegacyCodeSearcher初始化完成")
            
            # 3. 初始化增强上下文构建器
            from error_prone_json_reporter.stage2.enhanced_context_builder import EnhancedContextBuilder
            self.enhanced_context_builder = EnhancedContextBuilder(self.config)
            self.logger.debug("EnhancedContextBuilder初始化完成")
            
            self.logger.info("所有必要组件初始化完成")
            
        except Exception as e:
            self.logger.error(f"初始化必要组件失败: {str(e)}")
            raise RuntimeError(f"无法初始化必要组件: {str(e)}")
    
    def _integrate_existing_components(self):
        """集成现有组件到主工作流程"""
        try:
            self.logger.info("开始集成现有组件到主工作流程")
            
            # 集成所有组件
            integration_success = self.component_integrator.integrate_all_components(self)
            
            if integration_success:
                self.logger.info("所有组件集成成功")
                
                # 记录集成状态
                status = self.component_integrator.get_integration_status()
                self.logger.info(f"组件集成状态: {status}")
                
                # 测试集成效果
                test_results = self.component_integrator.test_component_integration(self)
                self.logger.info(f"组件集成测试结果: {test_results}")
                
            else:
                self.logger.warning("部分组件集成失败，将使用基础功能")
                
        except Exception as e:
            self.logger.error(f"组件集成过程中出现错误: {str(e)}")
            self.logger.warning("将继续使用基础功能，不影响主流程")
    
    def _get_vector_cache_dir(self) -> Optional[str]:
        """
        获取向量缓存目录路径
        
        Returns:
            向量缓存目录路径，如果未配置则返回None
        """
        if not self.config.vector_cache_dir:
            return None
            
        # 如果是绝对路径，直接使用
        if os.path.isabs(self.config.vector_cache_dir):
            cache_dir = self.config.vector_cache_dir
        else:
            # 如果是相对路径，相对于输出目录
            cache_dir = os.path.join(self.config.output_dir, self.config.vector_cache_dir)
        
        # 确保目录存在
        Path(cache_dir).mkdir(parents=True, exist_ok=True)
        return cache_dir
    
    def _get_vector_db_path(self) -> str:
        """
        获取向量库文件路径
        
        Returns:
            向量库文件的完整路径
        """
        cache_dir = self._get_vector_cache_dir()
        
        if cache_dir:
            # 修复：使用正确的文件格式，与VectorDatabase一致
            vector_db_path = os.path.join(cache_dir, "vector_library.json")
        else:
            # 如果没有配置vector_cache_dir，使用默认的vectors子目录
            default_cache_dir = os.path.join(self.config.output_dir, "vectors")
            Path(default_cache_dir).mkdir(parents=True, exist_ok=True)
            vector_db_path = os.path.join(default_cache_dir, "vector_library.json")
        
        return vector_db_path
    
    def match_apis(self, errors_json_path: str, src_path: str, 
                   output_path: Optional[str] = None) -> str:
        """
        执行完整的API匹配流程
        
        Args:
            errors_json_path: 错误清单JSON文件路径
            src_path: 新框架源码路径
            output_path: 输出文件路径（可选）
            
        Returns:
            迁移建议JSON字符串
        """
        self.logger.info("开始执行API匹配流程")
        start_time = time.time()
        
        try:
            # 阶段1：加载错误清单
            self._set_stage("loading_errors")
            self.error_list = self._load_error_list(errors_json_path)
            self.stats.total_errors = len(self.error_list)
            
            # 阶段2：解析源码
            self._set_stage("parsing_source")
            self.candidate_methods = self._parse_source_code(src_path)
            self.stats.total_methods = len(self.candidate_methods)
            
            # 阶段3：向量编码
            self._set_stage("encoding_vectors")
            self.candidate_vectors = self._encode_methods()
            
            # 阶段4：构建索引
            self._set_stage("building_index")
            self._build_similarity_index()
            
            # 阶段5：执行匹配
            self._set_stage("matching")
            matches = self._perform_matching()
            
            # 阶段6：生成结果
            self._set_stage("generating_results")
            result_json = self._generate_results(matches, output_path)
            
            # 完成统计
            self.stats.processing_time = time.time() - start_time
            self._set_stage("completed")
            
            self.logger.info(f"API匹配流程完成，总耗时: {self.stats.processing_time:.2f}秒")
            self._log_final_statistics()
            
            return result_json
            
        except Exception as e:
            self.logger.error(f"API匹配流程失败: {str(e)}")
            self._set_stage("failed")
            raise
        finally:
            # 清理内存和资源
            self._cleanup_memory()
            self.performance_optimizer.cleanup()
    
    def resume_from_checkpoint(self, checkpoint_name: str) -> bool:
        """
        从检查点恢复执行
        
        Args:
            checkpoint_name: 检查点名称
            
        Returns:
            是否成功恢复
        """
        try:
            checkpoint_file = self.checkpoint_dir / f"{checkpoint_name}.json"
            if not checkpoint_file.exists():
                self.logger.warning(f"检查点文件不存在: {checkpoint_file}")
                return False
            
            with open(checkpoint_file, 'r', encoding='utf-8') as f:
                checkpoint_data = json.load(f)
            
            # 恢复状态
            self.current_stage = checkpoint_data.get('stage', 'initialized')
            self.stats = ProcessingStats(**checkpoint_data.get('stats', {}))
            
            # 恢复数据
            if 'error_list' in checkpoint_data:
                self.error_list = checkpoint_data['error_list']
            
            if 'candidate_methods_file' in checkpoint_data:
                methods_file = self.checkpoint_dir / checkpoint_data['candidate_methods_file']
                if methods_file.exists():
                    with open(methods_file, 'r', encoding='utf-8') as f:
                        methods_data = json.load(f)
                    self.candidate_methods = [MethodInfo(**m) for m in methods_data]
            
            self.logger.info(f"从检查点 {checkpoint_name} 恢复成功，当前阶段: {self.current_stage}")
            return True
            
        except Exception as e:
            self.logger.error(f"从检查点恢复失败: {str(e)}")
            return False
    
    def save_checkpoint(self, checkpoint_name: str) -> bool:
        """
        保存当前状态到检查点
        
        Args:
            checkpoint_name: 检查点名称
            
        Returns:
            是否成功保存
        """
        try:
            checkpoint_data = {
                'stage': self.current_stage,
                'stats': self.stats.__dict__,
                'timestamp': time.time(),
                'config': self.config.__dict__
            }
            
            # 保存错误列表
            if self.error_list:
                checkpoint_data['error_list'] = self.error_list
            
            # 保存候选方法（单独文件）
            if self.candidate_methods:
                methods_file = f"{checkpoint_name}_methods.json"
                methods_path = self.checkpoint_dir / methods_file
                with open(methods_path, 'w', encoding='utf-8') as f:
                    methods_data = [m.__dict__ for m in self.candidate_methods]
                    json.dump(methods_data, f, ensure_ascii=False, indent=2)
                checkpoint_data['candidate_methods_file'] = methods_file
            
            # 保存检查点
            checkpoint_file = self.checkpoint_dir / f"{checkpoint_name}.json"
            with open(checkpoint_file, 'w', encoding='utf-8') as f:
                json.dump(checkpoint_data, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"检查点 {checkpoint_name} 保存成功")
            return True
            
        except Exception as e:
            self.logger.error(f"保存检查点失败: {str(e)}")
            return False
    
    def get_progress_info(self) -> Dict[str, Any]:
        """
        获取当前进度信息
        
        Returns:
            进度信息字典
        """
        progress = {
            'current_stage': self.current_stage,
            'total_errors': self.stats.total_errors,
            'processed_errors': self.stats.processed_errors,
            'total_methods': self.stats.total_methods,
            'processed_methods': self.stats.processed_methods,
            'successful_matches': self.stats.successful_matches,
            'failed_matches': self.stats.failed_matches,
            'processing_time': self.stats.processing_time,
            'memory_usage': self.stats.memory_usage
        }
        
        # 计算进度百分比
        if self.current_stage == "loading_errors":
            progress['progress_percent'] = 10
        elif self.current_stage == "parsing_source":
            progress['progress_percent'] = 25
        elif self.current_stage == "encoding_vectors":
            progress['progress_percent'] = 50
        elif self.current_stage == "building_index":
            progress['progress_percent'] = 65
        elif self.current_stage == "matching":
            if self.stats.total_errors > 0:
                match_progress = (self.stats.processed_errors / self.stats.total_errors) * 25
                progress['progress_percent'] = 65 + match_progress
            else:
                progress['progress_percent'] = 80
        elif self.current_stage == "generating_results":
            progress['progress_percent'] = 95
        elif self.current_stage == "completed":
            progress['progress_percent'] = 100
        else:
            progress['progress_percent'] = 0
        
        return progress
    
    def _load_error_list(self, errors_json_path: str) -> List[Dict[str, Any]]:
        """加载错误清单"""
        stage_start = time.time()
        
        self.logger.info(f"加载错误清单: {errors_json_path}")
        
        if not os.path.exists(errors_json_path):
            raise FileNotFoundError(f"错误清单文件不存在: {errors_json_path}")
        
        try:
            with open(errors_json_path, 'r', encoding='utf-8') as f:
                error_list = json.load(f)
            
            if not isinstance(error_list, list):
                raise ValueError("错误清单必须是JSON数组格式")
            
            self.logger.info(f"成功加载 {len(error_list)} 个错误")
            self.stage_times['loading_errors'] = time.time() - stage_start
            
            # 保存检查点
            self.save_checkpoint("after_loading_errors")
            
            return error_list
            
        except json.JSONDecodeError as e:
            raise ValueError(f"错误清单JSON格式无效: {str(e)}")
        except Exception as e:
            raise RuntimeError(f"加载错误清单失败: {str(e)}")
    
    def _parse_source_code(self, src_path: str) -> List[MethodInfo]:
        """解析源码"""
        stage_start = time.time()
        
        self.logger.info(f"开始解析源码: {src_path}")
        
        if not os.path.exists(src_path):
            raise FileNotFoundError(f"源码目录不存在: {src_path}")
        
        try:
            methods = self.source_parser.parse_java_files(src_path)
            
            self.logger.info(f"源码解析完成，提取到 {len(methods)} 个方法")
            self.stage_times['parsing_source'] = time.time() - stage_start
            
            # 保存检查点
            self.save_checkpoint("after_parsing_source")
            
            return methods
            
        except Exception as e:
            raise RuntimeError(f"源码解析失败: {str(e)}")
    
    def _encode_methods(self) -> np.ndarray:
        """编码方法向量（包含AI语义分析）"""
        stage_start = time.time()
        
        # 检查是否使用预生成的向量库
        base_init = getattr(self.config, 'base_init', False)
        
        if base_init:
            self.logger.info("检测到base_init=true，尝试加载预生成的向量库")
            
            # 构建向量库路径 - 使用统一的路径获取方法
            vector_db_path = self._get_vector_db_path()
            vector_db = VectorDatabase(vector_db_path)
            
            if vector_db.exists():
                try:
                    # 加载预生成的向量库
                    loaded_methods, loaded_vectors, metadata = vector_db.load()
                    
                    # 更新候选方法和向量
                    self.candidate_methods = loaded_methods
                    self.stats.total_methods = len(loaded_methods)
                    
                    self.logger.info(f"成功加载预生成向量库，包含 {len(loaded_methods)} 个方法")
                    self.stage_times['encoding_vectors'] = time.time() - stage_start
                    
                    # 保存检查点
                    self.save_checkpoint("after_loading_vectors")
                    
                    return loaded_vectors
                    
                except Exception as e:
                    # 取消重新生成功能，直接报错
                    error_msg = f"加载向量库失败: {str(e)}"
                    self.logger.error(error_msg)
                    raise RuntimeError(error_msg)
            else:
                # 修复：恢复自动重建功能，符合需求1
                self.logger.warning(f"向量库文件不存在: {vector_db_path}，将重新生成")
                # 继续执行下面的重新编码逻辑
        
        # 如果没有预生成向量库或加载失败，则重新编码
        self.logger.info(f"开始编码 {len(self.candidate_methods)} 个方法（包含AI语义分析）")
        
        try:
            # 检查是否启用AI增强编码
            use_ai_enhancement = getattr(self.config, 'use_ai_enhancement', True)
            
            if use_ai_enhancement:
                self.logger.info("使用AI增强的向量编码")
                vectors = self._encode_methods_with_ai_analysis()
            else:
                self.logger.info("使用基础向量编码")
                # 使用性能优化器进行基础编码
                method_texts = [self.vector_encoder._build_method_description(method) 
                              for method in self.candidate_methods]
                
                vectors = self.performance_optimizer.optimize_vector_encoding(
                    self.vector_encoder, method_texts
                )
            
            self.logger.info(f"方法编码完成，向量维度: {vectors.shape}")
            
            # 如果配置了base_init，保存向量库以供下次使用
            if base_init:
                try:
                    # 使用统一的路径获取方法
                    vector_db_path = self._get_vector_db_path()
                    vector_db = VectorDatabase(vector_db_path)
                    
                    metadata = {
                        'generated_at': time.strftime('%Y-%m-%d %H:%M:%S'),
                        'src_path': getattr(self.config, 'src_path', ''),
                        'method_count': len(self.candidate_methods),
                        'vector_shape': vectors.shape
                    }
                    
                    vector_db.save(self.candidate_methods, vectors, metadata)
                    self.logger.info(f"向量库已保存，下次可直接加载")
                    
                except Exception as e:
                    self.logger.warning(f"保存向量库失败: {str(e)}")
            
            self.stage_times['encoding_vectors'] = time.time() - stage_start
            
            # 保存检查点
            self.save_checkpoint("after_encoding_vectors")
            
            return vectors
            
        except Exception as e:
            raise RuntimeError(f"方法编码失败: {str(e)}")
    
    def _encode_methods_with_ai_analysis(self) -> np.ndarray:
        """使用增强AI语义分析进行方法编码"""
        from error_prone_json_reporter.stage2.enhanced_ai_semantic_analyzer import EnhancedAISemanticAnalyzer
        from error_prone_json_reporter.stage2.knowledge_base_query_enhancer import KnowledgeBaseQueryEnhancer
        from error_prone_json_reporter.stage2.precise_source_searcher import MethodLocation
        
        # 初始化增强AI语义分析器
        enhanced_analyzer = EnhancedAISemanticAnalyzer(config=self.config)
        kb_enhancer = KnowledgeBaseQueryEnhancer(
            knowledge_base_path=getattr(self.config, 'knowledge_base_path', 'knowledge_base'),
            use_ai_enhancement=True
        )
        
        semantic_analyses = []
        knowledge_base_infos = []
        
        self.logger.info("开始增强AI语义分析...")
        
        # 为每个方法进行增强AI语义分析
        for i, method in enumerate(self.candidate_methods):
            try:
                # 查询知识库信息
                kb_result = kb_enhancer.query_and_enhance(method)
                kb_info = {
                    'found': kb_result.found,
                    'description': kb_result.enhanced_description or kb_result.description,
                    'confidence': kb_result.confidence,
                    'semantic_tags': kb_result.semantic_tags,
                    'replacement_rules': getattr(kb_result, 'replacement_rules', [])
                }
                knowledge_base_infos.append(kb_info)
                
                # 尝试获取实际源代码上下文
                actual_source_context = self._extract_actual_source_context_for_method(method)
                
                # 尝试搜索原始方法实现
                original_method_location = self._search_original_method_implementation(method)
                
                # 使用增强AI语义分析器进行分析
                enhanced_context = enhanced_analyzer.analyze_method_with_enhanced_context(
                    method, actual_source_context, original_method_location, kb_info
                )
                
                # 转换为向量编码所需的格式
                semantic_analysis = {
                    'function_description': enhanced_context.functional_description,
                    'business_purpose': enhanced_context.business_purpose,
                    'parameter_analysis': enhanced_context.parameter_semantics,
                    'return_value_meaning': enhanced_context.return_value_semantics,
                    'usage_scenarios': [enhanced_context.business_purpose],
                    'business_tags': self._extract_business_tags_from_context(enhanced_context),
                    'complexity_score': enhanced_context.semantic_confidence,
                    'importance_score': enhanced_context.integration_quality,
                    'llm_confidence': enhanced_context.semantic_confidence,
                    'context_completeness': enhanced_context.context_completeness,
                    'integration_quality': enhanced_context.integration_quality
                }
                semantic_analyses.append(semantic_analysis)
                
                # 进度报告
                if (i + 1) % 50 == 0:
                    self.logger.info(f"增强AI分析进度: {i+1}/{len(self.candidate_methods)}")
                
            except Exception as e:
                self.logger.warning(f"方法增强AI分析失败 {method.method_name}: {e}")
                # 使用空的分析结果
                semantic_analyses.append({})
                knowledge_base_infos.append({'found': False})
        
        # 记录分析统计
        analyzer_stats = enhanced_analyzer.get_stats()
        self.logger.info(f"增强AI语义分析统计: {analyzer_stats}")
        
        self.logger.info("增强AI语义分析完成，开始优化向量编码...")
        
        # 使用增强特征进行向量编码
        vectors = self.vector_encoder.encode_enhanced_methods(
            self.candidate_methods,
            semantic_analyses,
            knowledge_base_infos
        )
        
        return vectors
    
    def _extract_actual_source_context_for_method(self, method: MethodInfo) -> Optional[str]:
        """为方法提取实际源代码上下文"""
        try:
            if method.file_path and os.path.exists(method.file_path):
                with open(method.file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 查找方法定义位置
                method_pattern = rf'(public|private|protected).*\s+{re.escape(method.method_name)}\s*\('
                match = re.search(method_pattern, content)
                
                if match:
                    lines = content.split('\n')
                    line_num = content[:match.start()].count('\n')
                    
                    # 提取方法周围的上下文
                    start_line = max(0, line_num - 3)
                    end_line = min(len(lines), line_num + 10)
                    
                    return '\n'.join(lines[start_line:end_line])
            
            return method.context if method.context else None
            
        except Exception as e:
            self.logger.debug(f"提取源代码上下文失败 {method.method_name}: {e}")
            return None
    
    def _search_original_method_implementation(self, method: MethodInfo) -> Optional[Any]:
        """搜索原始方法实现"""
        try:
            if hasattr(self, 'legacy_code_searcher') and self.legacy_code_searcher:
                # 使用遗留代码搜索器查找原始实现
                original_method = self.legacy_code_searcher.search_original_method(
                    method, self.legacy_code_searcher.legacy_paths
                )
                return original_method
            
            return None
            
        except Exception as e:
            self.logger.debug(f"搜索原始方法实现失败 {method.method_name}: {e}")
            return None
    
    def _extract_business_tags_from_context(self, enhanced_context) -> List[str]:
        """从增强上下文中提取业务标签"""
        tags = []
        
        # 从业务目的中提取关键词
        if enhanced_context.business_purpose:
            purpose_words = re.findall(r'[\u4e00-\u9fff]+', enhanced_context.business_purpose)
            tags.extend(purpose_words[:3])
        
        # 从功能描述中提取关键词
        if enhanced_context.functional_description:
            desc_words = re.findall(r'[\u4e00-\u9fff]+', enhanced_context.functional_description)
            tags.extend(desc_words[:2])
        
        # 从方法调用中提取
        if enhanced_context.method_calls:
            tags.extend(enhanced_context.method_calls[:3])
        
        return list(set(tags))[:5]  # 去重并限制数量
    
    def _reconstruct_method_code(self, method: MethodInfo) -> str:
        """重构方法代码（用于AI分析）"""
        # 构建基本的方法代码结构
        modifiers = " ".join(method.modifiers) if method.modifiers else "public"
        params = ", ".join(method.parameters) if method.parameters else ""
        
        method_code = f"""
{modifiers} {method.return_type} {method.method_name}({params}) {{
    // {method.context}
    // 方法实现...
}}
"""
        return method_code
    
    def _build_similarity_index(self):
        """构建相似度索引"""
        stage_start = time.time()
        
        self.logger.info("开始构建相似度索引")
        
        try:
            self.similarity_matcher.build_index(self.candidate_vectors, self.candidate_methods)
            
            self.logger.info("相似度索引构建完成")
            self.stage_times['building_index'] = time.time() - stage_start
            
            # 保存检查点
            self.save_checkpoint("after_building_index")
            
        except Exception as e:
            raise RuntimeError(f"构建相似度索引失败: {str(e)}")
    
    def _perform_matching(self) -> List[List[Tuple[MethodInfo, float]]]:
        """执行相似度匹配"""
        stage_start = time.time()
        
        self.logger.info(f"开始执行相似度匹配，处理 {len(self.error_list)} 个错误")
        
        try:
            # 编码错误描述（使用性能优化）
            error_texts = []
            for error in self.error_list:
                error_text = self._build_error_description(error)
                error_texts.append(error_text)
            
            self.logger.info("编码错误描述...")
            query_vectors = self.performance_optimizer.optimize_vector_encoding(
                self.vector_encoder, error_texts
            )
            
            # 执行匹配（使用性能优化）
            self.logger.info("执行相似度搜索...")
            matches = self.performance_optimizer.optimize_similarity_search(
                self.similarity_matcher, query_vectors
            )
            
            # 更新统计信息
            self.stats.processed_errors = len(self.error_list)
            
            # 确保匹配结果数量与错误数量一致
            if len(matches) != len(self.error_list):
                # 补齐匹配结果
                while len(matches) < len(self.error_list):
                    matches.append([])  # 添加空的匹配结果
            
            for match_list in matches:
                if match_list:
                    self.stats.successful_matches += 1
                else:
                    self.stats.failed_matches += 1
            
            self.logger.info(f"相似度匹配完成，成功匹配: {self.stats.successful_matches}, "
                           f"失败匹配: {self.stats.failed_matches}")
            
            self.stage_times['matching'] = time.time() - stage_start
            
            # 保存检查点
            self.save_checkpoint("after_matching")
            
            return matches
            
        except Exception as e:
            raise RuntimeError(f"相似度匹配失败: {str(e)}")
    
    def _generate_results(self, matches: List[List[Tuple[MethodInfo, float]]], 
                         output_path: Optional[str] = None) -> str:
        """生成结果"""
        stage_start = time.time()
        
        self.logger.info("开始生成迁移建议")
        
        try:
            # 确定输出路径
            if not output_path:
                output_path = self.config.output_json
            
            # 生成结果
            result_json = self.result_generator.generate_suggestions(
                self.error_list, matches, output_path
            )
            
            self.logger.info(f"迁移建议生成完成，输出到: {output_path}")
            self.stage_times['generating_results'] = time.time() - stage_start
            
            return result_json
            
        except Exception as e:
            raise RuntimeError(f"生成结果失败: {str(e)}")
    
    def _build_error_description(self, error: Dict[str, Any]) -> str:
        """
        构建增强的错误描述文本
        
        重构后的方法，集成实际源代码上下文、原始方法实现和综合分析
        替换简单JSON字段拼接为综合代码上下文分析
        不使用回退机制，遇到问题直接报错以便解决
        """
        self.logger.debug(f"构建增强错误描述: {error.get('class', '')}.{error.get('missing_method', '')}")
        
        # 1. 确保必要的组件已初始化
        if not hasattr(self, 'error_location_resolver'):
            raise RuntimeError("ErrorLocationResolver组件未初始化")
        
        if not hasattr(self, 'legacy_code_searcher'):
            raise RuntimeError("LegacyCodeSearcher组件未初始化")
        
        if not hasattr(self, 'enhanced_context_builder'):
            raise RuntimeError("EnhancedContextBuilder组件未初始化")
        
        # 2. 解析错误位置并提取当前项目源代码上下文
        error_location = self.error_location_resolver.resolve_error_location(error)
        current_context = None
        
        if error_location:
            current_context = self.error_location_resolver.extract_method_context(
                error_location.absolute_file_path or error_location.file_path, 
                error_location.line_number
            )
        
        # 3. 搜索原始方法实现
        method_info = self._create_method_info_from_error(error)
        if not method_info:
            raise ValueError(f"无法从错误信息创建方法信息: {error}")
        
        original_method = self.legacy_code_searcher.search_original_method(
            method_info, self.legacy_code_searcher.legacy_paths
        )
        
        # 4. 使用增强上下文构建器创建完整上下文
        complete_context = self.enhanced_context_builder.build_complete_context(
            current_context, original_method, error, error_location
        )
        
        if complete_context and complete_context.combined_description:
            self.logger.debug(f"使用完整上下文描述，质量: {complete_context.context_quality}")
            return complete_context.combined_description
        
        # 5. 如果完整上下文构建失败，使用增强的基础描述构建
        enhanced_description = self._build_enhanced_basic_description_with_source_context(
            error, error_location, current_context, original_method
        )
        
        if not enhanced_description or len(enhanced_description.strip()) == 0:
            raise RuntimeError("增强错误描述构建失败，生成的描述为空")
        
        return enhanced_description
    
    def _build_complete_context_for_error(self, error: Dict[str, Any]):
        """为错误构建完整上下文"""
        try:
            # 1. 解析错误位置
            error_location = None
            if hasattr(self, 'error_location_resolver'):
                error_location = self.error_location_resolver.resolve_error_location(error)
            
            # 2. 提取当前项目上下文
            current_context = None
            if error_location and hasattr(self, 'error_location_resolver'):
                current_context = self.error_location_resolver.extract_method_context(
                    error_location.file_path, error_location.line_number
                )
            
            # 3. 搜索原始方法实现
            original_method = None
            if hasattr(self, 'legacy_code_searcher'):
                method_info = self._create_method_info_from_error(error)
                original_method = self.legacy_code_searcher.search_original_method(
                    method_info, self.legacy_code_searcher.legacy_paths
                )
            
            # 4. 构建完整上下文
            if hasattr(self, 'enhanced_context_builder'):
                return self.enhanced_context_builder.build_complete_context(
                    current_context, original_method, error, error_location
                )
            
            return None
            
        except Exception as e:
            self.logger.warning(f"构建完整上下文失败: {str(e)}")
            return None
    
    def _create_method_info_from_error(self, error: Dict[str, Any]):
        """从错误信息创建方法信息对象"""
        from error_prone_json_reporter.common.models import MethodInfo
        
        # 验证必要字段
        class_name = error.get('class', '')
        method_name = error.get('missing_method', '')
        package_name = error.get('package', '')
        
        if not method_name:
            raise ValueError(f"错误信息中缺少method_name: {error}")
        
        # 处理参数信息
        in_param = error.get('in_param', {})
        parameters = []
        if isinstance(in_param, dict):
            parameters = [f"{name}:{type_}" for name, type_ in in_param.items()]
        elif isinstance(in_param, list):
            parameters = in_param
        
        return MethodInfo(
            package=package_name,
            class_name=class_name,
            method_name=method_name,
            parameters=parameters,
            return_type=error.get('out_return', ''),
            context=error.get('context', ''),
            file_path=error.get('location', {}).get('file', '') if error.get('location') else ''
        )
    
    def _build_enhanced_basic_description_with_source_context(self, 
                                                           error: Dict[str, Any],
                                                           error_location: Optional[Any] = None,
                                                           current_context: Optional[Any] = None,
                                                           original_method: Optional[Any] = None) -> str:
        """
        构建集成实际源代码上下文的增强基础描述
        
        Args:
            error: 错误JSON信息
            error_location: 错误位置信息
            current_context: 当前项目方法上下文
            original_method: 原始方法实现
            
        Returns:
            增强的错误描述文本
        """
        try:
            description_parts = []
            
            # 1. 基础方法信息部分（增强版）
            basic_info = self._build_enhanced_method_basic_info(error, error_location)
            if basic_info:
                description_parts.append(basic_info)
            
            # 2. 实际源代码上下文部分（新增）
            if current_context:
                source_context_info = self._build_actual_source_context_section(current_context, error_location)
                if source_context_info:
                    description_parts.append(source_context_info)
            elif error_location:
                # 如果没有方法上下文但有错误位置，尝试提取基础源代码
                basic_source_info = self._build_basic_source_location_section(error_location)
                if basic_source_info:
                    description_parts.append(basic_source_info)
            
            # 3. 方法调用上下文和周围代码（新增）
            if current_context:
                calling_context_info = self._build_method_calling_context_section(current_context)
                if calling_context_info:
                    description_parts.append(calling_context_info)
            
            # 4. 原始方法实现集成（增强版）
            if original_method:
                original_method_info = self._build_integrated_original_method_section(original_method)
                if original_method_info:
                    description_parts.append(original_method_info)
            
            # 5. 综合代码上下文分析（新增）
            comprehensive_analysis = self._build_comprehensive_code_analysis(
                error, current_context, original_method, error_location
            )
            if comprehensive_analysis:
                description_parts.append(comprehensive_analysis)
            
            return '\n\n'.join(description_parts)
            
        except Exception as e:
            self.logger.warning(f"构建增强基础描述失败: {str(e)}")
            return self._build_fallback_description(error)
    
    def _build_enhanced_basic_description(self, error: Dict[str, Any]) -> str:
        """构建增强的基础描述（保留原方法作为兼容性回退）"""
        try:
            description_parts = []
            
            # 1. 基础方法信息部分
            basic_info = self._build_method_basic_info(error)
            if basic_info:
                description_parts.append(basic_info)
            
            # 2. 尝试读取实际源代码
            source_context = self._try_extract_source_context(error)
            if source_context:
                description_parts.append("=== 源代码上下文 ===")
                description_parts.append(source_context)
            
            # 3. 尝试获取原始方法信息
            legacy_info = self._try_extract_legacy_info(error)
            if legacy_info:
                description_parts.append("=== 原始方法信息 ===")
                description_parts.append(legacy_info)
            
            # 4. 技术分析部分
            technical_analysis = self._build_technical_analysis(error)
            if technical_analysis:
                description_parts.append("=== 技术分析 ===")
                description_parts.append(technical_analysis)
            
            return '\n\n'.join(description_parts)
            
        except Exception as e:
            self.logger.warning(f"构建增强基础描述失败: {str(e)}")
            return self._build_fallback_description(error)
    
    def _build_enhanced_method_basic_info(self, error: Dict[str, Any], error_location: Optional[Any] = None) -> str:
        """构建增强的方法基础信息"""
        try:
            parts = []
            
            # 完整方法标识
            package = error.get('package', '')
            class_name = error.get('class', '')
            method_name = error.get('missing_method', '')
            
            if package and class_name and method_name:
                parts.append(f"=== 缺失方法信息 ===")
                parts.append(f"完整标识: {package}.{class_name}.{method_name}")
                parts.append(f"包名: {package}")
                parts.append(f"类名: {class_name}")
                parts.append(f"方法名: {method_name}")
            
            # 方法签名构建（增强版）
            params = error.get('in_param', {})
            if params:
                parts.append(f"输入参数 ({len(params)}个):")
                for name, type_ in params.items():
                    parts.append(f"  - {name}: {type_}")
            else:
                parts.append("输入参数: 无")
            
            # 返回类型
            return_type = error.get('out_return', '')
            if return_type:
                parts.append(f"返回类型: {return_type}")
            
            # 调用上下文
            context = error.get('context', '')
            if context:
                parts.append(f"调用上下文: {context}")
            
            # 增强的位置信息
            if error_location:
                parts.append(f"错误位置: {error_location.file_path}:{error_location.line_number}")
                if error_location.column_number:
                    parts.append(f"列号: {error_location.column_number}")
                if error_location.absolute_file_path:
                    parts.append(f"绝对路径: {error_location.absolute_file_path}")
            else:
                location = error.get('location', {})
                if location:
                    file_path = location.get('file', '')
                    line_number = location.get('line', '')
                    if file_path and line_number:
                        parts.append(f"错误位置: {file_path}:{line_number}")
            
            return '\n'.join(parts)
            
        except Exception as e:
            self.logger.warning(f"构建增强方法基础信息失败: {str(e)}")
            return self._build_method_basic_info(error)
    
    def _build_method_basic_info(self, error: Dict[str, Any]) -> str:
        """构建方法基础信息（保留原方法）"""
        try:
            parts = []
            
            # 完整方法标识
            package = error.get('package', '')
            class_name = error.get('class', '')
            method_name = error.get('missing_method', '')
            
            if package and class_name and method_name:
                parts.append(f"缺失方法: {package}.{class_name}.{method_name}")
            
            # 方法签名构建
            params = error.get('in_param', {})
            if params:
                param_strs = [f"{name}: {type_}" for name, type_ in params.items()]
                parts.append(f"输入参数: {', '.join(param_strs)}")
            else:
                parts.append("输入参数: 无")
            
            # 返回类型
            return_type = error.get('out_return', '')
            if return_type:
                parts.append(f"返回类型: {return_type}")
            
            # 调用上下文
            context = error.get('context', '')
            if context:
                parts.append(f"调用上下文: {context}")
            
            # 位置信息
            location = error.get('location', {})
            if location:
                file_path = location.get('file', '')
                line_number = location.get('line', '')
                if file_path and line_number:
                    parts.append(f"错误位置: {file_path}:{line_number}")
            
            return '\n'.join(parts)
            
        except Exception as e:
            self.logger.warning(f"构建方法基础信息失败: {str(e)}")
            return ''
    
    def _try_extract_source_context(self, error: Dict[str, Any]) -> str:
        """尝试提取源代码上下文"""
        try:
            location = error.get('location', {})
            if not location:
                return ''
            
            file_path = location.get('file', '')
            line_number = location.get('line', 0)
            
            if not file_path or not line_number:
                return ''
            
            # 尝试读取文件内容
            full_file_path = self._find_source_file(file_path)
            if not full_file_path or not os.path.exists(full_file_path):
                return f"源文件未找到: {file_path}"
            
            # 读取文件并提取上下文
            with open(full_file_path, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
            
            # 提取错误行周围的上下文（前后5行）
            start_line = max(0, line_number - 6)
            end_line = min(len(lines), line_number + 5)
            
            context_lines = []
            for i in range(start_line, end_line):
                line_num = i + 1
                line_content = lines[i].rstrip()
                marker = " >>> " if line_num == line_number else "     "
                context_lines.append(f"{line_num:4d}{marker}{line_content}")
            
            return f"文件: {file_path}\n" + '\n'.join(context_lines)
            
        except Exception as e:
            self.logger.warning(f"提取源代码上下文失败: {str(e)}")
            return ''
    
    def _find_source_file(self, relative_path: str) -> Optional[str]:
        """查找源文件的完整路径"""
        try:
            # 常见的Java项目源码路径
            search_paths = [
                self.config.project_path if hasattr(self.config, 'project_path') else os.getcwd(),
                os.path.join(os.getcwd(), 'src', 'main', 'java'),
                os.path.join(os.getcwd(), 'src', 'test', 'java'),
                os.path.join(os.getcwd(), 'src'),
                os.getcwd()
            ]
            
            for base_path in search_paths:
                if not base_path or not os.path.exists(base_path):
                    continue
                
                # 尝试直接拼接
                full_path = os.path.join(base_path, relative_path)
                if os.path.exists(full_path):
                    return full_path
                
                # 尝试递归搜索
                for root, dirs, files in os.walk(base_path):
                    if os.path.basename(relative_path) in files:
                        candidate_path = os.path.join(root, os.path.basename(relative_path))
                        if candidate_path.endswith(relative_path.replace('\\', os.sep).replace('/', os.sep)):
                            return candidate_path
            
            return None
            
        except Exception as e:
            self.logger.warning(f"查找源文件失败: {str(e)}")
            return None
    
    def _try_extract_legacy_info(self, error: Dict[str, Any]) -> str:
        """尝试提取原始方法信息"""
        try:
            if not hasattr(self, 'legacy_code_searcher'):
                return ''
            
            method_info = self._create_method_info_from_error(error)
            if not method_info:
                return ''
            
            original_method = self.legacy_code_searcher.search_original_method(
                method_info, self.legacy_code_searcher.legacy_paths
            )
            
            if not original_method:
                return '原始方法未找到'
            
            parts = []
            parts.append(f"文件: {original_method.file_path}")
            parts.append(f"行号: {original_method.line_number}")
            parts.append(f"方法签名: {original_method.method_signature}")
            
            if original_method.javadoc:
                parts.append("Javadoc:")
                # 限制Javadoc长度
                javadoc = original_method.javadoc[:300] + "..." if len(original_method.javadoc) > 300 else original_method.javadoc
                parts.append(javadoc)
            
            if original_method.method_code:
                parts.append("方法代码:")
                # 限制代码长度
                code = original_method.method_code
                if len(code) > 500:
                    code = code[:500] + "\n... (代码已截断)"
                parts.append(code)
            
            return '\n'.join(parts)
            
        except Exception as e:
            self.logger.warning(f"提取原始方法信息失败: {str(e)}")
            return ''
    
    def _build_technical_analysis(self, error: Dict[str, Any]) -> str:
        """构建技术分析"""
        try:
            analysis_parts = []
            
            # 方法类型分析
            method_name = error.get('missing_method', '').lower()
            method_type = self._analyze_method_type(method_name)
            if method_type:
                analysis_parts.append(f"方法类型: {method_type}")
            
            # 参数复杂度分析
            params = error.get('in_param', {})
            param_analysis = self._analyze_parameter_complexity(params)
            if param_analysis:
                analysis_parts.append(f"参数复杂度: {param_analysis}")
            
            # 业务领域分析
            domain = self._analyze_business_domain_from_error(error)
            if domain:
                analysis_parts.append(f"业务领域: {domain}")
            
            # 使用模式分析
            usage_pattern = self._analyze_usage_pattern(error)
            if usage_pattern:
                analysis_parts.append(f"使用模式: {usage_pattern}")
            
            return '\n'.join(analysis_parts)
            
        except Exception as e:
            self.logger.warning(f"构建技术分析失败: {str(e)}")
            return ''
    
    def _analyze_method_type(self, method_name: str) -> str:
        """分析方法类型"""
        if method_name.startswith('get'):
            return '查询方法'
        elif method_name.startswith(('set', 'update')):
            return '更新方法'
        elif method_name.startswith(('create', 'add', 'insert')):
            return '创建方法'
        elif method_name.startswith(('delete', 'remove')):
            return '删除方法'
        elif method_name.startswith(('validate', 'check', 'verify')):
            return '验证方法'
        elif method_name.startswith('calculate'):
            return '计算方法'
        elif method_name.startswith('process'):
            return '处理方法'
        else:
            return '业务方法'
    
    def _analyze_parameter_complexity(self, params: Dict[str, str]) -> str:
        """分析参数复杂度"""
        if not params:
            return '无参数'
        elif len(params) == 1:
            return '简单参数'
        elif len(params) <= 3:
            return '中等复杂度'
        else:
            return '高复杂度'
    
    def _analyze_business_domain_from_error(self, error: Dict[str, Any]) -> str:
        """从错误信息分析业务领域"""
        try:
            # 从包名、类名、方法名中提取关键词
            text_parts = [
                error.get('package', ''),
                error.get('class', ''),
                error.get('missing_method', ''),
                error.get('context', '')
            ]
            
            combined_text = ' '.join(text_parts).lower()
            
            # 业务领域关键词映射
            domain_keywords = {
                '能源管理': ['energy', 'power', 'electric', 'consumption', 'meter', 'node'],
                '用户管理': ['user', 'account', 'login', 'auth', 'profile', 'permission'],
                '数据处理': ['data', 'query', 'search', 'filter', 'report', 'export'],
                '系统配置': ['system', 'config', 'setting', 'admin', 'manage', 'property'],
                '服务接口': ['service', 'api', 'rest', 'http', 'client', 'request']
            }
            
            matched_domains = []
            for domain, keywords in domain_keywords.items():
                if any(keyword in combined_text for keyword in keywords):
                    matched_domains.append(domain)
            
            return ', '.join(matched_domains) if matched_domains else '通用业务'
            
        except Exception as e:
            self.logger.warning(f"分析业务领域失败: {str(e)}")
            return '未知领域'
    
    def _analyze_usage_pattern(self, error: Dict[str, Any]) -> str:
        """分析使用模式"""
        try:
            patterns = []
            
            # 基于返回类型分析
            return_type = error.get('out_return', '')
            if 'List' in return_type or 'Collection' in return_type:
                patterns.append('批量数据')
            elif 'Map' in return_type:
                patterns.append('键值映射')
            elif 'void' in return_type:
                patterns.append('操作执行')
            elif return_type:
                patterns.append('单值返回')
            
            # 基于参数分析
            params = error.get('in_param', {})
            if any('id' in param.lower() for param in params.keys()):
                patterns.append('ID查询')
            if any('filter' in param.lower() or 'condition' in param.lower() for param in params.keys()):
                patterns.append('条件筛选')
            
            return ', '.join(patterns) if patterns else '标准调用'
            
        except Exception as e:
            self.logger.warning(f"分析使用模式失败: {str(e)}")
            return '未知模式'
    
    def _build_actual_source_context_section(self, current_context: Any, error_location: Optional[Any] = None) -> str:
        """构建实际源代码上下文部分"""
        try:
            parts = []
            parts.append("=== 实际源代码上下文 ===")
            
            # 方法定义
            if hasattr(current_context, 'method_definition') and current_context.method_definition:
                parts.append(f"方法定义: {current_context.method_definition}")
            
            # 方法体摘要（限制长度）
            if hasattr(current_context, 'method_body') and current_context.method_body:
                method_body = current_context.method_body.strip()
                if len(method_body) > 500:
                    method_body = method_body[:500] + "\n... (方法体已截断)"
                parts.append("方法体:")
                parts.append(method_body)
            
            # 导入语句
            if hasattr(current_context, 'imports') and current_context.imports:
                parts.append(f"相关导入 ({len(current_context.imports)}个):")
                for imp in current_context.imports[:10]:  # 限制显示数量
                    parts.append(f"  {imp}")
                if len(current_context.imports) > 10:
                    parts.append(f"  ... 还有 {len(current_context.imports) - 10} 个导入")
            
            # 类上下文
            if hasattr(current_context, 'class_context') and current_context.class_context:
                parts.append("类上下文:")
                class_context = current_context.class_context.strip()
                if len(class_context) > 300:
                    class_context = class_context[:300] + "..."
                parts.append(class_context)
            
            return '\n'.join(parts)
            
        except Exception as e:
            self.logger.warning(f"构建实际源代码上下文部分失败: {str(e)}")
            return ''
    
    def _build_basic_source_location_section(self, error_location: Any) -> str:
        """构建基础源代码位置部分"""
        try:
            parts = []
            parts.append("=== 源代码位置信息 ===")
            
            parts.append(f"文件: {error_location.file_path}")
            parts.append(f"行号: {error_location.line_number}")
            
            if hasattr(error_location, 'column_number') and error_location.column_number:
                parts.append(f"列号: {error_location.column_number}")
            
            # 周围代码行
            if hasattr(error_location, 'surrounding_lines') and error_location.surrounding_lines:
                parts.append("周围代码:")
                for line in error_location.surrounding_lines[:15]:  # 限制行数
                    parts.append(line)
            
            return '\n'.join(parts)
            
        except Exception as e:
            self.logger.warning(f"构建基础源代码位置部分失败: {str(e)}")
            return ''
    
    def _build_method_calling_context_section(self, current_context: Any) -> str:
        """构建方法调用上下文和周围代码部分"""
        try:
            parts = []
            parts.append("=== 方法调用上下文和周围代码 ===")
            
            # 调用上下文
            if hasattr(current_context, 'calling_context') and current_context.calling_context:
                parts.append("调用上下文:")
                for context_line in current_context.calling_context:
                    parts.append(context_line)
            
            # 局部变量
            if hasattr(current_context, 'local_variables') and current_context.local_variables:
                parts.append(f"局部变量 ({len(current_context.local_variables)}个):")
                for var in current_context.local_variables[:15]:  # 限制数量
                    parts.append(f"  - {var}")
                if len(current_context.local_variables) > 15:
                    parts.append(f"  ... 还有 {len(current_context.local_variables) - 15} 个变量")
            
            # 方法调用
            if hasattr(current_context, 'method_calls') and current_context.method_calls:
                parts.append(f"方法调用 ({len(current_context.method_calls)}个):")
                for call in current_context.method_calls[:15]:  # 限制数量
                    parts.append(f"  - {call}")
                if len(current_context.method_calls) > 15:
                    parts.append(f"  ... 还有 {len(current_context.method_calls) - 15} 个调用")
            
            return '\n'.join(parts)
            
        except Exception as e:
            self.logger.warning(f"构建方法调用上下文部分失败: {str(e)}")
            return ''
    
    def _build_integrated_original_method_section(self, original_method: Any) -> str:
        """构建集成的原始方法实现部分"""
        try:
            parts = []
            parts.append("=== 原始方法实现 ===")
            
            parts.append(f"文件路径: {original_method.file_path}")
            if hasattr(original_method, 'line_number'):
                parts.append(f"行号: {original_method.line_number}")
            
            # 方法签名
            if hasattr(original_method, 'method_signature') and original_method.method_signature:
                parts.append(f"方法签名: {original_method.method_signature}")
            
            # Javadoc文档
            if hasattr(original_method, 'javadoc') and original_method.javadoc:
                parts.append("Javadoc文档:")
                javadoc = original_method.javadoc.strip()
                if len(javadoc) > 600:
                    javadoc = javadoc[:600] + "... (文档已截断)"
                parts.append(javadoc)
            
            # 注解
            if hasattr(original_method, 'annotations') and original_method.annotations:
                parts.append(f"注解: {', '.join(original_method.annotations)}")
            
            # 方法代码
            if hasattr(original_method, 'method_code') and original_method.method_code:
                parts.append("完整方法代码:")
                method_code = original_method.method_code.strip()
                if len(method_code) > 1200:
                    method_code = method_code[:1200] + "\n... (代码已截断)"
                parts.append(method_code)
            
            # 依赖信息
            if hasattr(original_method, 'dependencies') and original_method.dependencies:
                parts.append(f"依赖导入 ({len(original_method.dependencies)}个):")
                for dep in original_method.dependencies[:10]:
                    parts.append(f"  - {dep}")
                if len(original_method.dependencies) > 10:
                    parts.append(f"  ... 还有 {len(original_method.dependencies) - 10} 个依赖")
            
            return '\n'.join(parts)
            
        except Exception as e:
            self.logger.warning(f"构建集成原始方法部分失败: {str(e)}")
            return ''
    
    def _build_comprehensive_code_analysis(self, error: Dict[str, Any], 
                                         current_context: Optional[Any] = None,
                                         original_method: Optional[Any] = None,
                                         error_location: Optional[Any] = None) -> str:
        """构建综合代码上下文分析"""
        try:
            parts = []
            parts.append("=== 综合代码上下文分析 ===")
            
            # 分析可用信息质量
            info_quality = self._analyze_available_information_quality(
                current_context, original_method, error_location
            )
            parts.append(f"信息完整度: {info_quality}")
            
            # 方法复杂度分析
            complexity_analysis = self._analyze_comprehensive_method_complexity(
                current_context, original_method
            )
            if complexity_analysis:
                parts.append(f"复杂度分析: {complexity_analysis}")
            
            # 业务领域推断
            business_domain = self._infer_business_domain_from_all_sources(
                error, current_context, original_method
            )
            if business_domain:
                parts.append(f"业务领域: {business_domain}")
            
            # 技术栈分析
            tech_stack = self._analyze_technology_stack(current_context, original_method)
            if tech_stack:
                parts.append(f"技术栈: {', '.join(tech_stack)}")
            
            # 使用模式综合分析
            usage_patterns = self._analyze_comprehensive_usage_patterns(
                error, current_context, original_method
            )
            if usage_patterns:
                parts.append(f"使用模式: {', '.join(usage_patterns)}")
            
            # 迁移建议
            migration_hints = self._generate_migration_hints(
                error, current_context, original_method
            )
            if migration_hints:
                parts.append("迁移建议:")
                for hint in migration_hints:
                    parts.append(f"  - {hint}")
            
            return '\n'.join(parts)
            
        except Exception as e:
            self.logger.warning(f"构建综合代码上下文分析失败: {str(e)}")
            return ''
    
    def _build_fallback_description(self, error: Dict[str, Any]) -> str:
        """构建回退描述（原始简单版本）"""
        parts = []
        
        # 添加包和类信息
        if error.get('package'):
            parts.append(f"package {error['package']}")
        
        if error.get('class'):
            parts.append(f"class {error['class']}")
        
        # 添加方法信息
        method_name = error.get('missing_method', '')
        if '(' in method_name:
            # 方法名包含参数
            parts.append(f"method {method_name}")
        else:
            # 构建完整方法签名
            params = error.get('in_param', {})
            if params:
                param_strs = [f"{name}: {type_}" for name, type_ in params.items()]
                method_signature = f"{method_name}({', '.join(param_strs)})"
            else:
                method_signature = f"{method_name}()"
            
            return_type = error.get('out_return', '')
            if return_type:
                method_signature += f" -> {return_type}"
            
            parts.append(f"method {method_signature}")
        
        # 添加上下文信息
        if error.get('context'):
            parts.append(f"context {error['context']}")
        
        return " ".join(parts)
    
    def _set_stage(self, stage: str):
        """设置当前阶段"""
        self.current_stage = stage
        self.logger.info(f"进入阶段: {stage}")
    
    def _log_final_statistics(self):
        """记录最终统计信息"""
        self.logger.info("=== API匹配统计信息 ===")
        self.logger.info(f"总错误数: {self.stats.total_errors}")
        self.logger.info(f"总方法数: {self.stats.total_methods}")
        self.logger.info(f"成功匹配: {self.stats.successful_matches}")
        self.logger.info(f"失败匹配: {self.stats.failed_matches}")
        self.logger.info(f"匹配成功率: {self.stats.get_success_rate():.1%}")
        self.logger.info(f"总处理时间: {self.stats.processing_time:.2f}秒")
        self.logger.info(f"处理速度: {self.stats.get_processing_rate():.2f}错误/秒")
        
        # 各阶段耗时
        self.logger.info("=== 各阶段耗时 ===")
        for stage, duration in self.stage_times.items():
            self.logger.info(f"{stage}: {duration:.2f}秒")
    
    def _cleanup_memory(self):
        """清理内存"""
        try:
            # 清理大型数据结构
            if hasattr(self, 'candidate_vectors') and self.candidate_vectors is not None:
                del self.candidate_vectors
                self.candidate_vectors = None
            
            # 清理组件集成器资源
            if hasattr(self, 'component_integrator') and self.component_integrator:
                self.component_integrator.cleanup()
            
            # 强制垃圾回收
            gc.collect()
            
            self.logger.debug("内存清理完成")
            
        except Exception as e:
            self.logger.warning(f"内存清理失败: {str(e)}")
    
    def get_component_stats(self) -> Dict[str, Any]:
        """获取各组件统计信息"""
        stats = {
            'source_parser': self.source_parser.get_parsing_stats(),
            'vector_encoder': self.vector_encoder.get_stats(),
            'similarity_matcher': self.similarity_matcher.get_stats(),
            'result_generator': self.result_generator.get_stats(),
            'performance_optimizer': self.performance_optimizer.get_performance_stats(),
            'engine_stats': self.stats.__dict__,
            'stage_times': self.stage_times
        }
        
        # 添加组件集成状态
        if hasattr(self, 'component_integrator') and self.component_integrator:
            stats['component_integration'] = self.component_integrator.get_integration_status()
        
        return stats
    
    def _analyze_available_information_quality(self, current_context: Optional[Any] = None,
                                             original_method: Optional[Any] = None,
                                             error_location: Optional[Any] = None) -> str:
        """分析可用信息质量"""
        try:
            quality_score = 0
            quality_factors = []
            
            # 当前上下文质量评估
            if current_context:
                quality_score += 30
                quality_factors.append("当前上下文")
                
                if hasattr(current_context, 'method_body') and current_context.method_body:
                    quality_score += 20
                    quality_factors.append("方法体")
                
                if hasattr(current_context, 'method_calls') and current_context.method_calls:
                    quality_score += 10
                    quality_factors.append("方法调用")
            
            # 原始方法质量评估
            if original_method:
                quality_score += 25
                quality_factors.append("原始方法")
                
                if hasattr(original_method, 'method_code') and original_method.method_code:
                    quality_score += 15
                    quality_factors.append("原始代码")
                
                if hasattr(original_method, 'javadoc') and original_method.javadoc:
                    quality_score += 10
                    quality_factors.append("文档")
            
            # 错误位置质量评估
            if error_location:
                quality_score += 10
                quality_factors.append("错误位置")
                
                if hasattr(error_location, 'surrounding_lines') and error_location.surrounding_lines:
                    quality_score += 5
                    quality_factors.append("周围代码")
            
            # 质量等级判断
            if quality_score >= 80:
                quality_level = "高质量"
            elif quality_score >= 50:
                quality_level = "中等质量"
            elif quality_score >= 20:
                quality_level = "基础质量"
            else:
                quality_level = "低质量"
            
            return f"{quality_level} (评分: {quality_score}/100, 包含: {', '.join(quality_factors)})"
            
        except Exception as e:
            self.logger.warning(f"分析信息质量失败: {str(e)}")
            return "未知质量"
    
    def _analyze_comprehensive_method_complexity(self, current_context: Optional[Any] = None,
                                               original_method: Optional[Any] = None) -> str:
        """分析综合方法复杂度"""
        try:
            complexity_indicators = []
            
            # 基于当前上下文分析
            if current_context:
                if hasattr(current_context, 'method_calls') and current_context.method_calls:
                    call_count = len(current_context.method_calls)
                    if call_count > 15:
                        complexity_indicators.append("高方法调用复杂度")
                    elif call_count > 5:
                        complexity_indicators.append("中等方法调用复杂度")
                
                if hasattr(current_context, 'local_variables') and current_context.local_variables:
                    var_count = len(current_context.local_variables)
                    if var_count > 10:
                        complexity_indicators.append("多局部变量")
            
            # 基于原始方法分析
            if original_method and hasattr(original_method, 'method_code') and original_method.method_code:
                code = original_method.method_code.lower()
                
                # 代码行数分析
                lines = len(code.split('\n'))
                if lines > 100:
                    complexity_indicators.append("超长方法")
                elif lines > 50:
                    complexity_indicators.append("长方法")
                elif lines > 20:
                    complexity_indicators.append("中等长度方法")
                else:
                    complexity_indicators.append("短方法")
                
                # 控制结构分析
                control_structures = []
                if 'if' in code or 'switch' in code:
                    control_structures.append("条件逻辑")
                if 'for' in code or 'while' in code:
                    control_structures.append("循环逻辑")
                if 'try' in code or 'catch' in code:
                    control_structures.append("异常处理")
                
                if control_structures:
                    complexity_indicators.extend(control_structures)
            
            return ', '.join(complexity_indicators) if complexity_indicators else '简单方法'
            
        except Exception as e:
            self.logger.warning(f"分析方法复杂度失败: {str(e)}")
            return '未知复杂度'
    
    def _infer_business_domain_from_all_sources(self, error: Dict[str, Any],
                                              current_context: Optional[Any] = None,
                                              original_method: Optional[Any] = None) -> str:
        """从所有来源推断业务领域"""
        try:
            domain_keywords = {
                '能源管理': ['energy', 'power', 'electric', 'consumption', 'meter', 'node', 'device'],
                '用户管理': ['user', 'account', 'login', 'auth', 'profile', 'permission', 'role'],
                '数据处理': ['data', 'query', 'search', 'filter', 'report', 'export', 'import'],
                '系统配置': ['system', 'config', 'setting', 'admin', 'manage', 'property'],
                '服务接口': ['service', 'api', 'rest', 'http', 'client', 'request', 'response'],
                '数据库操作': ['dao', 'repository', 'entity', 'table', 'sql', 'database'],
                '业务逻辑': ['business', 'logic', 'process', 'workflow', 'rule', 'validate']
            }
            
            # 收集所有文本信息
            text_sources = []
            
            # 从错误信息收集
            text_sources.extend([
                error.get('package', ''),
                error.get('class', ''),
                error.get('missing_method', ''),
                error.get('context', '')
            ])
            
            # 从当前上下文收集
            if current_context:
                if hasattr(current_context, 'method_definition'):
                    text_sources.append(current_context.method_definition or '')
                if hasattr(current_context, 'imports'):
                    text_sources.extend(current_context.imports or [])
            
            # 从原始方法收集
            if original_method:
                text_sources.append(original_method.file_path or '')
                if hasattr(original_method, 'javadoc'):
                    text_sources.append(original_method.javadoc or '')
                if hasattr(original_method, 'method_code'):
                    text_sources.append(original_method.method_code or '')
            
            # 合并所有文本并转为小写
            combined_text = ' '.join(text_sources).lower()
            
            # 匹配业务领域
            matched_domains = []
            for domain, keywords in domain_keywords.items():
                match_count = sum(1 for keyword in keywords if keyword in combined_text)
                if match_count > 0:
                    matched_domains.append((domain, match_count))
            
            # 按匹配度排序
            matched_domains.sort(key=lambda x: x[1], reverse=True)
            
            if matched_domains:
                return ', '.join([domain for domain, _ in matched_domains[:3]])
            else:
                return '通用业务'
                
        except Exception as e:
            self.logger.warning(f"推断业务领域失败: {str(e)}")
            return '未知领域'
    
    def _analyze_technology_stack(self, current_context: Optional[Any] = None,
                                original_method: Optional[Any] = None) -> List[str]:
        """分析技术栈"""
        try:
            tech_stack = []
            
            # 从导入语句分析
            if current_context and hasattr(current_context, 'imports') and current_context.imports:
                import_text = ' '.join(current_context.imports).lower()
                
                if 'springframework' in import_text:
                    tech_stack.append('Spring Framework')
                if 'jackson' in import_text or 'gson' in import_text:
                    tech_stack.append('JSON处理')
                if 'hibernate' in import_text:
                    tech_stack.append('Hibernate ORM')
                if 'mybatis' in import_text:
                    tech_stack.append('MyBatis')
                if 'redis' in import_text:
                    tech_stack.append('Redis')
                if 'mysql' in import_text or 'oracle' in import_text:
                    tech_stack.append('关系数据库')
            
            # 从原始方法代码分析
            if original_method and hasattr(original_method, 'method_code') and original_method.method_code:
                code = original_method.method_code.lower()
                
                if '@autowired' in code or '@resource' in code:
                    tech_stack.append('依赖注入')
                if '@transactional' in code:
                    tech_stack.append('事务管理')
                if 'resttemplate' in code or 'httpclient' in code:
                    tech_stack.append('HTTP客户端')
                if 'sql' in code or 'query' in code:
                    tech_stack.append('数据库查询')
            
            # 从注解分析
            if original_method and hasattr(original_method, 'annotations') and original_method.annotations:
                annotations = ' '.join(original_method.annotations).lower()
                
                if '@service' in annotations:
                    tech_stack.append('Spring Service')
                if '@controller' in annotations or '@restcontroller' in annotations:
                    tech_stack.append('Spring MVC')
                if '@repository' in annotations:
                    tech_stack.append('数据访问层')
            
            return list(set(tech_stack))  # 去重
            
        except Exception as e:
            self.logger.warning(f"分析技术栈失败: {str(e)}")
            return []
    
    def _analyze_comprehensive_usage_patterns(self, error: Dict[str, Any],
                                            current_context: Optional[Any] = None,
                                            original_method: Optional[Any] = None) -> List[str]:
        """分析综合使用模式"""
        try:
            patterns = []
            
            # 基于方法名分析
            method_name = error.get('missing_method', '').lower()
            if method_name.startswith('get'):
                patterns.append('数据查询')
            elif method_name.startswith(('set', 'update')):
                patterns.append('数据更新')
            elif method_name.startswith(('create', 'add', 'insert')):
                patterns.append('数据创建')
            elif method_name.startswith(('delete', 'remove')):
                patterns.append('数据删除')
            elif method_name.startswith(('validate', 'check', 'verify')):
                patterns.append('数据验证')
            elif method_name.startswith('calculate'):
                patterns.append('数据计算')
            elif method_name.startswith('process'):
                patterns.append('业务处理')
            
            # 基于参数分析
            params = error.get('in_param', {})
            if not params:
                patterns.append('无参调用')
            elif len(params) == 1:
                patterns.append('单参调用')
            else:
                patterns.append('多参调用')
            
            # 基于返回类型分析
            return_type = error.get('out_return', '')
            if 'List' in return_type or 'Collection' in return_type:
                patterns.append('批量数据返回')
            elif 'Map' in return_type:
                patterns.append('键值对返回')
            elif 'void' in return_type:
                patterns.append('操作执行')
            elif return_type:
                patterns.append('单值返回')
            
            # 基于原始方法代码分析
            if original_method and hasattr(original_method, 'method_code') and original_method.method_code:
                code = original_method.method_code.lower()
                
                if 'select' in code or 'query' in code:
                    patterns.append('数据库查询')
                if 'insert' in code or 'save' in code:
                    patterns.append('数据库插入')
                if 'update' in code:
                    patterns.append('数据库更新')
                if 'delete' in code:
                    patterns.append('数据库删除')
                if 'http' in code or 'rest' in code:
                    patterns.append('远程调用')
                if 'cache' in code or 'redis' in code:
                    patterns.append('缓存操作')
            
            return list(set(patterns))  # 去重
            
        except Exception as e:
            self.logger.warning(f"分析使用模式失败: {str(e)}")
            return []
    
    def _generate_migration_hints(self, error: Dict[str, Any],
                                current_context: Optional[Any] = None,
                                original_method: Optional[Any] = None) -> List[str]:
        """生成迁移建议"""
        try:
            hints = []
            
            # 基于方法名的建议
            method_name = error.get('missing_method', '')
            if method_name:
                hints.append(f"寻找新框架中名称相似的方法，如 {method_name}*")
            
            # 基于业务功能的建议
            if method_name.lower().startswith('get'):
                hints.append("查找新框架中的查询相关API")
            elif method_name.lower().startswith(('create', 'add')):
                hints.append("查找新框架中的创建相关API")
            elif method_name.lower().startswith(('update', 'modify')):
                hints.append("查找新框架中的更新相关API")
            elif method_name.lower().startswith(('delete', 'remove')):
                hints.append("查找新框架中的删除相关API")
            
            # 基于参数的建议
            params = error.get('in_param', {})
            if params:
                hints.append(f"注意参数类型匹配：{', '.join(f'{k}:{v}' for k, v in params.items())}")
            
            # 基于返回类型的建议
            return_type = error.get('out_return', '')
            if return_type:
                hints.append(f"确保返回类型兼容：{return_type}")
            
            # 基于原始方法的建议
            if original_method and hasattr(original_method, 'javadoc') and original_method.javadoc:
                hints.append("参考原始方法的Javadoc文档理解业务逻辑")
            
            if original_method and hasattr(original_method, 'method_code') and original_method.method_code:
                code = original_method.method_code.lower()
                if 'sql' in code:
                    hints.append("可能需要重写SQL查询逻辑")
                if '@transactional' in code:
                    hints.append("注意事务管理的迁移")
                if 'cache' in code:
                    hints.append("注意缓存逻辑的迁移")
            
            return hints[:5]  # 限制建议数量
            
        except Exception as e:
            self.logger.warning(f"生成迁移建议失败: {str(e)}")
            return []
    
    def validate_configuration(self) -> List[str]:
        """
        验证配置
        
        Returns:
            验证错误列表
        """
        errors = []
        
        try:
            self.config.validate()
        except ValueError as e:
            errors.append(f"配置验证失败: {str(e)}")
        
        # 检查模型可用性
        if not self.vector_encoder.model_loaded:
            errors.append("GraphCodeBERT模型加载失败")
        
        # 检查输出目录
        output_dir = Path(self.config.output_dir)
        if not output_dir.exists():
            try:
                output_dir.mkdir(parents=True, exist_ok=True)
            except Exception as e:
                errors.append(f"无法创建输出目录: {str(e)}")
        
        return errors