# 需求文档

## 介绍

Java代码迁移API匹配工具是一个基于机器学习的Python脚本，用于分析Java项目中的缺失方法错误，并通过语义相似度匹配在新框架中找到对应的替代API。该工具使用GraphCodeBERT模型进行代码语义理解，通过FAISS向量检索技术快速找到最相似的候选方法，生成结构化的迁移建议清单。

## 需求

### 需求 1

**用户故事：** 作为Java开发工程师，我希望能够从现有的错误清单JSON文件中读取缺失方法信息，以便进行API迁移分析。

#### 验收标准

1. 当系统接收到错误清单JSON文件路径时，系统应当能够解析文件并提取缺失方法信息
2. 系统应当能够识别JSON中的package、class、missing_method、in_param、out_return、line等字段
3. 当JSON文件格式不正确时，系统应当提供清晰的错误信息并继续处理有效的条目

### 需求 2

**用户故事：** 作为Java开发工程师，我希望系统能够解析新框架的Java源码并提取所有可用的方法信息，以便建立候选API库。

#### 验收标准

1. 当系统接收到新框架源码目录路径时，系统应当递归扫描所有Java文件
2. 系统应当使用javalang解析器提取每个类的方法信息，包括方法名、参数类型、返回类型
3. 系统应当能够处理复杂的Java语法，包括泛型、内部类、注解等
4. 当遇到解析错误时，系统应当跳过有问题的文件并继续处理其他文件

### 需求 3

**用户故事：** 作为Java开发工程师，我希望系统能够使用机器学习模型进行语义相似度匹配，以便找到最合适的替代API。

#### 验收标准

1. 当系统加载GraphCodeBERT模型时，系统应当能够将方法描述转换为向量表示
2. 系统应当使用FAISS建立高效的向量索引，支持快速相似度检索
3. 对于每个缺失的方法，系统应当返回TOP-K个最相似的候选方法
4. 系统应当计算并返回相似度分数，帮助用户判断匹配质量

### 需求 4

**用户故事：** 作为Java开发工程师，我希望系统能够生成标准化的迁移建议JSON输出，以便后续处理和分析。

#### 验收标准

1. 当相似度匹配完成后，系统应当为每个缺失方法生成包含候选替代方法的JSON结构
2. 输出JSON应当包含原始缺失方法信息和TOP-K个候选方法的详细信息
3. 每个候选方法应当包含相似度分数、类名匹配标识和完整的方法签名信息
4. 系统应当支持将结果输出到指定的JSON文件

### 需求 5

**用户故事：** 作为Java开发工程师，我希望工具能够处理第一步生成的错误清单JSON格式，以便进行两步骤的完整流程。

#### 验收标准

1. 当系统执行第一步时，应当能够扫描Java项目并生成包含缺失方法的JSON错误清单
2. JSON错误清单应当包含package、class、missing_method、in_param、out_return、line等字段
3. 当系统执行第二步时，应当能够读取第一步生成的JSON文件作为输入
4. 系统应当支持独立执行每个步骤，也支持连续执行完整流程

### 需求 6

**用户故事：** 作为Java开发工程师，我希望工具具有良好的配置管理和错误处理功能，以便在不同环境中使用。

#### 验收标准

1. 当系统启动时，应当能够从配置文件或命令行参数读取项目路径、模型配置等参数
2. 系统应当提供清晰的命令行接口，支持分别执行第一步和第二步
3. 当遇到文件读写错误、模型加载失败等异常时，系统应当提供有意义的错误信息
4. 系统应当记录详细的执行日志，包括处理进度和结果统计

### 需求 7

**用户故事：** 作为Java开发工程师，我希望工具能够根据报错行号精确定位迁移前源码中的方法实现，以便更准确地理解缺失方法的功能。

#### 验收标准

1. 当系统接收到报错信息和迁移前源码路径时，应当能够根据行号精确定位到源码中的具体方法
2. 系统应当使用AST语法树分析提取完整的方法定义、参数、返回值和实现代码
3. 系统应当支持多个源码路径配置，优先在迁移前源码中查找方法实现
4. 当找到方法实现时，系统应当提取方法的完整上下文信息，包括调用链和依赖关系

### 需求 8

**用户故事：** 作为Java开发工程师，我希望工具能够使用大模型深度理解方法的功能和业务逻辑，以便生成高质量的方法语义知识库。

#### 验收标准

1. 当系统获得方法的完整实现代码时，应当使用大模型分析方法的业务逻辑和功能用途
2. 系统应当生成结构化的方法功能描述，包括业务用途、参数含义、返回值说明
3. 系统应当为每个方法生成业务标签和功能分类，便于后续匹配
4. 大模型生成的方法理解内容应当在知识库中具有最高权重，优先用于向量编码

### 需求 9

**用户故事：** 作为Java开发工程师，我希望向量编码策略能够突出大模型理解的方法语义，以便提升API匹配的准确性。

#### 验收标准

1. 当系统进行向量编码时，应当将大模型生成的方法功能描述作为主要编码内容
2. 系统应当实现加权向量融合，确保大模型理解内容占据最高权重（建议70%以上）
3. 系统应当结合方法签名、AST分析结果和调用上下文进行综合编码
4. 向量编码应当能够准确反映方法的语义特征和业务功能，提升匹配精度

### 需求 10

**用户故事：** 作为Java开发工程师，我希望系统能够智能处理新旧框架之间的参数差异，特别是新框架中新增的tenantId等框架参数。

#### 验收标准

1. 当系统分析方法匹配时，应当能够识别和区分框架参数（如tenantId、userId）和业务参数
2. 系统应当专注于核心业务参数的语义匹配，而不被框架参数差异影响匹配准确性
3. 系统应当能够检测参数封装模式（多个参数封装为DTO对象）并提供相应的迁移建议
4. 当找到匹配方法时，系统应当生成具体的参数迁移代码建议，包括如何获取和传递新增的框架参数