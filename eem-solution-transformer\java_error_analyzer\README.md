# Java Error Analyzer

Java错误分析器 - 自动分析Java编译错误并生成修复建议的工具

## 功能特性

- 🔍 **XML错误解析**: 解析JavaAnnotator.xml文件中的编译错误
- 📊 **错误分组**: 按文件自动分组和去重错误
- 🎯 **类名查找**: 自动查找缺失类的替代方案
- 🧠 **智能匹配**: 使用模糊匹配算法找到相似类
- 📝 **任务生成**: 自动生成按优先级排序的修复任务列表
- 📈 **详细报告**: 生成包含统计信息的详细分析报告

## 系统要求

- Python 3.7 或更高版本
- Windows 操作系统（支持cmd和PowerShell）
- Java项目（包含JavaAnnotator.xml错误文件）

## 安装和使用

### 1. 快速开始

#### 使用批处理脚本（推荐）
```cmd
# 使用默认参数运行
run_analyzer.bat

# 指定XML文件和项目路径
run_analyzer.bat "path\to\JavaAnnotator.xml" "path\to\project"
```

#### 使用PowerShell脚本
```powershell
# 使用默认参数运行
.\run_analyzer.ps1

# 指定参数运行
.\run_analyzer.ps1 -XmlFile "path\to\JavaAnnotator.xml" -ProjectPath "path\to\project" -OutputDir ".\output"
```

#### 直接使用Python
```cmd
python main.py --xml-file ../JavaAnnotator.xml --project-path ../ --output-dir ./output
```

### 2. 命令行参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--xml-file` | `../JavaAnnotator.xml` | JavaAnnotator.xml文件路径 |
| `--project-path` | `../` | 项目根目录路径 |
| `--output-dir` | `./output` | 输出目录 |
| `--log-level` | `INFO` | 日志级别 (DEBUG/INFO/WARNING/ERROR) |
| `--timeout` | `30` | 脚本执行超时时间(秒) |

### 3. 输出文件

运行完成后，会在输出目录生成以下文件：

- **问题列表.md**: 详细的错误列表，按文件分组
- **task.md**: 按优先级排序的修复任务列表
- **java_error_analyzer.log**: 详细的运行日志

## 工作流程

### 1. 错误分析阶段
```
XML文件 → 错误解析 → 错误分组 → 错误去重 → 问题分类
```

### 2. 解决方案生成阶段
```
错误分类 → 类名查找 → 模糊匹配 → 生成建议 → 任务排序
```

### 3. 任务执行顺序
1. 🟢 **常量类和配置类** - 优先级最高
2. 🟢 **实体类和数据模型** - 基础数据结构
3. 🟢 **工具类和帮助类** - 通用功能类
4. 🟡 **DAO层** - 数据访问层
5. 🟡 **Service层** - 业务逻辑层
6. 🟡 **Controller层** - 控制器层
7. 🔴 **废弃API处理** - 最后处理

## 核心组件

### ErrorParser
- 解析JavaAnnotator.xml文件
- 提取编译错误信息
- 支持多种错误类型识别

### ErrorGrouper
- 按文件路径分组错误
- 智能去重，避免重复错误
- 生成统计信息

### ClassNameFinder
- 集成FindNameFromJarAndSource.py脚本
- 在项目源码和依赖中查找类
- 支持精确匹配和模糊匹配

### FuzzyMatcher
- 基于多维度相似度算法
- 支持语义理解和业务逻辑匹配
- 提供详细的匹配原因分析

## 配置说明

### ProcessingConfig
```python
config = ProcessingConfig(
    script_path="./FindNameFromJarAndSource.py",  # 类查找脚本路径
    project_path="../",                   # 项目根目录
    timeout_seconds=30,                   # 超时时间
    max_results=10                        # 最大结果数
)
```

## 错误类型处理

### Import问题
- 自动查找替代类
- 提供多个候选方案
- 标注匹配置信度

### 符号未找到
- 检查类路径和依赖
- 分析包结构问题
- 提供修复建议

### 废弃API
- 识别废弃的类和方法
- 查找新的API替代
- 标记为低优先级处理

## 示例输出

### 问题列表示例
```markdown
# Java编译错误问题列表

## 统计信息
- 总文件数: 25
- 总错误数: 156
- 平均每文件错误数: 6.2

## 错误详情

### src/main/java/com/example/Service.java
错误数量: 8

#### 错误 1
- **行号**: 15
- **描述**: cannot find symbol: class UserService
- **错误类型**: Import问题
```

### 任务列表示例
```markdown
# Java错误修复任务列表

## 🟢 常量类和配置类修复

### 任务 1: 修复 src/main/java/com/example/Constants.java

#### Import问题修复
- **行 10**: package com.old.package does not exist
  - 问题元素: `com.old.package.Status`
  - 建议替换:
    - 🟢 `com.new.package.Status` (精确匹配)
    - 🟡 `com.other.package.StatusEnum` (相似度: 0.85)
```

## 故障排除

### 常见问题

1. **Python未找到**
   ```
   错误: 未找到Python
   解决: 安装Python并添加到PATH环境变量
   ```

2. **XML文件不存在**
   ```
   错误: XML文件不存在
   解决: 确保JavaAnnotator.xml文件路径正确
   ```

3. **权限问题**
   ```
   错误: 无法创建输出目录
   解决: 以管理员身份运行或检查目录权限
   ```

### 调试模式

启用详细日志输出：
```cmd
python main.py --log-level DEBUG --xml-file ../JavaAnnotator.xml
```

## 扩展开发

### 添加新的错误类型处理
```python
# 在ErrorParser中添加新的错误模式
def extract_custom_error(self, problem_node):
    # 自定义错误提取逻辑
    pass
```

### 自定义匹配算法
```python
# 在FuzzyMatcher中添加新的相似度计算
def _calculate_custom_similarity(self, name1, name2):
    # 自定义相似度算法
    pass
```

## 版本历史

- **v1.1.0**: 添加AI语义理解功能
- **v1.0.0**: 初始版本，基础错误分析功能

## 许可证

本项目采用MIT许可证。

## 贡献

欢迎提交Issue和Pull Request来改进这个工具。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 创建GitHub Issue
- 发送邮件至项目维护者